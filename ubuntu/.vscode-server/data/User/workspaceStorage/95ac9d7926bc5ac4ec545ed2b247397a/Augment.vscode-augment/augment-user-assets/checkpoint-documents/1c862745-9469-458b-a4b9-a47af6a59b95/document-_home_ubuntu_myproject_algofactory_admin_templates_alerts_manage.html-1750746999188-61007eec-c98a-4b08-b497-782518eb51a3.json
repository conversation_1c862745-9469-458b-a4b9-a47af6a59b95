{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/admin/templates/alerts/manage.html"}, "modifiedCode": "{% extends \"base.html\" %}\n\n{% block title %}Alerts & Notifications - AlgoFactory Admin{% endblock %}\n{% block page_title %}Alerts & Notifications{% endblock %}\n\n{% block content %}\n<div class=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n    <div class=\"stat bg-base-200 rounded-xl shadow-sm\">\n        <div class=\"stat-figure text-error\">\n            <i class=\"fas fa-exclamation-triangle text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">Critical Alerts</div>\n        <div class=\"stat-value text-error\">2</div>\n        <div class=\"stat-desc\">Require attention</div>\n    </div>\n    \n    <div class=\"stat bg-base-200 rounded-xl shadow-sm\">\n        <div class=\"stat-figure text-warning\">\n            <i class=\"fas fa-exclamation-circle text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">Warnings</div>\n        <div class=\"stat-value text-warning\">5</div>\n        <div class=\"stat-desc\">Monitor closely</div>\n    </div>\n    \n    <div class=\"stat bg-base-200 rounded-xl shadow-sm\">\n        <div class=\"stat-figure text-info\">\n            <i class=\"fas fa-info-circle text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">Info Alerts</div>\n        <div class=\"stat-value text-info\">12</div>\n        <div class=\"stat-desc\">Informational</div>\n    </div>\n</div>\n\n<div class=\"card bg-base-200 shadow-sm\">\n    <div class=\"card-body\">\n        <h2 class=\"card-title mb-6\">\n            <i class=\"fas fa-bell text-primary\"></i>\n            Recent Alerts\n        </h2>\n        \n        <div class=\"space-y-4\">\n            <div class=\"alert alert-error\">\n                <i class=\"fas fa-exclamation-triangle\"></i>\n                <div>\n                    <div class=\"font-bold\">High Memory Usage</div>\n                    <div class=\"text-sm\">Memory usage exceeded 90% threshold</div>\n                </div>\n                <div class=\"text-sm\">2 min ago</div>\n            </div>\n            \n            <div class=\"alert alert-warning\">\n                <i class=\"fas fa-exclamation-circle\"></i>\n                <div>\n                    <div class=\"font-bold\">SSL Certificate Expiring</div>\n                    <div class=\"text-sm\">Certificate for 8011.algofactory.in expires in 21 days</div>\n                </div>\n                <div class=\"text-sm\">1 hour ago</div>\n            </div>\n            \n            <div class=\"alert alert-info\">\n                <i class=\"fas fa-info-circle\"></i>\n                <div>\n                    <div class=\"font-bold\">System Optimization Completed</div>\n                    <div class=\"text-sm\">Automatic system cleanup freed 150MB</div>\n                </div>\n                <div class=\"text-sm\">3 hours ago</div>\n            </div>\n        </div>\n    </div>\n</div>\n{% endblock %}\n"}