{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/setup_24x7.sh"}, "originalCode": "#!/bin/bash\n\n# AlgoFactory 24/7 Setup and Management Script\n# This script helps you manage your AlgoFactory application for 24/7 operation\n\nset -e\n\n# Colors for output\nRED='\\033[0;31m'\nGREEN='\\033[0;32m'\nYELLOW='\\033[1;33m'\nBLUE='\\033[0;34m'\nNC='\\033[0m' # No Color\n\n# Configuration\nPROJECT_DIR=\"/home/<USER>/myproject/algofactory\"\nDOMAIN=\"algo.algofactory.in\"\n\n# Logging function\nlog() {\n    echo -e \"${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1\"\n}\n\nsuccess() {\n    echo -e \"${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1\"\n}\n\nwarning() {\n    echo -e \"${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1\"\n}\n\nerror() {\n    echo -e \"${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1\" >&2\n}\n\nshow_status() {\n    log \"=== AlgoFactory 24/7 Status ===\"\n    \n    # Check application status\n    echo -e \"\\n${BLUE}Application Status:${NC}\"\n    cd \"$PROJECT_DIR\"\n    ./start.sh status\n    \n    # Check systemd service (if enabled)\n    echo -e \"\\n${BLUE}SystemD Service Status:${NC}\"\n    if systemctl is-enabled algofactory >/dev/null 2>&1; then\n        sudo systemctl status algofactory --no-pager -l\n    else\n        warning \"SystemD service not enabled\"\n    fi\n    \n    # Check Nginx status\n    echo -e \"\\n${BLUE}Nginx Status:${NC}\"\n    sudo systemctl status nginx --no-pager -l\n    \n    # Check monitoring\n    echo -e \"\\n${BLUE}Health Check:${NC}\"\n    /home/<USER>/shared-venv/bin/python3 \"$PROJECT_DIR/monitoring.py\" health\n    \n    # Check firewall\n    echo -e \"\\n${BLUE}Firewall Status:${NC}\"\n    sudo ufw status\n}\n\nenable_24x7() {\n    log \"Enabling 24/7 operation...\"\n    \n    # Enable and start systemd service\n    log \"Configuring systemd service...\"\n    sudo systemctl enable algofactory\n    sudo systemctl start algofactory\n    \n    # Enable Nginx\n    log \"Enabling Nginx...\"\n    sudo systemctl enable nginx\n    sudo systemctl start nginx\n    \n    success \"24/7 operation enabled!\"\n    success \"Your application will now:\"\n    success \"  - Start automatically on boot\"\n    success \"  - Restart automatically if it crashes\"\n    success \"  - Be accessible via Nginx reverse proxy\"\n}\n\ndisable_24x7() {\n    log \"Disabling 24/7 operation...\"\n    \n    # Stop and disable systemd service\n    sudo systemctl stop algofactory\n    sudo systemctl disable algofactory\n    \n    warning \"24/7 operation disabled. Application will not start automatically.\"\n}\n\nsetup_ssl() {\n    log \"Setting up SSL certificate for $DOMAIN...\"\n    \n    # Check if domain is pointing to this server\n    log \"Checking domain configuration...\"\n    \n    # Get server's public IP\n    SERVER_IP=$(curl -s ifconfig.me || curl -s ipinfo.io/ip || echo \"Unable to detect\")\n    log \"Server IP: $SERVER_IP\"\n    \n    # Check domain resolution\n    DOMAIN_IP=$(dig +short \"$DOMAIN\" | tail -n1)\n    log \"Domain IP: $DOMAIN_IP\"\n    \n    if [ \"$SERVER_IP\" = \"$DOMAIN_IP\" ]; then\n        success \"Domain is correctly pointing to this server!\"\n        \n        # Get Let's Encrypt certificate\n        log \"Obtaining Let's Encrypt certificate...\"\n        sudo certbot --nginx -d \"$DOMAIN\" --non-interactive --agree-tos --email <EMAIL>\n        \n        success \"SSL certificate installed successfully!\"\n    else\n        warning \"Domain is not pointing to this server yet.\"\n        warning \"Please update your DNS settings in Hostinger:\"\n        warning \"  1. Go to your Hostinger control panel\"\n        warning \"  2. Navigate to DNS settings for algofactory.in\"\n        warning \"  3. Update the A record for 'algo' subdomain to: $SERVER_IP\"\n        warning \"  4. Wait for DNS propagation (5-30 minutes)\"\n        warning \"  5. Run this script again with 'ssl' option\"\n    fi\n}\n\nshow_monitoring_info() {\n    log \"=== Monitoring Information ===\"\n    \n    echo -e \"\\n${GREEN}Local Monitoring:${NC}\"\n    echo \"  • Status check: cd $PROJECT_DIR && ./start.sh status\"\n    echo \"  • Health check: /home/<USER>/shared-venv/bin/python3 $PROJECT_DIR/monitoring.py health\"\n    echo \"  • Full status: /home/<USER>/shared-venv/bin/python3 $PROJECT_DIR/monitoring.py full\"\n    echo \"  • Live monitoring: /home/<USER>/shared-venv/bin/python3 $PROJECT_DIR/monitoring.py monitor\"\n    \n    echo -e \"\\n${GREEN}Web-based Monitoring:${NC}\"\n    echo \"  • Dashboard: file://$PROJECT_DIR/monitor_dashboard.html\"\n    echo \"  • Simple status: http://$DOMAIN:8080/monitor\"\n    \n    echo -e \"\\n${GREEN}External Monitoring URLs:${NC}\"\n    echo \"  • Main site: https://$DOMAIN\"\n    echo \"  • Health endpoint: https://$DOMAIN/health (if implemented)\"\n    echo \"  • Status endpoint: https://$DOMAIN/status (restricted access)\"\n    \n    echo -e \"\\n${GREEN}Monitoring Services You Can Use:${NC}\"\n    echo \"  • UptimeRobot (free): https://uptimerobot.com\"\n    echo \"  • Pingdom: https://pingdom.com\"\n    echo \"  • StatusCake: https://statuscake.com\"\n    echo \"  • Site24x7: https://site24x7.com\"\n    \n    echo -e \"\\n${GREEN}Log Files:${NC}\"\n    echo \"  • Application logs: $PROJECT_DIR/logs/\"\n    echo \"  • Nginx access log: /var/log/nginx/algofactory_access.log\"\n    echo \"  • Nginx error log: /var/log/nginx/algofactory_error.log\"\n    echo \"  • System logs: sudo journalctl -u algofactory -f\"\n}\n\nshow_domain_setup() {\n    log \"=== Domain Setup Instructions ===\"\n    \n    SERVER_IP=$(curl -s ifconfig.me || curl -s ipinfo.io/ip || echo \"Unable to detect\")\n    \n    echo -e \"\\n${GREEN}Your Server IP:${NC} $SERVER_IP\"\n    echo -e \"\\n${GREEN}Hostinger DNS Setup:${NC}\"\n    echo \"  1. Login to your Hostinger account\"\n    echo \"  2. Go to 'Domains' section\"\n    echo \"  3. Click on 'algofactory.in'\"\n    echo \"  4. Go to 'DNS Zone'\"\n    echo \"  5. Add/Edit these records:\"\n    echo \"     • Type: A\"\n    echo \"     • Name: algo\"\n    echo \"     • Value: $SERVER_IP\"\n    echo \"     • TTL: 3600\"\n    echo \"  6. Save changes\"\n    echo \"  7. Wait 5-30 minutes for DNS propagation\"\n    echo \"  8. Test with: nslookup algo.algofactory.in\"\n    \n    echo -e \"\\n${GREEN}After DNS Setup:${NC}\"\n    echo \"  • Run: $0 ssl (to get SSL certificate)\"\n    echo \"  • Your site will be available at: https://algo.algofactory.in\"\n}\n\nshow_help() {\n    echo -e \"${GREEN}AlgoFactory 24/7 Management Script${NC}\"\n    echo \"\"\n    echo \"Usage: $0 [COMMAND]\"\n    echo \"\"\n    echo \"Commands:\"\n    echo \"  status      - Show comprehensive status\"\n    echo \"  enable      - Enable 24/7 operation (systemd + nginx)\"\n    echo \"  disable     - Disable 24/7 operation\"\n    echo \"  ssl         - Setup SSL certificate (after DNS is configured)\"\n    echo \"  monitor     - Show monitoring information\"\n    echo \"  domain      - Show domain setup instructions\"\n    echo \"  help        - Show this help message\"\n    echo \"\"\n    echo \"Examples:\"\n    echo \"  $0 status   # Check current status\"\n    echo \"  $0 enable   # Enable 24/7 operation\"\n    echo \"  $0 ssl      # Setup SSL certificate\"\n}\n\n# Main execution\ncase \"${1:-help}\" in\n    \"status\")\n        show_status\n        ;;\n    \"enable\")\n        enable_24x7\n        ;;\n    \"disable\")\n        disable_24x7\n        ;;\n    \"ssl\")\n        setup_ssl\n        ;;\n    \"monitor\")\n        show_monitoring_info\n        ;;\n    \"domain\")\n        show_domain_setup\n        ;;\n    \"help\"|*)\n        show_help\n        ;;\nesac\n", "modifiedCode": "#!/bin/bash\n\n# AlgoFactory 24/7 Setup and Management Script\n# This script helps you manage your AlgoFactory application for 24/7 operation\n\nset -e\n\n# Colors for output\nRED='\\033[0;31m'\nGREEN='\\033[0;32m'\nYELLOW='\\033[1;33m'\nBLUE='\\033[0;34m'\nNC='\\033[0m' # No Color\n\n# Configuration\nPROJECT_DIR=\"/home/<USER>/myproject/algofactory\"\nDOMAIN=\"algo.algofactory.in\"\n\n# Logging function\nlog() {\n    echo -e \"${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1\"\n}\n\nsuccess() {\n    echo -e \"${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1\"\n}\n\nwarning() {\n    echo -e \"${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1\"\n}\n\nerror() {\n    echo -e \"${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1\" >&2\n}\n\nshow_status() {\n    log \"=== AlgoFactory 24/7 Status ===\"\n    \n    # Check application status\n    echo -e \"\\n${BLUE}Application Status:${NC}\"\n    cd \"$PROJECT_DIR\"\n    ./start.sh status\n    \n    # Check systemd service (if enabled)\n    echo -e \"\\n${BLUE}SystemD Service Status:${NC}\"\n    if systemctl is-enabled algofactory >/dev/null 2>&1; then\n        sudo systemctl status algofactory --no-pager -l\n    else\n        warning \"SystemD service not enabled\"\n    fi\n    \n    # Check Nginx status\n    echo -e \"\\n${BLUE}Nginx Status:${NC}\"\n    sudo systemctl status nginx --no-pager -l\n    \n    # Check monitoring\n    echo -e \"\\n${BLUE}Health Check:${NC}\"\n    /home/<USER>/shared-venv/bin/python3 \"$PROJECT_DIR/monitoring.py\" health\n    \n    # Check firewall\n    echo -e \"\\n${BLUE}Firewall Status:${NC}\"\n    sudo ufw status\n}\n\nenable_24x7() {\n    log \"Enabling 24/7 operation...\"\n    \n    # Enable and start systemd service\n    log \"Configuring systemd service...\"\n    sudo systemctl enable algofactory\n    sudo systemctl start algofactory\n    \n    # Enable Nginx\n    log \"Enabling Nginx...\"\n    sudo systemctl enable nginx\n    sudo systemctl start nginx\n    \n    success \"24/7 operation enabled!\"\n    success \"Your application will now:\"\n    success \"  - Start automatically on boot\"\n    success \"  - Restart automatically if it crashes\"\n    success \"  - Be accessible via Nginx reverse proxy\"\n}\n\ndisable_24x7() {\n    log \"Disabling 24/7 operation...\"\n    \n    # Stop and disable systemd service\n    sudo systemctl stop algofactory\n    sudo systemctl disable algofactory\n    \n    warning \"24/7 operation disabled. Application will not start automatically.\"\n}\n\nsetup_ssl() {\n    log \"Setting up SSL certificate for $DOMAIN...\"\n    \n    # Check if domain is pointing to this server\n    log \"Checking domain configuration...\"\n    \n    # Get server's public IP\n    SERVER_IP=$(curl -s ifconfig.me || curl -s ipinfo.io/ip || echo \"Unable to detect\")\n    log \"Server IP: $SERVER_IP\"\n    \n    # Check domain resolution\n    DOMAIN_IP=$(dig +short \"$DOMAIN\" | tail -n1)\n    log \"Domain IP: $DOMAIN_IP\"\n    \n    if [ \"$SERVER_IP\" = \"$DOMAIN_IP\" ]; then\n        success \"Domain is correctly pointing to this server!\"\n        \n        # Get Let's Encrypt certificate\n        log \"Obtaining Let's Encrypt certificate...\"\n        sudo certbot --nginx -d \"$DOMAIN\" --non-interactive --agree-tos --email <EMAIL>\n        \n        success \"SSL certificate installed successfully!\"\n    else\n        warning \"Domain is not pointing to this server yet.\"\n        warning \"Please update your DNS settings in Hostinger:\"\n        warning \"  1. Go to your Hostinger control panel\"\n        warning \"  2. Navigate to DNS settings for algofactory.in\"\n        warning \"  3. Update the A record for 'algo' subdomain to: $SERVER_IP\"\n        warning \"  4. Wait for DNS propagation (5-30 minutes)\"\n        warning \"  5. Run this script again with 'ssl' option\"\n    fi\n}\n\nshow_monitoring_info() {\n    log \"=== Monitoring Information ===\"\n    \n    echo -e \"\\n${GREEN}Local Monitoring:${NC}\"\n    echo \"  • Status check: cd $PROJECT_DIR && ./start.sh status\"\n    echo \"  • Health check: /home/<USER>/shared-venv/bin/python3 $PROJECT_DIR/monitoring.py health\"\n    echo \"  • Full status: /home/<USER>/shared-venv/bin/python3 $PROJECT_DIR/monitoring.py full\"\n    echo \"  • Live monitoring: /home/<USER>/shared-venv/bin/python3 $PROJECT_DIR/monitoring.py monitor\"\n    \n    echo -e \"\\n${GREEN}Web-based Monitoring:${NC}\"\n    echo \"  • Dashboard: file://$PROJECT_DIR/monitor_dashboard.html\"\n    echo \"  • Simple status: http://$DOMAIN:8080/monitor\"\n    \n    echo -e \"\\n${GREEN}External Monitoring URLs:${NC}\"\n    echo \"  • Main site: https://$DOMAIN\"\n    echo \"  • Health endpoint: https://$DOMAIN/health (if implemented)\"\n    echo \"  • Status endpoint: https://$DOMAIN/status (restricted access)\"\n    \n    echo -e \"\\n${GREEN}Monitoring Services You Can Use:${NC}\"\n    echo \"  • UptimeRobot (free): https://uptimerobot.com\"\n    echo \"  • Pingdom: https://pingdom.com\"\n    echo \"  • StatusCake: https://statuscake.com\"\n    echo \"  • Site24x7: https://site24x7.com\"\n    \n    echo -e \"\\n${GREEN}Log Files:${NC}\"\n    echo \"  • Application logs: $PROJECT_DIR/logs/\"\n    echo \"  • Nginx access log: /var/log/nginx/algofactory_access.log\"\n    echo \"  • Nginx error log: /var/log/nginx/algofactory_error.log\"\n    echo \"  • System logs: sudo journalctl -u algofactory -f\"\n}\n\nshow_domain_setup() {\n    log \"=== Domain Setup Instructions ===\"\n    \n    SERVER_IP=$(curl -s ifconfig.me || curl -s ipinfo.io/ip || echo \"Unable to detect\")\n    \n    echo -e \"\\n${GREEN}Your Server IP:${NC} $SERVER_IP\"\n    echo -e \"\\n${GREEN}Hostinger DNS Setup:${NC}\"\n    echo \"  1. Login to your Hostinger account\"\n    echo \"  2. Go to 'Domains' section\"\n    echo \"  3. Click on 'algofactory.in'\"\n    echo \"  4. Go to 'DNS Zone'\"\n    echo \"  5. Add/Edit these records:\"\n    echo \"     • Type: A\"\n    echo \"     • Name: algo\"\n    echo \"     • Value: $SERVER_IP\"\n    echo \"     • TTL: 3600\"\n    echo \"  6. Save changes\"\n    echo \"  7. Wait 5-30 minutes for DNS propagation\"\n    echo \"  8. Test with: nslookup algo.algofactory.in\"\n    \n    echo -e \"\\n${GREEN}After DNS Setup:${NC}\"\n    echo \"  • Run: $0 ssl (to get SSL certificate)\"\n    echo \"  • Your site will be available at: https://algo.algofactory.in\"\n}\n\nshow_help() {\n    echo -e \"${GREEN}AlgoFactory 24/7 Management Script${NC}\"\n    echo \"\"\n    echo \"Usage: $0 [COMMAND]\"\n    echo \"\"\n    echo \"Commands:\"\n    echo \"  status      - Show comprehensive status\"\n    echo \"  enable      - Enable 24/7 operation (systemd + nginx)\"\n    echo \"  disable     - Disable 24/7 operation\"\n    echo \"  ssl         - Setup SSL certificate (after DNS is configured)\"\n    echo \"  monitor     - Show monitoring information\"\n    echo \"  domain      - Show domain setup instructions\"\n    echo \"  help        - Show this help message\"\n    echo \"\"\n    echo \"Examples:\"\n    echo \"  $0 status   # Check current status\"\n    echo \"  $0 enable   # Enable 24/7 operation\"\n    echo \"  $0 ssl      # Setup SSL certificate\"\n}\n\n# Main execution\ncase \"${1:-help}\" in\n    \"status\")\n        show_status\n        ;;\n    \"enable\")\n        enable_24x7\n        ;;\n    \"disable\")\n        disable_24x7\n        ;;\n    \"ssl\")\n        setup_ssl\n        ;;\n    \"monitor\")\n        show_monitoring_info\n        ;;\n    \"domain\")\n        show_domain_setup\n        ;;\n    \"help\"|*)\n        show_help\n        ;;\nesac\n"}