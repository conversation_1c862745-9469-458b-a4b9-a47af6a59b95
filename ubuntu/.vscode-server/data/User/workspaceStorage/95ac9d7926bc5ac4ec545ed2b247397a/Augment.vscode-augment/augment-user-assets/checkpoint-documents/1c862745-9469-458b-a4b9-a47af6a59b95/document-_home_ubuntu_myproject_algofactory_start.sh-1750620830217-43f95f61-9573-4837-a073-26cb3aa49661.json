{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/start.sh"}, "originalCode": "#!/bin/bash\r\n\r\n# AlgoFactory Robust Startup Script\r\n# This script automatically starts app.py with full error handling and recovery\r\n\r\nset -e  # Exit on any error\r\n\r\n# Configuration\r\nPROJECT_DIR=\"/home/<USER>/myproject/algofactory\"\r\nSHARED_VENV=\"/home/<USER>/shared-venv\"\r\nAPP_FILE=\"app.py\"\r\nLOG_DIR=\"$PROJECT_DIR/logs\"\r\nPID_FILE=\"$PROJECT_DIR/app.pid\"\r\nMAX_RETRIES=5\r\nRETRY_DELAY=10\r\n\r\n# Colors for output\r\nRED='\\033[0;31m'\r\nGREEN='\\033[0;32m'\r\nYELLOW='\\033[1;33m'\r\nBLUE='\\033[0;34m'\r\nNC='\\033[0m' # No Color\r\n\r\n# Logging function\r\nlog() {\r\n    echo -e \"${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1\"\r\n}\r\n\r\nerror() {\r\n    echo -e \"${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1\" >&2\r\n}\r\n\r\nsuccess() {\r\n    echo -e \"${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1\"\r\n}\r\n\r\nwarning() {\r\n    echo -e \"${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1\"\r\n}\r\n\r\n# Function to check if app is running\r\nis_app_running() {\r\n    if [ -f \"$PID_FILE\" ]; then\r\n        local pid=$(cat \"$PID_FILE\")\r\n        if ps -p \"$pid\" > /dev/null 2>&1; then\r\n            return 0\r\n        else\r\n            rm -f \"$PID_FILE\"\r\n            return 1\r\n        fi\r\n    fi\r\n    return 1\r\n}\r\n\r\n# Function to stop the app\r\nstop_app() {\r\n    if [ -f \"$PID_FILE\" ]; then\r\n        local pid=$(cat \"$PID_FILE\")\r\n        log \"Stopping application (PID: $pid)...\"\r\n        kill -TERM \"$pid\" 2>/dev/null || true\r\n        sleep 5\r\n        if ps -p \"$pid\" > /dev/null 2>&1; then\r\n            warning \"Application didn't stop gracefully, forcing kill...\"\r\n            kill -KILL \"$pid\" 2>/dev/null || true\r\n        fi\r\n        rm -f \"$PID_FILE\"\r\n        success \"Application stopped\"\r\n    fi\r\n}\r\n\r\n# Function to setup environment\r\nsetup_environment() {\r\n    log \"Setting up environment...\"\r\n\r\n    # Change to project directory\r\n    cd \"$PROJECT_DIR\" || {\r\n        error \"Failed to change to project directory: $PROJECT_DIR\"\r\n        exit 1\r\n    }\r\n\r\n    # Create necessary directories with full permissions\r\n    log \"Creating necessary directories...\"\r\n    mkdir -p db logs tmp static/uploads\r\n\r\n    # Set full permissions (777) for all necessary directories\r\n    chmod -R 777 db logs tmp static 2>/dev/null || {\r\n        warning \"Could not set permissions on some directories (may be mounted volumes)\"\r\n    }\r\n\r\n    # Check if shared virtual environment exists\r\n    if [ ! -d \"$SHARED_VENV\" ]; then\r\n        error \"Shared virtual environment not found at: $SHARED_VENV\"\r\n        log \"Creating shared virtual environment...\"\r\n        python3 -m venv \"$SHARED_VENV\" || {\r\n            error \"Failed to create virtual environment\"\r\n            exit 1\r\n        }\r\n    fi\r\n\r\n    # Check if virtual environment has Python\r\n    if [ ! -f \"$SHARED_VENV/bin/python\" ]; then\r\n        error \"Python not found in virtual environment: $SHARED_VENV/bin/python\"\r\n        exit 1\r\n    fi\r\n\r\n    success \"Environment setup completed\"\r\n}\r\n\r\n# Function to install/update dependencies\r\ninstall_dependencies() {\r\n    log \"Installing/updating dependencies...\"\r\n\r\n    # Activate virtual environment\r\n    source \"$SHARED_VENV/bin/activate\" || {\r\n        error \"Failed to activate virtual environment\"\r\n        exit 1\r\n    }\r\n\r\n    # Upgrade pip\r\n    pip install --upgrade pip > /dev/null 2>&1 || {\r\n        warning \"Failed to upgrade pip\"\r\n    }\r\n\r\n    # Install requirements if file exists\r\n    if [ -f \"requirements.txt\" ]; then\r\n        log \"Installing requirements from requirements.txt...\"\r\n        pip install -r requirements.txt || {\r\n            error \"Failed to install requirements\"\r\n            exit 1\r\n        }\r\n    fi\r\n\r\n    # Install additional packages that might be needed\r\n    pip install gunicorn eventlet python-dotenv > /dev/null 2>&1 || {\r\n        warning \"Failed to install some additional packages\"\r\n    }\r\n\r\n    success \"Dependencies installed successfully\"\r\n}\r\n\r\n# Function to start the application\r\nstart_app() {\r\n    local retry_count=0\r\n\r\n    while [ $retry_count -lt $MAX_RETRIES ]; do\r\n        log \"Starting AlgoFactory application (attempt $((retry_count + 1))/$MAX_RETRIES)...\"\r\n\r\n        # Activate virtual environment\r\n        source \"$SHARED_VENV/bin/activate\" || {\r\n            error \"Failed to activate virtual environment\"\r\n            exit 1\r\n        }\r\n\r\n        # Check if app.py exists\r\n        if [ ! -f \"$APP_FILE\" ]; then\r\n            error \"Application file not found: $APP_FILE\"\r\n            exit 1\r\n        fi\r\n\r\n        # Start the application with gunicorn\r\n        nohup \"$SHARED_VENV/bin/gunicorn\" \\\r\n            --bind=0.0.0.0:5000 \\\r\n            --worker-class=eventlet \\\r\n            --workers=1 \\\r\n            --timeout=120 \\\r\n            --keep-alive=2 \\\r\n            --max-requests=1000 \\\r\n            --max-requests-jitter=100 \\\r\n            --preload \\\r\n            --log-level=info \\\r\n            --access-logfile=\"$LOG_DIR/access.log\" \\\r\n            --error-logfile=\"$LOG_DIR/error.log\" \\\r\n            --capture-output \\\r\n            --enable-stdio-inheritance \\\r\n            app:app > \"$LOG_DIR/app.log\" 2>&1 &\r\n\r\n        local app_pid=$!\r\n        echo $app_pid > \"$PID_FILE\"\r\n\r\n        # Wait a moment and check if the app started successfully\r\n        sleep 5\r\n\r\n        if ps -p \"$app_pid\" > /dev/null 2>&1; then\r\n            success \"AlgoFactory application started successfully (PID: $app_pid)\"\r\n            success \"Application is running on http://0.0.0.0:5000\"\r\n            success \"Logs are available in: $LOG_DIR/\"\r\n            return 0\r\n        else\r\n            error \"Application failed to start (attempt $((retry_count + 1)))\"\r\n            rm -f \"$PID_FILE\"\r\n\r\n            # Show last few lines of error log\r\n            if [ -f \"$LOG_DIR/error.log\" ]; then\r\n                error \"Last few lines from error log:\"\r\n                tail -10 \"$LOG_DIR/error.log\" | while read line; do\r\n                    error \"  $line\"\r\n                done\r\n            fi\r\n\r\n            retry_count=$((retry_count + 1))\r\n            if [ $retry_count -lt $MAX_RETRIES ]; then\r\n                warning \"Retrying in $RETRY_DELAY seconds...\"\r\n                sleep $RETRY_DELAY\r\n            fi\r\n        fi\r\n    done\r\n\r\n    error \"Failed to start application after $MAX_RETRIES attempts\"\r\n    exit 1\r\n}\r\n\r\n# Function to monitor the application\r\nmonitor_app() {\r\n    log \"Starting application monitor...\"\r\n\r\n    while true; do\r\n        if ! is_app_running; then\r\n            warning \"Application is not running, attempting to restart...\"\r\n            start_app\r\n        fi\r\n        sleep 30  # Check every 30 seconds\r\n    done\r\n}\r\n\r\n# Main execution\r\nmain() {\r\n    log \"=== AlgoFactory Startup Script ===\"\r\n    log \"Project Directory: $PROJECT_DIR\"\r\n    log \"Shared Virtual Environment: $SHARED_VENV\"\r\n    log \"Application File: $APP_FILE\"\r\n\r\n    # Handle command line arguments\r\n    case \"${1:-start}\" in\r\n        \"start\")\r\n            if is_app_running; then\r\n                warning \"Application is already running\"\r\n                exit 0\r\n            fi\r\n            setup_environment\r\n            install_dependencies\r\n            start_app\r\n            ;;\r\n        \"stop\")\r\n            stop_app\r\n            ;;\r\n        \"restart\")\r\n            stop_app\r\n            setup_environment\r\n            install_dependencies\r\n            start_app\r\n            ;;\r\n        \"status\")\r\n            if is_app_running; then\r\n                success \"Application is running (PID: $(cat $PID_FILE))\"\r\n            else\r\n                warning \"Application is not running\"\r\n            fi\r\n            ;;\r\n        \"monitor\")\r\n            setup_environment\r\n            install_dependencies\r\n            monitor_app\r\n            ;;\r\n        \"logs\")\r\n            if [ -f \"$LOG_DIR/app.log\" ]; then\r\n                tail -f \"$LOG_DIR/app.log\"\r\n            else\r\n                error \"Log file not found: $LOG_DIR/app.log\"\r\n            fi\r\n            ;;\r\n        *)\r\n            echo \"Usage: $0 {start|stop|restart|status|monitor|logs}\"\r\n            echo \"  start   - Start the application\"\r\n            echo \"  stop    - Stop the application\"\r\n            echo \"  restart - Restart the application\"\r\n            echo \"  status  - Check application status\"\r\n            echo \"  monitor - Start with continuous monitoring\"\r\n            echo \"  logs    - Show application logs\"\r\n            exit 1\r\n            ;;\r\n    esac\r\n}\r\n\r\n# Trap signals for graceful shutdown\r\ntrap 'stop_app; exit 0' SIGTERM SIGINT\r\n\r\n# Run main function\r\nmain \"$@\"\r\n", "modifiedCode": "#!/bin/bash\r\n\r\n# AlgoFactory Robust Startup Script\r\n# This script automatically starts app.py with full error handling and recovery\r\n\r\nset -e  # Exit on any error\r\n\r\n# Configuration\r\nPROJECT_DIR=\"/home/<USER>/myproject/algofactory\"\r\nSHARED_VENV=\"/home/<USER>/shared-venv\"\r\nAPP_FILE=\"app.py\"\r\nLOG_DIR=\"$PROJECT_DIR/logs\"\r\nPID_FILE=\"$PROJECT_DIR/app.pid\"\r\nMAX_RETRIES=5\r\nRETRY_DELAY=10\r\n\r\n# Colors for output\r\nRED='\\033[0;31m'\r\nGREEN='\\033[0;32m'\r\nYELLOW='\\033[1;33m'\r\nBLUE='\\033[0;34m'\r\nNC='\\033[0m' # No Color\r\n\r\n# Logging function\r\nlog() {\r\n    echo -e \"${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1\"\r\n}\r\n\r\nerror() {\r\n    echo -e \"${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1\" >&2\r\n}\r\n\r\nsuccess() {\r\n    echo -e \"${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1\"\r\n}\r\n\r\nwarning() {\r\n    echo -e \"${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1\"\r\n}\r\n\r\n# Function to check if app is running\r\nis_app_running() {\r\n    if [ -f \"$PID_FILE\" ]; then\r\n        local pid=$(cat \"$PID_FILE\")\r\n        if ps -p \"$pid\" > /dev/null 2>&1; then\r\n            return 0\r\n        else\r\n            rm -f \"$PID_FILE\"\r\n            return 1\r\n        fi\r\n    fi\r\n    return 1\r\n}\r\n\r\n# Function to stop the app\r\nstop_app() {\r\n    if [ -f \"$PID_FILE\" ]; then\r\n        local pid=$(cat \"$PID_FILE\")\r\n        log \"Stopping application (PID: $pid)...\"\r\n        kill -TERM \"$pid\" 2>/dev/null || true\r\n        sleep 5\r\n        if ps -p \"$pid\" > /dev/null 2>&1; then\r\n            warning \"Application didn't stop gracefully, forcing kill...\"\r\n            kill -KILL \"$pid\" 2>/dev/null || true\r\n        fi\r\n        rm -f \"$PID_FILE\"\r\n        success \"Application stopped\"\r\n    fi\r\n}\r\n\r\n# Function to setup environment\r\nsetup_environment() {\r\n    log \"Setting up environment...\"\r\n\r\n    # Change to project directory\r\n    cd \"$PROJECT_DIR\" || {\r\n        error \"Failed to change to project directory: $PROJECT_DIR\"\r\n        exit 1\r\n    }\r\n\r\n    # Create necessary directories with full permissions\r\n    log \"Creating necessary directories...\"\r\n    mkdir -p db logs tmp static/uploads\r\n\r\n    # Set full permissions (777) for all necessary directories\r\n    chmod -R 777 db logs tmp static 2>/dev/null || {\r\n        warning \"Could not set permissions on some directories (may be mounted volumes)\"\r\n    }\r\n\r\n    # Check if shared virtual environment exists\r\n    if [ ! -d \"$SHARED_VENV\" ]; then\r\n        error \"Shared virtual environment not found at: $SHARED_VENV\"\r\n        log \"Creating shared virtual environment...\"\r\n        python3 -m venv \"$SHARED_VENV\" || {\r\n            error \"Failed to create virtual environment\"\r\n            exit 1\r\n        }\r\n    fi\r\n\r\n    # Check if virtual environment has Python\r\n    if [ ! -f \"$SHARED_VENV/bin/python\" ]; then\r\n        error \"Python not found in virtual environment: $SHARED_VENV/bin/python\"\r\n        exit 1\r\n    fi\r\n\r\n    success \"Environment setup completed\"\r\n}\r\n\r\n# Function to install/update dependencies\r\ninstall_dependencies() {\r\n    log \"Installing/updating dependencies...\"\r\n\r\n    # Activate virtual environment\r\n    source \"$SHARED_VENV/bin/activate\" || {\r\n        error \"Failed to activate virtual environment\"\r\n        exit 1\r\n    }\r\n\r\n    # Upgrade pip\r\n    pip install --upgrade pip > /dev/null 2>&1 || {\r\n        warning \"Failed to upgrade pip\"\r\n    }\r\n\r\n    # Install requirements if file exists\r\n    if [ -f \"requirements.txt\" ]; then\r\n        log \"Installing requirements from requirements.txt...\"\r\n        pip install -r requirements.txt || {\r\n            error \"Failed to install requirements\"\r\n            exit 1\r\n        }\r\n    fi\r\n\r\n    # Install additional packages that might be needed\r\n    pip install gunicorn eventlet python-dotenv > /dev/null 2>&1 || {\r\n        warning \"Failed to install some additional packages\"\r\n    }\r\n\r\n    success \"Dependencies installed successfully\"\r\n}\r\n\r\n# Function to start the application\r\nstart_app() {\r\n    local retry_count=0\r\n\r\n    while [ $retry_count -lt $MAX_RETRIES ]; do\r\n        log \"Starting AlgoFactory application (attempt $((retry_count + 1))/$MAX_RETRIES)...\"\r\n\r\n        # Activate virtual environment\r\n        source \"$SHARED_VENV/bin/activate\" || {\r\n            error \"Failed to activate virtual environment\"\r\n            exit 1\r\n        }\r\n\r\n        # Check if app.py exists\r\n        if [ ! -f \"$APP_FILE\" ]; then\r\n            error \"Application file not found: $APP_FILE\"\r\n            exit 1\r\n        fi\r\n\r\n        # Start the application with gunicorn\r\n        nohup \"$SHARED_VENV/bin/gunicorn\" \\\r\n            --bind=0.0.0.0:5000 \\\r\n            --worker-class=eventlet \\\r\n            --workers=1 \\\r\n            --timeout=120 \\\r\n            --keep-alive=2 \\\r\n            --max-requests=1000 \\\r\n            --max-requests-jitter=100 \\\r\n            --preload \\\r\n            --log-level=info \\\r\n            --access-logfile=\"$LOG_DIR/access.log\" \\\r\n            --error-logfile=\"$LOG_DIR/error.log\" \\\r\n            --capture-output \\\r\n            --enable-stdio-inheritance \\\r\n            app:app > \"$LOG_DIR/app.log\" 2>&1 &\r\n\r\n        local app_pid=$!\r\n        echo $app_pid > \"$PID_FILE\"\r\n\r\n        # Wait a moment and check if the app started successfully\r\n        sleep 5\r\n\r\n        if ps -p \"$app_pid\" > /dev/null 2>&1; then\r\n            success \"AlgoFactory application started successfully (PID: $app_pid)\"\r\n            success \"Application is running on http://0.0.0.0:5000\"\r\n            success \"Logs are available in: $LOG_DIR/\"\r\n            return 0\r\n        else\r\n            error \"Application failed to start (attempt $((retry_count + 1)))\"\r\n            rm -f \"$PID_FILE\"\r\n\r\n            # Show last few lines of error log\r\n            if [ -f \"$LOG_DIR/error.log\" ]; then\r\n                error \"Last few lines from error log:\"\r\n                tail -10 \"$LOG_DIR/error.log\" | while read line; do\r\n                    error \"  $line\"\r\n                done\r\n            fi\r\n\r\n            retry_count=$((retry_count + 1))\r\n            if [ $retry_count -lt $MAX_RETRIES ]; then\r\n                warning \"Retrying in $RETRY_DELAY seconds...\"\r\n                sleep $RETRY_DELAY\r\n            fi\r\n        fi\r\n    done\r\n\r\n    error \"Failed to start application after $MAX_RETRIES attempts\"\r\n    exit 1\r\n}\r\n\r\n# Function to monitor the application\r\nmonitor_app() {\r\n    log \"Starting application monitor...\"\r\n\r\n    while true; do\r\n        if ! is_app_running; then\r\n            warning \"Application is not running, attempting to restart...\"\r\n            start_app\r\n        fi\r\n        sleep 30  # Check every 30 seconds\r\n    done\r\n}\r\n\r\n# Main execution\r\nmain() {\r\n    log \"=== AlgoFactory Startup Script ===\"\r\n    log \"Project Directory: $PROJECT_DIR\"\r\n    log \"Shared Virtual Environment: $SHARED_VENV\"\r\n    log \"Application File: $APP_FILE\"\r\n\r\n    # Handle command line arguments\r\n    case \"${1:-start}\" in\r\n        \"start\")\r\n            if is_app_running; then\r\n                warning \"Application is already running\"\r\n                exit 0\r\n            fi\r\n            setup_environment\r\n            install_dependencies\r\n            start_app\r\n            ;;\r\n        \"stop\")\r\n            stop_app\r\n            ;;\r\n        \"restart\")\r\n            stop_app\r\n            setup_environment\r\n            install_dependencies\r\n            start_app\r\n            ;;\r\n        \"status\")\r\n            if is_app_running; then\r\n                success \"Application is running (PID: $(cat $PID_FILE))\"\r\n            else\r\n                warning \"Application is not running\"\r\n            fi\r\n            ;;\r\n        \"monitor\")\r\n            setup_environment\r\n            install_dependencies\r\n            monitor_app\r\n            ;;\r\n        \"logs\")\r\n            if [ -f \"$LOG_DIR/app.log\" ]; then\r\n                tail -f \"$LOG_DIR/app.log\"\r\n            else\r\n                error \"Log file not found: $LOG_DIR/app.log\"\r\n            fi\r\n            ;;\r\n        *)\r\n            echo \"Usage: $0 {start|stop|restart|status|monitor|logs}\"\r\n            echo \"  start   - Start the application\"\r\n            echo \"  stop    - Stop the application\"\r\n            echo \"  restart - Restart the application\"\r\n            echo \"  status  - Check application status\"\r\n            echo \"  monitor - Start with continuous monitoring\"\r\n            echo \"  logs    - Show application logs\"\r\n            exit 1\r\n            ;;\r\n    esac\r\n}\r\n\r\n# Trap signals for graceful shutdown\r\ntrap 'stop_app; exit 0' SIGTERM SIGINT\r\n\r\n# Run main function\r\nmain \"$@\"\r\n"}