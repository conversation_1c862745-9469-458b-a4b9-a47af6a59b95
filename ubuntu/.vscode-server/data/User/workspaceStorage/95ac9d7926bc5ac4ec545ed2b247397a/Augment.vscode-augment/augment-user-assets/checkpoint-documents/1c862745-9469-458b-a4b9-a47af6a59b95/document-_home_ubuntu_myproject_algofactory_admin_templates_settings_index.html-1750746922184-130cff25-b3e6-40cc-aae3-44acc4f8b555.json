{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/admin/templates/settings/index.html"}, "modifiedCode": "{% extends \"base.html\" %}\n\n{% block title %}Settings - AlgoFactory Admin{% endblock %}\n{% block page_title %}System Settings{% endblock %}\n\n{% block content %}\n<!-- Settings Categories -->\n<div class=\"grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8\">\n    <div class=\"card bg-base-200 shadow-sm cursor-pointer\" onclick=\"showSettingsCategory('general')\">\n        <div class=\"card-body text-center\">\n            <i class=\"fas fa-cog text-4xl text-primary mb-2\"></i>\n            <h3 class=\"font-bold\">General</h3>\n            <p class=\"text-sm text-base-content/70\">Basic system settings</p>\n        </div>\n    </div>\n    \n    <div class=\"card bg-base-200 shadow-sm cursor-pointer\" onclick=\"showSettingsCategory('security')\">\n        <div class=\"card-body text-center\">\n            <i class=\"fas fa-shield-alt text-4xl text-secondary mb-2\"></i>\n            <h3 class=\"font-bold\">Security</h3>\n            <p class=\"text-sm text-base-content/70\">Authentication & access</p>\n        </div>\n    </div>\n    \n    <div class=\"card bg-base-200 shadow-sm cursor-pointer\" onclick=\"showSettingsCategory('monitoring')\">\n        <div class=\"card-body text-center\">\n            <i class=\"fas fa-chart-line text-4xl text-accent mb-2\"></i>\n            <h3 class=\"font-bold\">Monitoring</h3>\n            <p class=\"text-sm text-base-content/70\">Alerts & thresholds</p>\n        </div>\n    </div>\n    \n    <div class=\"card bg-base-200 shadow-sm cursor-pointer\" onclick=\"showSettingsCategory('backup')\">\n        <div class=\"card-body text-center\">\n            <i class=\"fas fa-database text-4xl text-info mb-2\"></i>\n            <h3 class=\"font-bold\">Backup</h3>\n            <p class=\"text-sm text-base-content/70\">Automated backups</p>\n        </div>\n    </div>\n</div>\n\n<!-- Settings Content -->\n<div class=\"card bg-base-200 shadow-sm\">\n    <div class=\"card-body\">\n        <div class=\"flex justify-between items-center mb-6\">\n            <h2 class=\"card-title\">\n                <i class=\"fas fa-cog text-primary\"></i>\n                <span id=\"settings-title\">General Settings</span>\n            </h2>\n            <button class=\"btn btn-primary\" onclick=\"saveSettings()\">\n                <i class=\"fas fa-save\"></i>\n                Save Changes\n            </button>\n        </div>\n        \n        <!-- General Settings -->\n        <div id=\"general-settings\" class=\"settings-panel\">\n            <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div class=\"space-y-4\">\n                    <h3 class=\"font-bold text-lg\">System Information</h3>\n                    <div class=\"form-control\">\n                        <label class=\"label\">\n                            <span class=\"label-text\">System Name</span>\n                        </label>\n                        <input type=\"text\" value=\"AlgoFactory Production Server\" class=\"input input-bordered\">\n                    </div>\n                    <div class=\"form-control\">\n                        <label class=\"label\">\n                            <span class=\"label-text\">Admin Email</span>\n                        </label>\n                        <input type=\"email\" value=\"<EMAIL>\" class=\"input input-bordered\">\n                    </div>\n                    <div class=\"form-control\">\n                        <label class=\"label\">\n                            <span class=\"label-text\">Timezone</span>\n                        </label>\n                        <select class=\"select select-bordered\">\n                            <option>UTC</option>\n                            <option>Asia/Kolkata</option>\n                            <option>America/New_York</option>\n                            <option>Europe/London</option>\n                        </select>\n                    </div>\n                </div>\n                \n                <div class=\"space-y-4\">\n                    <h3 class=\"font-bold text-lg\">Performance</h3>\n                    <div class=\"form-control\">\n                        <label class=\"label\">\n                            <span class=\"label-text\">Memory Threshold (%)</span>\n                        </label>\n                        <input type=\"range\" min=\"50\" max=\"95\" value=\"85\" class=\"range range-primary\">\n                        <div class=\"w-full flex justify-between text-xs px-2\">\n                            <span>50%</span>\n                            <span>95%</span>\n                        </div>\n                    </div>\n                    <div class=\"form-control\">\n                        <label class=\"label cursor-pointer\">\n                            <span class=\"label-text\">Auto-optimization</span>\n                            <input type=\"checkbox\" class=\"toggle toggle-primary\" checked>\n                        </label>\n                    </div>\n                    <div class=\"form-control\">\n                        <label class=\"label cursor-pointer\">\n                            <span class=\"label-text\">Auto-restart failed services</span>\n                            <input type=\"checkbox\" class=\"toggle toggle-primary\" checked>\n                        </label>\n                    </div>\n                </div>\n            </div>\n        </div>\n        \n        <!-- Security Settings -->\n        <div id=\"security-settings\" class=\"settings-panel hidden\">\n            <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div class=\"space-y-4\">\n                    <h3 class=\"font-bold text-lg\">Authentication</h3>\n                    <div class=\"form-control\">\n                        <label class=\"label\">\n                            <span class=\"label-text\">Session Timeout (minutes)</span>\n                        </label>\n                        <input type=\"number\" value=\"60\" class=\"input input-bordered\">\n                    </div>\n                    <div class=\"form-control\">\n                        <label class=\"label cursor-pointer\">\n                            <span class=\"label-text\">Require password change on first login</span>\n                            <input type=\"checkbox\" class=\"toggle toggle-secondary\" checked>\n                        </label>\n                    </div>\n                    <div class=\"form-control\">\n                        <label class=\"label cursor-pointer\">\n                            <span class=\"label-text\">Enable two-factor authentication</span>\n                            <input type=\"checkbox\" class=\"toggle toggle-secondary\">\n                        </label>\n                    </div>\n                </div>\n                \n                <div class=\"space-y-4\">\n                    <h3 class=\"font-bold text-lg\">Access Control</h3>\n                    <div class=\"form-control\">\n                        <label class=\"label\">\n                            <span class=\"label-text\">Allowed IP Addresses</span>\n                        </label>\n                        <textarea class=\"textarea textarea-bordered\" placeholder=\"0.0.0.0/0 (all IPs)\">0.0.0.0/0</textarea>\n                    </div>\n                    <div class=\"form-control\">\n                        <label class=\"label cursor-pointer\">\n                            <span class=\"label-text\">Log all login attempts</span>\n                            <input type=\"checkbox\" class=\"toggle toggle-secondary\" checked>\n                        </label>\n                    </div>\n                </div>\n            </div>\n        </div>\n        \n        <!-- Monitoring Settings -->\n        <div id=\"monitoring-settings\" class=\"settings-panel hidden\">\n            <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div class=\"space-y-4\">\n                    <h3 class=\"font-bold text-lg\">Alert Thresholds</h3>\n                    <div class=\"form-control\">\n                        <label class=\"label\">\n                            <span class=\"label-text\">CPU Alert Threshold (%)</span>\n                        </label>\n                        <input type=\"range\" min=\"50\" max=\"100\" value=\"90\" class=\"range range-accent\">\n                    </div>\n                    <div class=\"form-control\">\n                        <label class=\"label\">\n                            <span class=\"label-text\">Memory Alert Threshold (%)</span>\n                        </label>\n                        <input type=\"range\" min=\"50\" max=\"100\" value=\"85\" class=\"range range-accent\">\n                    </div>\n                    <div class=\"form-control\">\n                        <label class=\"label\">\n                            <span class=\"label-text\">Disk Alert Threshold (%)</span>\n                        </label>\n                        <input type=\"range\" min=\"50\" max=\"100\" value=\"90\" class=\"range range-accent\">\n                    </div>\n                </div>\n                \n                <div class=\"space-y-4\">\n                    <h3 class=\"font-bold text-lg\">Notifications</h3>\n                    <div class=\"form-control\">\n                        <label class=\"label cursor-pointer\">\n                            <span class=\"label-text\">Email notifications</span>\n                            <input type=\"checkbox\" class=\"toggle toggle-accent\" checked>\n                        </label>\n                    </div>\n                    <div class=\"form-control\">\n                        <label class=\"label cursor-pointer\">\n                            <span class=\"label-text\">SMS notifications</span>\n                            <input type=\"checkbox\" class=\"toggle toggle-accent\">\n                        </label>\n                    </div>\n                    <div class=\"form-control\">\n                        <label class=\"label\">\n                            <span class=\"label-text\">Check Interval (seconds)</span>\n                        </label>\n                        <select class=\"select select-bordered\">\n                            <option value=\"30\">30 seconds</option>\n                            <option value=\"60\" selected>1 minute</option>\n                            <option value=\"300\">5 minutes</option>\n                            <option value=\"600\">10 minutes</option>\n                        </select>\n                    </div>\n                </div>\n            </div>\n        </div>\n        \n        <!-- Backup Settings -->\n        <div id=\"backup-settings\" class=\"settings-panel hidden\">\n            <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div class=\"space-y-4\">\n                    <h3 class=\"font-bold text-lg\">Automated Backups</h3>\n                    <div class=\"form-control\">\n                        <label class=\"label cursor-pointer\">\n                            <span class=\"label-text\">Enable automatic backups</span>\n                            <input type=\"checkbox\" class=\"toggle toggle-info\" checked>\n                        </label>\n                    </div>\n                    <div class=\"form-control\">\n                        <label class=\"label\">\n                            <span class=\"label-text\">Backup Frequency</span>\n                        </label>\n                        <select class=\"select select-bordered\">\n                            <option value=\"daily\" selected>Daily</option>\n                            <option value=\"weekly\">Weekly</option>\n                            <option value=\"monthly\">Monthly</option>\n                        </select>\n                    </div>\n                    <div class=\"form-control\">\n                        <label class=\"label\">\n                            <span class=\"label-text\">Backup Time</span>\n                        </label>\n                        <input type=\"time\" value=\"02:00\" class=\"input input-bordered\">\n                    </div>\n                </div>\n                \n                <div class=\"space-y-4\">\n                    <h3 class=\"font-bold text-lg\">Retention Policy</h3>\n                    <div class=\"form-control\">\n                        <label class=\"label\">\n                            <span class=\"label-text\">Keep backups for (days)</span>\n                        </label>\n                        <input type=\"number\" value=\"30\" class=\"input input-bordered\">\n                    </div>\n                    <div class=\"form-control\">\n                        <label class=\"label\">\n                            <span class=\"label-text\">Backup Location</span>\n                        </label>\n                        <input type=\"text\" value=\"/var/backups/algofactory\" class=\"input input-bordered\">\n                    </div>\n                    <div class=\"form-control\">\n                        <label class=\"label cursor-pointer\">\n                            <span class=\"label-text\">Compress backups</span>\n                            <input type=\"checkbox\" class=\"toggle toggle-info\" checked>\n                        </label>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n</div>\n{% endblock %}\n\n{% block scripts %}\n<script>\n    function showSettingsCategory(category) {\n        // Hide all panels\n        document.querySelectorAll('.settings-panel').forEach(panel => {\n            panel.classList.add('hidden');\n        });\n        \n        // Show selected panel\n        document.getElementById(category + '-settings').classList.remove('hidden');\n        \n        // Update title\n        const titles = {\n            'general': 'General Settings',\n            'security': 'Security Settings',\n            'monitoring': 'Monitoring Settings',\n            'backup': 'Backup Settings'\n        };\n        \n        document.getElementById('settings-title').textContent = titles[category];\n    }\n    \n    function saveSettings() {\n        showToast('Saving settings...', 'info');\n        \n        // Simulate saving\n        setTimeout(() => {\n            showToast('Settings saved successfully!', 'success');\n        }, 1000);\n    }\n    \n    // Initialize with general settings\n    document.addEventListener('DOMContentLoaded', function() {\n        showSettingsCategory('general');\n    });\n</script>\n{% endblock %}\n"}