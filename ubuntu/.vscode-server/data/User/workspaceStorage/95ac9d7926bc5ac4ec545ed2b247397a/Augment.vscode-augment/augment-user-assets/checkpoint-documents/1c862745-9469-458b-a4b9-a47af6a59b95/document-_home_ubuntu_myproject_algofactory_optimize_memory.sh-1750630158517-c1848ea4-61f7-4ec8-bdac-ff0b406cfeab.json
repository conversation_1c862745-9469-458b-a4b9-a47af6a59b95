{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/optimize_memory.sh"}, "originalCode": "#!/bin/bash\n\n# Memory Optimization Script for 1GB RAM Server\n# This script optimizes the system to prevent crashes during development\n\nset -e\n\n# Colors for output\nRED='\\033[0;31m'\nGREEN='\\033[0;32m'\nYELLOW='\\033[1;33m'\nBLUE='\\033[0;34m'\nNC='\\033[0m' # No Color\n\nlog() {\n    echo -e \"${BLUE}[$(date '+%H:%M:%S')]${NC} $1\"\n}\n\nsuccess() {\n    echo -e \"${GREEN}[$(date '+%H:%M:%S')] SUCCESS:${NC} $1\"\n}\n\nwarning() {\n    echo -e \"${YELLOW}[$(date '+%H:%M:%S')] WARNING:${NC} $1\"\n}\n\nerror() {\n    echo -e \"${RED}[$(date '+%H:%M:%S')] ERROR:${NC} $1\" >&2\n}\n\n# Function to show current memory usage\nshow_memory() {\n    echo -e \"\\n${BLUE}Current Memory Usage:${NC}\"\n    free -h\n    echo -e \"\\n${BLUE}Top Memory Consumers:${NC}\"\n    ps aux --sort=-%mem | head -10\n}\n\n# Function to create swap file\ncreate_swap() {\n    log \"Creating swap file to increase virtual memory...\"\n    \n    # Check if swap already exists\n    if swapon --show | grep -q \"/swapfile\"; then\n        warning \"Swap file already exists\"\n        return 0\n    fi\n    \n    # Create 2GB swap file\n    sudo fallocate -l 2G /swapfile\n    sudo chmod 600 /swapfile\n    sudo mkswap /swapfile\n    sudo swapon /swapfile\n    \n    # Make it permanent\n    echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab\n    \n    # Optimize swap usage\n    echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.conf\n    echo 'vm.vfs_cache_pressure=50' | sudo tee -a /etc/sysctl.conf\n    \n    success \"2GB swap file created and activated\"\n}\n\n# Function to optimize system services\noptimize_services() {\n    log \"Optimizing system services...\"\n    \n    # Disable unnecessary services\n    services_to_disable=(\n        \"snapd\"\n        \"bluetooth\"\n        \"cups\"\n        \"avahi-daemon\"\n        \"ModemManager\"\n    )\n    \n    for service in \"${services_to_disable[@]}\"; do\n        if systemctl is-enabled \"$service\" >/dev/null 2>&1; then\n            sudo systemctl disable \"$service\" >/dev/null 2>&1 || true\n            sudo systemctl stop \"$service\" >/dev/null 2>&1 || true\n            log \"Disabled $service\"\n        fi\n    done\n    \n    success \"System services optimized\"\n}\n\n# Function to optimize Python/Gunicorn settings\noptimize_python() {\n    log \"Optimizing Python and Gunicorn settings...\"\n    \n    # Create optimized gunicorn config\n    cat > /tmp/gunicorn_optimized.conf << 'EOF'\n# Optimized Gunicorn configuration for low memory\nbind = \"0.0.0.0:PORT_PLACEHOLDER\"\nworkers = 1\nworker_class = \"sync\"\nworker_connections = 100\nmax_requests = 500\nmax_requests_jitter = 50\ntimeout = 30\nkeepalive = 2\npreload_app = True\nworker_tmp_dir = \"/dev/shm\"\nEOF\n    \n    success \"Python optimization configured\"\n}\n\n# Function to clean system\nclean_system() {\n    log \"Cleaning system to free memory...\"\n    \n    # Clean package cache\n    sudo apt clean\n    sudo apt autoremove -y\n    \n    # Clear logs\n    sudo journalctl --vacuum-time=1d\n    \n    # Clear tmp files\n    sudo rm -rf /tmp/*\n    sudo rm -rf /var/tmp/*\n    \n    # Drop caches\n    sudo sync\n    echo 3 | sudo tee /proc/sys/vm/drop_caches > /dev/null\n    \n    success \"System cleaned\"\n}\n\n# Function to optimize instance startup\noptimize_instances() {\n    log \"Creating memory-optimized instance startup...\"\n    \n    # Create lightweight start script for instances\n    cat > /tmp/start_lightweight.sh << 'EOF'\n#!/bin/bash\n\n# Lightweight startup script for low memory environments\nset -e\n\nINSTANCE_DIR=\"$(cd \"$(dirname \"${BASH_SOURCE[0]}\")\" && pwd)\"\nINSTANCE_ID=$(basename \"$INSTANCE_DIR\" | cut -d'-' -f2)\nSHARED_VENV=\"/home/<USER>/shared-venv\"\nPID_FILE=\"$INSTANCE_DIR/app.pid\"\n\n# Source environment\nsource \"$INSTANCE_DIR/.env\"\n\n# Activate virtual environment\nsource \"$SHARED_VENV/bin/activate\"\n\n# Check if already running\nif [ -f \"$PID_FILE\" ]; then\n    if ps -p \"$(cat \"$PID_FILE\")\" > /dev/null 2>&1; then\n        echo \"Instance $INSTANCE_ID already running\"\n        exit 0\n    else\n        rm -f \"$PID_FILE\"\n    fi\nfi\n\n# Start with minimal resources\ncd \"$INSTANCE_DIR\"\nnohup \"$SHARED_VENV/bin/gunicorn\" \\\n    --bind=\"0.0.0.0:$FLASK_PORT\" \\\n    --workers=1 \\\n    --worker-class=sync \\\n    --worker-connections=50 \\\n    --max-requests=200 \\\n    --timeout=30 \\\n    --keepalive=2 \\\n    --preload \\\n    --worker-tmp-dir=/dev/shm \\\n    --log-level=warning \\\n    --access-logfile=\"$INSTANCE_DIR/logs/access.log\" \\\n    --error-logfile=\"$INSTANCE_DIR/logs/error.log\" \\\n    app:app > \"$INSTANCE_DIR/logs/app.log\" 2>&1 &\n\necho $! > \"$PID_FILE\"\necho \"Instance $INSTANCE_ID started on port $FLASK_PORT\"\nEOF\n    \n    chmod +x /tmp/start_lightweight.sh\n    success \"Lightweight instance startup created\"\n}\n\n# Function to set memory limits\nset_memory_limits() {\n    log \"Setting memory limits for processes...\"\n    \n    # Create systemd override for memory limits\n    sudo mkdir -p /etc/systemd/system/nginx.service.d/\n    cat > /tmp/nginx_memory.conf << 'EOF'\n[Service]\nMemoryMax=100M\nMemoryHigh=80M\nEOF\n    sudo mv /tmp/nginx_memory.conf /etc/systemd/system/nginx.service.d/memory.conf\n    \n    # Reload systemd\n    sudo systemctl daemon-reload\n    \n    success \"Memory limits configured\"\n}\n\n# Function to monitor memory usage\nsetup_monitoring() {\n    log \"Setting up memory monitoring...\"\n    \n    # Create memory monitor script\n    cat > /tmp/memory_monitor.sh << 'EOF'\n#!/bin/bash\n\n# Memory monitoring script\nwhile true; do\n    MEMORY_USAGE=$(free | grep Mem | awk '{printf \"%.0f\", $3/$2 * 100.0}')\n    \n    if [ \"$MEMORY_USAGE\" -gt 85 ]; then\n        echo \"$(date): High memory usage: ${MEMORY_USAGE}%\" >> /var/log/memory_alerts.log\n        \n        # Kill memory-heavy processes if needed\n        if [ \"$MEMORY_USAGE\" -gt 95 ]; then\n            echo \"$(date): Critical memory usage, cleaning caches\" >> /var/log/memory_alerts.log\n            echo 3 > /proc/sys/vm/drop_caches\n        fi\n    fi\n    \n    sleep 30\ndone\nEOF\n    \n    chmod +x /tmp/memory_monitor.sh\n    success \"Memory monitoring setup\"\n}\n\n# Main execution\nmain() {\n    echo -e \"${GREEN}=== Memory Optimization for 1GB RAM Server ===${NC}\"\n    \n    show_memory\n    \n    log \"Starting optimization process...\"\n    \n    create_swap\n    optimize_services\n    optimize_python\n    clean_system\n    optimize_instances\n    set_memory_limits\n    setup_monitoring\n    \n    echo -e \"\\n${GREEN}=== Optimization Complete ===${NC}\"\n    show_memory\n    \n    echo -e \"\\n${GREEN}Recommendations:${NC}\"\n    echo \"1. Use only 1-2 instances at a time during development\"\n    echo \"2. Stop instances when not needed: python3 instance_manager.py stop INSTANCE_ID\"\n    echo \"3. Monitor memory: watch -n 5 free -h\"\n    echo \"4. Use lightweight startup: /tmp/start_lightweight.sh\"\n    echo \"5. Consider upgrading to 2GB+ RAM for production\"\n}\n\n# Handle command line arguments\ncase \"${1:-optimize}\" in\n    \"optimize\")\n        main\n        ;;\n    \"memory\")\n        show_memory\n        ;;\n    \"clean\")\n        clean_system\n        ;;\n    \"swap\")\n        create_swap\n        ;;\n    *)\n        echo \"Usage: $0 {optimize|memory|clean|swap}\"\n        echo \"  optimize - Run full optimization\"\n        echo \"  memory   - Show current memory usage\"\n        echo \"  clean    - Clean system to free memory\"\n        echo \"  swap     - Create swap file\"\n        ;;\nesac\n", "modifiedCode": "#!/bin/bash\n\n# Memory Optimization Script for 1GB RAM Server\n# This script optimizes the system to prevent crashes during development\n\nset -e\n\n# Colors for output\nRED='\\033[0;31m'\nGREEN='\\033[0;32m'\nYELLOW='\\033[1;33m'\nBLUE='\\033[0;34m'\nNC='\\033[0m' # No Color\n\nlog() {\n    echo -e \"${BLUE}[$(date '+%H:%M:%S')]${NC} $1\"\n}\n\nsuccess() {\n    echo -e \"${GREEN}[$(date '+%H:%M:%S')] SUCCESS:${NC} $1\"\n}\n\nwarning() {\n    echo -e \"${YELLOW}[$(date '+%H:%M:%S')] WARNING:${NC} $1\"\n}\n\nerror() {\n    echo -e \"${RED}[$(date '+%H:%M:%S')] ERROR:${NC} $1\" >&2\n}\n\n# Function to show current memory usage\nshow_memory() {\n    echo -e \"\\n${BLUE}Current Memory Usage:${NC}\"\n    free -h\n    echo -e \"\\n${BLUE}Top Memory Consumers:${NC}\"\n    ps aux --sort=-%mem | head -10\n}\n\n# Function to create swap file\ncreate_swap() {\n    log \"Creating swap file to increase virtual memory...\"\n    \n    # Check if swap already exists\n    if swapon --show | grep -q \"/swapfile\"; then\n        warning \"Swap file already exists\"\n        return 0\n    fi\n    \n    # Create 2GB swap file\n    sudo fallocate -l 2G /swapfile\n    sudo chmod 600 /swapfile\n    sudo mkswap /swapfile\n    sudo swapon /swapfile\n    \n    # Make it permanent\n    echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab\n    \n    # Optimize swap usage\n    echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.conf\n    echo 'vm.vfs_cache_pressure=50' | sudo tee -a /etc/sysctl.conf\n    \n    success \"2GB swap file created and activated\"\n}\n\n# Function to optimize system services\noptimize_services() {\n    log \"Optimizing system services...\"\n    \n    # Disable unnecessary services\n    services_to_disable=(\n        \"snapd\"\n        \"bluetooth\"\n        \"cups\"\n        \"avahi-daemon\"\n        \"ModemManager\"\n    )\n    \n    for service in \"${services_to_disable[@]}\"; do\n        if systemctl is-enabled \"$service\" >/dev/null 2>&1; then\n            sudo systemctl disable \"$service\" >/dev/null 2>&1 || true\n            sudo systemctl stop \"$service\" >/dev/null 2>&1 || true\n            log \"Disabled $service\"\n        fi\n    done\n    \n    success \"System services optimized\"\n}\n\n# Function to optimize Python/Gunicorn settings\noptimize_python() {\n    log \"Optimizing Python and Gunicorn settings...\"\n    \n    # Create optimized gunicorn config\n    cat > /tmp/gunicorn_optimized.conf << 'EOF'\n# Optimized Gunicorn configuration for low memory\nbind = \"0.0.0.0:PORT_PLACEHOLDER\"\nworkers = 1\nworker_class = \"sync\"\nworker_connections = 100\nmax_requests = 500\nmax_requests_jitter = 50\ntimeout = 30\nkeepalive = 2\npreload_app = True\nworker_tmp_dir = \"/dev/shm\"\nEOF\n    \n    success \"Python optimization configured\"\n}\n\n# Function to clean system\nclean_system() {\n    log \"Cleaning system to free memory...\"\n    \n    # Clean package cache\n    sudo apt clean\n    sudo apt autoremove -y\n    \n    # Clear logs\n    sudo journalctl --vacuum-time=1d\n    \n    # Clear tmp files\n    sudo rm -rf /tmp/*\n    sudo rm -rf /var/tmp/*\n    \n    # Drop caches\n    sudo sync\n    echo 3 | sudo tee /proc/sys/vm/drop_caches > /dev/null\n    \n    success \"System cleaned\"\n}\n\n# Function to optimize instance startup\noptimize_instances() {\n    log \"Creating memory-optimized instance startup...\"\n    \n    # Create lightweight start script for instances\n    cat > /tmp/start_lightweight.sh << 'EOF'\n#!/bin/bash\n\n# Lightweight startup script for low memory environments\nset -e\n\nINSTANCE_DIR=\"$(cd \"$(dirname \"${BASH_SOURCE[0]}\")\" && pwd)\"\nINSTANCE_ID=$(basename \"$INSTANCE_DIR\" | cut -d'-' -f2)\nSHARED_VENV=\"/home/<USER>/shared-venv\"\nPID_FILE=\"$INSTANCE_DIR/app.pid\"\n\n# Source environment\nsource \"$INSTANCE_DIR/.env\"\n\n# Activate virtual environment\nsource \"$SHARED_VENV/bin/activate\"\n\n# Check if already running\nif [ -f \"$PID_FILE\" ]; then\n    if ps -p \"$(cat \"$PID_FILE\")\" > /dev/null 2>&1; then\n        echo \"Instance $INSTANCE_ID already running\"\n        exit 0\n    else\n        rm -f \"$PID_FILE\"\n    fi\nfi\n\n# Start with minimal resources\ncd \"$INSTANCE_DIR\"\nnohup \"$SHARED_VENV/bin/gunicorn\" \\\n    --bind=\"0.0.0.0:$FLASK_PORT\" \\\n    --workers=1 \\\n    --worker-class=sync \\\n    --worker-connections=50 \\\n    --max-requests=200 \\\n    --timeout=30 \\\n    --keepalive=2 \\\n    --preload \\\n    --worker-tmp-dir=/dev/shm \\\n    --log-level=warning \\\n    --access-logfile=\"$INSTANCE_DIR/logs/access.log\" \\\n    --error-logfile=\"$INSTANCE_DIR/logs/error.log\" \\\n    app:app > \"$INSTANCE_DIR/logs/app.log\" 2>&1 &\n\necho $! > \"$PID_FILE\"\necho \"Instance $INSTANCE_ID started on port $FLASK_PORT\"\nEOF\n    \n    chmod +x /tmp/start_lightweight.sh\n    success \"Lightweight instance startup created\"\n}\n\n# Function to set memory limits\nset_memory_limits() {\n    log \"Setting memory limits for processes...\"\n    \n    # Create systemd override for memory limits\n    sudo mkdir -p /etc/systemd/system/nginx.service.d/\n    cat > /tmp/nginx_memory.conf << 'EOF'\n[Service]\nMemoryMax=100M\nMemoryHigh=80M\nEOF\n    sudo mv /tmp/nginx_memory.conf /etc/systemd/system/nginx.service.d/memory.conf\n    \n    # Reload systemd\n    sudo systemctl daemon-reload\n    \n    success \"Memory limits configured\"\n}\n\n# Function to monitor memory usage\nsetup_monitoring() {\n    log \"Setting up memory monitoring...\"\n    \n    # Create memory monitor script\n    cat > /tmp/memory_monitor.sh << 'EOF'\n#!/bin/bash\n\n# Memory monitoring script\nwhile true; do\n    MEMORY_USAGE=$(free | grep Mem | awk '{printf \"%.0f\", $3/$2 * 100.0}')\n    \n    if [ \"$MEMORY_USAGE\" -gt 85 ]; then\n        echo \"$(date): High memory usage: ${MEMORY_USAGE}%\" >> /var/log/memory_alerts.log\n        \n        # Kill memory-heavy processes if needed\n        if [ \"$MEMORY_USAGE\" -gt 95 ]; then\n            echo \"$(date): Critical memory usage, cleaning caches\" >> /var/log/memory_alerts.log\n            echo 3 > /proc/sys/vm/drop_caches\n        fi\n    fi\n    \n    sleep 30\ndone\nEOF\n    \n    chmod +x /tmp/memory_monitor.sh\n    success \"Memory monitoring setup\"\n}\n\n# Main execution\nmain() {\n    echo -e \"${GREEN}=== Memory Optimization for 1GB RAM Server ===${NC}\"\n    \n    show_memory\n    \n    log \"Starting optimization process...\"\n    \n    create_swap\n    optimize_services\n    optimize_python\n    clean_system\n    optimize_instances\n    set_memory_limits\n    setup_monitoring\n    \n    echo -e \"\\n${GREEN}=== Optimization Complete ===${NC}\"\n    show_memory\n    \n    echo -e \"\\n${GREEN}Recommendations:${NC}\"\n    echo \"1. Use only 1-2 instances at a time during development\"\n    echo \"2. Stop instances when not needed: python3 instance_manager.py stop INSTANCE_ID\"\n    echo \"3. Monitor memory: watch -n 5 free -h\"\n    echo \"4. Use lightweight startup: /tmp/start_lightweight.sh\"\n    echo \"5. Consider upgrading to 2GB+ RAM for production\"\n}\n\n# Handle command line arguments\ncase \"${1:-optimize}\" in\n    \"optimize\")\n        main\n        ;;\n    \"memory\")\n        show_memory\n        ;;\n    \"clean\")\n        clean_system\n        ;;\n    \"swap\")\n        create_swap\n        ;;\n    *)\n        echo \"Usage: $0 {optimize|memory|clean|swap}\"\n        echo \"  optimize - Run full optimization\"\n        echo \"  memory   - Show current memory usage\"\n        echo \"  clean    - Clean system to free memory\"\n        echo \"  swap     - Create swap file\"\n        ;;\nesac\n"}