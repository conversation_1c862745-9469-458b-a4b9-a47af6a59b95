{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/start.sh"}, "originalCode": "#!/bin/bash\r\n\r\necho \"[OpenAlgo] Starting up...\"\r\n\r\nmkdir -p db logs\r\nchmod -R 777 db logs 2>/dev/null || echo \"⚠️  Skipping chmod (volume may be mounted)\"\r\n\r\n# Run gunicorn using full path inside virtualenv\r\nexec /app/.venv/bin/gunicorn --bind=0.0.0.0:5000 \\\r\n                              --worker-class=eventlet \\\r\n                              --workers=1 \\\r\n                              --log-level=info \\\r\n                              app:app\r\n", "modifiedCode": "#!/bin/bash\r\n\r\necho \"[OpenAlgo] Starting up...\"\r\n\r\nmkdir -p db logs\r\nchmod -R 777 db logs 2>/dev/null || echo \"⚠️  Skipping chmod (volume may be mounted)\"\r\n\r\n# Run gunicorn using full path inside virtualenv\r\nexec /app/.venv/bin/gunicorn --bind=0.0.0.0:5000 \\\r\n                              --worker-class=eventlet \\\r\n                              --workers=1 \\\r\n                              --log-level=info \\\r\n                              app:app\r\n"}