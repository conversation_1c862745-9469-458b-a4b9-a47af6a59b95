{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/admin/templates/backup/manage.html"}, "modifiedCode": "{% extends \"base.html\" %}\n\n{% block title %}Backup & Restore - AlgoFactory Admin{% endblock %}\n{% block page_title %}Backup & Restore{% endblock %}\n\n{% block content %}\n<div class=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n    <div class=\"stat bg-base-200 rounded-xl shadow-sm\">\n        <div class=\"stat-figure text-success\">\n            <i class=\"fas fa-database text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">Total Backups</div>\n        <div class=\"stat-value text-success\">15</div>\n        <div class=\"stat-desc\">Available backups</div>\n    </div>\n    \n    <div class=\"stat bg-base-200 rounded-xl shadow-sm\">\n        <div class=\"stat-figure text-info\">\n            <i class=\"fas fa-clock text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">Last Backup</div>\n        <div class=\"stat-value text-info\">2h ago</div>\n        <div class=\"stat-desc\">Automatic backup</div>\n    </div>\n    \n    <div class=\"stat bg-base-200 rounded-xl shadow-sm\">\n        <div class=\"stat-figure text-warning\">\n            <i class=\"fas fa-hdd text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">Backup Size</div>\n        <div class=\"stat-value text-warning\">2.4GB</div>\n        <div class=\"stat-desc\">Total storage used</div>\n    </div>\n</div>\n\n<div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n    <div class=\"card bg-base-200 shadow-sm\">\n        <div class=\"card-body\">\n            <h2 class=\"card-title mb-4\">\n                <i class=\"fas fa-plus text-success\"></i>\n                Create Backup\n            </h2>\n            <div class=\"space-y-4\">\n                <button class=\"btn btn-success w-full\">\n                    <i class=\"fas fa-database\"></i>\n                    Create Full Backup\n                </button>\n                <button class=\"btn btn-info w-full\">\n                    <i class=\"fas fa-file-archive\"></i>\n                    Create Configuration Backup\n                </button>\n                <button class=\"btn btn-warning w-full\">\n                    <i class=\"fas fa-download\"></i>\n                    Download Backup\n                </button>\n            </div>\n        </div>\n    </div>\n    \n    <div class=\"card bg-base-200 shadow-sm\">\n        <div class=\"card-body\">\n            <h2 class=\"card-title mb-4\">\n                <i class=\"fas fa-upload text-primary\"></i>\n                Restore from Backup\n            </h2>\n            <div class=\"space-y-4\">\n                <input type=\"file\" class=\"file-input file-input-bordered w-full\" />\n                <button class=\"btn btn-primary w-full\">\n                    <i class=\"fas fa-upload\"></i>\n                    Upload & Restore\n                </button>\n                <div class=\"alert alert-warning\">\n                    <i class=\"fas fa-exclamation-triangle\"></i>\n                    <span>Restore will overwrite current data</span>\n                </div>\n            </div>\n        </div>\n    </div>\n</div>\n{% endblock %}\n"}