{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/dashboard.sh"}, "modifiedCode": "#!/bin/bash\n\n# AlgoFactory Dashboard - Quick Status Overview\n# Shows real-time status of all instances and system health\n\nclear\n\necho \"🚀 AlgoFactory Multi-Instance Dashboard\"\necho \"========================================\"\necho \"$(date '+%Y-%m-%d %H:%M:%S UTC')\"\necho \"\"\n\n# Function to check HTTPS status\ncheck_https() {\n    local domain=$1\n    local status=$(curl -s -o /dev/null -w \"%{http_code}\" --max-time 5 \"https://$domain\" 2>/dev/null)\n    if [ \"$status\" = \"200\" ]; then\n        echo \"✅ HTTPS\"\n    else\n        echo \"❌ HTTPS\"\n    fi\n}\n\n# Function to check HTTP status\ncheck_http() {\n    local port=$1\n    local status=$(curl -s -o /dev/null -w \"%{http_code}\" --max-time 5 \"http://localhost:$port\" 2>/dev/null)\n    if [ \"$status\" = \"200\" ]; then\n        echo \"✅ HTTP\"\n    else\n        echo \"❌ HTTP\"\n    fi\n}\n\n# Instance status\necho \"📊 Instance Status:\"\necho \"===================\"\ninstances=(8010 8011 8012)\n\nfor instance in \"${instances[@]}\"; do\n    domain=\"$instance.algofactory.in\"\n    \n    # Check if process is running\n    if pgrep -f \"python.*app.py.*$instance\" > /dev/null; then\n        process_status=\"✅ Running\"\n        pid=$(pgrep -f \"python.*app.py.*$instance\")\n    else\n        process_status=\"❌ Stopped\"\n        pid=\"N/A\"\n    fi\n    \n    # Check HTTP and HTTPS\n    http_status=$(check_http $instance)\n    https_status=$(check_https $domain)\n    \n    echo \"🔹 Instance $instance:\"\n    echo \"   Domain: https://$domain\"\n    echo \"   Process: $process_status (PID: $pid)\"\n    echo \"   Local: $http_status | Public: $https_status\"\n    echo \"\"\ndone\n\n# System Resources\necho \"💻 System Resources:\"\necho \"====================\"\nmemory_info=$(free -h | grep Mem)\nmemory_used=$(echo $memory_info | awk '{print $3}')\nmemory_total=$(echo $memory_info | awk '{print $2}')\nmemory_percent=$(free | grep Mem | awk '{printf \"%.1f\", $3/$2 * 100.0}')\n\ndisk_info=$(df -h / | tail -1)\ndisk_used=$(echo $disk_info | awk '{print $3}')\ndisk_total=$(echo $disk_info | awk '{print $2}')\ndisk_percent=$(echo $disk_info | awk '{print $5}' | sed 's/%//')\n\nload_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')\n\necho \"🧠 Memory: $memory_used / $memory_total (${memory_percent}%)\"\necho \"💾 Disk: $disk_used / $disk_total (${disk_percent}%)\"\necho \"⚡ Load: $load_avg\"\necho \"\"\n\n# Monitor Service Status\necho \"🔍 Monitor Service:\"\necho \"==================\"\nif systemctl is-active --quiet algofactory-monitor; then\n    echo \"✅ AlgoFactory Monitor is running\"\n    uptime_info=$(systemctl show algofactory-monitor --property=ActiveEnterTimestamp | cut -d= -f2)\n    echo \"   Started: $uptime_info\"\nelse\n    echo \"❌ AlgoFactory Monitor is not running\"\n    echo \"   Run: sudo systemctl start algofactory-monitor\"\nfi\necho \"\"\n\n# SSL Certificate Status\necho \"🔒 SSL Certificate Status:\"\necho \"==========================\"\nfor instance in \"${instances[@]}\"; do\n    domain=\"$instance.algofactory.in\"\n    if [ -f \"/etc/letsencrypt/live/$domain/fullchain.pem\" ]; then\n        expiry=$(openssl x509 -enddate -noout -in \"/etc/letsencrypt/live/$domain/fullchain.pem\" | cut -d= -f2)\n        echo \"✅ $domain - Expires: $expiry\"\n    else\n        echo \"❌ $domain - No certificate found\"\n    fi\ndone\necho \"\"\n\n# Recent Alerts\necho \"🚨 Recent Alerts (last 3):\"\necho \"==========================\"\nif [ -f \"/var/log/algofactory-monitor/alerts.log\" ]; then\n    tail -3 /var/log/algofactory-monitor/alerts.log 2>/dev/null || echo \"   No recent alerts\"\nelse\n    echo \"   No alert log found\"\nfi\necho \"\"\n\n# Quick Actions\necho \"⚡ Quick Actions:\"\necho \"================\"\necho \"📊 View full status:     ./monitor_instances.sh status\"\necho \"📝 View monitor logs:    ./monitor_instances.sh logs\"\necho \"🚨 View alerts:          ./monitor_instances.sh alerts\"\necho \"🔄 Manual check:         ./monitor_instances.sh check\"\necho \"📋 List instances:       ./multi_instance.sh list\"\necho \"🔍 Monitor service logs: sudo journalctl -u algofactory-monitor -f\"\necho \"\"\n\n# Auto-refresh option\nif [ \"$1\" = \"watch\" ]; then\n    echo \"🔄 Auto-refreshing every 10 seconds... (Press Ctrl+C to stop)\"\n    sleep 10\n    exec $0 watch\nfi\n\necho \"💡 Tip: Run '$0 watch' for auto-refreshing dashboard\"\n"}