{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/admin/templates/instances/manage.html"}, "modifiedCode": "{% extends \"base.html\" %}\n\n{% block title %}AlgoFactory Instances - AlgoFactory Admin{% endblock %}\n{% block page_title %}AlgoFactory Instance Management{% endblock %}\n\n{% block content %}\n<!-- Instance Overview -->\n<div class=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n    <div class=\"stat bg-base-200 rounded-xl shadow-sm\">\n        <div class=\"stat-figure text-success\">\n            <i class=\"fas fa-play-circle text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">Running Instances</div>\n        <div class=\"stat-value text-success\" id=\"running-count\">0</div>\n        <div class=\"stat-desc\">Active processes</div>\n    </div>\n    \n    <div class=\"stat bg-base-200 rounded-xl shadow-sm\">\n        <div class=\"stat-figure text-error\">\n            <i class=\"fas fa-stop-circle text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">Stopped Instances</div>\n        <div class=\"stat-value text-error\" id=\"stopped-count\">0</div>\n        <div class=\"stat-desc\">Inactive processes</div>\n    </div>\n    \n    <div class=\"stat bg-base-200 rounded-xl shadow-sm\">\n        <div class=\"stat-figure text-info\">\n            <i class=\"fas fa-memory text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">Total Memory</div>\n        <div class=\"stat-value text-info\" id=\"total-memory\">0MB</div>\n        <div class=\"stat-desc\">Used by instances</div>\n    </div>\n    \n    <div class=\"stat bg-base-200 rounded-xl shadow-sm\">\n        <div class=\"stat-figure text-warning\">\n            <i class=\"fas fa-network-wired text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">Active Ports</div>\n        <div class=\"stat-value text-warning\" id=\"active-ports\">0</div>\n        <div class=\"stat-desc\">In use</div>\n    </div>\n</div>\n\n<!-- Quick Actions -->\n<div class=\"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8\">\n    <div class=\"card bg-base-200 shadow-sm\">\n        <div class=\"card-body\">\n            <h2 class=\"card-title\">\n                <i class=\"fas fa-plus text-success\"></i>\n                Create New Instance\n            </h2>\n            <div class=\"space-y-3\">\n                <input type=\"text\" placeholder=\"Instance ID (e.g., 8014)\" class=\"input input-bordered input-sm w-full\" id=\"new-instance-id\">\n                <input type=\"number\" placeholder=\"Flask Port (e.g., 8014)\" class=\"input input-bordered input-sm w-full\" id=\"new-flask-port\">\n                <input type=\"number\" placeholder=\"WebSocket Port (e.g., 20014)\" class=\"input input-bordered input-sm w-full\" id=\"new-ws-port\">\n                <button class=\"btn btn-success btn-sm w-full\" onclick=\"createNewInstance()\">\n                    <i class=\"fas fa-plus\"></i>\n                    Create Instance\n                </button>\n            </div>\n        </div>\n    </div>\n    \n    <div class=\"card bg-base-200 shadow-sm\">\n        <div class=\"card-body\">\n            <h2 class=\"card-title\">\n                <i class=\"fas fa-cogs text-primary\"></i>\n                Bulk Operations\n            </h2>\n            <div class=\"space-y-3\">\n                <button class=\"btn btn-primary btn-sm w-full\" onclick=\"startAllInstances()\">\n                    <i class=\"fas fa-play\"></i>\n                    Start All\n                </button>\n                <button class=\"btn btn-warning btn-sm w-full\" onclick=\"restartAllInstances()\">\n                    <i class=\"fas fa-redo\"></i>\n                    Restart All\n                </button>\n                <button class=\"btn btn-error btn-sm w-full\" onclick=\"stopAllInstances()\">\n                    <i class=\"fas fa-stop\"></i>\n                    Stop All\n                </button>\n            </div>\n        </div>\n    </div>\n    \n    <div class=\"card bg-base-200 shadow-sm\">\n        <div class=\"card-body\">\n            <h2 class=\"card-title\">\n                <i class=\"fas fa-search text-info\"></i>\n                Port Scanner\n            </h2>\n            <div class=\"space-y-3\">\n                <input type=\"number\" placeholder=\"Start Port (e.g., 8000)\" class=\"input input-bordered input-sm w-full\" id=\"scan-start-port\">\n                <input type=\"number\" placeholder=\"End Port (e.g., 9000)\" class=\"input input-bordered input-sm w-full\" id=\"scan-end-port\">\n                <button class=\"btn btn-info btn-sm w-full\" onclick=\"scanPorts()\">\n                    <i class=\"fas fa-search\"></i>\n                    Scan Ports\n                </button>\n            </div>\n        </div>\n    </div>\n</div>\n\n<!-- Instances Table -->\n<div class=\"card bg-base-200 shadow-sm\">\n    <div class=\"card-body\">\n        <div class=\"flex justify-between items-center mb-6\">\n            <h2 class=\"card-title\">\n                <i class=\"fas fa-cubes text-primary\"></i>\n                Instance Management\n            </h2>\n            <div class=\"flex gap-2\">\n                <button class=\"btn btn-ghost btn-sm\" onclick=\"refreshInstances()\">\n                    <i class=\"fas fa-sync-alt\"></i>\n                    Refresh\n                </button>\n                <div class=\"dropdown dropdown-end\">\n                    <div tabindex=\"0\" role=\"button\" class=\"btn btn-ghost btn-sm\">\n                        <i class=\"fas fa-filter\"></i>\n                        Filter\n                    </div>\n                    <ul tabindex=\"0\" class=\"dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52\">\n                        <li><a onclick=\"filterInstances('all')\">All Instances</a></li>\n                        <li><a onclick=\"filterInstances('running')\">Running Only</a></li>\n                        <li><a onclick=\"filterInstances('stopped')\">Stopped Only</a></li>\n                        <li><a onclick=\"filterInstances('high-memory')\">High Memory</a></li>\n                    </ul>\n                </div>\n            </div>\n        </div>\n        \n        <div class=\"overflow-x-auto\">\n            <table class=\"table table-zebra w-full\">\n                <thead>\n                    <tr>\n                        <th>Instance ID</th>\n                        <th>Status</th>\n                        <th>Ports</th>\n                        <th>Resources</th>\n                        <th>Uptime</th>\n                        <th>Actions</th>\n                    </tr>\n                </thead>\n                <tbody id=\"instances-table\">\n                    <tr>\n                        <td colspan=\"6\" class=\"text-center text-base-content/50\">\n                            <i class=\"fas fa-spinner fa-spin mr-2\"></i>\n                            Loading instances...\n                        </td>\n                    </tr>\n                </tbody>\n            </table>\n        </div>\n    </div>\n</div>\n\n<!-- Port Usage Table -->\n<div class=\"card bg-base-200 shadow-sm mt-8\">\n    <div class=\"card-body\">\n        <div class=\"flex justify-between items-center mb-6\">\n            <h2 class=\"card-title\">\n                <i class=\"fas fa-network-wired text-warning\"></i>\n                Port Usage\n            </h2>\n            <button class=\"btn btn-ghost btn-sm\" onclick=\"refreshPorts()\">\n                <i class=\"fas fa-sync-alt\"></i>\n                Refresh\n            </button>\n        </div>\n        \n        <div class=\"overflow-x-auto\">\n            <table class=\"table table-zebra w-full\">\n                <thead>\n                    <tr>\n                        <th>Port</th>\n                        <th>Process</th>\n                        <th>PID</th>\n                        <th>Type</th>\n                        <th>Actions</th>\n                    </tr>\n                </thead>\n                <tbody id=\"ports-table\">\n                    <tr>\n                        <td colspan=\"5\" class=\"text-center text-base-content/50\">\n                            <i class=\"fas fa-spinner fa-spin mr-2\"></i>\n                            Loading port information...\n                        </td>\n                    </tr>\n                </tbody>\n            </table>\n        </div>\n    </div>\n</div>\n\n<!-- Instance Logs Modal -->\n<dialog id=\"instance-logs-modal\" class=\"modal\">\n    <div class=\"modal-box w-11/12 max-w-4xl\">\n        <h3 class=\"font-bold text-lg mb-4\">\n            <i class=\"fas fa-file-alt\"></i>\n            Instance Logs - <span id=\"logs-instance-id\"></span>\n        </h3>\n        <div class=\"bg-base-300 rounded-lg p-4 h-96 overflow-y-auto font-mono text-sm\" id=\"instance-logs-content\">\n            Loading logs...\n        </div>\n        <div class=\"modal-action\">\n            <button class=\"btn btn-primary\" onclick=\"downloadInstanceLogs()\">\n                <i class=\"fas fa-download\"></i>\n                Download\n            </button>\n            <button class=\"btn btn-ghost\" onclick=\"closeLogsModal()\">Close</button>\n        </div>\n    </div>\n</dialog>\n\n<!-- Create Instance Modal -->\n<dialog id=\"create-instance-modal\" class=\"modal\">\n    <div class=\"modal-box\">\n        <h3 class=\"font-bold text-lg mb-4\">\n            <i class=\"fas fa-plus\"></i>\n            Create New AlgoFactory Instance\n        </h3>\n        <div class=\"space-y-4\">\n            <div class=\"form-control\">\n                <label class=\"label\">\n                    <span class=\"label-text\">Instance ID</span>\n                </label>\n                <input type=\"text\" placeholder=\"e.g., 8014\" class=\"input input-bordered w-full\" id=\"modal-instance-id\">\n            </div>\n            <div class=\"form-control\">\n                <label class=\"label\">\n                    <span class=\"label-text\">Flask Port</span>\n                </label>\n                <input type=\"number\" placeholder=\"e.g., 8014\" class=\"input input-bordered w-full\" id=\"modal-flask-port\">\n            </div>\n            <div class=\"form-control\">\n                <label class=\"label\">\n                    <span class=\"label-text\">WebSocket Port</span>\n                </label>\n                <input type=\"number\" placeholder=\"e.g., 20014\" class=\"input input-bordered w-full\" id=\"modal-ws-port\">\n            </div>\n            <div class=\"form-control\">\n                <label class=\"label\">\n                    <span class=\"label-text\">ZMQ Port</span>\n                </label>\n                <input type=\"number\" placeholder=\"e.g., 23014\" class=\"input input-bordered w-full\" id=\"modal-zmq-port\">\n            </div>\n            <div class=\"form-control\">\n                <label class=\"label cursor-pointer\">\n                    <span class=\"label-text\">Auto-start on boot</span>\n                    <input type=\"checkbox\" class=\"checkbox\" id=\"modal-auto-start\" checked>\n                </label>\n            </div>\n            <div class=\"form-control\">\n                <label class=\"label cursor-pointer\">\n                    <span class=\"label-text\">Create Nginx configuration</span>\n                    <input type=\"checkbox\" class=\"checkbox\" id=\"modal-create-nginx\" checked>\n                </label>\n            </div>\n        </div>\n        <div class=\"modal-action\">\n            <button class=\"btn btn-primary\" onclick=\"confirmCreateInstance()\">\n                <i class=\"fas fa-plus\"></i>\n                Create Instance\n            </button>\n            <button class=\"btn btn-ghost\" onclick=\"closeCreateModal()\">Cancel</button>\n        </div>\n    </div>\n</dialog>\n{% endblock %}\n\n{% block scripts %}\n<script>\n    let instancesData = [];\n    let portsData = [];\n    \n    // Initialize page\n    document.addEventListener('DOMContentLoaded', function() {\n        loadInstances();\n        loadPorts();\n        \n        // Listen for real-time updates\n        window.addEventListener('systemUpdate', function(event) {\n            if (event.detail.data && event.detail.data.instances) {\n                updateInstancesData(event.detail.data.instances);\n            }\n        });\n    });\n    \n    async function loadInstances() {\n        try {\n            const response = await fetch('/api/system/metrics');\n            const data = await response.json();\n            \n            if (data.instances) {\n                instancesData = data.instances;\n                updateInstancesData(data.instances);\n            }\n        } catch (error) {\n            console.error('Error loading instances:', error);\n            showToast('Failed to load instances', 'error');\n        }\n    }\n    \n    function updateInstancesData(instances) {\n        instancesData = instances;\n        \n        // Update overview stats\n        const runningCount = instances.length;\n        const stoppedCount = 0; // This would come from database\n        const totalMemory = instances.reduce((sum, inst) => sum + (inst.memory_mb || 0), 0);\n        const activePorts = instances.length * 3; // Flask + WebSocket + ZMQ\n        \n        document.getElementById('running-count').textContent = runningCount;\n        document.getElementById('stopped-count').textContent = stoppedCount;\n        document.getElementById('total-memory').textContent = totalMemory + 'MB';\n        document.getElementById('active-ports').textContent = activePorts;\n        \n        // Update table\n        renderInstancesTable(instances);\n    }\n    \n    function renderInstancesTable(instances) {\n        const tbody = document.getElementById('instances-table');\n        \n        if (instances.length === 0) {\n            tbody.innerHTML = `\n                <tr>\n                    <td colspan=\"6\" class=\"text-center text-base-content/50\">\n                        <i class=\"fas fa-info-circle mr-2\"></i>\n                        No AlgoFactory instances running\n                    </td>\n                </tr>\n            `;\n            return;\n        }\n        \n        tbody.innerHTML = instances.map(instance => `\n            <tr>\n                <td>\n                    <div class=\"font-bold\">${instance.instance_id}</div>\n                    <div class=\"text-sm text-base-content/70\">PID: ${instance.pid}</div>\n                </td>\n                <td>\n                    <div class=\"badge badge-success\">\n                        <i class=\"fas fa-circle text-xs mr-1\"></i>\n                        Running\n                    </div>\n                </td>\n                <td>\n                    <div class=\"text-sm\">\n                        <div><strong>Flask:</strong> ${instance.port}</div>\n                        <div><strong>WS:</strong> ${instance.websocket_port}</div>\n                        <div><strong>ZMQ:</strong> ${instance.zmq_port}</div>\n                    </div>\n                </td>\n                <td>\n                    <div class=\"text-sm\">\n                        <div><strong>CPU:</strong> ${instance.cpu_percent}%</div>\n                        <div><strong>RAM:</strong> ${instance.memory_mb}MB</div>\n                    </div>\n                </td>\n                <td>\n                    <div class=\"text-sm\">\n                        ${formatUptime(instance.uptime_seconds)}\n                    </div>\n                </td>\n                <td>\n                    <div class=\"flex gap-1\">\n                        <button class=\"btn btn-info btn-xs\" onclick=\"viewInstanceLogs('${instance.instance_id}')\" title=\"View Logs\">\n                            <i class=\"fas fa-file-alt\"></i>\n                        </button>\n                        <button class=\"btn btn-warning btn-xs\" onclick=\"restartInstance('${instance.instance_id}')\" title=\"Restart\">\n                            <i class=\"fas fa-redo\"></i>\n                        </button>\n                        <button class=\"btn btn-error btn-xs\" onclick=\"stopInstance('${instance.instance_id}')\" title=\"Stop\">\n                            <i class=\"fas fa-stop\"></i>\n                        </button>\n                        <button class=\"btn btn-ghost btn-xs\" onclick=\"openInstanceUrl('${instance.port}')\" title=\"Open\">\n                            <i class=\"fas fa-external-link-alt\"></i>\n                        </button>\n                    </div>\n                </td>\n            </tr>\n        `).join('');\n    }\n    \n    async function loadPorts() {\n        // Mock port data - in real implementation, this would come from the server\n        portsData = [\n            { port: 22, process: 'sshd', pid: 1234, type: 'System' },\n            { port: 80, process: 'nginx', pid: 5678, type: 'Web Server' },\n            { port: 443, process: 'nginx', pid: 5678, type: 'Web Server' },\n            { port: 8010, process: 'python3', pid: 9012, type: 'AlgoFactory' },\n            { port: 8011, process: 'python3', pid: 9013, type: 'AlgoFactory' },\n            { port: 8012, process: 'python3', pid: 9014, type: 'AlgoFactory' },\n            { port: 9001, process: 'python3', pid: 9015, type: 'Admin Panel' }\n        ];\n        \n        renderPortsTable(portsData);\n    }\n    \n    function renderPortsTable(ports) {\n        const tbody = document.getElementById('ports-table');\n        \n        tbody.innerHTML = ports.map(port => `\n            <tr>\n                <td>\n                    <div class=\"font-bold\">${port.port}</div>\n                </td>\n                <td>\n                    <div class=\"font-medium\">${port.process}</div>\n                </td>\n                <td>\n                    <div class=\"text-sm\">${port.pid}</div>\n                </td>\n                <td>\n                    <div class=\"badge ${getPortTypeBadge(port.type)}\">${port.type}</div>\n                </td>\n                <td>\n                    <div class=\"flex gap-1\">\n                        ${port.type === 'AlgoFactory' ? `\n                            <button class=\"btn btn-warning btn-xs\" onclick=\"killProcess(${port.pid})\" title=\"Kill Process\">\n                                <i class=\"fas fa-times\"></i>\n                            </button>\n                        ` : ''}\n                        <button class=\"btn btn-info btn-xs\" onclick=\"testPort(${port.port})\" title=\"Test Connection\">\n                            <i class=\"fas fa-plug\"></i>\n                        </button>\n                    </div>\n                </td>\n            </tr>\n        `).join('');\n    }\n    \n    function getPortTypeBadge(type) {\n        switch (type) {\n            case 'AlgoFactory': return 'badge-primary';\n            case 'Admin Panel': return 'badge-secondary';\n            case 'Web Server': return 'badge-accent';\n            case 'System': return 'badge-neutral';\n            default: return 'badge-ghost';\n        }\n    }\n    \n    // Action functions\n    function createNewInstance() {\n        document.getElementById('create-instance-modal').showModal();\n    }\n    \n    function closeCreateModal() {\n        document.getElementById('create-instance-modal').close();\n    }\n    \n    async function confirmCreateInstance() {\n        const instanceId = document.getElementById('modal-instance-id').value;\n        const flaskPort = document.getElementById('modal-flask-port').value;\n        const wsPort = document.getElementById('modal-ws-port').value;\n        const zmqPort = document.getElementById('modal-zmq-port').value;\n        \n        if (!instanceId || !flaskPort) {\n            showToast('Please fill in required fields', 'error');\n            return;\n        }\n        \n        showToast(`Creating instance ${instanceId}...`, 'info');\n        \n        // This would call the API to create the instance\n        setTimeout(() => {\n            showToast(`Instance ${instanceId} created successfully!`, 'success');\n            closeCreateModal();\n            loadInstances();\n        }, 2000);\n    }\n    \n    async function stopInstance(instanceId) {\n        if (confirm(`Stop instance ${instanceId}?`)) {\n            showToast(`Stopping instance ${instanceId}...`, 'warning');\n            \n            try {\n                const response = await fetch('/api/instances/manage', {\n                    method: 'POST',\n                    headers: { 'Content-Type': 'application/json' },\n                    body: JSON.stringify({ action: 'stop', instance_id: instanceId })\n                });\n                \n                const result = await response.json();\n                if (result.success) {\n                    showToast(result.message, 'success');\n                    loadInstances();\n                } else {\n                    showToast(result.message, 'error');\n                }\n            } catch (error) {\n                showToast('Failed to stop instance', 'error');\n            }\n        }\n    }\n    \n    async function restartInstance(instanceId) {\n        if (confirm(`Restart instance ${instanceId}?`)) {\n            showToast(`Restarting instance ${instanceId}...`, 'info');\n            \n            try {\n                const response = await fetch('/api/instances/manage', {\n                    method: 'POST',\n                    headers: { 'Content-Type': 'application/json' },\n                    body: JSON.stringify({ action: 'restart', instance_id: instanceId })\n                });\n                \n                const result = await response.json();\n                showToast(result.message, result.success ? 'success' : 'error');\n                if (result.success) loadInstances();\n            } catch (error) {\n                showToast('Failed to restart instance', 'error');\n            }\n        }\n    }\n    \n    function viewInstanceLogs(instanceId) {\n        document.getElementById('logs-instance-id').textContent = instanceId;\n        document.getElementById('instance-logs-modal').showModal();\n        document.getElementById('instance-logs-content').textContent = 'Loading logs...';\n        \n        // Load logs\n        setTimeout(() => {\n            document.getElementById('instance-logs-content').innerHTML = `\n                <div class=\"text-sm\">\n                    <div class=\"text-info\">[2025-06-24 00:15:23] INFO: AlgoFactory instance ${instanceId} started</div>\n                    <div class=\"text-success\">[2025-06-24 00:15:20] INFO: WebSocket server listening on port 20${instanceId}</div>\n                    <div class=\"text-success\">[2025-06-24 00:15:18] INFO: Flask server listening on port ${instanceId}</div>\n                    <div class=\"text-warning\">[2025-06-24 00:15:15] WARNING: High memory usage detected</div>\n                    <div class=\"text-success\">[2025-06-24 00:15:10] INFO: Database connection established</div>\n                </div>\n            `;\n        }, 500);\n    }\n    \n    function closeLogsModal() {\n        document.getElementById('instance-logs-modal').close();\n    }\n    \n    function openInstanceUrl(port) {\n        window.open(`http://localhost:${port}`, '_blank');\n    }\n    \n    function killProcess(pid) {\n        if (confirm(`Kill process ${pid}? This will stop the associated service.`)) {\n            showToast(`Killing process ${pid}...`, 'warning');\n            // This would call the API to kill the process\n            setTimeout(() => {\n                showToast(`Process ${pid} terminated`, 'success');\n                loadPorts();\n                loadInstances();\n            }, 1000);\n        }\n    }\n    \n    function testPort(port) {\n        showToast(`Testing connection to port ${port}...`, 'info');\n        // This would test the port connection\n        setTimeout(() => {\n            showToast(`Port ${port} is responding`, 'success');\n        }, 1000);\n    }\n    \n    function scanPorts() {\n        const startPort = document.getElementById('scan-start-port').value;\n        const endPort = document.getElementById('scan-end-port').value;\n        \n        if (!startPort || !endPort) {\n            showToast('Please enter start and end ports', 'error');\n            return;\n        }\n        \n        showToast(`Scanning ports ${startPort}-${endPort}...`, 'info');\n        // This would scan the port range\n        setTimeout(() => {\n            showToast(`Port scan completed. Found 5 open ports.`, 'success');\n        }, 2000);\n    }\n    \n    function refreshInstances() {\n        showToast('Refreshing instances...', 'info');\n        loadInstances();\n    }\n    \n    function refreshPorts() {\n        showToast('Refreshing port information...', 'info');\n        loadPorts();\n    }\n    \n    // Utility functions\n    function formatUptime(seconds) {\n        const days = Math.floor(seconds / 86400);\n        const hours = Math.floor((seconds % 86400) / 3600);\n        const minutes = Math.floor((seconds % 3600) / 60);\n        \n        if (days > 0) return `${days}d ${hours}h`;\n        if (hours > 0) return `${hours}h ${minutes}m`;\n        return `${minutes}m`;\n    }\n</script>\n{% endblock %}\n"}