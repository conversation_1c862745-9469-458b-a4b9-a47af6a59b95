{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/simple_websocket_test.py"}, "modifiedCode": "#!/usr/bin/env python3\n\n\"\"\"\nSimple WebSocket Port Test for AlgoFactory Instances\nTests if WebSocket ports are listening and accessible\n\"\"\"\n\nimport socket\nimport sys\n\ndef test_port_connection(host, port, instance_id):\n    \"\"\"Test if a port is listening and accepting connections\"\"\"\n    try:\n        print(f\"🔌 Testing connection to instance {instance_id} at {host}:{port}\")\n        \n        # Create a socket connection\n        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)\n        sock.settimeout(5)  # 5 second timeout\n        \n        # Try to connect\n        result = sock.connect_ex((host, port))\n        sock.close()\n        \n        if result == 0:\n            print(f\"✅ Instance {instance_id} WebSocket port {port} is listening and accessible\")\n            return True\n        else:\n            print(f\"❌ Instance {instance_id} WebSocket port {port} is not accessible (error code: {result})\")\n            return False\n            \n    except socket.gaierror as e:\n        print(f\"❌ DNS/Host error for instance {instance_id}: {e}\")\n        return False\n    except Exception as e:\n        print(f\"❌ Error testing instance {instance_id}: {e}\")\n        return False\n\ndef test_all_instances():\n    \"\"\"Test WebSocket ports for all instances\"\"\"\n    instances = [\n        {\"id\": \"8010\", \"port\": 20010},\n        {\"id\": \"8011\", \"port\": 20011},\n        {\"id\": \"8012\", \"port\": 20012}\n    ]\n    \n    print(\"🚀 AlgoFactory WebSocket Port Test\")\n    print(\"=\" * 40)\n    \n    results = []\n    \n    for instance in instances:\n        instance_id = instance[\"id\"]\n        port = instance[\"port\"]\n        \n        result = test_port_connection(\"localhost\", port, instance_id)\n        results.append({\n            \"instance\": instance_id,\n            \"port\": port,\n            \"success\": result\n        })\n        \n        print()  # Add spacing between tests\n    \n    # Summary\n    print(\"📊 Test Results Summary:\")\n    print(\"=\" * 30)\n    \n    success_count = 0\n    for result in results:\n        status = \"✅ PASS\" if result[\"success\"] else \"❌ FAIL\"\n        print(f\"Instance {result['instance']} (port {result['port']}): {status}\")\n        if result[\"success\"]:\n            success_count += 1\n    \n    print()\n    print(f\"🎯 Overall Result: {success_count}/{len(instances)} instances passed\")\n    \n    if success_count == len(instances):\n        print(\"🎉 All WebSocket ports are accessible!\")\n        return True\n    else:\n        print(\"⚠️  Some WebSocket ports have issues\")\n        return False\n\ndef test_single_instance(instance_id):\n    \"\"\"Test a single instance WebSocket port\"\"\"\n    port = int(instance_id) + 12000  # Calculate WebSocket port\n    \n    print(f\"🔌 Testing single instance {instance_id}\")\n    print(\"=\" * 40)\n    \n    result = test_port_connection(\"localhost\", port, instance_id)\n    \n    if result:\n        print(f\"✅ Instance {instance_id} WebSocket port is accessible!\")\n    else:\n        print(f\"❌ Instance {instance_id} WebSocket port has issues!\")\n    \n    return result\n\ndef main():\n    \"\"\"Main function\"\"\"\n    if len(sys.argv) > 1:\n        # Test specific instance\n        instance_id = sys.argv[1]\n        try:\n            result = test_single_instance(instance_id)\n            sys.exit(0 if result else 1)\n        except KeyboardInterrupt:\n            print(\"\\n🛑 Test interrupted by user\")\n            sys.exit(1)\n    else:\n        # Test all instances\n        try:\n            result = test_all_instances()\n            sys.exit(0 if result else 1)\n        except KeyboardInterrupt:\n            print(\"\\n🛑 Test interrupted by user\")\n            sys.exit(1)\n\nif __name__ == \"__main__\":\n    main()\n"}