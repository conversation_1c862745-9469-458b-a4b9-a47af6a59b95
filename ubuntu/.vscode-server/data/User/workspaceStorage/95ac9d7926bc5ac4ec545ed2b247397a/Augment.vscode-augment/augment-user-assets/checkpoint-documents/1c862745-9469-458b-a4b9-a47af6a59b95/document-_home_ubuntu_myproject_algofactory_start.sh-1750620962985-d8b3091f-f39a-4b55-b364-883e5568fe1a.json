{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/start.sh"}, "originalCode": "#!/bin/bash\r\n\r\n# AlgoFactory Robust Startup Script\r\n# This script automatically starts app.py with full error handling and recovery\r\n\r\nset -e  # Exit on any error\r\n\r\n# Configuration\r\nPROJECT_DIR=\"/home/<USER>/myproject/algofactory\"\r\nSHARED_VENV=\"/home/<USER>/shared-venv\"\r\nAPP_FILE=\"app.py\"\r\nLOG_DIR=\"$PROJECT_DIR/logs\"\r\nPID_FILE=\"$PROJECT_DIR/app.pid\"\r\nMAX_RETRIES=5\r\nRETRY_DELAY=10\r\n\r\n# Colors for output\r\nRED='\\033[0;31m'\r\nGREEN='\\033[0;32m'\r\nYELLOW='\\033[1;33m'\r\nBLUE='\\033[0;34m'\r\nNC='\\033[0m' # No Color\r\n\r\n# Logging function\r\nlog() {\r\n    echo -e \"${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1\"\r\n}\r\n\r\nerror() {\r\n    echo -e \"${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1\" >&2\r\n}\r\n\r\nsuccess() {\r\n    echo -e \"${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1\"\r\n}\r\n\r\nwarning() {\r\n    echo -e \"${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1\"\r\n}\r\n\r\n# Function to check if app is running\r\nis_app_running() {\r\n    if [ -f \"$PID_FILE\" ]; then\r\n        local pid=$(cat \"$PID_FILE\")\r\n        if ps -p \"$pid\" > /dev/null 2>&1; then\r\n            return 0\r\n        else\r\n            rm -f \"$PID_FILE\"\r\n            return 1\r\n        fi\r\n    fi\r\n    return 1\r\n}\r\n\r\n# Function to stop the app\r\nstop_app() {\r\n    if [ -f \"$PID_FILE\" ]; then\r\n        local pid=$(cat \"$PID_FILE\")\r\n        log \"Stopping application (PID: $pid)...\"\r\n        kill -TERM \"$pid\" 2>/dev/null || true\r\n        sleep 5\r\n        if ps -p \"$pid\" > /dev/null 2>&1; then\r\n            warning \"Application didn't stop gracefully, forcing kill...\"\r\n            kill -KILL \"$pid\" 2>/dev/null || true\r\n        fi\r\n        rm -f \"$PID_FILE\"\r\n        success \"Application stopped\"\r\n    fi\r\n}\r\n\r\n# Function to setup environment\r\nsetup_environment() {\r\n    log \"Setting up environment...\"\r\n\r\n    # Change to project directory\r\n    cd \"$PROJECT_DIR\" || {\r\n        error \"Failed to change to project directory: $PROJECT_DIR\"\r\n        exit 1\r\n    }\r\n\r\n    # Create necessary directories with full permissions\r\n    log \"Creating necessary directories...\"\r\n    mkdir -p db logs tmp static/uploads\r\n\r\n    # Set full permissions (777) for all necessary directories\r\n    chmod -R 777 db logs tmp static 2>/dev/null || {\r\n        warning \"Could not set permissions on some directories (may be mounted volumes)\"\r\n    }\r\n\r\n    # Check if shared virtual environment exists\r\n    if [ ! -d \"$SHARED_VENV\" ]; then\r\n        error \"Shared virtual environment not found at: $SHARED_VENV\"\r\n        log \"Creating shared virtual environment...\"\r\n        python3 -m venv \"$SHARED_VENV\" || {\r\n            error \"Failed to create virtual environment\"\r\n            exit 1\r\n        }\r\n    fi\r\n\r\n    # Check if virtual environment has Python\r\n    if [ ! -f \"$SHARED_VENV/bin/python\" ]; then\r\n        error \"Python not found in virtual environment: $SHARED_VENV/bin/python\"\r\n        exit 1\r\n    fi\r\n\r\n    success \"Environment setup completed\"\r\n}\r\n\r\n# Function to install/update dependencies\r\ninstall_dependencies() {\r\n    log \"Installing/updating dependencies...\"\r\n\r\n    # Activate virtual environment\r\n    source \"$SHARED_VENV/bin/activate\" || {\r\n        error \"Failed to activate virtual environment\"\r\n        exit 1\r\n    }\r\n\r\n    # Upgrade pip\r\n    pip install --upgrade pip > /dev/null 2>&1 || {\r\n        warning \"Failed to upgrade pip\"\r\n    }\r\n\r\n    # Install requirements if file exists\r\n    if [ -f \"requirements.txt\" ]; then\r\n        log \"Installing requirements from requirements.txt...\"\r\n        pip install -r requirements.txt || {\r\n            error \"Failed to install requirements\"\r\n            exit 1\r\n        }\r\n    fi\r\n\r\n    # Install additional packages that might be needed\r\n    pip install gunicorn eventlet python-dotenv > /dev/null 2>&1 || {\r\n        warning \"Failed to install some additional packages\"\r\n    }\r\n\r\n    success \"Dependencies installed successfully\"\r\n}\r\n\r\n# Function to start the application\r\nstart_app() {\r\n    local retry_count=0\r\n\r\n    while [ $retry_count -lt $MAX_RETRIES ]; do\r\n        log \"Starting AlgoFactory application (attempt $((retry_count + 1))/$MAX_RETRIES)...\"\r\n\r\n        # Activate virtual environment\r\n        source \"$SHARED_VENV/bin/activate\" || {\r\n            error \"Failed to activate virtual environment\"\r\n            exit 1\r\n        }\r\n\r\n        # Check if app.py exists\r\n        if [ ! -f \"$APP_FILE\" ]; then\r\n            error \"Application file not found: $APP_FILE\"\r\n            exit 1\r\n        fi\r\n\r\n        # Start the application with gunicorn\r\n        nohup \"$SHARED_VENV/bin/gunicorn\" \\\r\n            --bind=0.0.0.0:5000 \\\r\n            --worker-class=eventlet \\\r\n            --workers=1 \\\r\n            --timeout=120 \\\r\n            --keep-alive=2 \\\r\n            --max-requests=1000 \\\r\n            --max-requests-jitter=100 \\\r\n            --preload \\\r\n            --log-level=info \\\r\n            --access-logfile=\"$LOG_DIR/access.log\" \\\r\n            --error-logfile=\"$LOG_DIR/error.log\" \\\r\n            --capture-output \\\r\n            --enable-stdio-inheritance \\\r\n            app:app > \"$LOG_DIR/app.log\" 2>&1 &\r\n\r\n        local app_pid=$!\r\n        echo $app_pid > \"$PID_FILE\"\r\n\r\n        # Wait a moment and check if the app started successfully\r\n        sleep 5\r\n\r\n        if ps -p \"$app_pid\" > /dev/null 2>&1; then\r\n            success \"AlgoFactory application started successfully (PID: $app_pid)\"\r\n            success \"Application is running on http://0.0.0.0:5000\"\r\n            success \"Logs are available in: $LOG_DIR/\"\r\n            return 0\r\n        else\r\n            error \"Application failed to start (attempt $((retry_count + 1)))\"\r\n            rm -f \"$PID_FILE\"\r\n\r\n            # Show last few lines of error log\r\n            if [ -f \"$LOG_DIR/error.log\" ]; then\r\n                error \"Last few lines from error log:\"\r\n                tail -10 \"$LOG_DIR/error.log\" | while read line; do\r\n                    error \"  $line\"\r\n                done\r\n            fi\r\n\r\n            retry_count=$((retry_count + 1))\r\n            if [ $retry_count -lt $MAX_RETRIES ]; then\r\n                warning \"Retrying in $RETRY_DELAY seconds...\"\r\n                sleep $RETRY_DELAY\r\n            fi\r\n        fi\r\n    done\r\n\r\n    error \"Failed to start application after $MAX_RETRIES attempts\"\r\n    exit 1\r\n}\r\n\r\n# Function to monitor the application\r\nmonitor_app() {\r\n    log \"Starting application monitor...\"\r\n\r\n    while true; do\r\n        if ! is_app_running; then\r\n            warning \"Application is not running, attempting to restart...\"\r\n            start_app\r\n        fi\r\n        sleep 30  # Check every 30 seconds\r\n    done\r\n}\r\n\r\n# Main execution\r\nmain() {\r\n    log \"=== AlgoFactory Startup Script ===\"\r\n    log \"Project Directory: $PROJECT_DIR\"\r\n    log \"Shared Virtual Environment: $SHARED_VENV\"\r\n    log \"Application File: $APP_FILE\"\r\n\r\n    # Handle command line arguments\r\n    case \"${1:-start}\" in\r\n        \"start\")\r\n            if is_app_running; then\r\n                warning \"Application is already running\"\r\n                exit 0\r\n            fi\r\n            setup_environment\r\n            install_dependencies\r\n            start_app\r\n            ;;\r\n        \"stop\")\r\n            stop_app\r\n            ;;\r\n        \"restart\")\r\n            stop_app\r\n            setup_environment\r\n            install_dependencies\r\n            start_app\r\n            ;;\r\n        \"status\")\r\n            if is_app_running; then\r\n                success \"Application is running (PID: $(cat $PID_FILE))\"\r\n            else\r\n                warning \"Application is not running\"\r\n            fi\r\n            ;;\r\n        \"monitor\")\r\n            setup_environment\r\n            install_dependencies\r\n            monitor_app\r\n            ;;\r\n        \"logs\")\r\n            if [ -f \"$LOG_DIR/app.log\" ]; then\r\n                tail -f \"$LOG_DIR/app.log\"\r\n            else\r\n                error \"Log file not found: $LOG_DIR/app.log\"\r\n            fi\r\n            ;;\r\n        *)\r\n            echo \"Usage: $0 {start|stop|restart|status|monitor|logs}\"\r\n            echo \"  start   - Start the application\"\r\n            echo \"  stop    - Stop the application\"\r\n            echo \"  restart - Restart the application\"\r\n            echo \"  status  - Check application status\"\r\n            echo \"  monitor - Start with continuous monitoring\"\r\n            echo \"  logs    - Show application logs\"\r\n            exit 1\r\n            ;;\r\n    esac\r\n}\r\n\r\n# Trap signals for graceful shutdown\r\ntrap 'stop_app; exit 0' SIGTERM SIGINT\r\n\r\n# Run main function\r\nmain \"$@\"\r\n", "modifiedCode": "#!/bin/bash\n\n# AlgoFactory Robust Startup Script\n# This script automatically starts app.py with full error handling and recovery\n\nset -e  # Exit on any error\n\n# Configuration\nPROJECT_DIR=\"/home/<USER>/myproject/algofactory\"\nSHARED_VENV=\"/home/<USER>/shared-venv\"\nAPP_FILE=\"app.py\"\nLOG_DIR=\"$PROJECT_DIR/logs\"\nPID_FILE=\"$PROJECT_DIR/app.pid\"\nMAX_RETRIES=5\nRETRY_DELAY=10\n\n# Colors for output\nRED='\\033[0;31m'\nGREEN='\\033[0;32m'\nYELLOW='\\033[1;33m'\nBLUE='\\033[0;34m'\nNC='\\033[0m' # No Color\n\n# Logging function\nlog() {\n    echo -e \"${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1\"\n}\n\nerror() {\n    echo -e \"${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1\" >&2\n}\n\nsuccess() {\n    echo -e \"${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1\"\n}\n\nwarning() {\n    echo -e \"${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1\"\n}\n\n# Function to check if app is running\nis_app_running() {\n    if [ -f \"$PID_FILE\" ]; then\n        local pid=$(cat \"$PID_FILE\")\n        if ps -p \"$pid\" > /dev/null 2>&1; then\n            return 0\n        else\n            rm -f \"$PID_FILE\"\n            return 1\n        fi\n    fi\n    return 1\n}\n\n# Function to stop the app\nstop_app() {\n    if [ -f \"$PID_FILE\" ]; then\n        local pid=$(cat \"$PID_FILE\")\n        log \"Stopping application (PID: $pid)...\"\n        kill -TERM \"$pid\" 2>/dev/null || true\n        sleep 5\n        if ps -p \"$pid\" > /dev/null 2>&1; then\n            warning \"Application didn't stop gracefully, forcing kill...\"\n            kill -KILL \"$pid\" 2>/dev/null || true\n        fi\n        rm -f \"$PID_FILE\"\n        success \"Application stopped\"\n    fi\n}\n\n# Function to setup environment\nsetup_environment() {\n    log \"Setting up environment...\"\n\n    # Change to project directory\n    cd \"$PROJECT_DIR\" || {\n        error \"Failed to change to project directory: $PROJECT_DIR\"\n        exit 1\n    }\n\n    # Create necessary directories with full permissions\n    log \"Creating necessary directories...\"\n    mkdir -p db logs tmp static/uploads\n\n    # Set full permissions (777) for all necessary directories\n    chmod -R 777 db logs tmp static 2>/dev/null || {\n        warning \"Could not set permissions on some directories (may be mounted volumes)\"\n    }\n\n    # Check if shared virtual environment exists\n    if [ ! -d \"$SHARED_VENV\" ]; then\n        error \"Shared virtual environment not found at: $SHARED_VENV\"\n        log \"Creating shared virtual environment...\"\n        python3 -m venv \"$SHARED_VENV\" || {\n            error \"Failed to create virtual environment\"\n            exit 1\n        }\n    fi\n\n    # Check if virtual environment has Python\n    if [ ! -f \"$SHARED_VENV/bin/python\" ]; then\n        error \"Python not found in virtual environment: $SHARED_VENV/bin/python\"\n        exit 1\n    fi\n\n    success \"Environment setup completed\"\n}\n\n# Function to install/update dependencies\ninstall_dependencies() {\n    log \"Installing/updating dependencies...\"\n\n    # Activate virtual environment\n    source \"$SHARED_VENV/bin/activate\" || {\n        error \"Failed to activate virtual environment\"\n        exit 1\n    }\n\n    # Upgrade pip\n    pip install --upgrade pip > /dev/null 2>&1 || {\n        warning \"Failed to upgrade pip\"\n    }\n\n    # Install requirements if file exists\n    if [ -f \"requirements.txt\" ]; then\n        log \"Installing requirements from requirements.txt...\"\n        pip install -r requirements.txt || {\n            error \"Failed to install requirements\"\n            exit 1\n        }\n    fi\n\n    # Install additional packages that might be needed\n    pip install gunicorn eventlet python-dotenv > /dev/null 2>&1 || {\n        warning \"Failed to install some additional packages\"\n    }\n\n    success \"Dependencies installed successfully\"\n}\n\n# Function to start the application\nstart_app() {\n    local retry_count=0\n\n    while [ $retry_count -lt $MAX_RETRIES ]; do\n        log \"Starting AlgoFactory application (attempt $((retry_count + 1))/$MAX_RETRIES)...\"\n\n        # Activate virtual environment\n        source \"$SHARED_VENV/bin/activate\" || {\n            error \"Failed to activate virtual environment\"\n            exit 1\n        }\n\n        # Check if app.py exists\n        if [ ! -f \"$APP_FILE\" ]; then\n            error \"Application file not found: $APP_FILE\"\n            exit 1\n        fi\n\n        # Start the application with gunicorn\n        nohup \"$SHARED_VENV/bin/gunicorn\" \\\n            --bind=0.0.0.0:5000 \\\n            --worker-class=eventlet \\\n            --workers=1 \\\n            --timeout=120 \\\n            --keep-alive=2 \\\n            --max-requests=1000 \\\n            --max-requests-jitter=100 \\\n            --preload \\\n            --log-level=info \\\n            --access-logfile=\"$LOG_DIR/access.log\" \\\n            --error-logfile=\"$LOG_DIR/error.log\" \\\n            --capture-output \\\n            --enable-stdio-inheritance \\\n            app:app > \"$LOG_DIR/app.log\" 2>&1 &\n\n        local app_pid=$!\n        echo $app_pid > \"$PID_FILE\"\n\n        # Wait a moment and check if the app started successfully\n        sleep 5\n\n        if ps -p \"$app_pid\" > /dev/null 2>&1; then\n            success \"AlgoFactory application started successfully (PID: $app_pid)\"\n            success \"Application is running on http://0.0.0.0:5000\"\n            success \"Logs are available in: $LOG_DIR/\"\n            return 0\n        else\n            error \"Application failed to start (attempt $((retry_count + 1)))\"\n            rm -f \"$PID_FILE\"\n\n            # Show last few lines of error log\n            if [ -f \"$LOG_DIR/error.log\" ]; then\n                error \"Last few lines from error log:\"\n                tail -10 \"$LOG_DIR/error.log\" | while read line; do\n                    error \"  $line\"\n                done\n            fi\n\n            retry_count=$((retry_count + 1))\n            if [ $retry_count -lt $MAX_RETRIES ]; then\n                warning \"Retrying in $RETRY_DELAY seconds...\"\n                sleep $RETRY_DELAY\n            fi\n        fi\n    done\n\n    error \"Failed to start application after $MAX_RETRIES attempts\"\n    exit 1\n}\n\n# Function to monitor the application\nmonitor_app() {\n    log \"Starting application monitor...\"\n\n    while true; do\n        if ! is_app_running; then\n            warning \"Application is not running, attempting to restart...\"\n            start_app\n        fi\n        sleep 30  # Check every 30 seconds\n    done\n}\n\n# Main execution\nmain() {\n    log \"=== AlgoFactory Startup Script ===\"\n    log \"Project Directory: $PROJECT_DIR\"\n    log \"Shared Virtual Environment: $SHARED_VENV\"\n    log \"Application File: $APP_FILE\"\n\n    # Handle command line arguments\n    case \"${1:-start}\" in\n        \"start\")\n            if is_app_running; then\n                warning \"Application is already running\"\n                exit 0\n            fi\n            setup_environment\n            install_dependencies\n            start_app\n            ;;\n        \"stop\")\n            stop_app\n            ;;\n        \"restart\")\n            stop_app\n            setup_environment\n            install_dependencies\n            start_app\n            ;;\n        \"status\")\n            if is_app_running; then\n                success \"Application is running (PID: $(cat $PID_FILE))\"\n            else\n                warning \"Application is not running\"\n            fi\n            ;;\n        \"monitor\")\n            setup_environment\n            install_dependencies\n            monitor_app\n            ;;\n        \"logs\")\n            if [ -f \"$LOG_DIR/app.log\" ]; then\n                tail -f \"$LOG_DIR/app.log\"\n            else\n                error \"Log file not found: $LOG_DIR/app.log\"\n            fi\n            ;;\n        *)\n            echo \"Usage: $0 {start|stop|restart|status|monitor|logs}\"\n            echo \"  start   - Start the application\"\n            echo \"  stop    - Stop the application\"\n            echo \"  restart - Restart the application\"\n            echo \"  status  - Check application status\"\n            echo \"  monitor - Start with continuous monitoring\"\n            echo \"  logs    - Show application logs\"\n            exit 1\n            ;;\n    esac\n}\n\n# Trap signals for graceful shutdown\ntrap 'stop_app; exit 0' SIGTERM SIGINT\n\n# Run main function\nmain \"$@\"\n"}