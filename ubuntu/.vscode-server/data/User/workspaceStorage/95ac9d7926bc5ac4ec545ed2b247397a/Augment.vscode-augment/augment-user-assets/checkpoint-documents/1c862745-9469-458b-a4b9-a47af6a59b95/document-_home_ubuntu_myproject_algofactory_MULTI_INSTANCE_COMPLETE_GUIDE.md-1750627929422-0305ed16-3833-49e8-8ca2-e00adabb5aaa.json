{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/MULTI_INSTANCE_COMPLETE_GUIDE.md"}, "modifiedCode": "# 🚀 AlgoFactory Multi-Instance Complete Implementation Guide\n\n## 📋 **Current Status**\n- ✅ Memory optimized (2GB swap added)\n- ✅ Template system created\n- ✅ Instance manager (`quick_instance.sh`) ready\n- ✅ Port allocation strategy: Instance ID = Flask Port\n- ⚠️ Need to complete Nginx automation and SSL setup\n\n## 🎯 **Architecture Overview**\n\n### **Port Allocation**\n```\nInstance 1010: Flask=1010, WebSocket=13010, ZMQ=16010\nInstance 1011: Flask=1011, WebSocket=13011, ZMQ=16011\nInstance 1012: Flask=1012, WebSocket=13012, ZMQ=16012\n```\n\n### **Domain Strategy**\n```\n1010.algofactory.in → Instance 1010\n1011.algofactory.in → Instance 1011\n1012.algofactory.in → Instance 1012\n```\n\n## 🛠️ **Step-by-Step Implementation**\n\n### **Phase 1: Basic Multi-Instance (CURRENT)**\n```bash\n# Create instances\n./quick_instance.sh create 1010\n./quick_instance.sh create 1011\n\n# Start instances (one at a time for 1GB RAM)\n./quick_instance.sh start 1010\n\n# List instances\n./quick_instance.sh list\n\n# Stop when not needed\n./quick_instance.sh stop 1010\n```\n\n### **Phase 2: Nginx Automation**\n```bash\n# Create Nginx config for instance\n./quick_instance.sh nginx 1010\n\n# This will:\n# 1. Create /etc/nginx/sites-available/1010.algofactory.in.conf\n# 2. Enable the site\n# 3. Reload Nginx\n```\n\n### **Phase 3: SSL Automation**\n```bash\n# Get SSL certificate for new subdomain\nsudo certbot --nginx -d 1010.algofactory.in --non-interactive --agree-tos --email <EMAIL>\n```\n\n### **Phase 4: DNS Setup**\nIn Hostinger DNS:\n```\nType: A\nName: 1010\nValue: **************\nTTL: 3600\n\nType: A  \nName: 1011\nValue: **************\nTTL: 3600\n```\n\n## 📁 **File Structure**\n```\n/home/<USER>/algofactory-multi/\n├── template/                    # Master template\n├── instances/\n│   ├── algofactory-1010/       # Instance 1010\n│   │   ├── .env                # Port 1010, WebSocket 13010, ZMQ 16010\n│   │   ├── start_light.sh      # Lightweight startup\n│   │   ├── db/                 # Isolated database\n│   │   └── logs/               # Instance logs\n│   └── algofactory-1011/       # Instance 1011\n└── nginx/                      # Nginx configs (future)\n```\n\n## 🔧 **Management Commands**\n\n### **Instance Management**\n```bash\n# Create new instance\n./quick_instance.sh create INSTANCE_ID\n\n# Start instance (memory-optimized)\n./quick_instance.sh start INSTANCE_ID\n\n# Stop instance\n./quick_instance.sh stop INSTANCE_ID\n\n# List all instances\n./quick_instance.sh list\n\n# Create Nginx config\n./quick_instance.sh nginx INSTANCE_ID\n```\n\n### **Memory Management**\n```bash\n# Check memory usage\n./optimize_memory.sh memory\n\n# Clean system memory\n./optimize_memory.sh clean\n\n# Full optimization\n./optimize_memory.sh optimize\n```\n\n## 🚀 **Production Deployment Steps**\n\n### **Step 1: Create Instance**\n```bash\n./quick_instance.sh create 1010\n```\n\n### **Step 2: Setup DNS**\nAdd A record in Hostinger:\n- Name: `1010`\n- Value: `**************`\n\n### **Step 3: Create Nginx Config**\n```bash\n./quick_instance.sh nginx 1010\n```\n\n### **Step 4: Get SSL Certificate**\n```bash\nsudo certbot --nginx -d 1010.algofactory.in\n```\n\n### **Step 5: Start Instance**\n```bash\n./quick_instance.sh start 1010\n```\n\n### **Step 6: Test**\n```bash\ncurl -I https://1010.algofactory.in\n```\n\n## 💡 **Memory Optimization Tips**\n\n### **For 1GB RAM Server:**\n1. **Run only 1-2 instances at a time**\n2. **Stop instances when not needed**\n3. **Use swap file (already configured)**\n4. **Monitor memory**: `watch -n 5 free -h`\n\n### **Memory Usage Per Instance:**\n- Each instance: ~150-200MB RAM\n- VS Code: ~300MB RAM\n- System: ~200MB RAM\n- **Total for 1 instance**: ~650-700MB (safe for 1GB)\n\n## 🔄 **Scaling Strategy**\n\n### **Current (1GB RAM):**\n- 1-2 instances maximum\n- Manual start/stop as needed\n- Development/testing mode\n\n### **Future (2GB+ RAM):**\n- 5-10 instances simultaneously\n- Auto-start on boot\n- Production mode\n\n## 🛡️ **Security & Isolation**\n\n### **Per Instance:**\n- ✅ Separate database files\n- ✅ Separate log files\n- ✅ Separate .env configuration\n- ✅ Isolated ports\n- ✅ Individual SSL certificates\n\n## 📊 **Monitoring**\n\n### **Instance Status**\n```bash\n./quick_instance.sh list\n```\n\n### **Memory Monitoring**\n```bash\n./optimize_memory.sh memory\n```\n\n### **Application Logs**\n```bash\ntail -f /home/<USER>/algofactory-multi/instances/algofactory-1010/logs/app.log\n```\n\n## 🚀 **Next Steps**\n\n### **Immediate (Today):**\n1. Test instance creation: `./quick_instance.sh create 1010`\n2. Setup DNS for 1010.algofactory.in\n3. Create Nginx config: `./quick_instance.sh nginx 1010`\n4. Get SSL certificate\n5. Test full workflow\n\n### **Short Term (This Week):**\n1. Create 2-3 test instances\n2. Automate SSL certificate generation\n3. Create user assignment system\n4. Build monitoring dashboard\n\n### **Long Term (Production):**\n1. Upgrade server to 2GB+ RAM\n2. Implement auto-scaling\n3. Add load balancing\n4. Create web-based management interface\n\n## 🎯 **Success Metrics**\n\n- ✅ Create instance in <30 seconds\n- ✅ Start instance in <60 seconds  \n- ✅ Each instance isolated and secure\n- ✅ SSL certificate auto-generation\n- ✅ Memory usage <80% with 1 instance\n\n## 📞 **Support Commands**\n\n```bash\n# Emergency memory cleanup\n./optimize_memory.sh clean\n\n# Check all processes\nps aux --sort=-%mem | head -10\n\n# Check disk space\ndf -h\n\n# Check swap usage\nswapon --show\n```\n\nYour multi-instance AlgoFactory system is ready! 🎉\n"}