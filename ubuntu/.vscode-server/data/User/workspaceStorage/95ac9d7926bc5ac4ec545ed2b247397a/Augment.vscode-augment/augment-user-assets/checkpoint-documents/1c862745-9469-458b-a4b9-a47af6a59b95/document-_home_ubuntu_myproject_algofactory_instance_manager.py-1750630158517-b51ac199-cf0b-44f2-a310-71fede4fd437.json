{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/instance_manager.py"}, "originalCode": "#!/usr/bin/env python3\n\"\"\"\nAlgoFactory Multi-Instance Manager\nThis script manages multiple AlgoFactory instances with unique ports and configurations.\n\"\"\"\n\nimport os\nimport sys\nimport json\nimport shutil\nimport subprocess\nimport argparse\nfrom pathlib import Path\nimport time\n\nclass AlgoFactoryInstanceManager:\n    def __init__(self):\n        self.base_dir = Path(\"/home/<USER>/algofactory-multi\")\n        self.template_dir = self.base_dir / \"template\"\n        self.instances_dir = self.base_dir / \"instances\"\n        self.nginx_dir = self.base_dir / \"nginx\"\n        self.shared_venv = Path(\"/home/<USER>/shared-venv\")\n        \n        # Port ranges - using instance_id directly as port\n        self.flask_port_base = 0  # Will use instance_id directly (1010, 1011, etc.)\n        self.websocket_port_base = 12000\n        self.zmq_port_base = 15000\n        \n        # Ensure directories exist\n        self.base_dir.mkdir(exist_ok=True)\n        self.instances_dir.mkdir(exist_ok=True)\n        self.nginx_dir.mkdir(exist_ok=True)\n        (self.nginx_dir / \"sites\").mkdir(exist_ok=True)\n    \n    def get_instance_ports(self, instance_id):\n        \"\"\"Calculate ports for an instance\"\"\"\n        return {\n            'flask': instance_id,  # Use instance_id directly (1010, 1011, etc.)\n            'websocket': self.websocket_port_base + instance_id,\n            'zmq': self.zmq_port_base + instance_id\n        }\n    \n    def get_instance_path(self, instance_id):\n        \"\"\"Get the path for an instance\"\"\"\n        return self.instances_dir / f\"algofactory-{instance_id}\"\n    \n    def instance_exists(self, instance_id):\n        \"\"\"Check if instance exists\"\"\"\n        return self.get_instance_path(instance_id).exists()\n    \n    def create_template(self):\n        \"\"\"Create template from current algofactory\"\"\"\n        current_algo = Path(\"/home/<USER>/myproject/algofactory\")\n        \n        if self.template_dir.exists():\n            print(f\"Template already exists at {self.template_dir}\")\n            return True\n            \n        if not current_algo.exists():\n            print(f\"ERROR: Current algofactory not found at {current_algo}\")\n            return False\n        \n        print(f\"Creating template from {current_algo}...\")\n        shutil.copytree(current_algo, self.template_dir, \n                       ignore=shutil.ignore_patterns('__pycache__', '*.pyc', 'logs', 'db', 'app.pid'))\n        \n        # Create .env.template\n        self.create_env_template()\n        print(f\"✅ Template created at {self.template_dir}\")\n        return True\n    \n    def create_env_template(self):\n        \"\"\"Create environment template file\"\"\"\n        env_template = \"\"\"# AlgoFactory Instance Configuration\n# This file is auto-generated for instance {INSTANCE_ID}\n\n# Instance Configuration\nINSTANCE_ID={INSTANCE_ID}\n\n# Broker Configuration\nBROKER_API_KEY = 'MZA0cLWq'\nBROKER_API_SECRET = 'XIA6RJ3HPG4ZRKKYJLIZ6ROKAM'\n\n# Market Data Configuration (Optional and Required only for XTS API Supported Brokers)\nBROKER_API_KEY_MARKET = 'YOUR_BROKER_MARKET_API_KEY'\nBROKER_API_SECRET_MARKET = 'YOUR_BROKER_MARKET_API_SECRET'\n\nREDIRECT_URL = 'https://{INSTANCE_ID}.algofactory.in/angel/callback'\n\n# Valid Brokers Configuration\nVALID_BROKERS = 'fivepaisa,fivepaisaxts,aliceblue,angel,compositedge,dhan,dhan_sandbox,firstock,flattrade,fyers,groww,iifl,kotak,jainam,jainampro,paytm,pocketful,shoonya,tradejini,upstox,wisdom,zebu,zerodha'\n\n# Security Configuration\nAPP_KEY = '3daa0403ce2501ee7432b75bf100048e3cf510d63d2754f952e93d88bf07ea84'\nAPI_KEY_PEPPER = 'a25d94718479b170c16278e321ea6c989358bf499a658fd20c90033cef8ce772'\n\n# Database Configuration\nDATABASE_URL = 'sqlite:///db/algofactory-{INSTANCE_ID}.db' \n\n# Ngrok Configuration\nNGROK_ALLOW = 'FALSE' \n\n# Host Server Configuration\nHOST_SERVER = 'https://{INSTANCE_ID}.algofactory.in'  \n\n# Flask App Configuration\nFLASK_HOST_IP='0.0.0.0'  \nFLASK_PORT='{FLASK_PORT}' \nFLASK_DEBUG='False' \nFLASK_ENV='production'\n\n# WebSocket Configuration\nWEBSOCKET_HOST='localhost'\nWEBSOCKET_PORT='{WEBSOCKET_PORT}'\nWEBSOCKET_URL='ws://localhost:{WEBSOCKET_PORT}'\n\n# ZeroMQ Configuration\nZMQ_HOST='localhost'\nZMQ_PORT='{ZMQ_PORT}'\n\n# Rate Limit Settings\nLOGIN_RATE_LIMIT_MIN = \"5 per minute\" \nLOGIN_RATE_LIMIT_HOUR = \"25 per hour\"\nAPI_RATE_LIMIT=\"10 per second\"\n\n# API Configuration\nSMART_ORDER_DELAY = '0.5'\nSESSION_EXPIRY_TIME = '03:00'\n\n# CORS Configuration\nCORS_ENABLED = 'TRUE'\nCORS_ALLOWED_ORIGINS = 'https://{INSTANCE_ID}.algofactory.in'\nCORS_ALLOWED_METHODS = 'GET,POST,DELETE,PUT,PATCH'\nCORS_ALLOWED_HEADERS = 'Content-Type,Authorization,X-Requested-With'\nCORS_EXPOSED_HEADERS = ''\nCORS_ALLOW_CREDENTIALS = 'FALSE'\nCORS_MAX_AGE = '86400'\n\n# CSP Configuration\nCSP_ENABLED = 'TRUE'\nCSP_REPORT_ONLY = 'FALSE'\nCSP_DEFAULT_SRC = \"'self'\"\nCSP_SCRIPT_SRC = \"'self' 'unsafe-inline' https://cdn.socket.io https://static.cloudflareinsights.com\"\nCSP_STYLE_SRC = \"'self' 'unsafe-inline'\"\nCSP_IMG_SRC = \"'self' data:\"\nCSP_CONNECT_SRC = \"'self' wss: ws:\"\nCSP_FONT_SRC = \"'self'\"\nCSP_OBJECT_SRC = \"'none'\"\nCSP_MEDIA_SRC = \"'self' data: https://*.amazonaws.com https://*.cloudfront.net\"\nCSP_FRAME_SRC = \"'self'\"\nCSP_FORM_ACTION = \"'self'\"\nCSP_FRAME_ANCESTORS = \"'self'\"\nCSP_BASE_URI = \"'self'\"\nCSP_UPGRADE_INSECURE_REQUESTS = 'FALSE'\nCSP_REPORT_URI = ''\n\n# CSRF Configuration\nCSRF_ENABLED = 'TRUE'\nCSRF_TIME_LIMIT = ''\n\"\"\"\n        \n        template_file = self.template_dir / \".env.template\"\n        with open(template_file, 'w') as f:\n            f.write(env_template)\n    \n    def create_instance(self, instance_id):\n        \"\"\"Create a new instance\"\"\"\n        if self.instance_exists(instance_id):\n            print(f\"❌ Instance {instance_id} already exists\")\n            return False\n        \n        if not self.template_dir.exists():\n            print(\"📋 Creating template first...\")\n            if not self.create_template():\n                return False\n        \n        instance_path = self.get_instance_path(instance_id)\n        ports = self.get_instance_ports(instance_id)\n        \n        print(f\"🚀 Creating instance {instance_id}...\")\n        print(f\"   Path: {instance_path}\")\n        print(f\"   Flask Port: {ports['flask']}\")\n        print(f\"   WebSocket Port: {ports['websocket']}\")\n        print(f\"   ZMQ Port: {ports['zmq']}\")\n        \n        # Copy template\n        shutil.copytree(self.template_dir, instance_path)\n        \n        # Create instance-specific .env file\n        self.create_instance_env(instance_id, ports)\n        \n        # Create instance-specific directories\n        (instance_path / \"db\").mkdir(exist_ok=True)\n        (instance_path / \"logs\").mkdir(exist_ok=True)\n        (instance_path / \"tmp\").mkdir(exist_ok=True)\n        \n        # Set permissions\n        os.chmod(instance_path / \"start.sh\", 0o755)\n        \n        print(f\"✅ Instance {instance_id} created successfully\")\n        print(f\"   URL: https://{instance_id}.algofactory.in\")\n        return True\n    \n    def create_instance_env(self, instance_id, ports):\n        \"\"\"Create instance-specific .env file\"\"\"\n        template_file = self.template_dir / \".env.template\"\n        instance_env_file = self.get_instance_path(instance_id) / \".env\"\n        \n        with open(template_file, 'r') as f:\n            content = f.read()\n        \n        # Replace placeholders\n        content = content.replace('{INSTANCE_ID}', str(instance_id))\n        content = content.replace('{FLASK_PORT}', str(ports['flask']))\n        content = content.replace('{WEBSOCKET_PORT}', str(ports['websocket']))\n        content = content.replace('{ZMQ_PORT}', str(ports['zmq']))\n        \n        with open(instance_env_file, 'w') as f:\n            f.write(content)\n    \n    def start_instance(self, instance_id):\n        \"\"\"Start an instance\"\"\"\n        if not self.instance_exists(instance_id):\n            print(f\"❌ Instance {instance_id} does not exist\")\n            return False\n        \n        instance_path = self.get_instance_path(instance_id)\n        print(f\"🚀 Starting instance {instance_id}...\")\n        \n        try:\n            result = subprocess.run(\n                [\"./start.sh\", \"start\"],\n                cwd=instance_path,\n                capture_output=True,\n                text=True\n            )\n            \n            if result.returncode == 0:\n                print(f\"✅ Instance {instance_id} started successfully\")\n                return True\n            else:\n                print(f\"❌ Failed to start instance {instance_id}\")\n                print(f\"Error: {result.stderr}\")\n                return False\n        except Exception as e:\n            print(f\"❌ Error starting instance {instance_id}: {e}\")\n            return False\n    \n    def stop_instance(self, instance_id):\n        \"\"\"Stop an instance\"\"\"\n        if not self.instance_exists(instance_id):\n            print(f\"❌ Instance {instance_id} does not exist\")\n            return False\n        \n        instance_path = self.get_instance_path(instance_id)\n        print(f\"🛑 Stopping instance {instance_id}...\")\n        \n        try:\n            result = subprocess.run(\n                [\"./start.sh\", \"stop\"],\n                cwd=instance_path,\n                capture_output=True,\n                text=True\n            )\n            \n            if result.returncode == 0:\n                print(f\"✅ Instance {instance_id} stopped successfully\")\n                return True\n            else:\n                print(f\"❌ Failed to stop instance {instance_id}\")\n                return False\n        except Exception as e:\n            print(f\"❌ Error stopping instance {instance_id}: {e}\")\n            return False\n    \n    def delete_instance(self, instance_id):\n        \"\"\"Delete an instance\"\"\"\n        if not self.instance_exists(instance_id):\n            print(f\"❌ Instance {instance_id} does not exist\")\n            return False\n        \n        # Stop instance first\n        self.stop_instance(instance_id)\n        \n        instance_path = self.get_instance_path(instance_id)\n        print(f\"🗑️  Deleting instance {instance_id}...\")\n        \n        try:\n            shutil.rmtree(instance_path)\n            print(f\"✅ Instance {instance_id} deleted successfully\")\n            return True\n        except Exception as e:\n            print(f\"❌ Error deleting instance {instance_id}: {e}\")\n            return False\n    \n    def list_instances(self):\n        \"\"\"List all instances\"\"\"\n        instances = []\n        for path in self.instances_dir.glob(\"algofactory-*\"):\n            if path.is_dir():\n                instance_id = int(path.name.split(\"-\")[1])\n                instances.append(instance_id)\n        \n        instances.sort()\n        \n        if not instances:\n            print(\"📭 No instances found\")\n            return []\n        \n        print(\"📋 AlgoFactory Instances:\")\n        print(\"=\" * 50)\n        for instance_id in instances:\n            ports = self.get_instance_ports(instance_id)\n            status = self.get_instance_status(instance_id)\n            print(f\"Instance {instance_id:4d} | Status: {status:8s} | Port: {ports['flask']:4d} | URL: https://{instance_id}.algofactory.in\")\n        \n        return instances\n    \n    def get_instance_status(self, instance_id):\n        \"\"\"Get status of an instance\"\"\"\n        if not self.instance_exists(instance_id):\n            return \"NOT_FOUND\"\n        \n        instance_path = self.get_instance_path(instance_id)\n        pid_file = instance_path / \"app.pid\"\n        \n        if not pid_file.exists():\n            return \"STOPPED\"\n        \n        try:\n            with open(pid_file, 'r') as f:\n                pid = int(f.read().strip())\n            \n            # Check if process is running\n            result = subprocess.run([\"ps\", \"-p\", str(pid)], capture_output=True)\n            if result.returncode == 0:\n                return \"RUNNING\"\n            else:\n                return \"STOPPED\"\n        except:\n            return \"UNKNOWN\"\n\ndef main():\n    parser = argparse.ArgumentParser(description=\"AlgoFactory Multi-Instance Manager\")\n    parser.add_argument(\"action\", choices=[\"create\", \"start\", \"stop\", \"delete\", \"list\", \"status\", \"template\"])\n    parser.add_argument(\"instance_id\", nargs=\"?\", type=int, help=\"Instance ID (e.g., 1010)\")\n    \n    args = parser.parse_args()\n    manager = AlgoFactoryInstanceManager()\n    \n    if args.action == \"template\":\n        manager.create_template()\n    elif args.action == \"list\":\n        manager.list_instances()\n    elif args.action == \"status\":\n        if args.instance_id:\n            status = manager.get_instance_status(args.instance_id)\n            print(f\"Instance {args.instance_id}: {status}\")\n        else:\n            manager.list_instances()\n    else:\n        if not args.instance_id:\n            print(\"❌ Instance ID required for this action\")\n            sys.exit(1)\n        \n        if args.action == \"create\":\n            manager.create_instance(args.instance_id)\n        elif args.action == \"start\":\n            manager.start_instance(args.instance_id)\n        elif args.action == \"stop\":\n            manager.stop_instance(args.instance_id)\n        elif args.action == \"delete\":\n            manager.delete_instance(args.instance_id)\n\nif __name__ == \"__main__\":\n    main()\n", "modifiedCode": "#!/usr/bin/env python3\n\"\"\"\nAlgoFactory Multi-Instance Manager\nThis script manages multiple AlgoFactory instances with unique ports and configurations.\n\"\"\"\n\nimport os\nimport sys\nimport json\nimport shutil\nimport subprocess\nimport argparse\nfrom pathlib import Path\nimport time\n\nclass AlgoFactoryInstanceManager:\n    def __init__(self):\n        self.base_dir = Path(\"/home/<USER>/algofactory-multi\")\n        self.template_dir = self.base_dir / \"template\"\n        self.instances_dir = self.base_dir / \"instances\"\n        self.nginx_dir = self.base_dir / \"nginx\"\n        self.shared_venv = Path(\"/home/<USER>/shared-venv\")\n        \n        # Port ranges - using instance_id directly as port\n        self.flask_port_base = 0  # Will use instance_id directly (1010, 1011, etc.)\n        self.websocket_port_base = 12000\n        self.zmq_port_base = 15000\n        \n        # Ensure directories exist\n        self.base_dir.mkdir(exist_ok=True)\n        self.instances_dir.mkdir(exist_ok=True)\n        self.nginx_dir.mkdir(exist_ok=True)\n        (self.nginx_dir / \"sites\").mkdir(exist_ok=True)\n    \n    def get_instance_ports(self, instance_id):\n        \"\"\"Calculate ports for an instance\"\"\"\n        return {\n            'flask': instance_id,  # Use instance_id directly (1010, 1011, etc.)\n            'websocket': self.websocket_port_base + instance_id,\n            'zmq': self.zmq_port_base + instance_id\n        }\n    \n    def get_instance_path(self, instance_id):\n        \"\"\"Get the path for an instance\"\"\"\n        return self.instances_dir / f\"algofactory-{instance_id}\"\n    \n    def instance_exists(self, instance_id):\n        \"\"\"Check if instance exists\"\"\"\n        return self.get_instance_path(instance_id).exists()\n    \n    def create_template(self):\n        \"\"\"Create template from current algofactory\"\"\"\n        current_algo = Path(\"/home/<USER>/myproject/algofactory\")\n        \n        if self.template_dir.exists():\n            print(f\"Template already exists at {self.template_dir}\")\n            return True\n            \n        if not current_algo.exists():\n            print(f\"ERROR: Current algofactory not found at {current_algo}\")\n            return False\n        \n        print(f\"Creating template from {current_algo}...\")\n        shutil.copytree(current_algo, self.template_dir, \n                       ignore=shutil.ignore_patterns('__pycache__', '*.pyc', 'logs', 'db', 'app.pid'))\n        \n        # Create .env.template\n        self.create_env_template()\n        print(f\"✅ Template created at {self.template_dir}\")\n        return True\n    \n    def create_env_template(self):\n        \"\"\"Create environment template file\"\"\"\n        env_template = \"\"\"# AlgoFactory Instance Configuration\n# This file is auto-generated for instance {INSTANCE_ID}\n\n# Instance Configuration\nINSTANCE_ID={INSTANCE_ID}\n\n# Broker Configuration\nBROKER_API_KEY = 'MZA0cLWq'\nBROKER_API_SECRET = 'XIA6RJ3HPG4ZRKKYJLIZ6ROKAM'\n\n# Market Data Configuration (Optional and Required only for XTS API Supported Brokers)\nBROKER_API_KEY_MARKET = 'YOUR_BROKER_MARKET_API_KEY'\nBROKER_API_SECRET_MARKET = 'YOUR_BROKER_MARKET_API_SECRET'\n\nREDIRECT_URL = 'https://{INSTANCE_ID}.algofactory.in/angel/callback'\n\n# Valid Brokers Configuration\nVALID_BROKERS = 'fivepaisa,fivepaisaxts,aliceblue,angel,compositedge,dhan,dhan_sandbox,firstock,flattrade,fyers,groww,iifl,kotak,jainam,jainampro,paytm,pocketful,shoonya,tradejini,upstox,wisdom,zebu,zerodha'\n\n# Security Configuration\nAPP_KEY = '3daa0403ce2501ee7432b75bf100048e3cf510d63d2754f952e93d88bf07ea84'\nAPI_KEY_PEPPER = 'a25d94718479b170c16278e321ea6c989358bf499a658fd20c90033cef8ce772'\n\n# Database Configuration\nDATABASE_URL = 'sqlite:///db/algofactory-{INSTANCE_ID}.db' \n\n# Ngrok Configuration\nNGROK_ALLOW = 'FALSE' \n\n# Host Server Configuration\nHOST_SERVER = 'https://{INSTANCE_ID}.algofactory.in'  \n\n# Flask App Configuration\nFLASK_HOST_IP='0.0.0.0'  \nFLASK_PORT='{FLASK_PORT}' \nFLASK_DEBUG='False' \nFLASK_ENV='production'\n\n# WebSocket Configuration\nWEBSOCKET_HOST='localhost'\nWEBSOCKET_PORT='{WEBSOCKET_PORT}'\nWEBSOCKET_URL='ws://localhost:{WEBSOCKET_PORT}'\n\n# ZeroMQ Configuration\nZMQ_HOST='localhost'\nZMQ_PORT='{ZMQ_PORT}'\n\n# Rate Limit Settings\nLOGIN_RATE_LIMIT_MIN = \"5 per minute\" \nLOGIN_RATE_LIMIT_HOUR = \"25 per hour\"\nAPI_RATE_LIMIT=\"10 per second\"\n\n# API Configuration\nSMART_ORDER_DELAY = '0.5'\nSESSION_EXPIRY_TIME = '03:00'\n\n# CORS Configuration\nCORS_ENABLED = 'TRUE'\nCORS_ALLOWED_ORIGINS = 'https://{INSTANCE_ID}.algofactory.in'\nCORS_ALLOWED_METHODS = 'GET,POST,DELETE,PUT,PATCH'\nCORS_ALLOWED_HEADERS = 'Content-Type,Authorization,X-Requested-With'\nCORS_EXPOSED_HEADERS = ''\nCORS_ALLOW_CREDENTIALS = 'FALSE'\nCORS_MAX_AGE = '86400'\n\n# CSP Configuration\nCSP_ENABLED = 'TRUE'\nCSP_REPORT_ONLY = 'FALSE'\nCSP_DEFAULT_SRC = \"'self'\"\nCSP_SCRIPT_SRC = \"'self' 'unsafe-inline' https://cdn.socket.io https://static.cloudflareinsights.com\"\nCSP_STYLE_SRC = \"'self' 'unsafe-inline'\"\nCSP_IMG_SRC = \"'self' data:\"\nCSP_CONNECT_SRC = \"'self' wss: ws:\"\nCSP_FONT_SRC = \"'self'\"\nCSP_OBJECT_SRC = \"'none'\"\nCSP_MEDIA_SRC = \"'self' data: https://*.amazonaws.com https://*.cloudfront.net\"\nCSP_FRAME_SRC = \"'self'\"\nCSP_FORM_ACTION = \"'self'\"\nCSP_FRAME_ANCESTORS = \"'self'\"\nCSP_BASE_URI = \"'self'\"\nCSP_UPGRADE_INSECURE_REQUESTS = 'FALSE'\nCSP_REPORT_URI = ''\n\n# CSRF Configuration\nCSRF_ENABLED = 'TRUE'\nCSRF_TIME_LIMIT = ''\n\"\"\"\n        \n        template_file = self.template_dir / \".env.template\"\n        with open(template_file, 'w') as f:\n            f.write(env_template)\n    \n    def create_instance(self, instance_id):\n        \"\"\"Create a new instance\"\"\"\n        if self.instance_exists(instance_id):\n            print(f\"❌ Instance {instance_id} already exists\")\n            return False\n        \n        if not self.template_dir.exists():\n            print(\"📋 Creating template first...\")\n            if not self.create_template():\n                return False\n        \n        instance_path = self.get_instance_path(instance_id)\n        ports = self.get_instance_ports(instance_id)\n        \n        print(f\"🚀 Creating instance {instance_id}...\")\n        print(f\"   Path: {instance_path}\")\n        print(f\"   Flask Port: {ports['flask']}\")\n        print(f\"   WebSocket Port: {ports['websocket']}\")\n        print(f\"   ZMQ Port: {ports['zmq']}\")\n        \n        # Copy template\n        shutil.copytree(self.template_dir, instance_path)\n        \n        # Create instance-specific .env file\n        self.create_instance_env(instance_id, ports)\n        \n        # Create instance-specific directories\n        (instance_path / \"db\").mkdir(exist_ok=True)\n        (instance_path / \"logs\").mkdir(exist_ok=True)\n        (instance_path / \"tmp\").mkdir(exist_ok=True)\n        \n        # Set permissions\n        os.chmod(instance_path / \"start.sh\", 0o755)\n        \n        print(f\"✅ Instance {instance_id} created successfully\")\n        print(f\"   URL: https://{instance_id}.algofactory.in\")\n        return True\n    \n    def create_instance_env(self, instance_id, ports):\n        \"\"\"Create instance-specific .env file\"\"\"\n        template_file = self.template_dir / \".env.template\"\n        instance_env_file = self.get_instance_path(instance_id) / \".env\"\n        \n        with open(template_file, 'r') as f:\n            content = f.read()\n        \n        # Replace placeholders\n        content = content.replace('{INSTANCE_ID}', str(instance_id))\n        content = content.replace('{FLASK_PORT}', str(ports['flask']))\n        content = content.replace('{WEBSOCKET_PORT}', str(ports['websocket']))\n        content = content.replace('{ZMQ_PORT}', str(ports['zmq']))\n        \n        with open(instance_env_file, 'w') as f:\n            f.write(content)\n    \n    def start_instance(self, instance_id):\n        \"\"\"Start an instance\"\"\"\n        if not self.instance_exists(instance_id):\n            print(f\"❌ Instance {instance_id} does not exist\")\n            return False\n        \n        instance_path = self.get_instance_path(instance_id)\n        print(f\"🚀 Starting instance {instance_id}...\")\n        \n        try:\n            result = subprocess.run(\n                [\"./start.sh\", \"start\"],\n                cwd=instance_path,\n                capture_output=True,\n                text=True\n            )\n            \n            if result.returncode == 0:\n                print(f\"✅ Instance {instance_id} started successfully\")\n                return True\n            else:\n                print(f\"❌ Failed to start instance {instance_id}\")\n                print(f\"Error: {result.stderr}\")\n                return False\n        except Exception as e:\n            print(f\"❌ Error starting instance {instance_id}: {e}\")\n            return False\n    \n    def stop_instance(self, instance_id):\n        \"\"\"Stop an instance\"\"\"\n        if not self.instance_exists(instance_id):\n            print(f\"❌ Instance {instance_id} does not exist\")\n            return False\n        \n        instance_path = self.get_instance_path(instance_id)\n        print(f\"🛑 Stopping instance {instance_id}...\")\n        \n        try:\n            result = subprocess.run(\n                [\"./start.sh\", \"stop\"],\n                cwd=instance_path,\n                capture_output=True,\n                text=True\n            )\n            \n            if result.returncode == 0:\n                print(f\"✅ Instance {instance_id} stopped successfully\")\n                return True\n            else:\n                print(f\"❌ Failed to stop instance {instance_id}\")\n                return False\n        except Exception as e:\n            print(f\"❌ Error stopping instance {instance_id}: {e}\")\n            return False\n    \n    def delete_instance(self, instance_id):\n        \"\"\"Delete an instance\"\"\"\n        if not self.instance_exists(instance_id):\n            print(f\"❌ Instance {instance_id} does not exist\")\n            return False\n        \n        # Stop instance first\n        self.stop_instance(instance_id)\n        \n        instance_path = self.get_instance_path(instance_id)\n        print(f\"🗑️  Deleting instance {instance_id}...\")\n        \n        try:\n            shutil.rmtree(instance_path)\n            print(f\"✅ Instance {instance_id} deleted successfully\")\n            return True\n        except Exception as e:\n            print(f\"❌ Error deleting instance {instance_id}: {e}\")\n            return False\n    \n    def list_instances(self):\n        \"\"\"List all instances\"\"\"\n        instances = []\n        for path in self.instances_dir.glob(\"algofactory-*\"):\n            if path.is_dir():\n                instance_id = int(path.name.split(\"-\")[1])\n                instances.append(instance_id)\n        \n        instances.sort()\n        \n        if not instances:\n            print(\"📭 No instances found\")\n            return []\n        \n        print(\"📋 AlgoFactory Instances:\")\n        print(\"=\" * 50)\n        for instance_id in instances:\n            ports = self.get_instance_ports(instance_id)\n            status = self.get_instance_status(instance_id)\n            print(f\"Instance {instance_id:4d} | Status: {status:8s} | Port: {ports['flask']:4d} | URL: https://{instance_id}.algofactory.in\")\n        \n        return instances\n    \n    def get_instance_status(self, instance_id):\n        \"\"\"Get status of an instance\"\"\"\n        if not self.instance_exists(instance_id):\n            return \"NOT_FOUND\"\n        \n        instance_path = self.get_instance_path(instance_id)\n        pid_file = instance_path / \"app.pid\"\n        \n        if not pid_file.exists():\n            return \"STOPPED\"\n        \n        try:\n            with open(pid_file, 'r') as f:\n                pid = int(f.read().strip())\n            \n            # Check if process is running\n            result = subprocess.run([\"ps\", \"-p\", str(pid)], capture_output=True)\n            if result.returncode == 0:\n                return \"RUNNING\"\n            else:\n                return \"STOPPED\"\n        except:\n            return \"UNKNOWN\"\n\ndef main():\n    parser = argparse.ArgumentParser(description=\"AlgoFactory Multi-Instance Manager\")\n    parser.add_argument(\"action\", choices=[\"create\", \"start\", \"stop\", \"delete\", \"list\", \"status\", \"template\"])\n    parser.add_argument(\"instance_id\", nargs=\"?\", type=int, help=\"Instance ID (e.g., 1010)\")\n    \n    args = parser.parse_args()\n    manager = AlgoFactoryInstanceManager()\n    \n    if args.action == \"template\":\n        manager.create_template()\n    elif args.action == \"list\":\n        manager.list_instances()\n    elif args.action == \"status\":\n        if args.instance_id:\n            status = manager.get_instance_status(args.instance_id)\n            print(f\"Instance {args.instance_id}: {status}\")\n        else:\n            manager.list_instances()\n    else:\n        if not args.instance_id:\n            print(\"❌ Instance ID required for this action\")\n            sys.exit(1)\n        \n        if args.action == \"create\":\n            manager.create_instance(args.instance_id)\n        elif args.action == \"start\":\n            manager.start_instance(args.instance_id)\n        elif args.action == \"stop\":\n            manager.stop_instance(args.instance_id)\n        elif args.action == \"delete\":\n            manager.delete_instance(args.instance_id)\n\nif __name__ == \"__main__\":\n    main()\n"}