{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/nginx_bulk_setup.sh"}, "modifiedCode": "#!/bin/bash\n\n# Nginx Bulk Setup Script for AlgoFactory\n# Sets up multiple subdomains with SSL certificates\n\nset -e\n\necho \"🌐 AlgoFactory Nginx Bulk Setup\"\necho \"===============================\"\n\n# Check if running as root\nif [ \"$EUID\" -ne 0 ]; then\n    echo \"❌ Please run as root (use sudo)\"\n    exit 1\nfi\n\n# Function to setup domains 1010-1020\nsetup_1010_to_1020() {\n    echo \"🚀 Setting up domains 1010.algofactory.in to 1020.algofactory.in\"\n    echo \"================================================================\"\n    \n    # First, create all Nginx configurations without SSL\n    echo \"📝 Step 1: Creating Nginx configurations...\"\n    python3 nginx_manager.py --bulk 1010 1020\n    \n    echo \"\"\n    echo \"⏳ Waiting 5 seconds before SSL installation...\"\n    sleep 5\n    \n    # Then install SSL certificates\n    echo \"🔒 Step 2: Installing SSL certificates...\"\n    python3 nginx_manager.py --bulk-ssl 1010 1020\n    \n    echo \"\"\n    echo \"✅ Bulk setup completed!\"\n}\n\n# Function to setup a custom range\nsetup_custom_range() {\n    local start=$1\n    local end=$2\n    \n    echo \"🚀 Setting up domains ${start}.algofactory.in to ${end}.algofactory.in\"\n    echo \"================================================================\"\n    \n    # Create configurations\n    echo \"📝 Step 1: Creating Nginx configurations...\"\n    python3 nginx_manager.py --bulk \"$start\" \"$end\"\n    \n    echo \"\"\n    echo \"⏳ Waiting 5 seconds before SSL installation...\"\n    sleep 5\n    \n    # Install SSL\n    echo \"🔒 Step 2: Installing SSL certificates...\"\n    python3 nginx_manager.py --bulk-ssl \"$start\" \"$end\"\n    \n    echo \"\"\n    echo \"✅ Custom range setup completed!\"\n}\n\n# Function to show current status\nshow_status() {\n    echo \"📊 Current Nginx Configuration Status\"\n    echo \"=====================================\"\n    python3 nginx_manager.py --list\n    \n    echo \"\"\n    echo \"🔍 Active Nginx Sites:\"\n    ls -la /etc/nginx/sites-enabled/ | grep algofactory || echo \"   No AlgoFactory sites enabled\"\n    \n    echo \"\"\n    echo \"🔒 SSL Certificates:\"\n    ls -la /etc/letsencrypt/live/ | grep algofactory || echo \"   No SSL certificates found\"\n}\n\n# Function to test a specific domain\ntest_domain() {\n    local domain=$1\n    \n    echo \"🧪 Testing domain: $domain\"\n    echo \"==========================\"\n    \n    # Test HTTP\n    echo \"📡 Testing HTTP...\"\n    if curl -I \"http://$domain\" 2>/dev/null | head -1 | grep -q \"301\\|200\"; then\n        echo \"✅ HTTP working\"\n    else\n        echo \"❌ HTTP not working\"\n    fi\n    \n    # Test HTTPS\n    echo \"🔒 Testing HTTPS...\"\n    if curl -I \"https://$domain\" 2>/dev/null | head -1 | grep -q \"200\"; then\n        echo \"✅ HTTPS working\"\n    else\n        echo \"❌ HTTPS not working\"\n    fi\n    \n    # Test SSL certificate\n    echo \"📜 Checking SSL certificate...\"\n    if openssl s_client -connect \"$domain:443\" -servername \"$domain\" </dev/null 2>/dev/null | openssl x509 -noout -dates 2>/dev/null; then\n        echo \"✅ SSL certificate valid\"\n    else\n        echo \"❌ SSL certificate issues\"\n    fi\n}\n\n# Function to create a monitoring dashboard\ncreate_monitoring_dashboard() {\n    echo \"📊 Creating Nginx monitoring dashboard...\"\n    \n    cat > /home/<USER>/myproject/algofactory/nginx_dashboard.html << 'EOF'\n<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>AlgoFactory Nginx Dashboard</title>\n    <style>\n        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }\n        .container { max-width: 1200px; margin: 0 auto; }\n        .card { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }\n        .status-good { color: #28a745; }\n        .status-bad { color: #dc3545; }\n        .status-warning { color: #ffc107; }\n        table { width: 100%; border-collapse: collapse; }\n        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }\n        th { background-color: #f8f9fa; }\n        .refresh-btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <h1>🌐 AlgoFactory Nginx Dashboard</h1>\n        \n        <div class=\"card\">\n            <h2>📊 System Status</h2>\n            <p><strong>Last Updated:</strong> <span id=\"lastUpdate\"></span></p>\n            <button class=\"refresh-btn\" onclick=\"location.reload()\">🔄 Refresh</button>\n        </div>\n        \n        <div class=\"card\">\n            <h2>🔗 Configured Domains</h2>\n            <table>\n                <thead>\n                    <tr>\n                        <th>Domain</th>\n                        <th>HTTP Status</th>\n                        <th>HTTPS Status</th>\n                        <th>SSL Certificate</th>\n                        <th>Actions</th>\n                    </tr>\n                </thead>\n                <tbody id=\"domainsTable\">\n                    <!-- Domains will be populated here -->\n                </tbody>\n            </table>\n        </div>\n        \n        <div class=\"card\">\n            <h2>📝 Quick Commands</h2>\n            <pre>\n# List all configured sites\nsudo python3 nginx_manager.py --list\n\n# Setup new subdomain\nsudo python3 nginx_manager.py --setup 8013 8013\n\n# Bulk setup range\nsudo python3 nginx_manager.py --bulk 1010 1020\n\n# Install SSL for specific domain\nsudo python3 nginx_manager.py --ssl 8013\n\n# Test Nginx configuration\nsudo nginx -t\n\n# Reload Nginx\nsudo systemctl reload nginx\n            </pre>\n        </div>\n    </div>\n    \n    <script>\n        document.getElementById('lastUpdate').textContent = new Date().toLocaleString();\n        \n        // Sample domains - in real implementation, this would be populated from server\n        const domains = [\n            { name: '8010.algofactory.in', http: 'good', https: 'good', ssl: 'good' },\n            { name: '8011.algofactory.in', http: 'good', https: 'good', ssl: 'good' },\n            { name: '8012.algofactory.in', http: 'good', https: 'good', ssl: 'good' }\n        ];\n        \n        function populateTable() {\n            const tbody = document.getElementById('domainsTable');\n            tbody.innerHTML = '';\n            \n            domains.forEach(domain => {\n                const row = tbody.insertRow();\n                row.innerHTML = `\n                    <td><a href=\"https://${domain.name}\" target=\"_blank\">${domain.name}</a></td>\n                    <td class=\"status-${domain.http}\">✅ Working</td>\n                    <td class=\"status-${domain.https}\">✅ Working</td>\n                    <td class=\"status-${domain.ssl}\">✅ Valid</td>\n                    <td><button onclick=\"testDomain('${domain.name}')\">🧪 Test</button></td>\n                `;\n            });\n        }\n        \n        function testDomain(domain) {\n            alert(`Testing ${domain}... (This would run actual tests in a real implementation)`);\n        }\n        \n        populateTable();\n    </script>\n</body>\n</html>\nEOF\n    \n    echo \"✅ Monitoring dashboard created: /home/<USER>/myproject/algofactory/nginx_dashboard.html\"\n}\n\n# Main script logic\ncase \"${1:-}\" in\n    \"1010-1020\")\n        setup_1010_to_1020\n        ;;\n    \"custom\")\n        if [ -z \"${2:-}\" ] || [ -z \"${3:-}\" ]; then\n            echo \"Usage: $0 custom <start> <end>\"\n            echo \"Example: $0 custom 1010 1020\"\n            exit 1\n        fi\n        setup_custom_range \"$2\" \"$3\"\n        ;;\n    \"status\")\n        show_status\n        ;;\n    \"test\")\n        if [ -z \"${2:-}\" ]; then\n            echo \"Usage: $0 test <domain>\"\n            echo \"Example: $0 test 8010.algofactory.in\"\n            exit 1\n        fi\n        test_domain \"$2\"\n        ;;\n    \"dashboard\")\n        create_monitoring_dashboard\n        ;;\n    *)\n        echo \"AlgoFactory Nginx Bulk Setup Script\"\n        echo \"===================================\"\n        echo \"\"\n        echo \"Usage: $0 <command> [options]\"\n        echo \"\"\n        echo \"Commands:\"\n        echo \"  1010-1020           Setup domains 1010.algofactory.in to 1020.algofactory.in\"\n        echo \"  custom <start> <end> Setup custom range of domains\"\n        echo \"  status              Show current configuration status\"\n        echo \"  test <domain>       Test specific domain\"\n        echo \"  dashboard           Create monitoring dashboard\"\n        echo \"\"\n        echo \"Examples:\"\n        echo \"  sudo $0 1010-1020\"\n        echo \"  sudo $0 custom 1010 1015\"\n        echo \"  sudo $0 status\"\n        echo \"  sudo $0 test 8010.algofactory.in\"\n        echo \"  sudo $0 dashboard\"\n        echo \"\"\n        echo \"📋 Current Status:\"\n        python3 nginx_manager.py --list\n        ;;\nesac\n"}