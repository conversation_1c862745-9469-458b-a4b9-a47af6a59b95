{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}, "originalCode": "#!/bin/bash\n\n# Quick Instance Manager for Low Memory Environment\n# Simplified version for 1GB RAM server\n\nset -e\n\n# Colors\nGREEN='\\033[0;32m'\nBLUE='\\033[0;34m'\nRED='\\033[0;31m'\nNC='\\033[0m'\n\nBASE_DIR=\"/home/<USER>/algofactory-multi\"\nTEMPLATE_DIR=\"$BASE_DIR/template\"\nINSTANCES_DIR=\"$BASE_DIR/instances\"\nSHARED_VENV=\"/home/<USER>/shared-venv\"\n\nlog() {\n    echo -e \"${BLUE}[$(date '+%H:%M:%S')]${NC} $1\"\n}\n\nsuccess() {\n    echo -e \"${GREEN}✅${NC} $1\"\n}\n\nerror() {\n    echo -e \"${RED}❌${NC} $1\" >&2\n}\n\n# Create template if not exists\ncreate_template() {\n    if [ ! -d \"$TEMPLATE_DIR\" ]; then\n        log \"Creating template...\"\n        mkdir -p \"$BASE_DIR\"\n        cp -r /home/<USER>/myproject/algofactory \"$TEMPLATE_DIR\"\n        rm -rf \"$TEMPLATE_DIR\"/{logs,db,app.pid,__pycache__}\n        success \"Template created\"\n    fi\n}\n\n# Create instance\ncreate_instance() {\n    local instance_id=$1\n    local instance_dir=\"$INSTANCES_DIR/algofactory-$instance_id\"\n    \n    if [ -d \"$instance_dir\" ]; then\n        error \"Instance $instance_id already exists\"\n        return 1\n    fi\n    \n    create_template\n    \n    log \"Creating instance $instance_id...\"\n    mkdir -p \"$instance_dir\"\n    cp -r \"$TEMPLATE_DIR\"/* \"$instance_dir/\"\n    \n    # Create directories\n    mkdir -p \"$instance_dir\"/{db,logs,tmp}\n    \n    # Create .env file\n    cat > \"$instance_dir/.env\" << EOF\n# Instance $instance_id Configuration\nINSTANCE_ID=$instance_id\nBROKER_API_KEY=MZA0cLWq\nBROKER_API_SECRET=XIA6RJ3HPG4ZRKKYJLIZ6ROKAM\nREDIRECT_URL=https://$instance_id.algofactory.in/angel/callback\nVALID_BROKERS=fivepaisa,fivepaisaxts,aliceblue,angel,compositedge,dhan,dhan_sandbox,firstock,flattrade,fyers,groww,iifl,kotak,jainam,jainampro,paytm,pocketful,shoonya,tradejini,upstox,wisdom,zebu,zerodha\nAPP_KEY=3daa0403ce2501ee7432b75bf100048e3cf510d63d2754f952e93d88bf07ea84\nAPI_KEY_PEPPER=a25d94718479b170c16278e321ea6c989358bf499a658fd20c90033cef8ce772\nDATABASE_URL=sqlite:///db/algofactory-$instance_id.db\nNGROK_ALLOW=FALSE\nHOST_SERVER=https://$instance_id.algofactory.in\nFLASK_HOST_IP=0.0.0.0\nFLASK_PORT=$instance_id\nFLASK_DEBUG=False\nFLASK_ENV=production\nWEBSOCKET_HOST=localhost\nWEBSOCKET_PORT=$((12000 + instance_id))\nWEBSOCKET_URL=ws://localhost:$((12000 + instance_id))\nZMQ_HOST=localhost\nZMQ_PORT=$((15000 + instance_id))\nLOGIN_RATE_LIMIT_MIN=\"5 per minute\"\nLOGIN_RATE_LIMIT_HOUR=\"25 per hour\"\nAPI_RATE_LIMIT=\"10 per second\"\nSMART_ORDER_DELAY=0.5\nSESSION_EXPIRY_TIME=03:00\nCORS_ENABLED=TRUE\nCORS_ALLOWED_ORIGINS=https://$instance_id.algofactory.in\nCORS_ALLOWED_METHODS=GET,POST,DELETE,PUT,PATCH\nCORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Requested-With\nCORS_EXPOSED_HEADERS=\nCORS_ALLOW_CREDENTIALS=FALSE\nCORS_MAX_AGE=86400\nCSP_ENABLED=TRUE\nCSP_REPORT_ONLY=FALSE\nCSP_DEFAULT_SRC=\"'self'\"\nCSP_SCRIPT_SRC=\"'self' 'unsafe-inline' https://cdn.socket.io https://static.cloudflareinsights.com\"\nCSP_STYLE_SRC=\"'self' 'unsafe-inline'\"\nCSP_IMG_SRC=\"'self' data:\"\nCSP_CONNECT_SRC=\"'self' wss: ws:\"\nCSP_FONT_SRC=\"'self'\"\nCSP_OBJECT_SRC=\"'none'\"\nCSP_MEDIA_SRC=\"'self' data: https://*.amazonaws.com https://*.cloudfront.net\"\nCSP_FRAME_SRC=\"'self'\"\nCSP_FORM_ACTION=\"'self'\"\nCSP_FRAME_ANCESTORS=\"'self'\"\nCSP_BASE_URI=\"'self'\"\nCSP_UPGRADE_INSECURE_REQUESTS=FALSE\nCSP_REPORT_URI=\"\"\nCSRF_ENABLED=TRUE\nCSRF_TIME_LIMIT=\"\"\nEOF\n    \n    # Create lightweight start script\n    cat > \"$instance_dir/start_light.sh\" << 'EOF'\n#!/bin/bash\nset -e\n\nINSTANCE_DIR=\"$(cd \"$(dirname \"${BASH_SOURCE[0]}\")\" && pwd)\"\nINSTANCE_ID=$(basename \"$INSTANCE_DIR\" | cut -d'-' -f2)\nSHARED_VENV=\"/home/<USER>/shared-venv\"\nPID_FILE=\"$INSTANCE_DIR/app.pid\"\n\nsource \"$INSTANCE_DIR/.env\"\nsource \"$SHARED_VENV/bin/activate\"\n\ncase \"${1:-start}\" in\n    \"start\")\n        if [ -f \"$PID_FILE\" ] && ps -p \"$(cat \"$PID_FILE\")\" > /dev/null 2>&1; then\n            echo \"Instance $INSTANCE_ID already running\"\n            exit 0\n        fi\n        \n        cd \"$INSTANCE_DIR\"\n        nohup \"$SHARED_VENV/bin/gunicorn\" \\\n            --bind=\"0.0.0.0:$FLASK_PORT\" \\\n            --workers=1 \\\n            --worker-class=eventlet \\\n            --worker-connections=50 \\\n            --max-requests=200 \\\n            --timeout=30 \\\n            --preload \\\n            --log-level=warning \\\n            --access-logfile=\"logs/access.log\" \\\n            --error-logfile=\"logs/error.log\" \\\n            app:app > \"logs/app.log\" 2>&1 &\n        \n        echo $! > \"$PID_FILE\"\n        echo \"Instance $INSTANCE_ID started on port $FLASK_PORT\"\n        ;;\n    \"stop\")\n        if [ -f \"$PID_FILE\" ]; then\n            kill \"$(cat \"$PID_FILE\")\" 2>/dev/null || true\n            rm -f \"$PID_FILE\"\n            echo \"Instance $INSTANCE_ID stopped\"\n        fi\n        ;;\n    \"status\")\n        if [ -f \"$PID_FILE\" ] && ps -p \"$(cat \"$PID_FILE\")\" > /dev/null 2>&1; then\n            echo \"Instance $INSTANCE_ID: RUNNING (PID: $(cat \"$PID_FILE\"))\"\n        else\n            echo \"Instance $INSTANCE_ID: STOPPED\"\n        fi\n        ;;\nesac\nEOF\n    \n    chmod +x \"$instance_dir/start_light.sh\"\n    success \"Instance $instance_id created at port $instance_id\"\n}\n\n# Start instance\nstart_instance() {\n    local instance_id=$1\n    local instance_dir=\"$INSTANCES_DIR/algofactory-$instance_id\"\n    \n    if [ ! -d \"$instance_dir\" ]; then\n        error \"Instance $instance_id does not exist\"\n        return 1\n    fi\n    \n    cd \"$instance_dir\"\n    ./start_light.sh start\n}\n\n# Stop instance\nstop_instance() {\n    local instance_id=$1\n    local instance_dir=\"$INSTANCES_DIR/algofactory-$instance_id\"\n    \n    if [ ! -d \"$instance_dir\" ]; then\n        error \"Instance $instance_id does not exist\"\n        return 1\n    fi\n    \n    cd \"$instance_dir\"\n    ./start_light.sh stop\n}\n\n# List instances\nlist_instances() {\n    echo \"AlgoFactory Instances:\"\n    echo \"=====================\"\n    \n    if [ ! -d \"$INSTANCES_DIR\" ]; then\n        echo \"No instances found\"\n        return 0\n    fi\n    \n    for dir in \"$INSTANCES_DIR\"/algofactory-*; do\n        if [ -d \"$dir\" ]; then\n            instance_id=$(basename \"$dir\" | cut -d'-' -f2)\n            cd \"$dir\"\n            status=$(./start_light.sh status | cut -d':' -f2 | xargs)\n            echo \"Instance $instance_id: $status | URL: https://$instance_id.algofactory.in\"\n        fi\n    done\n}\n\n# Create Nginx config for instance\ncreate_nginx_config() {\n    local instance_id=$1\n    \n    cat > \"/tmp/$instance_id.algofactory.in.conf\" << EOF\nserver {\n    listen 80;\n    server_name $instance_id.algofactory.in;\n    return 301 https://\\$server_name\\$request_uri;\n}\n\nserver {\n    listen 443 ssl http2;\n    server_name $instance_id.algofactory.in;\n    \n    ssl_certificate /etc/letsencrypt/live/$instance_id.algofactory.in/fullchain.pem;\n    ssl_certificate_key /etc/letsencrypt/live/$instance_id.algofactory.in/privkey.pem;\n    \n    location / {\n        proxy_pass http://127.0.0.1:$instance_id;\n        proxy_set_header Host \\$host;\n        proxy_set_header X-Real-IP \\$remote_addr;\n        proxy_set_header X-Forwarded-For \\$proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto \\$scheme;\n    }\n}\nEOF\n    \n    sudo mv \"/tmp/$instance_id.algofactory.in.conf\" \"/etc/nginx/sites-available/\"\n    sudo ln -sf \"/etc/nginx/sites-available/$instance_id.algofactory.in.conf\" \"/etc/nginx/sites-enabled/\"\n    sudo nginx -t && sudo systemctl reload nginx\n    \n    success \"Nginx config created for $instance_id.algofactory.in\"\n}\n\n# Main function\nmain() {\n    case \"${1:-help}\" in\n        \"create\")\n            if [ -z \"$2\" ]; then\n                error \"Usage: $0 create INSTANCE_ID\"\n                exit 1\n            fi\n            create_instance \"$2\"\n            ;;\n        \"start\")\n            if [ -z \"$2\" ]; then\n                error \"Usage: $0 start INSTANCE_ID\"\n                exit 1\n            fi\n            start_instance \"$2\"\n            ;;\n        \"stop\")\n            if [ -z \"$2\" ]; then\n                error \"Usage: $0 stop INSTANCE_ID\"\n                exit 1\n            fi\n            stop_instance \"$2\"\n            ;;\n        \"list\")\n            list_instances\n            ;;\n        \"nginx\")\n            if [ -z \"$2\" ]; then\n                error \"Usage: $0 nginx INSTANCE_ID\"\n                exit 1\n            fi\n            create_nginx_config \"$2\"\n            ;;\n        \"help\"|*)\n            echo \"Quick Instance Manager for AlgoFactory\"\n            echo \"\"\n            echo \"Usage: $0 COMMAND [INSTANCE_ID]\"\n            echo \"\"\n            echo \"Commands:\"\n            echo \"  create ID  - Create new instance\"\n            echo \"  start ID   - Start instance\"\n            echo \"  stop ID    - Stop instance\"\n            echo \"  list       - List all instances\"\n            echo \"  nginx ID   - Create Nginx config for instance\"\n            echo \"\"\n            echo \"Examples:\"\n            echo \"  $0 create 1010\"\n            echo \"  $0 start 1010\"\n            echo \"  $0 nginx 1010\"\n            ;;\n    esac\n}\n\nmain \"$@\"\n", "modifiedCode": "#!/bin/bash\n\n# Quick Instance Manager for Low Memory Environment\n# Simplified version for 1GB RAM server\n\nset -e\n\n# Colors\nGREEN='\\033[0;32m'\nBLUE='\\033[0;34m'\nRED='\\033[0;31m'\nNC='\\033[0m'\n\nBASE_DIR=\"/home/<USER>/algofactory-multi\"\nTEMPLATE_DIR=\"$BASE_DIR/template\"\nINSTANCES_DIR=\"$BASE_DIR/instances\"\nSHARED_VENV=\"/home/<USER>/shared-venv\"\n\nlog() {\n    echo -e \"${BLUE}[$(date '+%H:%M:%S')]${NC} $1\"\n}\n\nsuccess() {\n    echo -e \"${GREEN}✅${NC} $1\"\n}\n\nerror() {\n    echo -e \"${RED}❌${NC} $1\" >&2\n}\n\n# Create template if not exists\ncreate_template() {\n    if [ ! -d \"$TEMPLATE_DIR\" ]; then\n        log \"Creating template...\"\n        mkdir -p \"$BASE_DIR\"\n        cp -r /home/<USER>/myproject/algofactory \"$TEMPLATE_DIR\"\n        rm -rf \"$TEMPLATE_DIR\"/{logs,db,app.pid,__pycache__}\n        success \"Template created\"\n    fi\n}\n\n# Create instance\ncreate_instance() {\n    local instance_id=$1\n    local instance_dir=\"$INSTANCES_DIR/algofactory-$instance_id\"\n    \n    if [ -d \"$instance_dir\" ]; then\n        error \"Instance $instance_id already exists\"\n        return 1\n    fi\n    \n    create_template\n    \n    log \"Creating instance $instance_id...\"\n    mkdir -p \"$instance_dir\"\n    cp -r \"$TEMPLATE_DIR\"/* \"$instance_dir/\"\n    \n    # Create directories\n    mkdir -p \"$instance_dir\"/{db,logs,tmp}\n    \n    # Create .env file\n    cat > \"$instance_dir/.env\" << EOF\n# Instance $instance_id Configuration\nINSTANCE_ID=$instance_id\nBROKER_API_KEY=MZA0cLWq\nBROKER_API_SECRET=XIA6RJ3HPG4ZRKKYJLIZ6ROKAM\nREDIRECT_URL=https://$instance_id.algofactory.in/angel/callback\nVALID_BROKERS=fivepaisa,fivepaisaxts,aliceblue,angel,compositedge,dhan,dhan_sandbox,firstock,flattrade,fyers,groww,iifl,kotak,jainam,jainampro,paytm,pocketful,shoonya,tradejini,upstox,wisdom,zebu,zerodha\nAPP_KEY=3daa0403ce2501ee7432b75bf100048e3cf510d63d2754f952e93d88bf07ea84\nAPI_KEY_PEPPER=a25d94718479b170c16278e321ea6c989358bf499a658fd20c90033cef8ce772\nDATABASE_URL=sqlite:///db/algofactory-$instance_id.db\nNGROK_ALLOW=FALSE\nHOST_SERVER=https://$instance_id.algofactory.in\nFLASK_HOST_IP=0.0.0.0\nFLASK_PORT=$instance_id\nFLASK_DEBUG=False\nFLASK_ENV=production\nWEBSOCKET_HOST=localhost\nWEBSOCKET_PORT=$((12000 + instance_id))\nWEBSOCKET_URL=ws://localhost:$((12000 + instance_id))\nZMQ_HOST=localhost\nZMQ_PORT=$((15000 + instance_id))\nLOGIN_RATE_LIMIT_MIN=\"5 per minute\"\nLOGIN_RATE_LIMIT_HOUR=\"25 per hour\"\nAPI_RATE_LIMIT=\"10 per second\"\nSMART_ORDER_DELAY=0.5\nSESSION_EXPIRY_TIME=03:00\nCORS_ENABLED=TRUE\nCORS_ALLOWED_ORIGINS=https://$instance_id.algofactory.in\nCORS_ALLOWED_METHODS=GET,POST,DELETE,PUT,PATCH\nCORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Requested-With\nCORS_EXPOSED_HEADERS=\nCORS_ALLOW_CREDENTIALS=FALSE\nCORS_MAX_AGE=86400\nCSP_ENABLED=TRUE\nCSP_REPORT_ONLY=FALSE\nCSP_DEFAULT_SRC=\"'self'\"\nCSP_SCRIPT_SRC=\"'self' 'unsafe-inline' https://cdn.socket.io https://static.cloudflareinsights.com\"\nCSP_STYLE_SRC=\"'self' 'unsafe-inline'\"\nCSP_IMG_SRC=\"'self' data:\"\nCSP_CONNECT_SRC=\"'self' wss: ws:\"\nCSP_FONT_SRC=\"'self'\"\nCSP_OBJECT_SRC=\"'none'\"\nCSP_MEDIA_SRC=\"'self' data: https://*.amazonaws.com https://*.cloudfront.net\"\nCSP_FRAME_SRC=\"'self'\"\nCSP_FORM_ACTION=\"'self'\"\nCSP_FRAME_ANCESTORS=\"'self'\"\nCSP_BASE_URI=\"'self'\"\nCSP_UPGRADE_INSECURE_REQUESTS=FALSE\nCSP_REPORT_URI=\"\"\nCSRF_ENABLED=TRUE\nCSRF_TIME_LIMIT=\"\"\nEOF\n    \n    # Create lightweight start script\n    cat > \"$instance_dir/start_light.sh\" << 'EOF'\n#!/bin/bash\nset -e\n\nINSTANCE_DIR=\"$(cd \"$(dirname \"${BASH_SOURCE[0]}\")\" && pwd)\"\nINSTANCE_ID=$(basename \"$INSTANCE_DIR\" | cut -d'-' -f2)\nSHARED_VENV=\"/home/<USER>/shared-venv\"\nPID_FILE=\"$INSTANCE_DIR/app.pid\"\n\nsource \"$INSTANCE_DIR/.env\"\nsource \"$SHARED_VENV/bin/activate\"\n\ncase \"${1:-start}\" in\n    \"start\")\n        if [ -f \"$PID_FILE\" ] && ps -p \"$(cat \"$PID_FILE\")\" > /dev/null 2>&1; then\n            echo \"Instance $INSTANCE_ID already running\"\n            exit 0\n        fi\n        \n        cd \"$INSTANCE_DIR\"\n        nohup \"$SHARED_VENV/bin/gunicorn\" \\\n            --bind=\"0.0.0.0:$FLASK_PORT\" \\\n            --workers=1 \\\n            --worker-class=eventlet \\\n            --worker-connections=50 \\\n            --max-requests=200 \\\n            --timeout=30 \\\n            --preload \\\n            --log-level=warning \\\n            --access-logfile=\"logs/access.log\" \\\n            --error-logfile=\"logs/error.log\" \\\n            app:app > \"logs/app.log\" 2>&1 &\n        \n        echo $! > \"$PID_FILE\"\n        echo \"Instance $INSTANCE_ID started on port $FLASK_PORT\"\n        ;;\n    \"stop\")\n        if [ -f \"$PID_FILE\" ]; then\n            kill \"$(cat \"$PID_FILE\")\" 2>/dev/null || true\n            rm -f \"$PID_FILE\"\n            echo \"Instance $INSTANCE_ID stopped\"\n        fi\n        ;;\n    \"status\")\n        if [ -f \"$PID_FILE\" ] && ps -p \"$(cat \"$PID_FILE\")\" > /dev/null 2>&1; then\n            echo \"Instance $INSTANCE_ID: RUNNING (PID: $(cat \"$PID_FILE\"))\"\n        else\n            echo \"Instance $INSTANCE_ID: STOPPED\"\n        fi\n        ;;\nesac\nEOF\n    \n    chmod +x \"$instance_dir/start_light.sh\"\n    success \"Instance $instance_id created at port $instance_id\"\n}\n\n# Start instance\nstart_instance() {\n    local instance_id=$1\n    local instance_dir=\"$INSTANCES_DIR/algofactory-$instance_id\"\n    \n    if [ ! -d \"$instance_dir\" ]; then\n        error \"Instance $instance_id does not exist\"\n        return 1\n    fi\n    \n    cd \"$instance_dir\"\n    ./start_light.sh start\n}\n\n# Stop instance\nstop_instance() {\n    local instance_id=$1\n    local instance_dir=\"$INSTANCES_DIR/algofactory-$instance_id\"\n    \n    if [ ! -d \"$instance_dir\" ]; then\n        error \"Instance $instance_id does not exist\"\n        return 1\n    fi\n    \n    cd \"$instance_dir\"\n    ./start_light.sh stop\n}\n\n# List instances\nlist_instances() {\n    echo \"AlgoFactory Instances:\"\n    echo \"=====================\"\n    \n    if [ ! -d \"$INSTANCES_DIR\" ]; then\n        echo \"No instances found\"\n        return 0\n    fi\n    \n    for dir in \"$INSTANCES_DIR\"/algofactory-*; do\n        if [ -d \"$dir\" ]; then\n            instance_id=$(basename \"$dir\" | cut -d'-' -f2)\n            cd \"$dir\"\n            status=$(./start_light.sh status | cut -d':' -f2 | xargs)\n            echo \"Instance $instance_id: $status | URL: https://$instance_id.algofactory.in\"\n        fi\n    done\n}\n\n# Create Nginx config for instance\ncreate_nginx_config() {\n    local instance_id=$1\n    \n    cat > \"/tmp/$instance_id.algofactory.in.conf\" << EOF\nserver {\n    listen 80;\n    server_name $instance_id.algofactory.in;\n    return 301 https://\\$server_name\\$request_uri;\n}\n\nserver {\n    listen 443 ssl http2;\n    server_name $instance_id.algofactory.in;\n    \n    ssl_certificate /etc/letsencrypt/live/$instance_id.algofactory.in/fullchain.pem;\n    ssl_certificate_key /etc/letsencrypt/live/$instance_id.algofactory.in/privkey.pem;\n    \n    location / {\n        proxy_pass http://127.0.0.1:$instance_id;\n        proxy_set_header Host \\$host;\n        proxy_set_header X-Real-IP \\$remote_addr;\n        proxy_set_header X-Forwarded-For \\$proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto \\$scheme;\n    }\n}\nEOF\n    \n    sudo mv \"/tmp/$instance_id.algofactory.in.conf\" \"/etc/nginx/sites-available/\"\n    sudo ln -sf \"/etc/nginx/sites-available/$instance_id.algofactory.in.conf\" \"/etc/nginx/sites-enabled/\"\n    sudo nginx -t && sudo systemctl reload nginx\n    \n    success \"Nginx config created for $instance_id.algofactory.in\"\n}\n\n# Main function\nmain() {\n    case \"${1:-help}\" in\n        \"create\")\n            if [ -z \"$2\" ]; then\n                error \"Usage: $0 create INSTANCE_ID\"\n                exit 1\n            fi\n            create_instance \"$2\"\n            ;;\n        \"start\")\n            if [ -z \"$2\" ]; then\n                error \"Usage: $0 start INSTANCE_ID\"\n                exit 1\n            fi\n            start_instance \"$2\"\n            ;;\n        \"stop\")\n            if [ -z \"$2\" ]; then\n                error \"Usage: $0 stop INSTANCE_ID\"\n                exit 1\n            fi\n            stop_instance \"$2\"\n            ;;\n        \"list\")\n            list_instances\n            ;;\n        \"nginx\")\n            if [ -z \"$2\" ]; then\n                error \"Usage: $0 nginx INSTANCE_ID\"\n                exit 1\n            fi\n            create_nginx_config \"$2\"\n            ;;\n        \"help\"|*)\n            echo \"Quick Instance Manager for AlgoFactory\"\n            echo \"\"\n            echo \"Usage: $0 COMMAND [INSTANCE_ID]\"\n            echo \"\"\n            echo \"Commands:\"\n            echo \"  create ID  - Create new instance\"\n            echo \"  start ID   - Start instance\"\n            echo \"  stop ID    - Stop instance\"\n            echo \"  list       - List all instances\"\n            echo \"  nginx ID   - Create Nginx config for instance\"\n            echo \"\"\n            echo \"Examples:\"\n            echo \"  $0 create 1010\"\n            echo \"  $0 start 1010\"\n            echo \"  $0 nginx 1010\"\n            ;;\n    esac\n}\n\nmain \"$@\"\n"}