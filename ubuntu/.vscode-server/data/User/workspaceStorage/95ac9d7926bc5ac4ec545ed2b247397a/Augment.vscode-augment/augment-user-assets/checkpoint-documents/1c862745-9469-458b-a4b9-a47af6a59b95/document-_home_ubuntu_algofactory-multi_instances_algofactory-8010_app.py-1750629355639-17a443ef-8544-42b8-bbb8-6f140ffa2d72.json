{"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/app.py"}, "originalCode": "# Load and check environment variables before anything else\r\nfrom utils.env_check import load_and_check_env_variables  # Import the environment check function\r\nload_and_check_env_variables()\r\n\r\nfrom flask import Flask, render_template\r\nfrom flask_wtf.csrf import CSRFProtect  # Import CSRF protection\r\nfrom extensions import socketio  # Import SocketIO\r\nfrom limiter import limiter  # Import the Limiter instance\r\nfrom cors import cors        # Import the CORS instance\r\nfrom csp import apply_csp_middleware  # Import the CSP middleware\r\nfrom utils.version import get_version  # Import version management\r\nfrom utils.latency_monitor import init_latency_monitoring  # Import latency monitoring\r\nfrom utils.traffic_logger import init_traffic_logging  # Import traffic logging\r\n# Import WebSocket proxy server - using relative import to avoid @ symbol issues\r\nfrom websocket_proxy.app_integration import start_websocket_proxy\r\n\r\nfrom blueprints.auth import auth_bp\r\nfrom blueprints.dashboard import dashboard_bp\r\nfrom blueprints.orders import orders_bp\r\nfrom blueprints.search import search_bp\r\nfrom blueprints.apikey import api_key_bp\r\nfrom blueprints.log import log_bp\r\nfrom blueprints.tv_json import tv_json_bp\r\nfrom blueprints.brlogin import brlogin_bp\r\nfrom blueprints.core import core_bp\r\nfrom blueprints.analyzer import analyzer_bp  # Import the analyzer blueprint\r\nfrom blueprints.settings import settings_bp  # Import the settings blueprint\r\nfrom blueprints.chartink import chartink_bp  # Import the chartink blueprint\r\nfrom blueprints.traffic import traffic_bp  # Import the traffic blueprint\r\nfrom blueprints.latency import latency_bp  # Import the latency blueprint\r\nfrom blueprints.strategy import strategy_bp  # Import the strategy blueprint\r\nfrom blueprints.master_contract_status import master_contract_status_bp  # Import the master contract status blueprint\r\n\r\nfrom restx_api import api_v1_bp, api\r\n\r\nfrom database.auth_db import init_db as ensure_auth_tables_exists\r\nfrom database.user_db import init_db as ensure_user_tables_exists\r\nfrom database.symbol import init_db as ensure_master_contract_tables_exists\r\nfrom database.apilog_db import init_db as ensure_api_log_tables_exists\r\nfrom database.analyzer_db import init_db as ensure_analyzer_tables_exists\r\nfrom database.settings_db import init_db as ensure_settings_tables_exists\r\nfrom database.chartink_db import init_db as ensure_chartink_tables_exists\r\nfrom database.traffic_db import init_logs_db as ensure_traffic_logs_exists\r\nfrom database.latency_db import init_latency_db as ensure_latency_tables_exists\r\nfrom database.strategy_db import init_db as ensure_strategy_tables_exists\r\n\r\nfrom utils.plugin_loader import load_broker_auth_functions\r\n\r\nimport os\r\n\r\ndef create_app():\r\n    # Initialize Flask application\r\n    app = Flask(__name__)\r\n\r\n    # Initialize SocketIO\r\n    socketio.init_app(app)  # Link SocketIO to the Flask app\r\n\r\n    # Initialize CSRF protection\r\n    csrf = CSRFProtect(app)\r\n    \r\n    # Store csrf instance in app config for use in other modules\r\n    app.csrf = csrf\r\n\r\n    # Initialize Flask-Limiter with the app object\r\n    limiter.init_app(app)\r\n\r\n    # Initialize Flask-CORS with the app object using configuration from environment variables\r\n    from cors import get_cors_config\r\n    cors.init_app(app, **get_cors_config())\r\n\r\n    # Apply Content Security Policy middleware\r\n    apply_csp_middleware(app)\r\n\r\n    # Environment variables\r\n    app.secret_key = os.getenv('APP_KEY')\r\n    app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL')\r\n    \r\n    # Dynamic cookie security configuration based on HOST_SERVER\r\n    HOST_SERVER = os.getenv('HOST_SERVER', 'http://127.0.0.1:5000')\r\n    USE_HTTPS = HOST_SERVER.startswith('https://')\r\n    \r\n    # Configure session cookie security\r\n    app.config.update(\r\n        SESSION_COOKIE_HTTPONLY=True,\r\n        SESSION_COOKIE_SAMESITE='Lax',\r\n        SESSION_COOKIE_SECURE=USE_HTTPS,\r\n        SESSION_COOKIE_NAME='session'\r\n        # PERMANENT_SESSION_LIFETIME is dynamically set at login to expire at 3:30 AM IST\r\n    )\r\n    \r\n    # Add cookie prefix for HTTPS environments\r\n    if USE_HTTPS:\r\n        app.config['SESSION_COOKIE_NAME'] = '__Secure-session'\r\n    \r\n    # CSRF configuration from environment variables\r\n    csrf_enabled = os.getenv('CSRF_ENABLED', 'TRUE').upper() == 'TRUE'\r\n    app.config['WTF_CSRF_ENABLED'] = csrf_enabled\r\n    \r\n    # Configure CSRF cookie security to match session cookie\r\n    app.config.update(\r\n        WTF_CSRF_COOKIE_HTTPONLY=True,\r\n        WTF_CSRF_COOKIE_SAMESITE='Lax',\r\n        WTF_CSRF_COOKIE_SECURE=USE_HTTPS,\r\n        WTF_CSRF_COOKIE_NAME='csrf_token'\r\n    )\r\n    \r\n    # Add cookie prefix for CSRF token in HTTPS environments\r\n    if USE_HTTPS:\r\n        app.config['WTF_CSRF_COOKIE_NAME'] = '__Secure-csrf_token'\r\n    \r\n    # Parse CSRF time limit from environment\r\n    csrf_time_limit = os.getenv('CSRF_TIME_LIMIT', '').strip()\r\n    if csrf_time_limit:\r\n        try:\r\n            app.config['WTF_CSRF_TIME_LIMIT'] = int(csrf_time_limit)\r\n        except ValueError:\r\n            app.config['WTF_CSRF_TIME_LIMIT'] = None  # Default to no limit if invalid\r\n    else:\r\n        app.config['WTF_CSRF_TIME_LIMIT'] = None  # No time limit if empty\r\n\r\n    # Register RESTx API blueprint first\r\n    app.register_blueprint(api_v1_bp)\r\n    \r\n    # Exempt API endpoints from CSRF protection (they use API key authentication)\r\n    csrf.exempt(api_v1_bp)\r\n\r\n    # Initialize traffic logging middleware after RESTx but before other blueprints\r\n    init_traffic_logging(app)\r\n\r\n    # Register other blueprints\r\n    app.register_blueprint(auth_bp)\r\n    app.register_blueprint(dashboard_bp)\r\n    app.register_blueprint(orders_bp)\r\n    app.register_blueprint(search_bp)\r\n    app.register_blueprint(api_key_bp)\r\n    app.register_blueprint(log_bp)\r\n    app.register_blueprint(tv_json_bp)\r\n    app.register_blueprint(brlogin_bp)\r\n    app.register_blueprint(core_bp)\r\n    app.register_blueprint(analyzer_bp)\r\n    app.register_blueprint(settings_bp)\r\n    app.register_blueprint(chartink_bp)\r\n    app.register_blueprint(traffic_bp)\r\n    app.register_blueprint(latency_bp)\r\n    app.register_blueprint(strategy_bp)\r\n    app.register_blueprint(master_contract_status_bp)\r\n\r\n    # Initialize latency monitoring (after registering API blueprint)\r\n    with app.app_context():\r\n        init_latency_monitoring(app)\r\n\r\n    @app.errorhandler(404)\r\n    def not_found_error(error):\r\n        return render_template('404.html'), 404\r\n\r\n    @app.errorhandler(500)\r\n    def internal_server_error(e):\r\n        \"\"\"Custom handler for 500 Internal Server Error\"\"\"\r\n        # Log the error (optional)\r\n        app.logger.error(f\"Server Error: {e}\")\r\n\r\n        # Provide a logout option\r\n        return render_template(\"500.html\"), 500\r\n        \r\n    @app.context_processor\r\n    def inject_version():\r\n        return dict(version=get_version())\r\n\r\n    @app.context_processor\r\n    def inject_instance_id():\r\n        # Extract instance ID from environment or derive from port\r\n        instance_id = os.getenv('INSTANCE_ID')\r\n        if not instance_id:\r\n            # Fallback: derive from FLASK_PORT\r\n            port = os.getenv('FLASK_PORT', '8010')\r\n            instance_id = port\r\n        return dict(instance_id=instance_id)\r\n\r\n    return app\r\n\r\ndef setup_environment(app):\r\n    with app.app_context():\r\n        #load broker plugins\r\n        app.broker_auth_functions = load_broker_auth_functions()\r\n        # Ensure all the tables exist\r\n        ensure_auth_tables_exists()\r\n        ensure_user_tables_exists()\r\n        ensure_master_contract_tables_exists()\r\n        ensure_api_log_tables_exists()\r\n        ensure_analyzer_tables_exists()\r\n        ensure_settings_tables_exists()\r\n        ensure_chartink_tables_exists()\r\n        ensure_traffic_logs_exists()\r\n        ensure_latency_tables_exists()\r\n        ensure_strategy_tables_exists()\r\n\r\n    # Conditionally setup ngrok in development environment\r\n    if os.getenv('NGROK_ALLOW') == 'TRUE':\r\n        from pyngrok import ngrok\r\n        public_url = ngrok.connect(name='flask').public_url  # Assuming Flask runs on the default port 5000\r\n        print(\" * ngrok URL: \" + public_url + \" *\")\r\n\r\napp = create_app()\r\n\r\n# Explicitly call the setup environment function\r\nsetup_environment(app)\r\n\r\n# Integrate the WebSocket proxy server with the Flask app\r\nstart_websocket_proxy(app)\r\n\r\n# Start Flask development server with SocketIO support if directly executed\r\nif __name__ == '__main__':\r\n    # Get environment variables\r\n    host_ip = os.getenv('FLASK_HOST_IP', '127.0.0.1')  # Default to '127.0.0.1' if not set\r\n    port = int(os.getenv('FLASK_PORT', 5000))  # Default to 5000 if not set\r\n    debug = os.getenv('FLASK_DEBUG', 'False').lower() in ('true', '1', 't')  # Default to False if not set\r\n\r\n    socketio.run(app, host=host_ip, port=port, debug=debug)\r\n", "modifiedCode": "# Load and check environment variables before anything else\r\nfrom utils.env_check import load_and_check_env_variables  # Import the environment check function\r\nload_and_check_env_variables()\r\n\r\nfrom flask import Flask, render_template\r\nfrom flask_wtf.csrf import CSRFProtect  # Import CSRF protection\r\nfrom extensions import socketio  # Import SocketIO\r\nfrom limiter import limiter  # Import the Limiter instance\r\nfrom cors import cors        # Import the CORS instance\r\nfrom csp import apply_csp_middleware  # Import the CSP middleware\r\nfrom utils.version import get_version  # Import version management\r\nfrom utils.latency_monitor import init_latency_monitoring  # Import latency monitoring\r\nfrom utils.traffic_logger import init_traffic_logging  # Import traffic logging\r\n# Import WebSocket proxy server - using relative import to avoid @ symbol issues\r\nfrom websocket_proxy.app_integration import start_websocket_proxy\r\n\r\nfrom blueprints.auth import auth_bp\r\nfrom blueprints.dashboard import dashboard_bp\r\nfrom blueprints.orders import orders_bp\r\nfrom blueprints.search import search_bp\r\nfrom blueprints.apikey import api_key_bp\r\nfrom blueprints.log import log_bp\r\nfrom blueprints.tv_json import tv_json_bp\r\nfrom blueprints.brlogin import brlogin_bp\r\nfrom blueprints.core import core_bp\r\nfrom blueprints.analyzer import analyzer_bp  # Import the analyzer blueprint\r\nfrom blueprints.settings import settings_bp  # Import the settings blueprint\r\nfrom blueprints.chartink import chartink_bp  # Import the chartink blueprint\r\nfrom blueprints.traffic import traffic_bp  # Import the traffic blueprint\r\nfrom blueprints.latency import latency_bp  # Import the latency blueprint\r\nfrom blueprints.strategy import strategy_bp  # Import the strategy blueprint\r\nfrom blueprints.master_contract_status import master_contract_status_bp  # Import the master contract status blueprint\r\n\r\nfrom restx_api import api_v1_bp, api\r\n\r\nfrom database.auth_db import init_db as ensure_auth_tables_exists\r\nfrom database.user_db import init_db as ensure_user_tables_exists\r\nfrom database.symbol import init_db as ensure_master_contract_tables_exists\r\nfrom database.apilog_db import init_db as ensure_api_log_tables_exists\r\nfrom database.analyzer_db import init_db as ensure_analyzer_tables_exists\r\nfrom database.settings_db import init_db as ensure_settings_tables_exists\r\nfrom database.chartink_db import init_db as ensure_chartink_tables_exists\r\nfrom database.traffic_db import init_logs_db as ensure_traffic_logs_exists\r\nfrom database.latency_db import init_latency_db as ensure_latency_tables_exists\r\nfrom database.strategy_db import init_db as ensure_strategy_tables_exists\r\n\r\nfrom utils.plugin_loader import load_broker_auth_functions\r\n\r\nimport os\r\n\r\ndef create_app():\r\n    # Initialize Flask application\r\n    app = Flask(__name__)\r\n\r\n    # Initialize SocketIO\r\n    socketio.init_app(app)  # Link SocketIO to the Flask app\r\n\r\n    # Initialize CSRF protection\r\n    csrf = CSRFProtect(app)\r\n    \r\n    # Store csrf instance in app config for use in other modules\r\n    app.csrf = csrf\r\n\r\n    # Initialize Flask-Limiter with the app object\r\n    limiter.init_app(app)\r\n\r\n    # Initialize Flask-CORS with the app object using configuration from environment variables\r\n    from cors import get_cors_config\r\n    cors.init_app(app, **get_cors_config())\r\n\r\n    # Apply Content Security Policy middleware\r\n    apply_csp_middleware(app)\r\n\r\n    # Environment variables\r\n    app.secret_key = os.getenv('APP_KEY')\r\n    app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL')\r\n    \r\n    # Dynamic cookie security configuration based on HOST_SERVER\r\n    HOST_SERVER = os.getenv('HOST_SERVER', 'http://127.0.0.1:5000')\r\n    USE_HTTPS = HOST_SERVER.startswith('https://')\r\n    \r\n    # Configure session cookie security\r\n    app.config.update(\r\n        SESSION_COOKIE_HTTPONLY=True,\r\n        SESSION_COOKIE_SAMESITE='Lax',\r\n        SESSION_COOKIE_SECURE=USE_HTTPS,\r\n        SESSION_COOKIE_NAME='session'\r\n        # PERMANENT_SESSION_LIFETIME is dynamically set at login to expire at 3:30 AM IST\r\n    )\r\n    \r\n    # Add cookie prefix for HTTPS environments\r\n    if USE_HTTPS:\r\n        app.config['SESSION_COOKIE_NAME'] = '__Secure-session'\r\n    \r\n    # CSRF configuration from environment variables\r\n    csrf_enabled = os.getenv('CSRF_ENABLED', 'TRUE').upper() == 'TRUE'\r\n    app.config['WTF_CSRF_ENABLED'] = csrf_enabled\r\n    \r\n    # Configure CSRF cookie security to match session cookie\r\n    app.config.update(\r\n        WTF_CSRF_COOKIE_HTTPONLY=True,\r\n        WTF_CSRF_COOKIE_SAMESITE='Lax',\r\n        WTF_CSRF_COOKIE_SECURE=USE_HTTPS,\r\n        WTF_CSRF_COOKIE_NAME='csrf_token'\r\n    )\r\n    \r\n    # Add cookie prefix for CSRF token in HTTPS environments\r\n    if USE_HTTPS:\r\n        app.config['WTF_CSRF_COOKIE_NAME'] = '__Secure-csrf_token'\r\n    \r\n    # Parse CSRF time limit from environment\r\n    csrf_time_limit = os.getenv('CSRF_TIME_LIMIT', '').strip()\r\n    if csrf_time_limit:\r\n        try:\r\n            app.config['WTF_CSRF_TIME_LIMIT'] = int(csrf_time_limit)\r\n        except ValueError:\r\n            app.config['WTF_CSRF_TIME_LIMIT'] = None  # Default to no limit if invalid\r\n    else:\r\n        app.config['WTF_CSRF_TIME_LIMIT'] = None  # No time limit if empty\r\n\r\n    # Register RESTx API blueprint first\r\n    app.register_blueprint(api_v1_bp)\r\n    \r\n    # Exempt API endpoints from CSRF protection (they use API key authentication)\r\n    csrf.exempt(api_v1_bp)\r\n\r\n    # Initialize traffic logging middleware after RESTx but before other blueprints\r\n    init_traffic_logging(app)\r\n\r\n    # Register other blueprints\r\n    app.register_blueprint(auth_bp)\r\n    app.register_blueprint(dashboard_bp)\r\n    app.register_blueprint(orders_bp)\r\n    app.register_blueprint(search_bp)\r\n    app.register_blueprint(api_key_bp)\r\n    app.register_blueprint(log_bp)\r\n    app.register_blueprint(tv_json_bp)\r\n    app.register_blueprint(brlogin_bp)\r\n    app.register_blueprint(core_bp)\r\n    app.register_blueprint(analyzer_bp)\r\n    app.register_blueprint(settings_bp)\r\n    app.register_blueprint(chartink_bp)\r\n    app.register_blueprint(traffic_bp)\r\n    app.register_blueprint(latency_bp)\r\n    app.register_blueprint(strategy_bp)\r\n    app.register_blueprint(master_contract_status_bp)\r\n\r\n    # Initialize latency monitoring (after registering API blueprint)\r\n    with app.app_context():\r\n        init_latency_monitoring(app)\r\n\r\n    @app.errorhandler(404)\r\n    def not_found_error(error):\r\n        return render_template('404.html'), 404\r\n\r\n    @app.errorhandler(500)\r\n    def internal_server_error(e):\r\n        \"\"\"Custom handler for 500 Internal Server Error\"\"\"\r\n        # Log the error (optional)\r\n        app.logger.error(f\"Server Error: {e}\")\r\n\r\n        # Provide a logout option\r\n        return render_template(\"500.html\"), 500\r\n        \r\n    @app.context_processor\r\n    def inject_version():\r\n        return dict(version=get_version())\r\n\r\n    @app.context_processor\r\n    def inject_instance_id():\r\n        # Extract instance ID from environment or derive from port\r\n        instance_id = os.getenv('INSTANCE_ID')\r\n        if not instance_id:\r\n            # Fallback: derive from FLASK_PORT\r\n            port = os.getenv('FLASK_PORT', '8010')\r\n            instance_id = port\r\n        return dict(instance_id=instance_id)\r\n\r\n    return app\r\n\r\ndef setup_environment(app):\r\n    with app.app_context():\r\n        #load broker plugins\r\n        app.broker_auth_functions = load_broker_auth_functions()\r\n        # Ensure all the tables exist\r\n        ensure_auth_tables_exists()\r\n        ensure_user_tables_exists()\r\n        ensure_master_contract_tables_exists()\r\n        ensure_api_log_tables_exists()\r\n        ensure_analyzer_tables_exists()\r\n        ensure_settings_tables_exists()\r\n        ensure_chartink_tables_exists()\r\n        ensure_traffic_logs_exists()\r\n        ensure_latency_tables_exists()\r\n        ensure_strategy_tables_exists()\r\n\r\n    # Conditionally setup ngrok in development environment\r\n    if os.getenv('NGROK_ALLOW') == 'TRUE':\r\n        from pyngrok import ngrok\r\n        public_url = ngrok.connect(name='flask').public_url  # Assuming Flask runs on the default port 5000\r\n        print(\" * ngrok URL: \" + public_url + \" *\")\r\n\r\napp = create_app()\r\n\r\n# Explicitly call the setup environment function\r\nsetup_environment(app)\r\n\r\n# Integrate the WebSocket proxy server with the Flask app\r\nstart_websocket_proxy(app)\r\n\r\n# Start Flask development server with SocketIO support if directly executed\r\nif __name__ == '__main__':\r\n    # Get environment variables\r\n    host_ip = os.getenv('FLASK_HOST_IP', '127.0.0.1')  # Default to '127.0.0.1' if not set\r\n    port = int(os.getenv('FLASK_PORT', 5000))  # Default to 5000 if not set\r\n    debug = os.getenv('FLASK_DEBUG', 'False').lower() in ('true', '1', 't')  # Default to False if not set\r\n\r\n    socketio.run(app, host=host_ip, port=port, debug=debug)\r\n"}