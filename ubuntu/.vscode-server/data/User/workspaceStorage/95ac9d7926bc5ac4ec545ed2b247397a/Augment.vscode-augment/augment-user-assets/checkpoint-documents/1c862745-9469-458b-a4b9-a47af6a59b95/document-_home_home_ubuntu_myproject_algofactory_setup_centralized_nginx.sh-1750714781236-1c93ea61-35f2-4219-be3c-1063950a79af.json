{"path": {"rootPath": "/home", "relPath": "home/ubuntu/myproject/algofactory/setup_centralized_nginx.sh"}, "modifiedCode": "#!/bin/bash\n\n# Centralized Nginx Setup for AlgoFactory\n# Sets up optimized Nginx configuration with security and performance\n\nset -e\n\necho \"🌐 Setting up Centralized Nginx Configuration for AlgoFactory\"\necho \"=============================================================\"\n\n# Check if running as root\nif [ \"$EUID\" -ne 0 ]; then\n    echo \"❌ Please run as root (use sudo)\"\n    exit 1\nfi\n\n# Create main Nginx configuration optimizations\ncreate_main_nginx_config() {\n    echo \"🔧 Optimizing main Nginx configuration...\"\n    \n    # Backup original nginx.conf\n    cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.backup.$(date +%Y%m%d_%H%M%S)\n    \n    # Create optimized nginx.conf\n    cat > /etc/nginx/nginx.conf << 'EOF'\nuser www-data;\nworker_processes auto;\npid /run/nginx.pid;\ninclude /etc/nginx/modules-enabled/*.conf;\n\nevents {\n    worker_connections 1024;\n    use epoll;\n    multi_accept on;\n}\n\nhttp {\n    # Basic Settings\n    sendfile on;\n    tcp_nopush on;\n    tcp_nodelay on;\n    keepalive_timeout 65;\n    types_hash_max_size 2048;\n    server_tokens off;\n    \n    # File size limits\n    client_max_body_size 50M;\n    client_body_buffer_size 128k;\n    client_header_buffer_size 1k;\n    large_client_header_buffers 4 4k;\n    \n    # Timeouts\n    client_body_timeout 12;\n    client_header_timeout 12;\n    keepalive_requests 100;\n    send_timeout 10;\n    \n    # MIME types\n    include /etc/nginx/mime.types;\n    default_type application/octet-stream;\n    \n    # Logging\n    log_format main '$remote_addr - $remote_user [$time_local] \"$request\" '\n                   '$status $body_bytes_sent \"$http_referer\" '\n                   '\"$http_user_agent\" \"$http_x_forwarded_for\" '\n                   'rt=$request_time uct=\"$upstream_connect_time\" '\n                   'uht=\"$upstream_header_time\" urt=\"$upstream_response_time\"';\n    \n    access_log /var/log/nginx/access.log main;\n    error_log /var/log/nginx/error.log warn;\n    \n    # Gzip Compression\n    gzip on;\n    gzip_vary on;\n    gzip_proxied any;\n    gzip_comp_level 6;\n    gzip_types\n        text/plain\n        text/css\n        text/xml\n        text/javascript\n        application/json\n        application/javascript\n        application/xml+rss\n        application/atom+xml\n        image/svg+xml;\n    \n    # Rate Limiting Zones\n    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;\n    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;\n    limit_req_zone $binary_remote_addr zone=general:10m rate=20r/s;\n    \n    # Connection Limiting\n    limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;\n    limit_conn conn_limit_per_ip 20;\n    \n    # Security Headers (global)\n    add_header X-Frame-Options DENY always;\n    add_header X-Content-Type-Options nosniff always;\n    add_header X-XSS-Protection \"1; mode=block\" always;\n    add_header Referrer-Policy strict-origin-when-cross-origin always;\n    add_header Permissions-Policy \"camera=(), microphone=(), geolocation=(), payment=(), usb=(), screen-wake-lock=(), web-share=()\" always;\n    \n    # Hide Nginx version\n    server_tokens off;\n    \n    # Default server (catch-all)\n    server {\n        listen 80 default_server;\n        listen [::]:80 default_server;\n        server_name _;\n        return 444;\n    }\n    \n    # Include site configurations\n    include /etc/nginx/conf.d/*.conf;\n    include /etc/nginx/sites-enabled/*;\n}\nEOF\n    \n    echo \"✅ Main Nginx configuration updated\"\n}\n\n# Create security configuration\ncreate_security_config() {\n    echo \"🔒 Creating security configuration...\"\n    \n    cat > /etc/nginx/conf.d/security.conf << 'EOF'\n# Security Configuration for AlgoFactory\n\n# Hide server information\nserver_tokens off;\nmore_clear_headers Server;\n\n# Prevent access to hidden files\nlocation ~ /\\. {\n    deny all;\n    access_log off;\n    log_not_found off;\n}\n\n# Prevent access to backup files\nlocation ~ ~$ {\n    deny all;\n    access_log off;\n    log_not_found off;\n}\n\n# Block common attack patterns\nlocation ~* \\.(php|asp|aspx|jsp)$ {\n    deny all;\n    access_log off;\n    log_not_found off;\n}\n\n# Block SQL injection attempts\nlocation ~* (union.*select|select.*union|select.*from|insert.*into|delete.*from|drop.*table) {\n    deny all;\n    access_log off;\n    log_not_found off;\n}\nEOF\n    \n    echo \"✅ Security configuration created\"\n}\n\n# Create SSL configuration\ncreate_ssl_config() {\n    echo \"🔐 Creating SSL configuration...\"\n    \n    cat > /etc/nginx/conf.d/ssl.conf << 'EOF'\n# SSL Configuration for AlgoFactory\n\n# SSL Settings\nssl_protocols TLSv1.2 TLSv1.3;\nssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;\nssl_prefer_server_ciphers off;\n\n# SSL Session\nssl_session_cache shared:SSL:10m;\nssl_session_timeout 10m;\nssl_session_tickets off;\n\n# OCSP Stapling\nssl_stapling on;\nssl_stapling_verify on;\nresolver ******* ******* valid=300s;\nresolver_timeout 5s;\n\n# Security Headers for HTTPS\nadd_header Strict-Transport-Security \"max-age=31536000; includeSubDomains; preload\" always;\nadd_header X-Frame-Options DENY always;\nadd_header X-Content-Type-Options nosniff always;\nadd_header X-XSS-Protection \"1; mode=block\" always;\nadd_header Referrer-Policy strict-origin-when-cross-origin always;\nEOF\n    \n    echo \"✅ SSL configuration created\"\n}\n\n# Install required packages\ninstall_packages() {\n    echo \"📦 Installing required packages...\"\n    \n    apt update\n    apt install -y nginx certbot python3-certbot-nginx\n    \n    # Install nginx-extras for more_clear_headers\n    apt install -y nginx-extras || echo \"⚠️  nginx-extras not available, continuing...\"\n    \n    echo \"✅ Packages installed\"\n}\n\n# Create log rotation\nsetup_log_rotation() {\n    echo \"📝 Setting up log rotation...\"\n    \n    cat > /etc/logrotate.d/algofactory-nginx << 'EOF'\n/var/log/nginx/*.log {\n    daily\n    missingok\n    rotate 52\n    compress\n    delaycompress\n    notifempty\n    create 644 www-data adm\n    sharedscripts\n    prerotate\n        if [ -d /etc/logrotate.d/httpd-prerotate ]; then \\\n            run-parts /etc/logrotate.d/httpd-prerotate; \\\n        fi \\\n    endscript\n    postrotate\n        invoke-rc.d nginx rotate >/dev/null 2>&1\n    endscript\n}\nEOF\n    \n    echo \"✅ Log rotation configured\"\n}\n\n# Create monitoring script\ncreate_monitoring_script() {\n    echo \"📊 Creating Nginx monitoring script...\"\n    \n    cat > /usr/local/bin/nginx-monitor.sh << 'EOF'\n#!/bin/bash\n\n# Nginx Monitoring Script for AlgoFactory\n\nLOG_FILE=\"/var/log/nginx-monitor.log\"\n\nlog_message() {\n    echo \"[$(date '+%Y-%m-%d %H:%M:%S')] $1\" | tee -a \"$LOG_FILE\"\n}\n\n# Check if Nginx is running\nif ! systemctl is-active --quiet nginx; then\n    log_message \"ERROR: Nginx is not running. Attempting to start...\"\n    systemctl start nginx\n    if systemctl is-active --quiet nginx; then\n        log_message \"SUCCESS: Nginx started successfully\"\n    else\n        log_message \"CRITICAL: Failed to start Nginx\"\n        exit 1\n    fi\nfi\n\n# Check configuration\nif ! nginx -t >/dev/null 2>&1; then\n    log_message \"ERROR: Nginx configuration test failed\"\n    nginx -t 2>&1 | tee -a \"$LOG_FILE\"\n    exit 1\nfi\n\n# Check SSL certificates expiration\nfor cert_dir in /etc/letsencrypt/live/*/; do\n    if [ -d \"$cert_dir\" ]; then\n        domain=$(basename \"$cert_dir\")\n        if [[ \"$domain\" == *\"algofactory.in\" ]]; then\n            expiry_date=$(openssl x509 -enddate -noout -in \"${cert_dir}cert.pem\" 2>/dev/null | cut -d= -f2)\n            if [ -n \"$expiry_date\" ]; then\n                expiry_epoch=$(date -d \"$expiry_date\" +%s)\n                current_epoch=$(date +%s)\n                days_until_expiry=$(( (expiry_epoch - current_epoch) / 86400 ))\n                \n                if [ $days_until_expiry -lt 30 ]; then\n                    log_message \"WARNING: SSL certificate for $domain expires in $days_until_expiry days\"\n                fi\n            fi\n        fi\n    fi\ndone\n\nlog_message \"INFO: Nginx monitoring check completed\"\nEOF\n    \n    chmod +x /usr/local/bin/nginx-monitor.sh\n    \n    # Add to crontab\n    (crontab -l 2>/dev/null; echo \"*/15 * * * * /usr/local/bin/nginx-monitor.sh\") | crontab -\n    \n    echo \"✅ Nginx monitoring script created and scheduled\"\n}\n\n# Main setup function\nmain() {\n    echo \"Starting centralized Nginx setup...\"\n    \n    install_packages\n    create_main_nginx_config\n    create_security_config\n    create_ssl_config\n    setup_log_rotation\n    create_monitoring_script\n    \n    # Test configuration\n    echo \"🧪 Testing Nginx configuration...\"\n    if nginx -t; then\n        echo \"✅ Nginx configuration test passed\"\n        \n        # Restart Nginx\n        systemctl restart nginx\n        systemctl enable nginx\n        \n        echo \"✅ Nginx restarted and enabled\"\n    else\n        echo \"❌ Nginx configuration test failed\"\n        exit 1\n    fi\n    \n    echo \"\"\n    echo \"🎉 Centralized Nginx setup completed!\"\n    echo \"\"\n    echo \"📋 Next steps:\"\n    echo \"1. Make nginx_manager.py executable:\"\n    echo \"   chmod +x /home/<USER>/myproject/algofactory/nginx_manager.py\"\n    echo \"\"\n    echo \"2. Setup individual subdomains:\"\n    echo \"   sudo python3 nginx_manager.py --setup 8013 8013\"\n    echo \"\"\n    echo \"3. Bulk setup multiple subdomains:\"\n    echo \"   sudo python3 nginx_manager.py --bulk 1010 1020\"\n    echo \"\"\n    echo \"4. Install SSL certificates:\"\n    echo \"   sudo python3 nginx_manager.py --bulk-ssl 1010 1020\"\n    echo \"\"\n    echo \"5. List configured sites:\"\n    echo \"   sudo python3 nginx_manager.py --list\"\n}\n\n# Run main function\nmain\n"}