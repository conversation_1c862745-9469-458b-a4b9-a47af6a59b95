{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/admin/templates/system/monitor.html"}, "modifiedCode": "{% extends \"base.html\" %}\n\n{% block title %}System Monitor - AlgoFactory Admin{% endblock %}\n{% block page_title %}System Monitoring{% endblock %}\n\n{% block content %}\n<!-- System Overview -->\n<div class=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n    <div class=\"stat bg-base-200 rounded-xl shadow-sm\">\n        <div class=\"stat-figure text-primary\">\n            <i class=\"fas fa-microchip text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">CPU Usage</div>\n        <div class=\"stat-value text-primary\" id=\"cpu-usage\">0%</div>\n        <div class=\"stat-desc\">Load average: <span id=\"load-average\">0.00</span></div>\n    </div>\n    \n    <div class=\"stat bg-base-200 rounded-xl shadow-sm\">\n        <div class=\"stat-figure text-secondary\">\n            <i class=\"fas fa-memory text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">Memory Usage</div>\n        <div class=\"stat-value text-secondary\" id=\"memory-usage\">0%</div>\n        <div class=\"stat-desc\"><span id=\"memory-used\">0</span>MB / <span id=\"memory-total\">0</span>MB</div>\n    </div>\n    \n    <div class=\"stat bg-base-200 rounded-xl shadow-sm\">\n        <div class=\"stat-figure text-accent\">\n            <i class=\"fas fa-hdd text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">Disk Usage</div>\n        <div class=\"stat-value text-accent\" id=\"disk-usage\">0%</div>\n        <div class=\"stat-desc\"><span id=\"disk-used\">0</span>GB / <span id=\"disk-total\">0</span>GB</div>\n    </div>\n    \n    <div class=\"stat bg-base-200 rounded-xl shadow-sm\">\n        <div class=\"stat-figure text-info\">\n            <i class=\"fas fa-network-wired text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">Network I/O</div>\n        <div class=\"stat-value text-info\" id=\"network-io\">0 KB/s</div>\n        <div class=\"stat-desc\">↑ <span id=\"network-sent\">0</span> ↓ <span id=\"network-recv\">0</span></div>\n    </div>\n</div>\n\n<!-- Performance Charts -->\n<div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\">\n    <div class=\"card bg-base-200 shadow-sm\">\n        <div class=\"card-body\">\n            <h2 class=\"card-title\">\n                <i class=\"fas fa-chart-line text-primary\"></i>\n                CPU & Memory Usage\n            </h2>\n            <div class=\"h-64\">\n                <canvas id=\"cpu-memory-chart\"></canvas>\n            </div>\n        </div>\n    </div>\n    \n    <div class=\"card bg-base-200 shadow-sm\">\n        <div class=\"card-body\">\n            <h2 class=\"card-title\">\n                <i class=\"fas fa-chart-area text-secondary\"></i>\n                Network Activity\n            </h2>\n            <div class=\"h-64\">\n                <canvas id=\"network-chart\"></canvas>\n            </div>\n        </div>\n    </div>\n</div>\n\n<!-- Process Management -->\n<div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\">\n    <div class=\"card bg-base-200 shadow-sm\">\n        <div class=\"card-body\">\n            <div class=\"flex justify-between items-center mb-4\">\n                <h2 class=\"card-title\">\n                    <i class=\"fas fa-tasks text-warning\"></i>\n                    Top Processes\n                </h2>\n                <button class=\"btn btn-ghost btn-sm\" onclick=\"refreshProcesses()\">\n                    <i class=\"fas fa-sync-alt\"></i>\n                    Refresh\n                </button>\n            </div>\n            <div class=\"overflow-x-auto\">\n                <table class=\"table table-zebra table-compact w-full\">\n                    <thead>\n                        <tr>\n                            <th>Process</th>\n                            <th>CPU%</th>\n                            <th>Memory</th>\n                            <th>Action</th>\n                        </tr>\n                    </thead>\n                    <tbody id=\"processes-table\">\n                        <tr>\n                            <td colspan=\"4\" class=\"text-center\">\n                                <i class=\"fas fa-spinner fa-spin mr-2\"></i>\n                                Loading processes...\n                            </td>\n                        </tr>\n                    </tbody>\n                </table>\n            </div>\n        </div>\n    </div>\n    \n    <div class=\"card bg-base-200 shadow-sm\">\n        <div class=\"card-body\">\n            <div class=\"flex justify-between items-center mb-4\">\n                <h2 class=\"card-title\">\n                    <i class=\"fas fa-server text-success\"></i>\n                    System Services\n                </h2>\n                <button class=\"btn btn-ghost btn-sm\" onclick=\"refreshServices()\">\n                    <i class=\"fas fa-sync-alt\"></i>\n                    Refresh\n                </button>\n            </div>\n            <div class=\"space-y-2\" id=\"services-list\">\n                <div class=\"text-center\">\n                    <i class=\"fas fa-spinner fa-spin mr-2\"></i>\n                    Loading services...\n                </div>\n            </div>\n        </div>\n    </div>\n</div>\n\n<!-- System Information -->\n<div class=\"card bg-base-200 shadow-sm\">\n    <div class=\"card-body\">\n        <h2 class=\"card-title mb-6\">\n            <i class=\"fas fa-info-circle text-info\"></i>\n            System Information\n        </h2>\n        <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            <div class=\"space-y-3\">\n                <h3 class=\"font-bold text-lg\">Hardware</h3>\n                <div class=\"space-y-2 text-sm\">\n                    <div><strong>CPU:</strong> <span id=\"cpu-info\">Loading...</span></div>\n                    <div><strong>Cores:</strong> <span id=\"cpu-cores\">0</span></div>\n                    <div><strong>Architecture:</strong> <span id=\"cpu-arch\">Loading...</span></div>\n                    <div><strong>Total RAM:</strong> <span id=\"total-ram\">0 MB</span></div>\n                    <div><strong>Swap:</strong> <span id=\"swap-info\">0 MB</span></div>\n                </div>\n            </div>\n            \n            <div class=\"space-y-3\">\n                <h3 class=\"font-bold text-lg\">System</h3>\n                <div class=\"space-y-2 text-sm\">\n                    <div><strong>OS:</strong> <span id=\"os-info\">Loading...</span></div>\n                    <div><strong>Kernel:</strong> <span id=\"kernel-info\">Loading...</span></div>\n                    <div><strong>Uptime:</strong> <span id=\"system-uptime\">Loading...</span></div>\n                    <div><strong>Hostname:</strong> <span id=\"hostname\">Loading...</span></div>\n                    <div><strong>IP Address:</strong> <span id=\"ip-address\">Loading...</span></div>\n                </div>\n            </div>\n            \n            <div class=\"space-y-3\">\n                <h3 class=\"font-bold text-lg\">Storage</h3>\n                <div class=\"space-y-2 text-sm\">\n                    <div><strong>Root Filesystem:</strong> <span id=\"root-fs\">Loading...</span></div>\n                    <div><strong>Total Space:</strong> <span id=\"total-space\">0 GB</span></div>\n                    <div><strong>Available:</strong> <span id=\"available-space\">0 GB</span></div>\n                    <div><strong>Inodes Used:</strong> <span id=\"inodes-used\">0%</span></div>\n                </div>\n            </div>\n        </div>\n    </div>\n</div>\n\n<!-- Quick Actions -->\n<div class=\"fixed bottom-6 right-6 z-50\">\n    <div class=\"dropdown dropdown-top dropdown-end\">\n        <div tabindex=\"0\" role=\"button\" class=\"btn btn-primary btn-circle btn-lg shadow-lg\">\n            <i class=\"fas fa-cog text-xl\"></i>\n        </div>\n        <ul tabindex=\"0\" class=\"dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52 mb-2\">\n            <li><a onclick=\"optimizeSystem()\"><i class=\"fas fa-magic\"></i> Optimize System</a></li>\n            <li><a onclick=\"clearCache()\"><i class=\"fas fa-broom\"></i> Clear Cache</a></li>\n            <li><a onclick=\"restartServices()\"><i class=\"fas fa-redo\"></i> Restart Services</a></li>\n            <li><a onclick=\"emergencyCleanup()\"><i class=\"fas fa-exclamation-triangle\"></i> Emergency Cleanup</a></li>\n        </ul>\n    </div>\n</div>\n{% endblock %}\n\n{% block scripts %}\n<script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n<script>\n    let cpuMemoryChart, networkChart;\n    let systemData = {\n        cpu: [],\n        memory: [],\n        network_sent: [],\n        network_recv: [],\n        timestamps: []\n    };\n    \n    // Initialize page\n    document.addEventListener('DOMContentLoaded', function() {\n        initializeCharts();\n        loadSystemInfo();\n        loadProcesses();\n        loadServices();\n        \n        // Start real-time updates\n        startRealTimeUpdates();\n    });\n    \n    function initializeCharts() {\n        // CPU & Memory Chart\n        const cpuMemoryCtx = document.getElementById('cpu-memory-chart').getContext('2d');\n        cpuMemoryChart = new Chart(cpuMemoryCtx, {\n            type: 'line',\n            data: {\n                labels: [],\n                datasets: [{\n                    label: 'CPU %',\n                    data: [],\n                    borderColor: 'rgb(59, 130, 246)',\n                    backgroundColor: 'rgba(59, 130, 246, 0.1)',\n                    tension: 0.4\n                }, {\n                    label: 'Memory %',\n                    data: [],\n                    borderColor: 'rgb(16, 185, 129)',\n                    backgroundColor: 'rgba(16, 185, 129, 0.1)',\n                    tension: 0.4\n                }]\n            },\n            options: {\n                responsive: true,\n                maintainAspectRatio: false,\n                scales: {\n                    y: {\n                        beginAtZero: true,\n                        max: 100\n                    }\n                },\n                plugins: {\n                    legend: {\n                        position: 'top'\n                    }\n                }\n            }\n        });\n        \n        // Network Chart\n        const networkCtx = document.getElementById('network-chart').getContext('2d');\n        networkChart = new Chart(networkCtx, {\n            type: 'line',\n            data: {\n                labels: [],\n                datasets: [{\n                    label: 'Sent (KB/s)',\n                    data: [],\n                    borderColor: 'rgb(245, 101, 101)',\n                    backgroundColor: 'rgba(245, 101, 101, 0.1)',\n                    tension: 0.4\n                }, {\n                    label: 'Received (KB/s)',\n                    data: [],\n                    borderColor: 'rgb(251, 191, 36)',\n                    backgroundColor: 'rgba(251, 191, 36, 0.1)',\n                    tension: 0.4\n                }]\n            },\n            options: {\n                responsive: true,\n                maintainAspectRatio: false,\n                scales: {\n                    y: {\n                        beginAtZero: true\n                    }\n                },\n                plugins: {\n                    legend: {\n                        position: 'top'\n                    }\n                }\n            }\n        });\n    }\n    \n    function startRealTimeUpdates() {\n        // Listen for real-time updates from WebSocket\n        window.addEventListener('systemUpdate', function(event) {\n            if (event.detail.data) {\n                updateSystemMetrics(event.detail.data);\n            }\n        });\n        \n        // Fallback: poll every 5 seconds\n        setInterval(async () => {\n            try {\n                const response = await fetch('/api/system/metrics');\n                const data = await response.json();\n                updateSystemMetrics(data);\n            } catch (error) {\n                console.error('Error fetching system metrics:', error);\n            }\n        }, 5000);\n    }\n    \n    function updateSystemMetrics(data) {\n        // Update overview cards\n        if (data.cpu_percent !== undefined) {\n            document.getElementById('cpu-usage').textContent = data.cpu_percent.toFixed(1) + '%';\n        }\n        \n        if (data.memory_percent !== undefined) {\n            document.getElementById('memory-usage').textContent = data.memory_percent.toFixed(1) + '%';\n            document.getElementById('memory-used').textContent = Math.round(data.memory_used_mb || 0);\n            document.getElementById('memory-total').textContent = Math.round(data.memory_total_mb || 0);\n        }\n        \n        if (data.disk_percent !== undefined) {\n            document.getElementById('disk-usage').textContent = data.disk_percent.toFixed(1) + '%';\n            document.getElementById('disk-used').textContent = (data.disk_used_gb || 0).toFixed(1);\n            document.getElementById('disk-total').textContent = (data.disk_total_gb || 0).toFixed(1);\n        }\n        \n        if (data.load_average !== undefined) {\n            document.getElementById('load-average').textContent = data.load_average.toFixed(2);\n        }\n        \n        if (data.network_io !== undefined) {\n            document.getElementById('network-io').textContent = (data.network_io || 0).toFixed(1) + ' KB/s';\n            document.getElementById('network-sent').textContent = (data.network_sent || 0).toFixed(1) + ' KB/s';\n            document.getElementById('network-recv').textContent = (data.network_recv || 0).toFixed(1) + ' KB/s';\n        }\n        \n        // Update charts\n        updateCharts(data);\n    }\n    \n    function updateCharts(data) {\n        const now = new Date().toLocaleTimeString();\n        \n        // Add new data point\n        systemData.timestamps.push(now);\n        systemData.cpu.push(data.cpu_percent || 0);\n        systemData.memory.push(data.memory_percent || 0);\n        systemData.network_sent.push(data.network_sent || 0);\n        systemData.network_recv.push(data.network_recv || 0);\n        \n        // Keep only last 20 data points\n        if (systemData.timestamps.length > 20) {\n            systemData.timestamps.shift();\n            systemData.cpu.shift();\n            systemData.memory.shift();\n            systemData.network_sent.shift();\n            systemData.network_recv.shift();\n        }\n        \n        // Update CPU & Memory chart\n        cpuMemoryChart.data.labels = systemData.timestamps;\n        cpuMemoryChart.data.datasets[0].data = systemData.cpu;\n        cpuMemoryChart.data.datasets[1].data = systemData.memory;\n        cpuMemoryChart.update('none');\n        \n        // Update Network chart\n        networkChart.data.labels = systemData.timestamps;\n        networkChart.data.datasets[0].data = systemData.network_sent;\n        networkChart.data.datasets[1].data = systemData.network_recv;\n        networkChart.update('none');\n    }\n    \n    async function loadSystemInfo() {\n        // Mock system information - in real implementation, this would come from the server\n        const systemInfo = {\n            cpu_info: 'Intel(R) Xeon(R) CPU E5-2686 v4 @ 2.30GHz',\n            cpu_cores: 1,\n            cpu_arch: 'x86_64',\n            total_ram: 957,\n            swap_info: '2048 MB',\n            os_info: 'Ubuntu 24.04 LTS',\n            kernel_info: '6.8.0-1014-aws',\n            system_uptime: '2 days, 14 hours, 32 minutes',\n            hostname: 'ip-172-31-14-191',\n            ip_address: '*************',\n            root_fs: 'ext4',\n            total_space: 6.8,\n            available_space: 1.5,\n            inodes_used: 25\n        };\n        \n        // Update system information\n        document.getElementById('cpu-info').textContent = systemInfo.cpu_info;\n        document.getElementById('cpu-cores').textContent = systemInfo.cpu_cores;\n        document.getElementById('cpu-arch').textContent = systemInfo.cpu_arch;\n        document.getElementById('total-ram').textContent = systemInfo.total_ram + ' MB';\n        document.getElementById('swap-info').textContent = systemInfo.swap_info;\n        document.getElementById('os-info').textContent = systemInfo.os_info;\n        document.getElementById('kernel-info').textContent = systemInfo.kernel_info;\n        document.getElementById('system-uptime').textContent = systemInfo.system_uptime;\n        document.getElementById('hostname').textContent = systemInfo.hostname;\n        document.getElementById('ip-address').textContent = systemInfo.ip_address;\n        document.getElementById('root-fs').textContent = systemInfo.root_fs;\n        document.getElementById('total-space').textContent = systemInfo.total_space + ' GB';\n        document.getElementById('available-space').textContent = systemInfo.available_space + ' GB';\n        document.getElementById('inodes-used').textContent = systemInfo.inodes_used + '%';\n    }\n    \n    async function loadProcesses() {\n        // Mock process data\n        const processes = [\n            { name: 'python3 (admin)', cpu: 2.1, memory: '37MB', pid: 306179, killable: false },\n            { name: 'python3 (8010)', cpu: 1.8, memory: '45MB', pid: 123456, killable: true },\n            { name: 'python3 (8011)', cpu: 1.5, memory: '42MB', pid: 123457, killable: true },\n            { name: 'nginx', cpu: 0.8, memory: '12MB', pid: 5678, killable: false },\n            { name: 'systemd', cpu: 0.3, memory: '8MB', pid: 1, killable: false },\n            { name: 'ssh', cpu: 0.1, memory: '4MB', pid: 1234, killable: false }\n        ];\n        \n        const tbody = document.getElementById('processes-table');\n        tbody.innerHTML = processes.map(proc => `\n            <tr>\n                <td>\n                    <div class=\"font-medium\">${proc.name}</div>\n                    <div class=\"text-xs text-base-content/70\">PID: ${proc.pid}</div>\n                </td>\n                <td>\n                    <div class=\"text-sm\">${proc.cpu}%</div>\n                </td>\n                <td>\n                    <div class=\"text-sm\">${proc.memory}</div>\n                </td>\n                <td>\n                    ${proc.killable ? `\n                        <button class=\"btn btn-error btn-xs\" onclick=\"killProcess(${proc.pid}, '${proc.name}')\" title=\"Kill Process\">\n                            <i class=\"fas fa-times\"></i>\n                        </button>\n                    ` : `\n                        <span class=\"text-xs text-base-content/50\">System</span>\n                    `}\n                </td>\n            </tr>\n        `).join('');\n    }\n    \n    async function loadServices() {\n        // Mock service data\n        const services = [\n            { name: 'algofactory-admin', status: 'active', description: 'AlgoFactory Admin Dashboard' },\n            { name: 'nginx', status: 'active', description: 'Web Server' },\n            { name: 'ssh', status: 'active', description: 'SSH Daemon' },\n            { name: 'algofactory-optimizer', status: 'active', description: 'System Optimizer' },\n            { name: 'snapd', status: 'inactive', description: 'Snap Package Manager' }\n        ];\n        \n        const servicesList = document.getElementById('services-list');\n        servicesList.innerHTML = services.map(service => `\n            <div class=\"flex items-center justify-between p-3 bg-base-300 rounded-lg\">\n                <div class=\"flex items-center gap-3\">\n                    <div class=\"badge ${service.status === 'active' ? 'badge-success' : 'badge-error'} badge-sm\">\n                        <i class=\"fas ${service.status === 'active' ? 'fa-check' : 'fa-times'} mr-1\"></i>\n                        ${service.status}\n                    </div>\n                    <div>\n                        <div class=\"font-medium\">${service.name}</div>\n                        <div class=\"text-xs text-base-content/70\">${service.description}</div>\n                    </div>\n                </div>\n                <div class=\"flex gap-1\">\n                    ${service.status === 'active' ? `\n                        <button class=\"btn btn-warning btn-xs\" onclick=\"restartService('${service.name}')\" title=\"Restart\">\n                            <i class=\"fas fa-redo\"></i>\n                        </button>\n                        <button class=\"btn btn-error btn-xs\" onclick=\"stopService('${service.name}')\" title=\"Stop\">\n                            <i class=\"fas fa-stop\"></i>\n                        </button>\n                    ` : `\n                        <button class=\"btn btn-success btn-xs\" onclick=\"startService('${service.name}')\" title=\"Start\">\n                            <i class=\"fas fa-play\"></i>\n                        </button>\n                    `}\n                </div>\n            </div>\n        `).join('');\n    }\n    \n    // Action functions\n    async function killProcess(pid, name) {\n        if (confirm(`Kill process \"${name}\" (PID: ${pid})?`)) {\n            showToast(`Killing process ${name}...`, 'warning');\n            // This would call the API to kill the process\n            setTimeout(() => {\n                showToast(`Process ${name} terminated`, 'success');\n                loadProcesses();\n            }, 1000);\n        }\n    }\n    \n    async function restartService(serviceName) {\n        if (confirm(`Restart service \"${serviceName}\"?`)) {\n            showToast(`Restarting ${serviceName}...`, 'info');\n            // This would call systemctl restart\n            setTimeout(() => {\n                showToast(`Service ${serviceName} restarted`, 'success');\n                loadServices();\n            }, 2000);\n        }\n    }\n    \n    async function stopService(serviceName) {\n        if (confirm(`Stop service \"${serviceName}\"?`)) {\n            showToast(`Stopping ${serviceName}...`, 'warning');\n            // This would call systemctl stop\n            setTimeout(() => {\n                showToast(`Service ${serviceName} stopped`, 'success');\n                loadServices();\n            }, 1000);\n        }\n    }\n    \n    async function startService(serviceName) {\n        showToast(`Starting ${serviceName}...`, 'info');\n        // This would call systemctl start\n        setTimeout(() => {\n            showToast(`Service ${serviceName} started`, 'success');\n            loadServices();\n        }, 1000);\n    }\n    \n    async function optimizeSystem() {\n        if (confirm('Run system optimization? This will clean cache and optimize performance.')) {\n            showToast('Running system optimization...', 'info');\n            \n            try {\n                const response = await fetch('/api/system/cleanup', {\n                    method: 'POST',\n                    headers: { 'Content-Type': 'application/json' }\n                });\n                \n                const result = await response.json();\n                showToast(result.message, result.success ? 'success' : 'error');\n            } catch (error) {\n                showToast('System optimization failed', 'error');\n            }\n        }\n    }\n    \n    function refreshProcesses() {\n        showToast('Refreshing processes...', 'info');\n        loadProcesses();\n    }\n    \n    function refreshServices() {\n        showToast('Refreshing services...', 'info');\n        loadServices();\n    }\n</script>\n{% endblock %}\n"}