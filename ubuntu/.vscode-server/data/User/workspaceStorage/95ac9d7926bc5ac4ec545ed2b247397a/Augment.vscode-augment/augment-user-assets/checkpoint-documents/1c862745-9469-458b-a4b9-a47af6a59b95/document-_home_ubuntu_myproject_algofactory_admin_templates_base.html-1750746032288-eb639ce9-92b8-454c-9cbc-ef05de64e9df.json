{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/admin/templates/base.html"}, "originalCode": "<!DOCTYPE html>\n<html lang=\"en\" data-theme=\"light\">\n<head>\n    <!-- Theme script to prevent flash -->\n    <script>\n        (function() {\n            const savedTheme = localStorage.getItem('admin-theme') || 'light';\n            document.documentElement.setAttribute('data-theme', savedTheme);\n        })();\n    </script>\n    \n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <meta name=\"description\" content=\"AlgoFactory Admin Dashboard - Advanced server management system\">\n    <title>{% block title %}AlgoFactory Admin{% endblock %}</title>\n    \n    <!-- Favicon -->\n    <link rel=\"shortcut icon\" href=\"{{ url_for('static', filename='favicon.ico') }}\">\n    \n    <!-- DaisyUI + Tailwind CSS -->\n    <link href=\"https://cdn.jsdelivr.net/npm/daisyui@4.4.24/dist/full.min.css\" rel=\"stylesheet\" type=\"text/css\" />\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    \n    <!-- Font Awesome for icons -->\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css\">\n    \n    <!-- Chart.js for metrics visualization -->\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n    \n    <!-- Socket.IO for real-time updates -->\n    <script src=\"https://cdn.socket.io/4.7.4/socket.io.min.js\"></script>\n    \n    <!-- Custom styles -->\n    <style>\n        .sidebar-transition {\n            transition: transform 0.3s ease-in-out;\n        }\n        \n        .metric-card {\n            transition: all 0.3s ease;\n        }\n        \n        .metric-card:hover {\n            transform: translateY(-2px);\n            box-shadow: 0 8px 25px rgba(0,0,0,0.1);\n        }\n        \n        .status-indicator {\n            animation: pulse 2s infinite;\n        }\n        \n        .status-online {\n            background-color: #10b981;\n        }\n        \n        .status-warning {\n            background-color: #f59e0b;\n        }\n        \n        .status-error {\n            background-color: #ef4444;\n        }\n        \n        .chart-container {\n            position: relative;\n            height: 300px;\n        }\n        \n        .log-entry {\n            font-family: 'Courier New', monospace;\n            font-size: 0.875rem;\n        }\n        \n        .progress-ring {\n            transform: rotate(-90deg);\n        }\n        \n        .progress-ring-circle {\n            transition: stroke-dasharray 0.35s;\n            transform-origin: 50% 50%;\n        }\n        \n        /* Custom scrollbar */\n        .custom-scrollbar::-webkit-scrollbar {\n            width: 6px;\n        }\n        \n        .custom-scrollbar::-webkit-scrollbar-track {\n            background: hsl(var(--b2));\n        }\n        \n        .custom-scrollbar::-webkit-scrollbar-thumb {\n            background: hsl(var(--bc) / 0.3);\n            border-radius: 3px;\n        }\n        \n        .custom-scrollbar::-webkit-scrollbar-thumb:hover {\n            background: hsl(var(--bc) / 0.5);\n        }\n    </style>\n    \n    {% block head %}{% endblock %}\n</head>\n<body class=\"min-h-screen bg-base-100\">\n    <!-- Main Layout -->\n    <div class=\"drawer lg:drawer-open\">\n        <input id=\"drawer-toggle\" type=\"checkbox\" class=\"drawer-toggle\" />\n        \n        <!-- Main Content -->\n        <div class=\"drawer-content flex flex-col\">\n            <!-- Top Navigation -->\n            <div class=\"navbar bg-base-100 shadow-sm border-b border-base-200\">\n                <div class=\"flex-none lg:hidden\">\n                    <label for=\"drawer-toggle\" class=\"btn btn-square btn-ghost\">\n                        <i class=\"fas fa-bars text-lg\"></i>\n                    </label>\n                </div>\n                \n                <div class=\"flex-1\">\n                    <h1 class=\"text-xl font-semibold text-base-content\">\n                        {% block page_title %}AlgoFactory Admin Dashboard{% endblock %}\n                    </h1>\n                </div>\n                \n                <div class=\"flex-none gap-2\">\n                    <!-- Connection Status -->\n                    <div class=\"indicator\">\n                        <span id=\"connection-status\" class=\"indicator-item badge badge-error badge-xs\"></span>\n                        <div class=\"tooltip tooltip-bottom\" data-tip=\"Connection Status\">\n                            <i class=\"fas fa-wifi text-lg\"></i>\n                        </div>\n                    </div>\n                    \n                    <!-- Theme Toggle -->\n                    <div class=\"dropdown dropdown-end\">\n                        <div tabindex=\"0\" role=\"button\" class=\"btn btn-ghost btn-circle\">\n                            <i class=\"fas fa-palette text-lg\"></i>\n                        </div>\n                        <ul tabindex=\"0\" class=\"dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52\">\n                            <li><a onclick=\"setTheme('light')\"><i class=\"fas fa-sun\"></i> Light</a></li>\n                            <li><a onclick=\"setTheme('dark')\"><i class=\"fas fa-moon\"></i> Dark</a></li>\n                            <li><a onclick=\"setTheme('cupcake')\"><i class=\"fas fa-heart\"></i> Cupcake</a></li>\n                            <li><a onclick=\"setTheme('cyberpunk')\"><i class=\"fas fa-robot\"></i> Cyberpunk</a></li>\n                            <li><a onclick=\"setTheme('synthwave')\"><i class=\"fas fa-wave-square\"></i> Synthwave</a></li>\n                        </ul>\n                    </div>\n                    \n                    <!-- User Menu -->\n                    <div class=\"dropdown dropdown-end\">\n                        <div tabindex=\"0\" role=\"button\" class=\"btn btn-ghost btn-circle avatar\">\n                            <div class=\"w-8 rounded-full bg-primary text-primary-content flex items-center justify-center\">\n                                <i class=\"fas fa-user\"></i>\n                            </div>\n                        </div>\n                        <ul tabindex=\"0\" class=\"dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52\">\n                            <li class=\"menu-title\">\n                                <span>{{ session.username or 'Admin' }}</span>\n                            </li>\n                            <li><a href=\"{{ url_for('dashboard') }}\"><i class=\"fas fa-tachometer-alt\"></i> Dashboard</a></li>\n                            <li><a href=\"#\"><i class=\"fas fa-cog\"></i> Settings</a></li>\n                            <li><a href=\"#\"><i class=\"fas fa-user-cog\"></i> Profile</a></li>\n                            <div class=\"divider my-1\"></div>\n                            <li><a href=\"{{ url_for('logout') }}\" class=\"text-error\"><i class=\"fas fa-sign-out-alt\"></i> Logout</a></li>\n                        </ul>\n                    </div>\n                </div>\n            </div>\n            \n            <!-- Page Content -->\n            <main class=\"flex-1 p-4 lg:p-6\">\n                <!-- Flash Messages -->\n                {% with messages = get_flashed_messages(with_categories=true) %}\n                    {% if messages %}\n                        <div class=\"mb-4 space-y-2\">\n                            {% for category, message in messages %}\n                                <div class=\"alert alert-{{ 'success' if category == 'success' else 'error' }} shadow-lg\">\n                                    <div class=\"flex items-center\">\n                                        {% if category == 'success' %}\n                                            <i class=\"fas fa-check-circle text-lg\"></i>\n                                        {% else %}\n                                            <i class=\"fas fa-exclamation-circle text-lg\"></i>\n                                        {% endif %}\n                                        <span>{{ message }}</span>\n                                        <button onclick=\"this.parentElement.parentElement.remove()\" class=\"btn btn-ghost btn-sm btn-circle ml-auto\">\n                                            <i class=\"fas fa-times\"></i>\n                                        </button>\n                                    </div>\n                                </div>\n                            {% endfor %}\n                        </div>\n                    {% endif %}\n                {% endwith %}\n                \n                <!-- Toast Container -->\n                <div id=\"toast-container\" class=\"toast toast-top toast-end z-50\"></div>\n                \n                {% block content %}{% endblock %}\n            </main>\n        </div>\n        \n        <!-- Sidebar -->\n        <div class=\"drawer-side\">\n            <label for=\"drawer-toggle\" aria-label=\"close sidebar\" class=\"drawer-overlay\"></label>\n            <aside class=\"min-h-full w-64 bg-base-200 text-base-content\">\n                <!-- Logo -->\n                <div class=\"p-4 border-b border-base-300\">\n                    <div class=\"flex items-center gap-3\">\n                        <div class=\"w-10 h-10 bg-primary rounded-lg flex items-center justify-center\">\n                            <i class=\"fas fa-cogs text-primary-content text-lg\"></i>\n                        </div>\n                        <div>\n                            <h2 class=\"font-bold text-lg\">AlgoFactory</h2>\n                            <p class=\"text-xs text-base-content/70\">Admin Dashboard</p>\n                        </div>\n                    </div>\n                </div>\n                \n                <!-- Navigation Menu -->\n                <ul class=\"menu p-4 space-y-2\">\n                    <!-- Dashboard -->\n                    <li>\n                        <a href=\"{{ url_for('dashboard') }}\" class=\"{% if request.endpoint == 'dashboard' %}active{% endif %}\">\n                            <i class=\"fas fa-tachometer-alt\"></i>\n                            Dashboard\n                        </a>\n                    </li>\n                    \n                    <!-- System Management -->\n                    <li class=\"menu-title\">\n                        <span>System Management</span>\n                    </li>\n                    <li>\n                        <a href=\"{{ url_for('dashboard') }}#system\" class=\"{% if request.endpoint and 'system' in request.endpoint %}active{% endif %}\">\n                            <i class=\"fas fa-server\"></i>\n                            System Monitor\n                        </a>\n                    </li>\n                    <li>\n                        <a href=\"{{ url_for('dashboard') }}#instances\" class=\"{% if request.endpoint and 'instances' in request.endpoint %}active{% endif %}\">\n                            <i class=\"fas fa-cubes\"></i>\n                            AlgoFactory Instances\n                        </a>\n                    </li>\n                    <li>\n                        <a href=\"{{ url_for('dashboard') }}#nginx\" class=\"{% if request.endpoint and 'nginx' in request.endpoint %}active{% endif %}\">\n                            <i class=\"fas fa-globe\"></i>\n                            Nginx Management\n                        </a>\n                    </li>\n                    <li>\n                        <a href=\"{{ url_for('dashboard') }}#ssl\" class=\"{% if request.endpoint and 'ssl' in request.endpoint %}active{% endif %}\">\n                            <i class=\"fas fa-lock\"></i>\n                            SSL Certificates\n                        </a>\n                    </li>\n                    \n                    <!-- Monitoring & Logs -->\n                    <li class=\"menu-title\">\n                        <span>Monitoring & Logs</span>\n                    </li>\n                    <li>\n                        <a href=\"#\" class=\"{% if request.endpoint and 'logs' in request.endpoint %}active{% endif %}\">\n                            <i class=\"fas fa-file-alt\"></i>\n                            System Logs\n                        </a>\n                    </li>\n                    <li>\n                        <a href=\"#\" class=\"{% if request.endpoint and 'metrics' in request.endpoint %}active{% endif %}\">\n                            <i class=\"fas fa-chart-line\"></i>\n                            Performance Metrics\n                        </a>\n                    </li>\n                    <li>\n                        <a href=\"#\" class=\"{% if request.endpoint and 'alerts' in request.endpoint %}active{% endif %}\">\n                            <i class=\"fas fa-bell\"></i>\n                            Alerts & Notifications\n                        </a>\n                    </li>\n                    \n                    <!-- Tools & Utilities -->\n                    <li class=\"menu-title\">\n                        <span>Tools & Utilities</span>\n                    </li>\n                    <li>\n                        <a href=\"#\" class=\"{% if request.endpoint and 'backup' in request.endpoint %}active{% endif %}\">\n                            <i class=\"fas fa-database\"></i>\n                            Backup & Restore\n                        </a>\n                    </li>\n                    <li>\n                        <a href=\"#\" class=\"{% if request.endpoint and 'tasks' in request.endpoint %}active{% endif %}\">\n                            <i class=\"fas fa-tasks\"></i>\n                            Task Manager\n                        </a>\n                    </li>\n                    <li>\n                        <a href=\"#\" class=\"{% if request.endpoint and 'terminal' in request.endpoint %}active{% endif %}\">\n                            <i class=\"fas fa-terminal\"></i>\n                            Web Terminal\n                        </a>\n                    </li>\n                    \n                    <!-- Settings -->\n                    <li class=\"menu-title\">\n                        <span>Configuration</span>\n                    </li>\n                    <li>\n                        <a href=\"#\" class=\"{% if request.endpoint and 'settings' in request.endpoint %}active{% endif %}\">\n                            <i class=\"fas fa-cog\"></i>\n                            System Settings\n                        </a>\n                    </li>\n                    <li>\n                        <a href=\"#\" class=\"{% if request.endpoint and 'users' in request.endpoint %}active{% endif %}\">\n                            <i class=\"fas fa-users\"></i>\n                            User Management\n                        </a>\n                    </li>\n                </ul>\n                \n                <!-- System Status Footer -->\n                <div class=\"absolute bottom-0 left-0 right-0 p-4 border-t border-base-300\">\n                    <div class=\"text-xs space-y-1\">\n                        <div class=\"flex justify-between\">\n                            <span>System Status:</span>\n                            <span id=\"system-status\" class=\"badge badge-success badge-xs\">Online</span>\n                        </div>\n                        <div class=\"flex justify-between\">\n                            <span>Uptime:</span>\n                            <span id=\"system-uptime\" class=\"text-base-content/70\">--</span>\n                        </div>\n                        <div class=\"flex justify-between\">\n                            <span>Memory:</span>\n                            <span id=\"memory-usage\" class=\"text-base-content/70\">--</span>\n                        </div>\n                    </div>\n                </div>\n            </aside>\n        </div>\n    </div>\n    \n    <!-- Global JavaScript -->\n    <script>\n        // Theme management\n        function setTheme(theme) {\n            document.documentElement.setAttribute('data-theme', theme);\n            localStorage.setItem('admin-theme', theme);\n            showToast(`Theme changed to ${theme}`, 'success');\n        }\n        \n        // Toast notifications\n        function showToast(message, type = 'info', duration = 3000) {\n            const container = document.getElementById('toast-container');\n            const toast = document.createElement('div');\n            \n            const alertClass = type === 'success' ? 'alert-success' : \n                              type === 'error' ? 'alert-error' : \n                              type === 'warning' ? 'alert-warning' : 'alert-info';\n            \n            const icon = type === 'success' ? 'fa-check-circle' : \n                        type === 'error' ? 'fa-exclamation-circle' : \n                        type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle';\n            \n            toast.className = `alert ${alertClass} shadow-lg mb-2`;\n            toast.innerHTML = `\n                <div class=\"flex items-center\">\n                    <i class=\"fas ${icon}\"></i>\n                    <span>${message}</span>\n                    <button onclick=\"this.parentElement.parentElement.remove()\" class=\"btn btn-ghost btn-sm btn-circle ml-auto\">\n                        <i class=\"fas fa-times\"></i>\n                    </button>\n                </div>\n            `;\n            \n            container.appendChild(toast);\n            \n            // Auto remove after duration\n            setTimeout(() => {\n                if (toast.parentNode) {\n                    toast.remove();\n                }\n            }, duration);\n        }\n        \n        // Socket.IO connection\n        let socket = null;\n        \n        function initializeSocket() {\n            socket = io('/admin');\n            \n            socket.on('connect', function() {\n                console.log('Connected to admin dashboard');\n                updateConnectionStatus(true);\n            });\n            \n            socket.on('disconnect', function() {\n                console.log('Disconnected from admin dashboard');\n                updateConnectionStatus(false);\n            });\n            \n            socket.on('system_update', function(data) {\n                handleSystemUpdate(data);\n            });\n            \n            socket.on('system_alert', function(alert) {\n                showToast(alert.message, alert.severity === 'critical' ? 'error' : 'warning');\n            });\n        }\n        \n        function updateConnectionStatus(connected) {\n            const statusElement = document.getElementById('connection-status');\n            if (connected) {\n                statusElement.className = 'indicator-item badge badge-success badge-xs';\n            } else {\n                statusElement.className = 'indicator-item badge badge-error badge-xs';\n            }\n        }\n        \n        function handleSystemUpdate(data) {\n            // Update sidebar status\n            if (data.data && data.data.system) {\n                const system = data.data.system;\n                \n                // Update memory usage\n                const memoryElement = document.getElementById('memory-usage');\n                if (memoryElement && system.memory) {\n                    memoryElement.textContent = `${system.memory.percent}%`;\n                }\n                \n                // Update system status\n                const statusElement = document.getElementById('system-status');\n                if (statusElement) {\n                    const memoryPercent = system.memory ? system.memory.percent : 0;\n                    const cpuPercent = system.cpu ? system.cpu.percent : 0;\n                    \n                    if (memoryPercent > 90 || cpuPercent > 90) {\n                        statusElement.className = 'badge badge-error badge-xs';\n                        statusElement.textContent = 'Critical';\n                    } else if (memoryPercent > 80 || cpuPercent > 80) {\n                        statusElement.className = 'badge badge-warning badge-xs';\n                        statusElement.textContent = 'Warning';\n                    } else {\n                        statusElement.className = 'badge badge-success badge-xs';\n                        statusElement.textContent = 'Online';\n                    }\n                }\n            }\n            \n            // Trigger custom event for page-specific handlers\n            window.dispatchEvent(new CustomEvent('systemUpdate', { detail: data }));\n        }\n        \n        // Initialize on page load\n        document.addEventListener('DOMContentLoaded', function() {\n            initializeSocket();\n        });\n        \n        // Cleanup on page unload\n        window.addEventListener('beforeunload', function() {\n            if (socket) {\n                socket.disconnect();\n            }\n        });\n    </script>\n    \n    {% block scripts %}{% endblock %}\n</body>\n</html>\n", "modifiedCode": "<!DOCTYPE html>\n<html lang=\"en\" data-theme=\"light\">\n<head>\n    <!-- Theme script to prevent flash -->\n    <script>\n        (function() {\n            const savedTheme = localStorage.getItem('admin-theme') || 'light';\n            document.documentElement.setAttribute('data-theme', savedTheme);\n        })();\n    </script>\n    \n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <meta name=\"description\" content=\"AlgoFactory Admin Dashboard - Advanced server management system\">\n    <title>{% block title %}AlgoFactory Admin{% endblock %}</title>\n    \n    <!-- Favicon -->\n    <link rel=\"shortcut icon\" href=\"{{ url_for('static', filename='favicon.ico') }}\">\n    \n    <!-- DaisyUI + Tailwind CSS -->\n    <link href=\"https://cdn.jsdelivr.net/npm/daisyui@4.4.24/dist/full.min.css\" rel=\"stylesheet\" type=\"text/css\" />\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    \n    <!-- Font Awesome for icons -->\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css\">\n    \n    <!-- Chart.js for metrics visualization -->\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n    \n    <!-- Socket.IO for real-time updates -->\n    <script src=\"https://cdn.socket.io/4.7.4/socket.io.min.js\"></script>\n    \n    <!-- Custom styles -->\n    <style>\n        .sidebar-transition {\n            transition: transform 0.3s ease-in-out;\n        }\n        \n        .metric-card {\n            transition: all 0.3s ease;\n        }\n        \n        .metric-card:hover {\n            transform: translateY(-2px);\n            box-shadow: 0 8px 25px rgba(0,0,0,0.1);\n        }\n        \n        .status-indicator {\n            animation: pulse 2s infinite;\n        }\n        \n        .status-online {\n            background-color: #10b981;\n        }\n        \n        .status-warning {\n            background-color: #f59e0b;\n        }\n        \n        .status-error {\n            background-color: #ef4444;\n        }\n        \n        .chart-container {\n            position: relative;\n            height: 300px;\n        }\n        \n        .log-entry {\n            font-family: 'Courier New', monospace;\n            font-size: 0.875rem;\n        }\n        \n        .progress-ring {\n            transform: rotate(-90deg);\n        }\n        \n        .progress-ring-circle {\n            transition: stroke-dasharray 0.35s;\n            transform-origin: 50% 50%;\n        }\n        \n        /* Custom scrollbar */\n        .custom-scrollbar::-webkit-scrollbar {\n            width: 6px;\n        }\n        \n        .custom-scrollbar::-webkit-scrollbar-track {\n            background: hsl(var(--b2));\n        }\n        \n        .custom-scrollbar::-webkit-scrollbar-thumb {\n            background: hsl(var(--bc) / 0.3);\n            border-radius: 3px;\n        }\n        \n        .custom-scrollbar::-webkit-scrollbar-thumb:hover {\n            background: hsl(var(--bc) / 0.5);\n        }\n    </style>\n    \n    {% block head %}{% endblock %}\n</head>\n<body class=\"min-h-screen bg-base-100\">\n    <!-- Main Layout -->\n    <div class=\"drawer lg:drawer-open\">\n        <input id=\"drawer-toggle\" type=\"checkbox\" class=\"drawer-toggle\" />\n        \n        <!-- Main Content -->\n        <div class=\"drawer-content flex flex-col\">\n            <!-- Top Navigation -->\n            <div class=\"navbar bg-base-100 shadow-sm border-b border-base-200\">\n                <div class=\"flex-none lg:hidden\">\n                    <label for=\"drawer-toggle\" class=\"btn btn-square btn-ghost\">\n                        <i class=\"fas fa-bars text-lg\"></i>\n                    </label>\n                </div>\n                \n                <div class=\"flex-1\">\n                    <h1 class=\"text-xl font-semibold text-base-content\">\n                        {% block page_title %}AlgoFactory Admin Dashboard{% endblock %}\n                    </h1>\n                </div>\n                \n                <div class=\"flex-none gap-2\">\n                    <!-- Connection Status -->\n                    <div class=\"indicator\">\n                        <span id=\"connection-status\" class=\"indicator-item badge badge-error badge-xs\"></span>\n                        <div class=\"tooltip tooltip-bottom\" data-tip=\"Connection Status\">\n                            <i class=\"fas fa-wifi text-lg\"></i>\n                        </div>\n                    </div>\n                    \n                    <!-- Theme Toggle -->\n                    <div class=\"dropdown dropdown-end\">\n                        <div tabindex=\"0\" role=\"button\" class=\"btn btn-ghost btn-circle\">\n                            <i class=\"fas fa-palette text-lg\"></i>\n                        </div>\n                        <ul tabindex=\"0\" class=\"dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52\">\n                            <li><a onclick=\"setTheme('light')\"><i class=\"fas fa-sun\"></i> Light</a></li>\n                            <li><a onclick=\"setTheme('dark')\"><i class=\"fas fa-moon\"></i> Dark</a></li>\n                            <li><a onclick=\"setTheme('cupcake')\"><i class=\"fas fa-heart\"></i> Cupcake</a></li>\n                            <li><a onclick=\"setTheme('cyberpunk')\"><i class=\"fas fa-robot\"></i> Cyberpunk</a></li>\n                            <li><a onclick=\"setTheme('synthwave')\"><i class=\"fas fa-wave-square\"></i> Synthwave</a></li>\n                        </ul>\n                    </div>\n                    \n                    <!-- User Menu -->\n                    <div class=\"dropdown dropdown-end\">\n                        <div tabindex=\"0\" role=\"button\" class=\"btn btn-ghost btn-circle avatar\">\n                            <div class=\"w-8 rounded-full bg-primary text-primary-content flex items-center justify-center\">\n                                <i class=\"fas fa-user\"></i>\n                            </div>\n                        </div>\n                        <ul tabindex=\"0\" class=\"dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52\">\n                            <li class=\"menu-title\">\n                                <span>{{ session.username or 'Admin' }}</span>\n                            </li>\n                            <li><a href=\"{{ url_for('dashboard') }}\"><i class=\"fas fa-tachometer-alt\"></i> Dashboard</a></li>\n                            <li><a href=\"{{ url_for('change_password') }}\"><i class=\"fas fa-key\"></i> Change Password</a></li>\n                            <li><a href=\"#\"><i class=\"fas fa-cog\"></i> Settings</a></li>\n                            <div class=\"divider my-1\"></div>\n                            <li><a href=\"{{ url_for('logout') }}\" class=\"text-error\"><i class=\"fas fa-sign-out-alt\"></i> Logout</a></li>\n                        </ul>\n                    </div>\n                </div>\n            </div>\n            \n            <!-- Page Content -->\n            <main class=\"flex-1 p-4 lg:p-6\">\n                <!-- Flash Messages -->\n                {% with messages = get_flashed_messages(with_categories=true) %}\n                    {% if messages %}\n                        <div class=\"mb-4 space-y-2\">\n                            {% for category, message in messages %}\n                                <div class=\"alert alert-{{ 'success' if category == 'success' else 'error' }} shadow-lg\">\n                                    <div class=\"flex items-center\">\n                                        {% if category == 'success' %}\n                                            <i class=\"fas fa-check-circle text-lg\"></i>\n                                        {% else %}\n                                            <i class=\"fas fa-exclamation-circle text-lg\"></i>\n                                        {% endif %}\n                                        <span>{{ message }}</span>\n                                        <button onclick=\"this.parentElement.parentElement.remove()\" class=\"btn btn-ghost btn-sm btn-circle ml-auto\">\n                                            <i class=\"fas fa-times\"></i>\n                                        </button>\n                                    </div>\n                                </div>\n                            {% endfor %}\n                        </div>\n                    {% endif %}\n                {% endwith %}\n                \n                <!-- Toast Container -->\n                <div id=\"toast-container\" class=\"toast toast-top toast-end z-50\"></div>\n                \n                {% block content %}{% endblock %}\n            </main>\n        </div>\n        \n        <!-- Sidebar -->\n        <div class=\"drawer-side\">\n            <label for=\"drawer-toggle\" aria-label=\"close sidebar\" class=\"drawer-overlay\"></label>\n            <aside class=\"min-h-full w-64 bg-base-200 text-base-content\">\n                <!-- Logo -->\n                <div class=\"p-4 border-b border-base-300\">\n                    <div class=\"flex items-center gap-3\">\n                        <div class=\"w-10 h-10 bg-primary rounded-lg flex items-center justify-center\">\n                            <i class=\"fas fa-cogs text-primary-content text-lg\"></i>\n                        </div>\n                        <div>\n                            <h2 class=\"font-bold text-lg\">AlgoFactory</h2>\n                            <p class=\"text-xs text-base-content/70\">Admin Dashboard</p>\n                        </div>\n                    </div>\n                </div>\n                \n                <!-- Navigation Menu -->\n                <ul class=\"menu p-4 space-y-2\">\n                    <!-- Dashboard -->\n                    <li>\n                        <a href=\"{{ url_for('dashboard') }}\" class=\"{% if request.endpoint == 'dashboard' %}active{% endif %}\">\n                            <i class=\"fas fa-tachometer-alt\"></i>\n                            Dashboard\n                        </a>\n                    </li>\n                    \n                    <!-- System Management -->\n                    <li class=\"menu-title\">\n                        <span>System Management</span>\n                    </li>\n                    <li>\n                        <a href=\"{{ url_for('dashboard') }}#system\" class=\"{% if request.endpoint and 'system' in request.endpoint %}active{% endif %}\">\n                            <i class=\"fas fa-server\"></i>\n                            System Monitor\n                        </a>\n                    </li>\n                    <li>\n                        <a href=\"{{ url_for('dashboard') }}#instances\" class=\"{% if request.endpoint and 'instances' in request.endpoint %}active{% endif %}\">\n                            <i class=\"fas fa-cubes\"></i>\n                            AlgoFactory Instances\n                        </a>\n                    </li>\n                    <li>\n                        <a href=\"{{ url_for('dashboard') }}#nginx\" class=\"{% if request.endpoint and 'nginx' in request.endpoint %}active{% endif %}\">\n                            <i class=\"fas fa-globe\"></i>\n                            Nginx Management\n                        </a>\n                    </li>\n                    <li>\n                        <a href=\"{{ url_for('dashboard') }}#ssl\" class=\"{% if request.endpoint and 'ssl' in request.endpoint %}active{% endif %}\">\n                            <i class=\"fas fa-lock\"></i>\n                            SSL Certificates\n                        </a>\n                    </li>\n                    \n                    <!-- Monitoring & Logs -->\n                    <li class=\"menu-title\">\n                        <span>Monitoring & Logs</span>\n                    </li>\n                    <li>\n                        <a href=\"#\" class=\"{% if request.endpoint and 'logs' in request.endpoint %}active{% endif %}\">\n                            <i class=\"fas fa-file-alt\"></i>\n                            System Logs\n                        </a>\n                    </li>\n                    <li>\n                        <a href=\"#\" class=\"{% if request.endpoint and 'metrics' in request.endpoint %}active{% endif %}\">\n                            <i class=\"fas fa-chart-line\"></i>\n                            Performance Metrics\n                        </a>\n                    </li>\n                    <li>\n                        <a href=\"#\" class=\"{% if request.endpoint and 'alerts' in request.endpoint %}active{% endif %}\">\n                            <i class=\"fas fa-bell\"></i>\n                            Alerts & Notifications\n                        </a>\n                    </li>\n                    \n                    <!-- Tools & Utilities -->\n                    <li class=\"menu-title\">\n                        <span>Tools & Utilities</span>\n                    </li>\n                    <li>\n                        <a href=\"#\" class=\"{% if request.endpoint and 'backup' in request.endpoint %}active{% endif %}\">\n                            <i class=\"fas fa-database\"></i>\n                            Backup & Restore\n                        </a>\n                    </li>\n                    <li>\n                        <a href=\"#\" class=\"{% if request.endpoint and 'tasks' in request.endpoint %}active{% endif %}\">\n                            <i class=\"fas fa-tasks\"></i>\n                            Task Manager\n                        </a>\n                    </li>\n                    <li>\n                        <a href=\"#\" class=\"{% if request.endpoint and 'terminal' in request.endpoint %}active{% endif %}\">\n                            <i class=\"fas fa-terminal\"></i>\n                            Web Terminal\n                        </a>\n                    </li>\n                    \n                    <!-- Settings -->\n                    <li class=\"menu-title\">\n                        <span>Configuration</span>\n                    </li>\n                    <li>\n                        <a href=\"#\" class=\"{% if request.endpoint and 'settings' in request.endpoint %}active{% endif %}\">\n                            <i class=\"fas fa-cog\"></i>\n                            System Settings\n                        </a>\n                    </li>\n                    <li>\n                        <a href=\"#\" class=\"{% if request.endpoint and 'users' in request.endpoint %}active{% endif %}\">\n                            <i class=\"fas fa-users\"></i>\n                            User Management\n                        </a>\n                    </li>\n                </ul>\n                \n                <!-- System Status Footer -->\n                <div class=\"absolute bottom-0 left-0 right-0 p-4 border-t border-base-300\">\n                    <div class=\"text-xs space-y-1\">\n                        <div class=\"flex justify-between\">\n                            <span>System Status:</span>\n                            <span id=\"system-status\" class=\"badge badge-success badge-xs\">Online</span>\n                        </div>\n                        <div class=\"flex justify-between\">\n                            <span>Uptime:</span>\n                            <span id=\"system-uptime\" class=\"text-base-content/70\">--</span>\n                        </div>\n                        <div class=\"flex justify-between\">\n                            <span>Memory:</span>\n                            <span id=\"memory-usage\" class=\"text-base-content/70\">--</span>\n                        </div>\n                    </div>\n                </div>\n            </aside>\n        </div>\n    </div>\n    \n    <!-- Global JavaScript -->\n    <script>\n        // Theme management\n        function setTheme(theme) {\n            document.documentElement.setAttribute('data-theme', theme);\n            localStorage.setItem('admin-theme', theme);\n            showToast(`Theme changed to ${theme}`, 'success');\n        }\n        \n        // Toast notifications\n        function showToast(message, type = 'info', duration = 3000) {\n            const container = document.getElementById('toast-container');\n            const toast = document.createElement('div');\n            \n            const alertClass = type === 'success' ? 'alert-success' : \n                              type === 'error' ? 'alert-error' : \n                              type === 'warning' ? 'alert-warning' : 'alert-info';\n            \n            const icon = type === 'success' ? 'fa-check-circle' : \n                        type === 'error' ? 'fa-exclamation-circle' : \n                        type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle';\n            \n            toast.className = `alert ${alertClass} shadow-lg mb-2`;\n            toast.innerHTML = `\n                <div class=\"flex items-center\">\n                    <i class=\"fas ${icon}\"></i>\n                    <span>${message}</span>\n                    <button onclick=\"this.parentElement.parentElement.remove()\" class=\"btn btn-ghost btn-sm btn-circle ml-auto\">\n                        <i class=\"fas fa-times\"></i>\n                    </button>\n                </div>\n            `;\n            \n            container.appendChild(toast);\n            \n            // Auto remove after duration\n            setTimeout(() => {\n                if (toast.parentNode) {\n                    toast.remove();\n                }\n            }, duration);\n        }\n        \n        // Socket.IO connection\n        let socket = null;\n        \n        function initializeSocket() {\n            socket = io('/admin');\n            \n            socket.on('connect', function() {\n                console.log('Connected to admin dashboard');\n                updateConnectionStatus(true);\n            });\n            \n            socket.on('disconnect', function() {\n                console.log('Disconnected from admin dashboard');\n                updateConnectionStatus(false);\n            });\n            \n            socket.on('system_update', function(data) {\n                handleSystemUpdate(data);\n            });\n            \n            socket.on('system_alert', function(alert) {\n                showToast(alert.message, alert.severity === 'critical' ? 'error' : 'warning');\n            });\n        }\n        \n        function updateConnectionStatus(connected) {\n            const statusElement = document.getElementById('connection-status');\n            if (connected) {\n                statusElement.className = 'indicator-item badge badge-success badge-xs';\n            } else {\n                statusElement.className = 'indicator-item badge badge-error badge-xs';\n            }\n        }\n        \n        function handleSystemUpdate(data) {\n            // Update sidebar status\n            if (data.data && data.data.system) {\n                const system = data.data.system;\n                \n                // Update memory usage\n                const memoryElement = document.getElementById('memory-usage');\n                if (memoryElement && system.memory) {\n                    memoryElement.textContent = `${system.memory.percent}%`;\n                }\n                \n                // Update system status\n                const statusElement = document.getElementById('system-status');\n                if (statusElement) {\n                    const memoryPercent = system.memory ? system.memory.percent : 0;\n                    const cpuPercent = system.cpu ? system.cpu.percent : 0;\n                    \n                    if (memoryPercent > 90 || cpuPercent > 90) {\n                        statusElement.className = 'badge badge-error badge-xs';\n                        statusElement.textContent = 'Critical';\n                    } else if (memoryPercent > 80 || cpuPercent > 80) {\n                        statusElement.className = 'badge badge-warning badge-xs';\n                        statusElement.textContent = 'Warning';\n                    } else {\n                        statusElement.className = 'badge badge-success badge-xs';\n                        statusElement.textContent = 'Online';\n                    }\n                }\n            }\n            \n            // Trigger custom event for page-specific handlers\n            window.dispatchEvent(new CustomEvent('systemUpdate', { detail: data }));\n        }\n        \n        // Initialize on page load\n        document.addEventListener('DOMContentLoaded', function() {\n            initializeSocket();\n        });\n        \n        // Cleanup on page unload\n        window.addEventListener('beforeunload', function() {\n            if (socket) {\n                socket.disconnect();\n            }\n        });\n    </script>\n    \n    {% block scripts %}{% endblock %}\n</body>\n</html>\n"}