{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/nginx_global.conf"}, "modifiedCode": "# AlgoFactory Global Nginx Configuration\n# Place this in /etc/nginx/conf.d/algofactory-global.conf\n\n# Rate Limiting Zones for AlgoFactory\nlimit_req_zone $binary_remote_addr zone=algofactory_api:10m rate=10r/s;\nlimit_req_zone $binary_remote_addr zone=algofactory_login:10m rate=5r/m;\nlimit_req_zone $binary_remote_addr zone=algofactory_general:10m rate=20r/s;\nlimit_req_zone $binary_remote_addr zone=algofactory_websocket:10m rate=50r/s;\n\n# Connection Limiting\nlimit_conn_zone $binary_remote_addr zone=algofactory_conn:10m;\n\n# Cache zones\nproxy_cache_path /var/cache/nginx/algofactory levels=1:2 keys_zone=algofactory_cache:10m max_size=100m inactive=60m use_temp_path=off;\n\n# Map for WebSocket upgrade\nmap $http_upgrade $connection_upgrade {\n    default upgrade;\n    '' close;\n}\n\n# Log format for AlgoFactory\nlog_format algofactory_access '$remote_addr - $remote_user [$time_local] '\n                              '\"$request\" $status $body_bytes_sent '\n                              '\"$http_referer\" \"$http_user_agent\" '\n                              '\"$http_x_forwarded_for\" '\n                              'rt=$request_time uct=\"$upstream_connect_time\" '\n                              'uht=\"$upstream_header_time\" urt=\"$upstream_response_time\" '\n                              'instance=\"$upstream_addr\"';\n\n# Create cache directory\n# This will be created automatically by nginx\n"}