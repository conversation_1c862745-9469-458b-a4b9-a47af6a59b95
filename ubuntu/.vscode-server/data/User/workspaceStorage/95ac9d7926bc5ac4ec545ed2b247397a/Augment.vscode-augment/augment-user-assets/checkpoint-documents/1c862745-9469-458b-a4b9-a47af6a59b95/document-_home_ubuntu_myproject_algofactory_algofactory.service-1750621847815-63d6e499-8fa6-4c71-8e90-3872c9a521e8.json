{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/algofactory.service"}, "originalCode": "[Unit]\nDescription=AlgoFactory Trading Application\nAfter=network.target\nWants=network-online.target\nStartLimitIntervalSec=0\n\n[Service]\nType=simple\nUser=ubuntu\nGroup=ubuntu\nWorkingDirectory=/home/<USER>/myproject/algofactory\nEnvironment=PATH=/home/<USER>/shared-venv/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin\nExecStart=/bin/bash -c \"cd /home/<USER>/myproject/algofactory && ./start.sh monitor\"\nExecStop=/bin/bash -c \"cd /home/<USER>/myproject/algofactory && ./start.sh stop\"\nExecReload=/bin/bash -c \"cd /home/<USER>/myproject/algofactory && ./start.sh restart\"\nRestart=always\nRestartSec=10\nStandardOutput=journal\nStandardError=journal\nSyslogIdentifier=algofactory\nKillMode=mixed\nKillSignal=SIGTERM\nTimeoutStopSec=30\n\n# Security settings\nNoNewPrivileges=true\nPrivateTmp=true\nProtectSystem=strict\nReadWritePaths=/home/<USER>/myproject/algofactory\nProtectHome=true\n\n# Resource limits\nLimitNOFILE=65536\nLimitNPROC=4096\n\n[Install]\nWantedBy=multi-user.target\n", "modifiedCode": "[Unit]\nDescription=AlgoFactory Trading Application\nAfter=network.target\nWants=network-online.target\nStartLimitIntervalSec=0\n\n[Service]\nType=simple\nUser=ubuntu\nGroup=ubuntu\nWorkingDirectory=/home/<USER>/myproject/algofactory\nEnvironment=PATH=/home/<USER>/shared-venv/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin\nExecStart=/bin/bash -c \"cd /home/<USER>/myproject/algofactory && ./start.sh monitor\"\nExecStop=/bin/bash -c \"cd /home/<USER>/myproject/algofactory && ./start.sh stop\"\nExecReload=/bin/bash -c \"cd /home/<USER>/myproject/algofactory && ./start.sh restart\"\nRestart=always\nRestartSec=10\nStandardOutput=journal\nStandardError=journal\nSyslogIdentifier=algofactory\nKillMode=mixed\nKillSignal=SIGTERM\nTimeoutStopSec=30\n\n# Security settings\nNoNewPrivileges=true\nPrivateTmp=true\nProtectSystem=strict\nReadWritePaths=/home/<USER>/myproject/algofactory\nProtectHome=true\n\n# Resource limits\nLimitNOFILE=65536\nLimitNPROC=4096\n\n[Install]\nWantedBy=multi-user.target\n"}