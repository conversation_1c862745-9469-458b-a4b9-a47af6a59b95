{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/monitor_instances.sh"}, "modifiedCode": "#!/bin/bash\n\n# AlgoFactory 24/7 Instance Monitor\n# Automatically monitors and restarts instances if they go down\n# Logs all activities and sends alerts\n\nset -e\n\nSCRIPT_DIR=\"$(cd \"$(dirname \"${BASH_SOURCE[0]}\")\" && pwd)\"\nINSTANCES_DIR=\"/home/<USER>/algofactory-multi\"\nLOG_DIR=\"/var/log/algofactory-monitor\"\nLOG_FILE=\"$LOG_DIR/monitor.log\"\nALERT_LOG=\"$LOG_DIR/alerts.log\"\n\n# Create log directory\nsudo mkdir -p \"$LOG_DIR\"\nsudo chown ubuntu:ubuntu \"$LOG_DIR\"\n\n# Function to log messages\nlog_message() {\n    local level=$1\n    local message=$2\n    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')\n    echo \"[$timestamp] [$level] $message\" | tee -a \"$LOG_FILE\"\n    \n    # Also log alerts to separate file\n    if [ \"$level\" = \"ALERT\" ] || [ \"$level\" = \"ERROR\" ]; then\n        echo \"[$timestamp] [$level] $message\" >> \"$ALERT_LOG\"\n    fi\n}\n\n# Function to check if instance is running\ncheck_instance() {\n    local instance_id=$1\n    local instance_dir=\"$INSTANCES_DIR/algofactory-$instance_id\"\n    local pid_file=\"$instance_dir/app.pid\"\n    \n    if [ ! -d \"$instance_dir\" ]; then\n        return 1\n    fi\n    \n    if [ ! -f \"$pid_file\" ]; then\n        return 1\n    fi\n    \n    local pid=$(cat \"$pid_file\")\n    if ! kill -0 \"$pid\" 2>/dev/null; then\n        return 1\n    fi\n    \n    # Check if the port is responding\n    local flask_port=$instance_id\n    if ! curl -s --max-time 10 \"http://localhost:$flask_port\" > /dev/null; then\n        return 1\n    fi\n    \n    return 0\n}\n\n# Function to restart instance\nrestart_instance() {\n    local instance_id=$1\n    \n    log_message \"ALERT\" \"Restarting instance $instance_id\"\n    \n    # Stop the instance\n    cd \"$SCRIPT_DIR\"\n    ./multi_instance.sh stop \"$instance_id\" 2>/dev/null || true\n    \n    # Wait a moment\n    sleep 5\n    \n    # Start the instance\n    if ./multi_instance.sh start \"$instance_id\"; then\n        log_message \"INFO\" \"Successfully restarted instance $instance_id\"\n        \n        # Wait for startup and verify\n        sleep 10\n        if check_instance \"$instance_id\"; then\n            log_message \"INFO\" \"Instance $instance_id is healthy after restart\"\n        else\n            log_message \"ERROR\" \"Instance $instance_id failed health check after restart\"\n        fi\n    else\n        log_message \"ERROR\" \"Failed to restart instance $instance_id\"\n    fi\n}\n\n# Function to check system resources\ncheck_system_resources() {\n    local memory_usage=$(free | grep Mem | awk '{printf \"%.1f\", $3/$2 * 100.0}')\n    local disk_usage=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')\n    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')\n    \n    log_message \"INFO\" \"System Resources - Memory: ${memory_usage}%, Disk: ${disk_usage}%, Load: ${load_avg}\"\n    \n    # Alert if resources are high\n    if (( $(echo \"$memory_usage > 90\" | bc -l) )); then\n        log_message \"ALERT\" \"High memory usage: ${memory_usage}%\"\n    fi\n    \n    if [ \"$disk_usage\" -gt 90 ]; then\n        log_message \"ALERT\" \"High disk usage: ${disk_usage}%\"\n    fi\n}\n\n# Function to monitor all instances\nmonitor_instances() {\n    local instances=(8010 8011 8012)\n    local restart_count=0\n    \n    log_message \"INFO\" \"Starting monitoring cycle\"\n    \n    for instance_id in \"${instances[@]}\"; do\n        if check_instance \"$instance_id\"; then\n            log_message \"INFO\" \"Instance $instance_id is healthy\"\n        else\n            log_message \"ALERT\" \"Instance $instance_id is down - attempting restart\"\n            restart_instance \"$instance_id\"\n            ((restart_count++))\n        fi\n    done\n    \n    # Check system resources every cycle\n    check_system_resources\n    \n    if [ $restart_count -gt 0 ]; then\n        log_message \"ALERT\" \"Monitoring cycle completed - $restart_count instances restarted\"\n    else\n        log_message \"INFO\" \"Monitoring cycle completed - all instances healthy\"\n    fi\n}\n\n# Function to run continuous monitoring\nrun_monitor() {\n    log_message \"INFO\" \"AlgoFactory 24/7 Monitor started\"\n    log_message \"INFO\" \"Monitoring instances: 8010, 8011, 8012\"\n    log_message \"INFO\" \"Check interval: 60 seconds\"\n    \n    while true; do\n        monitor_instances\n        sleep 60\n    done\n}\n\n# Function to show status\nshow_status() {\n    echo \"🔍 AlgoFactory Instance Monitor Status\"\n    echo \"======================================\"\n    echo \"\"\n    \n    # Show running instances\n    cd \"$SCRIPT_DIR\"\n    ./multi_instance.sh list\n    \n    echo \"\"\n    echo \"📊 System Resources:\"\n    echo \"   Memory: $(free -h | grep Mem | awk '{print $3 \"/\" $2}')\"\n    echo \"   Disk: $(df -h / | tail -1 | awk '{print $3 \"/\" $2 \" (\" $5 \" used)\"}')\"\n    echo \"   Load: $(uptime | awk -F'load average:' '{print $2}')\"\n    \n    echo \"\"\n    echo \"📝 Recent Monitor Logs (last 10 lines):\"\n    if [ -f \"$LOG_FILE\" ]; then\n        tail -10 \"$LOG_FILE\"\n    else\n        echo \"   No logs found\"\n    fi\n    \n    echo \"\"\n    echo \"🚨 Recent Alerts (last 5):\"\n    if [ -f \"$ALERT_LOG\" ]; then\n        tail -5 \"$ALERT_LOG\"\n    else\n        echo \"   No alerts found\"\n    fi\n}\n\n# Function to show logs\nshow_logs() {\n    local lines=${1:-50}\n    \n    echo \"📝 Monitor Logs (last $lines lines):\"\n    echo \"==================================\"\n    if [ -f \"$LOG_FILE\" ]; then\n        tail -$lines \"$LOG_FILE\"\n    else\n        echo \"No logs found\"\n    fi\n}\n\n# Function to show alerts\nshow_alerts() {\n    local lines=${1:-20}\n    \n    echo \"🚨 Alert Logs (last $lines lines):\"\n    echo \"================================\"\n    if [ -f \"$ALERT_LOG\" ]; then\n        tail -$lines \"$ALERT_LOG\"\n    else\n        echo \"No alerts found\"\n    fi\n}\n\n# Function to install as systemd service\ninstall_service() {\n    log_message \"INFO\" \"Installing AlgoFactory Monitor as systemd service\"\n    \n    # Create systemd service file\n    sudo tee /etc/systemd/system/algofactory-monitor.service > /dev/null << EOF\n[Unit]\nDescription=AlgoFactory 24/7 Instance Monitor\nAfter=network.target\nWants=network.target\n\n[Service]\nType=simple\nUser=ubuntu\nGroup=ubuntu\nWorkingDirectory=$SCRIPT_DIR\nExecStart=$SCRIPT_DIR/monitor_instances.sh run\nRestart=always\nRestartSec=10\nStandardOutput=journal\nStandardError=journal\n\n[Install]\nWantedBy=multi-user.target\nEOF\n    \n    # Reload systemd and enable service\n    sudo systemctl daemon-reload\n    sudo systemctl enable algofactory-monitor.service\n    \n    log_message \"INFO\" \"Service installed successfully\"\n    echo \"✅ AlgoFactory Monitor installed as systemd service\"\n    echo \"\"\n    echo \"🚀 To start the service:\"\n    echo \"   sudo systemctl start algofactory-monitor\"\n    echo \"\"\n    echo \"📊 To check status:\"\n    echo \"   sudo systemctl status algofactory-monitor\"\n    echo \"\"\n    echo \"📝 To view logs:\"\n    echo \"   sudo journalctl -u algofactory-monitor -f\"\n}\n\n# Function to display usage\nusage() {\n    echo \"AlgoFactory 24/7 Instance Monitor\"\n    echo \"================================\"\n    echo \"\"\n    echo \"Usage: $0 <command>\"\n    echo \"\"\n    echo \"Commands:\"\n    echo \"  run              Start continuous monitoring (foreground)\"\n    echo \"  status           Show current status of all instances\"\n    echo \"  logs [lines]     Show monitor logs (default: 50 lines)\"\n    echo \"  alerts [lines]   Show alert logs (default: 20 lines)\"\n    echo \"  install          Install as systemd service for 24/7 operation\"\n    echo \"  check            Run single monitoring check\"\n    echo \"\"\n    echo \"Examples:\"\n    echo \"  $0 run           # Start monitoring in foreground\"\n    echo \"  $0 status        # Show current status\"\n    echo \"  $0 logs 100      # Show last 100 log lines\"\n    echo \"  $0 install       # Install as system service\"\n    echo \"\"\n    exit 1\n}\n\n# Main script logic\ncase \"${1:-}\" in\n    run)\n        run_monitor\n        ;;\n    status)\n        show_status\n        ;;\n    logs)\n        show_logs \"${2:-50}\"\n        ;;\n    alerts)\n        show_alerts \"${2:-20}\"\n        ;;\n    install)\n        install_service\n        ;;\n    check)\n        monitor_instances\n        ;;\n    *)\n        usage\n        ;;\nesac\n"}