{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/admin/templates/tasks/manage.html"}, "modifiedCode": "{% extends \"base.html\" %}\n\n{% block title %}Task Manager - AlgoFactory Admin{% endblock %}\n{% block page_title %}Task Manager{% endblock %}\n\n{% block content %}\n<div class=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n    <div class=\"stat bg-base-200 rounded-xl shadow-sm\">\n        <div class=\"stat-figure text-info\">\n            <i class=\"fas fa-tasks text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">Active Tasks</div>\n        <div class=\"stat-value text-info\">3</div>\n        <div class=\"stat-desc\">Currently running</div>\n    </div>\n    \n    <div class=\"stat bg-base-200 rounded-xl shadow-sm\">\n        <div class=\"stat-figure text-warning\">\n            <i class=\"fas fa-clock text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">Scheduled</div>\n        <div class=\"stat-value text-warning\">7</div>\n        <div class=\"stat-desc\">Pending execution</div>\n    </div>\n    \n    <div class=\"stat bg-base-200 rounded-xl shadow-sm\">\n        <div class=\"stat-figure text-success\">\n            <i class=\"fas fa-check-circle text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">Completed</div>\n        <div class=\"stat-value text-success\">45</div>\n        <div class=\"stat-desc\">Today</div>\n    </div>\n    \n    <div class=\"stat bg-base-200 rounded-xl shadow-sm\">\n        <div class=\"stat-figure text-error\">\n            <i class=\"fas fa-times-circle text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">Failed</div>\n        <div class=\"stat-value text-error\">2</div>\n        <div class=\"stat-desc\">Require attention</div>\n    </div>\n</div>\n\n<div class=\"card bg-base-200 shadow-sm\">\n    <div class=\"card-body\">\n        <div class=\"flex justify-between items-center mb-6\">\n            <h2 class=\"card-title\">\n                <i class=\"fas fa-list text-primary\"></i>\n                Task Queue\n            </h2>\n            <button class=\"btn btn-primary\">\n                <i class=\"fas fa-plus\"></i>\n                New Task\n            </button>\n        </div>\n        \n        <div class=\"overflow-x-auto\">\n            <table class=\"table table-zebra w-full\">\n                <thead>\n                    <tr>\n                        <th>Task</th>\n                        <th>Status</th>\n                        <th>Progress</th>\n                        <th>Started</th>\n                        <th>Actions</th>\n                    </tr>\n                </thead>\n                <tbody>\n                    <tr>\n                        <td>\n                            <div class=\"font-bold\">System Backup</div>\n                            <div class=\"text-sm text-base-content/70\">Full system backup</div>\n                        </td>\n                        <td><div class=\"badge badge-info\">Running</div></td>\n                        <td>\n                            <div class=\"flex items-center gap-2\">\n                                <progress class=\"progress progress-primary w-20\" value=\"65\" max=\"100\"></progress>\n                                <span class=\"text-sm\">65%</span>\n                            </div>\n                        </td>\n                        <td>2 min ago</td>\n                        <td>\n                            <button class=\"btn btn-error btn-xs\">\n                                <i class=\"fas fa-stop\"></i>\n                            </button>\n                        </td>\n                    </tr>\n                    <tr>\n                        <td>\n                            <div class=\"font-bold\">SSL Renewal</div>\n                            <div class=\"text-sm text-base-content/70\">Renew expiring certificates</div>\n                        </td>\n                        <td><div class=\"badge badge-warning\">Scheduled</div></td>\n                        <td>\n                            <div class=\"text-sm\">Waiting...</div>\n                        </td>\n                        <td>In 2 hours</td>\n                        <td>\n                            <button class=\"btn btn-primary btn-xs\">\n                                <i class=\"fas fa-play\"></i>\n                            </button>\n                        </td>\n                    </tr>\n                </tbody>\n            </table>\n        </div>\n    </div>\n</div>\n{% endblock %}\n"}