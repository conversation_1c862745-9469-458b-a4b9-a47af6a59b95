{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/test_websocket.py"}, "modifiedCode": "#!/usr/bin/env python3\n\n\"\"\"\nWebSocket Connection Test for AlgoFactory Instances\nTests WebSocket connectivity for all running instances\n\"\"\"\n\nimport asyncio\nimport websockets\nimport json\nimport sys\n\nasync def test_websocket_connection(host, port, instance_id):\n    \"\"\"Test WebSocket connection to a specific instance\"\"\"\n    uri = f\"ws://{host}:{port}\"\n    \n    try:\n        print(f\"🔌 Testing WebSocket connection to instance {instance_id} at {uri}\")\n        \n        # Connect to WebSocket server\n        async with websockets.connect(uri, timeout=10) as websocket:\n            print(f\"✅ Connected to instance {instance_id} WebSocket server\")\n            \n            # Send a test message\n            test_message = {\n                \"action\": \"get_supported_brokers\"\n            }\n            \n            await websocket.send(json.dumps(test_message))\n            print(f\"📤 Sent test message to instance {instance_id}\")\n            \n            # Wait for response\n            try:\n                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)\n                response_data = json.loads(response)\n                print(f\"📥 Received response from instance {instance_id}: {response_data.get('type', 'unknown')}\")\n                \n                if response_data.get('type') == 'supported_brokers':\n                    brokers = response_data.get('brokers', [])\n                    print(f\"🔧 Supported brokers: {len(brokers)} brokers available\")\n                    return True\n                else:\n                    print(f\"⚠️  Unexpected response type: {response_data.get('type')}\")\n                    return True  # Still connected, just different response\n                    \n            except asyncio.TimeoutError:\n                print(f\"⏰ Timeout waiting for response from instance {instance_id}\")\n                return False\n                \n    except websockets.exceptions.ConnectionRefused:\n        print(f\"❌ Connection refused to instance {instance_id} at {uri}\")\n        return False\n    except websockets.exceptions.InvalidURI:\n        print(f\"❌ Invalid URI for instance {instance_id}: {uri}\")\n        return False\n    except Exception as e:\n        print(f\"❌ Error connecting to instance {instance_id}: {e}\")\n        return False\n\nasync def test_all_instances():\n    \"\"\"Test WebSocket connections for all instances\"\"\"\n    instances = [\n        {\"id\": \"8010\", \"port\": 20010},\n        {\"id\": \"8011\", \"port\": 20011},\n        {\"id\": \"8012\", \"port\": 20012}\n    ]\n    \n    print(\"🚀 AlgoFactory WebSocket Connection Test\")\n    print(\"=\" * 50)\n    \n    results = []\n    \n    for instance in instances:\n        instance_id = instance[\"id\"]\n        port = instance[\"port\"]\n        \n        result = await test_websocket_connection(\"localhost\", port, instance_id)\n        results.append({\n            \"instance\": instance_id,\n            \"port\": port,\n            \"success\": result\n        })\n        \n        print()  # Add spacing between tests\n    \n    # Summary\n    print(\"📊 Test Results Summary:\")\n    print(\"=\" * 30)\n    \n    success_count = 0\n    for result in results:\n        status = \"✅ PASS\" if result[\"success\"] else \"❌ FAIL\"\n        print(f\"Instance {result['instance']} (port {result['port']}): {status}\")\n        if result[\"success\"]:\n            success_count += 1\n    \n    print()\n    print(f\"🎯 Overall Result: {success_count}/{len(instances)} instances passed\")\n    \n    if success_count == len(instances):\n        print(\"🎉 All WebSocket servers are working correctly!\")\n        return True\n    else:\n        print(\"⚠️  Some WebSocket servers have issues\")\n        return False\n\nasync def test_single_instance(instance_id):\n    \"\"\"Test a single instance WebSocket connection\"\"\"\n    port = int(instance_id) + 12000  # Calculate WebSocket port\n    \n    print(f\"🔌 Testing single instance {instance_id}\")\n    print(\"=\" * 40)\n    \n    result = await test_websocket_connection(\"localhost\", port, instance_id)\n    \n    if result:\n        print(f\"✅ Instance {instance_id} WebSocket is working correctly!\")\n    else:\n        print(f\"❌ Instance {instance_id} WebSocket has issues!\")\n    \n    return result\n\ndef main():\n    \"\"\"Main function\"\"\"\n    if len(sys.argv) > 1:\n        # Test specific instance\n        instance_id = sys.argv[1]\n        try:\n            result = asyncio.run(test_single_instance(instance_id))\n            sys.exit(0 if result else 1)\n        except KeyboardInterrupt:\n            print(\"\\n🛑 Test interrupted by user\")\n            sys.exit(1)\n    else:\n        # Test all instances\n        try:\n            result = asyncio.run(test_all_instances())\n            sys.exit(0 if result else 1)\n        except KeyboardInterrupt:\n            print(\"\\n🛑 Test interrupted by user\")\n            sys.exit(1)\n\nif __name__ == \"__main__\":\n    main()\n"}