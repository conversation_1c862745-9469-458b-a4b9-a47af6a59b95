{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/nginx_optimization.conf"}, "originalCode": "# AlgoFactory Nginx Optimization Configuration\n# Place this in /etc/nginx/conf.d/algofactory-optimization.conf\n\n# Rate Limiting Zones for AlgoFactory\nlimit_req_zone $binary_remote_addr zone=algofactory_api:10m rate=10r/s;\nlimit_req_zone $binary_remote_addr zone=algofactory_login:10m rate=5r/m;\nlimit_req_zone $binary_remote_addr zone=algofactory_general:10m rate=20r/s;\nlimit_req_zone $binary_remote_addr zone=algofactory_websocket:10m rate=50r/s;\n\n# Connection Limiting\nlimit_conn_zone $binary_remote_addr zone=algofactory_conn:10m;\n\n# Upstream definitions for load balancing (if needed)\nupstream algofactory_backend {\n    least_conn;\n    server 127.0.0.1:8010 max_fails=3 fail_timeout=30s;\n    server 127.0.0.1:8011 max_fails=3 fail_timeout=30s;\n    server 127.0.0.1:8012 max_fails=3 fail_timeout=30s;\n    keepalive 32;\n}\n\n# Cache zones\nproxy_cache_path /var/cache/nginx/algofactory levels=1:2 keys_zone=algofactory_cache:10m max_size=100m inactive=60m use_temp_path=off;\n\n# Map for WebSocket upgrade\nmap $http_upgrade $connection_upgrade {\n    default upgrade;\n    '' close;\n}\n\n# Security headers map\nmap $sent_http_content_type $security_headers {\n    ~*text/html \"nosniff\";\n    default \"\";\n}\n\n# Geo-blocking (optional - uncomment and configure as needed)\n# geo $blocked_country {\n#     default 0;\n#     # Block specific countries if needed\n#     # CN 1;  # China\n#     # RU 1;  # Russia\n# }\n\n# Log format for AlgoFactory\nlog_format algofactory_access '$remote_addr - $remote_user [$time_local] '\n                              '\"$request\" $status $body_bytes_sent '\n                              '\"$http_referer\" \"$http_user_agent\" '\n                              '\"$http_x_forwarded_for\" '\n                              'rt=$request_time uct=\"$upstream_connect_time\" '\n                              'uht=\"$upstream_header_time\" urt=\"$upstream_response_time\" '\n                              'instance=\"$upstream_addr\"';\n\n# Server block template for AlgoFactory instances\n# This is included by individual site configurations\n\n# Common security headers\nadd_header X-Frame-Options DENY always;\nadd_header X-Content-Type-Options nosniff always;\nadd_header X-XSS-Protection \"1; mode=block\" always;\nadd_header Referrer-Policy strict-origin-when-cross-origin always;\nadd_header Permissions-Policy \"camera=(), microphone=(), geolocation=(), payment=(), usb=(), screen-wake-lock=(), web-share=()\" always;\n\n# HSTS (only for HTTPS)\nadd_header Strict-Transport-Security \"max-age=31536000; includeSubDomains; preload\" always;\n\n# Hide server information\nserver_tokens off;\nmore_clear_headers Server;\n\n# Common location blocks for AlgoFactory\nlocation /health {\n    access_log off;\n    return 200 \"healthy\\n\";\n    add_header Content-Type text/plain;\n}\n\nlocation /nginx-status {\n    stub_status on;\n    access_log off;\n    allow 127.0.0.1;\n    deny all;\n}\n\n# Block common attack patterns\nlocation ~* \\.(php|asp|aspx|jsp)$ {\n    deny all;\n    access_log off;\n    log_not_found off;\n}\n\nlocation ~ /\\. {\n    deny all;\n    access_log off;\n    log_not_found off;\n}\n\nlocation ~ ~$ {\n    deny all;\n    access_log off;\n    log_not_found off;\n}\n\n# Block SQL injection attempts\nlocation ~* (union.*select|select.*union|select.*from|insert.*into|delete.*from|drop.*table) {\n    deny all;\n    access_log off;\n    log_not_found off;\n}\n\n# Block common bot patterns\nlocation ~* (bot|crawler|spider|scraper) {\n    limit_req zone=algofactory_general burst=5 nodelay;\n}\n\n# Favicon handling\nlocation = /favicon.ico {\n    log_not_found off;\n    access_log off;\n    expires 1y;\n    add_header Cache-Control \"public, immutable\";\n}\n\n# Robots.txt\nlocation = /robots.txt {\n    log_not_found off;\n    access_log off;\n    expires 1y;\n    add_header Cache-Control \"public, immutable\";\n}\n\n# Static files optimization\nlocation ~* \\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {\n    expires 1y;\n    add_header Cache-Control \"public, immutable\";\n    add_header Vary Accept-Encoding;\n    \n    # Enable compression\n    gzip_static on;\n    \n    # Security headers for static files\n    add_header X-Content-Type-Options nosniff;\n    add_header X-Frame-Options DENY;\n}\n\n# API endpoints rate limiting\nlocation /api/ {\n    limit_req zone=algofactory_api burst=20 nodelay;\n    limit_conn algofactory_conn 10;\n    \n    # API specific headers\n    add_header X-API-Version \"1.0\";\n    add_header X-RateLimit-Limit \"10\";\n    add_header X-RateLimit-Remaining $limit_req_status;\n}\n\n# Authentication endpoints\nlocation ~ ^/(auth|login|logout) {\n    limit_req zone=algofactory_login burst=5 nodelay;\n    limit_conn algofactory_conn 5;\n}\n\n# WebSocket endpoints\nlocation /socket.io/ {\n    limit_req zone=algofactory_websocket burst=100 nodelay;\n    \n    proxy_http_version 1.1;\n    proxy_set_header Upgrade $http_upgrade;\n    proxy_set_header Connection $connection_upgrade;\n    proxy_set_header Host $host;\n    proxy_set_header X-Real-IP $remote_addr;\n    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n    proxy_set_header X-Forwarded-Proto $scheme;\n    \n    # WebSocket specific timeouts\n    proxy_read_timeout 86400;\n    proxy_send_timeout 86400;\n    proxy_connect_timeout 60;\n}\n\n# Error pages\nerror_page 404 /404.html;\nerror_page 500 502 503 504 /50x.html;\n\nlocation = /404.html {\n    root /var/www/html;\n    internal;\n}\n\nlocation = /50x.html {\n    root /var/www/html;\n    internal;\n}\n\n# Monitoring and metrics\nlocation /metrics {\n    access_log off;\n    allow 127.0.0.1;\n    deny all;\n    return 200 \"# AlgoFactory Nginx Metrics\\n\";\n    add_header Content-Type text/plain;\n}\n", "modifiedCode": "# AlgoFactory Nginx Optimization Configuration\n# Place this in /etc/nginx/conf.d/algofactory-optimization.conf\n\n# Rate Limiting Zones for AlgoFactory\nlimit_req_zone $binary_remote_addr zone=algofactory_api:10m rate=10r/s;\nlimit_req_zone $binary_remote_addr zone=algofactory_login:10m rate=5r/m;\nlimit_req_zone $binary_remote_addr zone=algofactory_general:10m rate=20r/s;\nlimit_req_zone $binary_remote_addr zone=algofactory_websocket:10m rate=50r/s;\n\n# Connection Limiting\nlimit_conn_zone $binary_remote_addr zone=algofactory_conn:10m;\n\n# Upstream definitions for load balancing (if needed)\nupstream algofactory_backend {\n    least_conn;\n    server 127.0.0.1:8010 max_fails=3 fail_timeout=30s;\n    server 127.0.0.1:8011 max_fails=3 fail_timeout=30s;\n    server 127.0.0.1:8012 max_fails=3 fail_timeout=30s;\n    keepalive 32;\n}\n\n# Cache zones\nproxy_cache_path /var/cache/nginx/algofactory levels=1:2 keys_zone=algofactory_cache:10m max_size=100m inactive=60m use_temp_path=off;\n\n# Map for WebSocket upgrade\nmap $http_upgrade $connection_upgrade {\n    default upgrade;\n    '' close;\n}\n\n# Security headers map\nmap $sent_http_content_type $security_headers {\n    ~*text/html \"nosniff\";\n    default \"\";\n}\n\n# Geo-blocking (optional - uncomment and configure as needed)\n# geo $blocked_country {\n#     default 0;\n#     # Block specific countries if needed\n#     # CN 1;  # China\n#     # RU 1;  # Russia\n# }\n\n# Log format for AlgoFactory\nlog_format algofactory_access '$remote_addr - $remote_user [$time_local] '\n                              '\"$request\" $status $body_bytes_sent '\n                              '\"$http_referer\" \"$http_user_agent\" '\n                              '\"$http_x_forwarded_for\" '\n                              'rt=$request_time uct=\"$upstream_connect_time\" '\n                              'uht=\"$upstream_header_time\" urt=\"$upstream_response_time\" '\n                              'instance=\"$upstream_addr\"';\n\n# Server block template for AlgoFactory instances\n# This is included by individual site configurations\n\n# Common security headers\nadd_header X-Frame-Options DENY always;\nadd_header X-Content-Type-Options nosniff always;\nadd_header X-XSS-Protection \"1; mode=block\" always;\nadd_header Referrer-Policy strict-origin-when-cross-origin always;\nadd_header Permissions-Policy \"camera=(), microphone=(), geolocation=(), payment=(), usb=(), screen-wake-lock=(), web-share=()\" always;\n\n# HSTS (only for HTTPS)\nadd_header Strict-Transport-Security \"max-age=31536000; includeSubDomains; preload\" always;\n\n# Hide server information\nserver_tokens off;\n\n# Common location blocks for AlgoFactory\nlocation /health {\n    access_log off;\n    return 200 \"healthy\\n\";\n    add_header Content-Type text/plain;\n}\n\nlocation /nginx-status {\n    stub_status on;\n    access_log off;\n    allow 127.0.0.1;\n    deny all;\n}\n\n# Block common attack patterns\nlocation ~* \\.(php|asp|aspx|jsp)$ {\n    deny all;\n    access_log off;\n    log_not_found off;\n}\n\nlocation ~ /\\. {\n    deny all;\n    access_log off;\n    log_not_found off;\n}\n\nlocation ~ ~$ {\n    deny all;\n    access_log off;\n    log_not_found off;\n}\n\n# Block SQL injection attempts\nlocation ~* (union.*select|select.*union|select.*from|insert.*into|delete.*from|drop.*table) {\n    deny all;\n    access_log off;\n    log_not_found off;\n}\n\n# Block common bot patterns\nlocation ~* (bot|crawler|spider|scraper) {\n    limit_req zone=algofactory_general burst=5 nodelay;\n}\n\n# Favicon handling\nlocation = /favicon.ico {\n    log_not_found off;\n    access_log off;\n    expires 1y;\n    add_header Cache-Control \"public, immutable\";\n}\n\n# Robots.txt\nlocation = /robots.txt {\n    log_not_found off;\n    access_log off;\n    expires 1y;\n    add_header Cache-Control \"public, immutable\";\n}\n\n# Static files optimization\nlocation ~* \\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {\n    expires 1y;\n    add_header Cache-Control \"public, immutable\";\n    add_header Vary Accept-Encoding;\n    \n    # Enable compression\n    gzip_static on;\n    \n    # Security headers for static files\n    add_header X-Content-Type-Options nosniff;\n    add_header X-Frame-Options DENY;\n}\n\n# API endpoints rate limiting\nlocation /api/ {\n    limit_req zone=algofactory_api burst=20 nodelay;\n    limit_conn algofactory_conn 10;\n    \n    # API specific headers\n    add_header X-API-Version \"1.0\";\n    add_header X-RateLimit-Limit \"10\";\n    add_header X-RateLimit-Remaining $limit_req_status;\n}\n\n# Authentication endpoints\nlocation ~ ^/(auth|login|logout) {\n    limit_req zone=algofactory_login burst=5 nodelay;\n    limit_conn algofactory_conn 5;\n}\n\n# WebSocket endpoints\nlocation /socket.io/ {\n    limit_req zone=algofactory_websocket burst=100 nodelay;\n    \n    proxy_http_version 1.1;\n    proxy_set_header Upgrade $http_upgrade;\n    proxy_set_header Connection $connection_upgrade;\n    proxy_set_header Host $host;\n    proxy_set_header X-Real-IP $remote_addr;\n    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n    proxy_set_header X-Forwarded-Proto $scheme;\n    \n    # WebSocket specific timeouts\n    proxy_read_timeout 86400;\n    proxy_send_timeout 86400;\n    proxy_connect_timeout 60;\n}\n\n# Error pages\nerror_page 404 /404.html;\nerror_page 500 502 503 504 /50x.html;\n\nlocation = /404.html {\n    root /var/www/html;\n    internal;\n}\n\nlocation = /50x.html {\n    root /var/www/html;\n    internal;\n}\n\n# Monitoring and metrics\nlocation /metrics {\n    access_log off;\n    allow 127.0.0.1;\n    deny all;\n    return 200 \"# AlgoFactory Nginx Metrics\\n\";\n    add_header Content-Type text/plain;\n}\n"}