{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/start.sh"}, "originalCode": "#!/bin/bash\n\n# AlgoFactory Robust Startup Script\n# This script automatically starts app.py with full error handling and recovery\n\nset -e  # Exit on any error\n\n# Configuration\nPROJECT_DIR=\"/home/<USER>/myproject/algofactory\"\nSHARED_VENV=\"/home/<USER>/shared-venv\"\nAPP_FILE=\"app.py\"\nLOG_DIR=\"$PROJECT_DIR/logs\"\nPID_FILE=\"$PROJECT_DIR/app.pid\"\nMAX_RETRIES=5\nRETRY_DELAY=10\n\n# Colors for output\nRED='\\033[0;31m'\nGREEN='\\033[0;32m'\nYELLOW='\\033[1;33m'\nBLUE='\\033[0;34m'\nNC='\\033[0m' # No Color\n\n# Logging function\nlog() {\n    echo -e \"${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1\"\n}\n\nerror() {\n    echo -e \"${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1\" >&2\n}\n\nsuccess() {\n    echo -e \"${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1\"\n}\n\nwarning() {\n    echo -e \"${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1\"\n}\n\n# Function to check if app is running\nis_app_running() {\n    if [ -f \"$PID_FILE\" ]; then\n        local pid=$(cat \"$PID_FILE\")\n        if ps -p \"$pid\" > /dev/null 2>&1; then\n            return 0\n        else\n            rm -f \"$PID_FILE\"\n            return 1\n        fi\n    fi\n    return 1\n}\n\n# Function to stop the app\nstop_app() {\n    if [ -f \"$PID_FILE\" ]; then\n        local pid=$(cat \"$PID_FILE\")\n        log \"Stopping application (PID: $pid)...\"\n        kill -TERM \"$pid\" 2>/dev/null || true\n        sleep 5\n        if ps -p \"$pid\" > /dev/null 2>&1; then\n            warning \"Application didn't stop gracefully, forcing kill...\"\n            kill -KILL \"$pid\" 2>/dev/null || true\n        fi\n        rm -f \"$PID_FILE\"\n        success \"Application stopped\"\n    fi\n}\n\n# Function to setup environment\nsetup_environment() {\n    log \"Setting up environment...\"\n\n    # Change to project directory\n    cd \"$PROJECT_DIR\" || {\n        error \"Failed to change to project directory: $PROJECT_DIR\"\n        exit 1\n    }\n\n    # Create necessary directories with full permissions\n    log \"Creating necessary directories...\"\n    mkdir -p db logs tmp static/uploads\n\n    # Set full permissions (777) for all necessary directories\n    chmod -R 777 db logs tmp static 2>/dev/null || {\n        warning \"Could not set permissions on some directories (may be mounted volumes)\"\n    }\n\n    # Check if shared virtual environment exists\n    if [ ! -d \"$SHARED_VENV\" ]; then\n        error \"Shared virtual environment not found at: $SHARED_VENV\"\n        log \"Creating shared virtual environment...\"\n        python3 -m venv \"$SHARED_VENV\" || {\n            error \"Failed to create virtual environment\"\n            exit 1\n        }\n    fi\n\n    # Check if virtual environment has Python\n    if [ ! -f \"$SHARED_VENV/bin/python\" ]; then\n        error \"Python not found in virtual environment: $SHARED_VENV/bin/python\"\n        exit 1\n    fi\n\n    success \"Environment setup completed\"\n}\n\n# Function to install/update dependencies\ninstall_dependencies() {\n    log \"Installing/updating dependencies...\"\n\n    # Activate virtual environment\n    source \"$SHARED_VENV/bin/activate\" || {\n        error \"Failed to activate virtual environment\"\n        exit 1\n    }\n\n    # Upgrade pip\n    pip install --upgrade pip > /dev/null 2>&1 || {\n        warning \"Failed to upgrade pip\"\n    }\n\n    # Install requirements if file exists\n    if [ -f \"requirements.txt\" ]; then\n        log \"Installing requirements from requirements.txt...\"\n        pip install -r requirements.txt || {\n            error \"Failed to install requirements\"\n            exit 1\n        }\n    fi\n\n    # Install additional packages that might be needed\n    pip install gunicorn eventlet python-dotenv > /dev/null 2>&1 || {\n        warning \"Failed to install some additional packages\"\n    }\n\n    success \"Dependencies installed successfully\"\n}\n\n# Function to start the application\nstart_app() {\n    local retry_count=0\n\n    while [ $retry_count -lt $MAX_RETRIES ]; do\n        log \"Starting AlgoFactory application (attempt $((retry_count + 1))/$MAX_RETRIES)...\"\n\n        # Activate virtual environment\n        source \"$SHARED_VENV/bin/activate\" || {\n            error \"Failed to activate virtual environment\"\n            exit 1\n        }\n\n        # Check if app.py exists\n        if [ ! -f \"$APP_FILE\" ]; then\n            error \"Application file not found: $APP_FILE\"\n            exit 1\n        fi\n\n        # Start the application with gunicorn\n        nohup \"$SHARED_VENV/bin/gunicorn\" \\\n            --bind=0.0.0.0:5000 \\\n            --worker-class=eventlet \\\n            --workers=1 \\\n            --timeout=120 \\\n            --keep-alive=2 \\\n            --max-requests=1000 \\\n            --max-requests-jitter=100 \\\n            --preload \\\n            --log-level=info \\\n            --access-logfile=\"$LOG_DIR/access.log\" \\\n            --error-logfile=\"$LOG_DIR/error.log\" \\\n            --capture-output \\\n            --enable-stdio-inheritance \\\n            app:app > \"$LOG_DIR/app.log\" 2>&1 &\n\n        local app_pid=$!\n        echo $app_pid > \"$PID_FILE\"\n\n        # Wait a moment and check if the app started successfully\n        sleep 5\n\n        if ps -p \"$app_pid\" > /dev/null 2>&1; then\n            success \"AlgoFactory application started successfully (PID: $app_pid)\"\n            success \"Application is running on http://0.0.0.0:5000\"\n            success \"Logs are available in: $LOG_DIR/\"\n            return 0\n        else\n            error \"Application failed to start (attempt $((retry_count + 1)))\"\n            rm -f \"$PID_FILE\"\n\n            # Show last few lines of error log\n            if [ -f \"$LOG_DIR/error.log\" ]; then\n                error \"Last few lines from error log:\"\n                tail -10 \"$LOG_DIR/error.log\" | while read line; do\n                    error \"  $line\"\n                done\n            fi\n\n            retry_count=$((retry_count + 1))\n            if [ $retry_count -lt $MAX_RETRIES ]; then\n                warning \"Retrying in $RETRY_DELAY seconds...\"\n                sleep $RETRY_DELAY\n            fi\n        fi\n    done\n\n    error \"Failed to start application after $MAX_RETRIES attempts\"\n    exit 1\n}\n\n# Function to monitor the application\nmonitor_app() {\n    log \"Starting application monitor...\"\n\n    # Start the application first\n    if ! is_app_running; then\n        start_app\n    fi\n\n    # Monitor loop\n    while true; do\n        if ! is_app_running; then\n            warning \"Application is not running, attempting to restart...\"\n            start_app\n        fi\n        sleep 30  # Check every 30 seconds\n    done\n}\n\n# Main execution\nmain() {\n    log \"=== AlgoFactory Startup Script ===\"\n    log \"Project Directory: $PROJECT_DIR\"\n    log \"Shared Virtual Environment: $SHARED_VENV\"\n    log \"Application File: $APP_FILE\"\n\n    # Handle command line arguments\n    case \"${1:-start}\" in\n        \"start\")\n            if is_app_running; then\n                warning \"Application is already running\"\n                exit 0\n            fi\n            setup_environment\n            install_dependencies\n            start_app\n            ;;\n        \"stop\")\n            stop_app\n            ;;\n        \"restart\")\n            stop_app\n            setup_environment\n            install_dependencies\n            start_app\n            ;;\n        \"status\")\n            if is_app_running; then\n                success \"Application is running (PID: $(cat $PID_FILE))\"\n            else\n                warning \"Application is not running\"\n            fi\n            ;;\n        \"monitor\")\n            setup_environment\n            install_dependencies\n            monitor_app\n            ;;\n        \"logs\")\n            if [ -f \"$LOG_DIR/app.log\" ]; then\n                tail -f \"$LOG_DIR/app.log\"\n            else\n                error \"Log file not found: $LOG_DIR/app.log\"\n            fi\n            ;;\n        *)\n            echo \"Usage: $0 {start|stop|restart|status|monitor|logs}\"\n            echo \"  start   - Start the application\"\n            echo \"  stop    - Stop the application\"\n            echo \"  restart - Restart the application\"\n            echo \"  status  - Check application status\"\n            echo \"  monitor - Start with continuous monitoring\"\n            echo \"  logs    - Show application logs\"\n            exit 1\n            ;;\n    esac\n}\n\n# Trap signals for graceful shutdown\ntrap 'stop_app; exit 0' SIGTERM SIGINT\n\n# Run main function\nmain \"$@\"\n", "modifiedCode": "#!/bin/bash\n\n# AlgoFactory Robust Startup Script\n# This script automatically starts app.py with full error handling and recovery\n\nset -e  # Exit on any error\n\n# Configuration\nPROJECT_DIR=\"/home/<USER>/myproject/algofactory\"\nSHARED_VENV=\"/home/<USER>/shared-venv\"\nAPP_FILE=\"app.py\"\nLOG_DIR=\"$PROJECT_DIR/logs\"\nPID_FILE=\"$PROJECT_DIR/app.pid\"\nMAX_RETRIES=5\nRETRY_DELAY=10\n\n# Colors for output\nRED='\\033[0;31m'\nGREEN='\\033[0;32m'\nYELLOW='\\033[1;33m'\nBLUE='\\033[0;34m'\nNC='\\033[0m' # No Color\n\n# Logging function\nlog() {\n    echo -e \"${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1\"\n}\n\nerror() {\n    echo -e \"${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1\" >&2\n}\n\nsuccess() {\n    echo -e \"${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1\"\n}\n\nwarning() {\n    echo -e \"${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1\"\n}\n\n# Function to check if app is running\nis_app_running() {\n    if [ -f \"$PID_FILE\" ]; then\n        local pid=$(cat \"$PID_FILE\")\n        if ps -p \"$pid\" > /dev/null 2>&1; then\n            return 0\n        else\n            rm -f \"$PID_FILE\"\n            return 1\n        fi\n    fi\n    return 1\n}\n\n# Function to stop the app\nstop_app() {\n    if [ -f \"$PID_FILE\" ]; then\n        local pid=$(cat \"$PID_FILE\")\n        log \"Stopping application (PID: $pid)...\"\n        kill -TERM \"$pid\" 2>/dev/null || true\n        sleep 5\n        if ps -p \"$pid\" > /dev/null 2>&1; then\n            warning \"Application didn't stop gracefully, forcing kill...\"\n            kill -KILL \"$pid\" 2>/dev/null || true\n        fi\n        rm -f \"$PID_FILE\"\n        success \"Application stopped\"\n    fi\n}\n\n# Function to setup environment\nsetup_environment() {\n    log \"Setting up environment...\"\n\n    # Change to project directory\n    cd \"$PROJECT_DIR\" || {\n        error \"Failed to change to project directory: $PROJECT_DIR\"\n        exit 1\n    }\n\n    # Create necessary directories with full permissions\n    log \"Creating necessary directories...\"\n    mkdir -p db logs tmp static/uploads\n\n    # Set full permissions (777) for all necessary directories\n    chmod -R 777 db logs tmp static 2>/dev/null || {\n        warning \"Could not set permissions on some directories (may be mounted volumes)\"\n    }\n\n    # Check if shared virtual environment exists\n    if [ ! -d \"$SHARED_VENV\" ]; then\n        error \"Shared virtual environment not found at: $SHARED_VENV\"\n        log \"Creating shared virtual environment...\"\n        python3 -m venv \"$SHARED_VENV\" || {\n            error \"Failed to create virtual environment\"\n            exit 1\n        }\n    fi\n\n    # Check if virtual environment has Python\n    if [ ! -f \"$SHARED_VENV/bin/python\" ]; then\n        error \"Python not found in virtual environment: $SHARED_VENV/bin/python\"\n        exit 1\n    fi\n\n    success \"Environment setup completed\"\n}\n\n# Function to install/update dependencies\ninstall_dependencies() {\n    log \"Installing/updating dependencies...\"\n\n    # Activate virtual environment\n    source \"$SHARED_VENV/bin/activate\" || {\n        error \"Failed to activate virtual environment\"\n        exit 1\n    }\n\n    # Upgrade pip\n    pip install --upgrade pip > /dev/null 2>&1 || {\n        warning \"Failed to upgrade pip\"\n    }\n\n    # Install requirements if file exists\n    if [ -f \"requirements.txt\" ]; then\n        log \"Installing requirements from requirements.txt...\"\n        pip install -r requirements.txt || {\n            error \"Failed to install requirements\"\n            exit 1\n        }\n    fi\n\n    # Install additional packages that might be needed\n    pip install gunicorn eventlet python-dotenv > /dev/null 2>&1 || {\n        warning \"Failed to install some additional packages\"\n    }\n\n    success \"Dependencies installed successfully\"\n}\n\n# Function to start the application\nstart_app() {\n    local retry_count=0\n\n    while [ $retry_count -lt $MAX_RETRIES ]; do\n        log \"Starting AlgoFactory application (attempt $((retry_count + 1))/$MAX_RETRIES)...\"\n\n        # Activate virtual environment\n        source \"$SHARED_VENV/bin/activate\" || {\n            error \"Failed to activate virtual environment\"\n            exit 1\n        }\n\n        # Check if app.py exists\n        if [ ! -f \"$APP_FILE\" ]; then\n            error \"Application file not found: $APP_FILE\"\n            exit 1\n        fi\n\n        # Start the application with gunicorn\n        nohup \"$SHARED_VENV/bin/gunicorn\" \\\n            --bind=0.0.0.0:5000 \\\n            --worker-class=eventlet \\\n            --workers=1 \\\n            --timeout=120 \\\n            --keep-alive=2 \\\n            --max-requests=1000 \\\n            --max-requests-jitter=100 \\\n            --preload \\\n            --log-level=info \\\n            --access-logfile=\"$LOG_DIR/access.log\" \\\n            --error-logfile=\"$LOG_DIR/error.log\" \\\n            --capture-output \\\n            --enable-stdio-inheritance \\\n            app:app > \"$LOG_DIR/app.log\" 2>&1 &\n\n        local app_pid=$!\n        echo $app_pid > \"$PID_FILE\"\n\n        # Wait a moment and check if the app started successfully\n        sleep 5\n\n        if ps -p \"$app_pid\" > /dev/null 2>&1; then\n            success \"AlgoFactory application started successfully (PID: $app_pid)\"\n            success \"Application is running on http://0.0.0.0:5000\"\n            success \"Logs are available in: $LOG_DIR/\"\n            return 0\n        else\n            error \"Application failed to start (attempt $((retry_count + 1)))\"\n            rm -f \"$PID_FILE\"\n\n            # Show last few lines of error log\n            if [ -f \"$LOG_DIR/error.log\" ]; then\n                error \"Last few lines from error log:\"\n                tail -10 \"$LOG_DIR/error.log\" | while read line; do\n                    error \"  $line\"\n                done\n            fi\n\n            retry_count=$((retry_count + 1))\n            if [ $retry_count -lt $MAX_RETRIES ]; then\n                warning \"Retrying in $RETRY_DELAY seconds...\"\n                sleep $RETRY_DELAY\n            fi\n        fi\n    done\n\n    error \"Failed to start application after $MAX_RETRIES attempts\"\n    exit 1\n}\n\n# Function to monitor the application\nmonitor_app() {\n    log \"Starting application monitor...\"\n\n    # Start the application first\n    if ! is_app_running; then\n        start_app\n    fi\n\n    # Monitor loop\n    while true; do\n        if ! is_app_running; then\n            warning \"Application is not running, attempting to restart...\"\n            start_app\n        fi\n        sleep 30  # Check every 30 seconds\n    done\n}\n\n# Main execution\nmain() {\n    log \"=== AlgoFactory Startup Script ===\"\n    log \"Project Directory: $PROJECT_DIR\"\n    log \"Shared Virtual Environment: $SHARED_VENV\"\n    log \"Application File: $APP_FILE\"\n\n    # Handle command line arguments\n    case \"${1:-start}\" in\n        \"start\")\n            if is_app_running; then\n                warning \"Application is already running\"\n                exit 0\n            fi\n            setup_environment\n            install_dependencies\n            start_app\n            ;;\n        \"stop\")\n            stop_app\n            ;;\n        \"restart\")\n            stop_app\n            setup_environment\n            install_dependencies\n            start_app\n            ;;\n        \"status\")\n            if is_app_running; then\n                success \"Application is running (PID: $(cat $PID_FILE))\"\n            else\n                warning \"Application is not running\"\n            fi\n            ;;\n        \"monitor\")\n            setup_environment\n            install_dependencies\n            monitor_app\n            ;;\n        \"logs\")\n            if [ -f \"$LOG_DIR/app.log\" ]; then\n                tail -f \"$LOG_DIR/app.log\"\n            else\n                error \"Log file not found: $LOG_DIR/app.log\"\n            fi\n            ;;\n        *)\n            echo \"Usage: $0 {start|stop|restart|status|monitor|logs}\"\n            echo \"  start   - Start the application\"\n            echo \"  stop    - Stop the application\"\n            echo \"  restart - Restart the application\"\n            echo \"  status  - Check application status\"\n            echo \"  monitor - Start with continuous monitoring\"\n            echo \"  logs    - Show application logs\"\n            exit 1\n            ;;\n    esac\n}\n\n# Trap signals for graceful shutdown\ntrap 'stop_app; exit 0' SIGTERM SIGINT\n\n# Run main function\nmain \"$@\"\n"}