{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/monitoring.py"}, "originalCode": "#!/usr/bin/env python3\n\"\"\"\nAlgoFactory Monitoring Script\nThis script provides various monitoring capabilities for the AlgoFactory application.\n\"\"\"\n\nimport requests\nimport json\nimport time\nimport psutil\nimport os\nimport sys\nfrom datetime import datetime\nimport subprocess\n\nclass AlgoFactoryMonitor:\n    def __init__(self, base_url=\"http://localhost:5000\"):\n        self.base_url = base_url\n        self.app_pid_file = \"/home/<USER>/myproject/algofactory/app.pid\"\n        \n    def check_application_health(self):\n        \"\"\"Check if the application is responding\"\"\"\n        try:\n            response = requests.get(f\"{self.base_url}/\", timeout=10)\n            return {\n                \"status\": \"healthy\" if response.status_code == 200 else \"unhealthy\",\n                \"status_code\": response.status_code,\n                \"response_time\": response.elapsed.total_seconds(),\n                \"timestamp\": datetime.now().isoformat()\n            }\n        except requests.exceptions.RequestException as e:\n            return {\n                \"status\": \"unhealthy\",\n                \"error\": str(e),\n                \"timestamp\": datetime.now().isoformat()\n            }\n    \n    def check_process_status(self):\n        \"\"\"Check if the application process is running\"\"\"\n        try:\n            if os.path.exists(self.app_pid_file):\n                with open(self.app_pid_file, 'r') as f:\n                    pid = int(f.read().strip())\n                \n                if psutil.pid_exists(pid):\n                    process = psutil.Process(pid)\n                    return {\n                        \"status\": \"running\",\n                        \"pid\": pid,\n                        \"cpu_percent\": process.cpu_percent(),\n                        \"memory_percent\": process.memory_percent(),\n                        \"memory_info\": process.memory_info()._asdict(),\n                        \"create_time\": datetime.fromtimestamp(process.create_time()).isoformat(),\n                        \"timestamp\": datetime.now().isoformat()\n                    }\n                else:\n                    return {\n                        \"status\": \"not_running\",\n                        \"error\": \"PID exists but process not found\",\n                        \"timestamp\": datetime.now().isoformat()\n                    }\n            else:\n                return {\n                    \"status\": \"not_running\",\n                    \"error\": \"PID file not found\",\n                    \"timestamp\": datetime.now().isoformat()\n                }\n        except Exception as e:\n            return {\n                \"status\": \"error\",\n                \"error\": str(e),\n                \"timestamp\": datetime.now().isoformat()\n            }\n    \n    def check_system_resources(self):\n        \"\"\"Check system resource usage\"\"\"\n        try:\n            cpu_percent = psutil.cpu_percent(interval=1)\n            memory = psutil.virtual_memory()\n            disk = psutil.disk_usage('/')\n            \n            return {\n                \"cpu_percent\": cpu_percent,\n                \"memory\": {\n                    \"total\": memory.total,\n                    \"available\": memory.available,\n                    \"percent\": memory.percent,\n                    \"used\": memory.used,\n                    \"free\": memory.free\n                },\n                \"disk\": {\n                    \"total\": disk.total,\n                    \"used\": disk.used,\n                    \"free\": disk.free,\n                    \"percent\": (disk.used / disk.total) * 100\n                },\n                \"timestamp\": datetime.now().isoformat()\n            }\n        except Exception as e:\n            return {\n                \"error\": str(e),\n                \"timestamp\": datetime.now().isoformat()\n            }\n    \n    def check_nginx_status(self):\n        \"\"\"Check Nginx status\"\"\"\n        try:\n            result = subprocess.run(['sudo', 'systemctl', 'is-active', 'nginx'], \n                                  capture_output=True, text=True)\n            return {\n                \"status\": result.stdout.strip(),\n                \"active\": result.stdout.strip() == \"active\",\n                \"timestamp\": datetime.now().isoformat()\n            }\n        except Exception as e:\n            return {\n                \"status\": \"error\",\n                \"error\": str(e),\n                \"timestamp\": datetime.now().isoformat()\n            }\n    \n    def get_full_status(self):\n        \"\"\"Get comprehensive status report\"\"\"\n        return {\n            \"application\": self.check_application_health(),\n            \"process\": self.check_process_status(),\n            \"system\": self.check_system_resources(),\n            \"nginx\": self.check_nginx_status(),\n            \"overall_timestamp\": datetime.now().isoformat()\n        }\n    \n    def send_to_external_monitor(self, webhook_url, status_data):\n        \"\"\"Send status data to external monitoring service\"\"\"\n        try:\n            headers = {'Content-Type': 'application/json'}\n            response = requests.post(webhook_url, \n                                   data=json.dumps(status_data), \n                                   headers=headers, \n                                   timeout=30)\n            return {\n                \"success\": response.status_code == 200,\n                \"status_code\": response.status_code,\n                \"timestamp\": datetime.now().isoformat()\n            }\n        except Exception as e:\n            return {\n                \"success\": False,\n                \"error\": str(e),\n                \"timestamp\": datetime.now().isoformat()\n            }\n\ndef main():\n    monitor = AlgoFactoryMonitor()\n    \n    if len(sys.argv) > 1:\n        command = sys.argv[1]\n        \n        if command == \"health\":\n            result = monitor.check_application_health()\n            print(json.dumps(result, indent=2))\n            sys.exit(0 if result[\"status\"] == \"healthy\" else 1)\n            \n        elif command == \"process\":\n            result = monitor.check_process_status()\n            print(json.dumps(result, indent=2))\n            sys.exit(0 if result[\"status\"] == \"running\" else 1)\n            \n        elif command == \"system\":\n            result = monitor.check_system_resources()\n            print(json.dumps(result, indent=2))\n            \n        elif command == \"nginx\":\n            result = monitor.check_nginx_status()\n            print(json.dumps(result, indent=2))\n            sys.exit(0 if result[\"active\"] else 1)\n            \n        elif command == \"full\":\n            result = monitor.get_full_status()\n            print(json.dumps(result, indent=2))\n            \n            # Determine overall health\n            app_healthy = result[\"application\"][\"status\"] == \"healthy\"\n            process_running = result[\"process\"][\"status\"] == \"running\"\n            nginx_active = result[\"nginx\"][\"active\"]\n            \n            overall_healthy = app_healthy and process_running and nginx_active\n            sys.exit(0 if overall_healthy else 1)\n            \n        elif command == \"monitor\":\n            # Continuous monitoring mode\n            print(\"Starting continuous monitoring...\")\n            while True:\n                status = monitor.get_full_status()\n                print(f\"\\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Status Check:\")\n                print(f\"  Application: {status['application']['status']}\")\n                print(f\"  Process: {status['process']['status']}\")\n                print(f\"  Nginx: {'active' if status['nginx']['active'] else 'inactive'}\")\n                print(f\"  CPU: {status['system']['cpu_percent']:.1f}%\")\n                print(f\"  Memory: {status['system']['memory']['percent']:.1f}%\")\n                \n                time.sleep(60)  # Check every minute\n                \n        else:\n            print(f\"Unknown command: {command}\")\n            sys.exit(1)\n    else:\n        # Default: show full status\n        result = monitor.get_full_status()\n        print(json.dumps(result, indent=2))\n\nif __name__ == \"__main__\":\n    main()\n", "modifiedCode": "#!/usr/bin/env python3\n\"\"\"\nAlgoFactory Monitoring Script\nThis script provides various monitoring capabilities for the AlgoFactory application.\n\"\"\"\n\nimport requests\nimport json\nimport time\nimport psutil\nimport os\nimport sys\nfrom datetime import datetime\nimport subprocess\n\nclass AlgoFactoryMonitor:\n    def __init__(self, base_url=\"http://localhost:5000\"):\n        self.base_url = base_url\n        self.app_pid_file = \"/home/<USER>/myproject/algofactory/app.pid\"\n        \n    def check_application_health(self):\n        \"\"\"Check if the application is responding\"\"\"\n        try:\n            response = requests.get(f\"{self.base_url}/\", timeout=10)\n            return {\n                \"status\": \"healthy\" if response.status_code == 200 else \"unhealthy\",\n                \"status_code\": response.status_code,\n                \"response_time\": response.elapsed.total_seconds(),\n                \"timestamp\": datetime.now().isoformat()\n            }\n        except requests.exceptions.RequestException as e:\n            return {\n                \"status\": \"unhealthy\",\n                \"error\": str(e),\n                \"timestamp\": datetime.now().isoformat()\n            }\n    \n    def check_process_status(self):\n        \"\"\"Check if the application process is running\"\"\"\n        try:\n            if os.path.exists(self.app_pid_file):\n                with open(self.app_pid_file, 'r') as f:\n                    pid = int(f.read().strip())\n                \n                if psutil.pid_exists(pid):\n                    process = psutil.Process(pid)\n                    return {\n                        \"status\": \"running\",\n                        \"pid\": pid,\n                        \"cpu_percent\": process.cpu_percent(),\n                        \"memory_percent\": process.memory_percent(),\n                        \"memory_info\": process.memory_info()._asdict(),\n                        \"create_time\": datetime.fromtimestamp(process.create_time()).isoformat(),\n                        \"timestamp\": datetime.now().isoformat()\n                    }\n                else:\n                    return {\n                        \"status\": \"not_running\",\n                        \"error\": \"PID exists but process not found\",\n                        \"timestamp\": datetime.now().isoformat()\n                    }\n            else:\n                return {\n                    \"status\": \"not_running\",\n                    \"error\": \"PID file not found\",\n                    \"timestamp\": datetime.now().isoformat()\n                }\n        except Exception as e:\n            return {\n                \"status\": \"error\",\n                \"error\": str(e),\n                \"timestamp\": datetime.now().isoformat()\n            }\n    \n    def check_system_resources(self):\n        \"\"\"Check system resource usage\"\"\"\n        try:\n            cpu_percent = psutil.cpu_percent(interval=1)\n            memory = psutil.virtual_memory()\n            disk = psutil.disk_usage('/')\n            \n            return {\n                \"cpu_percent\": cpu_percent,\n                \"memory\": {\n                    \"total\": memory.total,\n                    \"available\": memory.available,\n                    \"percent\": memory.percent,\n                    \"used\": memory.used,\n                    \"free\": memory.free\n                },\n                \"disk\": {\n                    \"total\": disk.total,\n                    \"used\": disk.used,\n                    \"free\": disk.free,\n                    \"percent\": (disk.used / disk.total) * 100\n                },\n                \"timestamp\": datetime.now().isoformat()\n            }\n        except Exception as e:\n            return {\n                \"error\": str(e),\n                \"timestamp\": datetime.now().isoformat()\n            }\n    \n    def check_nginx_status(self):\n        \"\"\"Check Nginx status\"\"\"\n        try:\n            result = subprocess.run(['sudo', 'systemctl', 'is-active', 'nginx'], \n                                  capture_output=True, text=True)\n            return {\n                \"status\": result.stdout.strip(),\n                \"active\": result.stdout.strip() == \"active\",\n                \"timestamp\": datetime.now().isoformat()\n            }\n        except Exception as e:\n            return {\n                \"status\": \"error\",\n                \"error\": str(e),\n                \"timestamp\": datetime.now().isoformat()\n            }\n    \n    def get_full_status(self):\n        \"\"\"Get comprehensive status report\"\"\"\n        return {\n            \"application\": self.check_application_health(),\n            \"process\": self.check_process_status(),\n            \"system\": self.check_system_resources(),\n            \"nginx\": self.check_nginx_status(),\n            \"overall_timestamp\": datetime.now().isoformat()\n        }\n    \n    def send_to_external_monitor(self, webhook_url, status_data):\n        \"\"\"Send status data to external monitoring service\"\"\"\n        try:\n            headers = {'Content-Type': 'application/json'}\n            response = requests.post(webhook_url, \n                                   data=json.dumps(status_data), \n                                   headers=headers, \n                                   timeout=30)\n            return {\n                \"success\": response.status_code == 200,\n                \"status_code\": response.status_code,\n                \"timestamp\": datetime.now().isoformat()\n            }\n        except Exception as e:\n            return {\n                \"success\": False,\n                \"error\": str(e),\n                \"timestamp\": datetime.now().isoformat()\n            }\n\ndef main():\n    monitor = AlgoFactoryMonitor()\n    \n    if len(sys.argv) > 1:\n        command = sys.argv[1]\n        \n        if command == \"health\":\n            result = monitor.check_application_health()\n            print(json.dumps(result, indent=2))\n            sys.exit(0 if result[\"status\"] == \"healthy\" else 1)\n            \n        elif command == \"process\":\n            result = monitor.check_process_status()\n            print(json.dumps(result, indent=2))\n            sys.exit(0 if result[\"status\"] == \"running\" else 1)\n            \n        elif command == \"system\":\n            result = monitor.check_system_resources()\n            print(json.dumps(result, indent=2))\n            \n        elif command == \"nginx\":\n            result = monitor.check_nginx_status()\n            print(json.dumps(result, indent=2))\n            sys.exit(0 if result[\"active\"] else 1)\n            \n        elif command == \"full\":\n            result = monitor.get_full_status()\n            print(json.dumps(result, indent=2))\n            \n            # Determine overall health\n            app_healthy = result[\"application\"][\"status\"] == \"healthy\"\n            process_running = result[\"process\"][\"status\"] == \"running\"\n            nginx_active = result[\"nginx\"][\"active\"]\n            \n            overall_healthy = app_healthy and process_running and nginx_active\n            sys.exit(0 if overall_healthy else 1)\n            \n        elif command == \"monitor\":\n            # Continuous monitoring mode\n            print(\"Starting continuous monitoring...\")\n            while True:\n                status = monitor.get_full_status()\n                print(f\"\\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Status Check:\")\n                print(f\"  Application: {status['application']['status']}\")\n                print(f\"  Process: {status['process']['status']}\")\n                print(f\"  Nginx: {'active' if status['nginx']['active'] else 'inactive'}\")\n                print(f\"  CPU: {status['system']['cpu_percent']:.1f}%\")\n                print(f\"  Memory: {status['system']['memory']['percent']:.1f}%\")\n                \n                time.sleep(60)  # Check every minute\n                \n        else:\n            print(f\"Unknown command: {command}\")\n            sys.exit(1)\n    else:\n        # Default: show full status\n        result = monitor.get_full_status()\n        print(json.dumps(result, indent=2))\n\nif __name__ == \"__main__\":\n    main()\n"}