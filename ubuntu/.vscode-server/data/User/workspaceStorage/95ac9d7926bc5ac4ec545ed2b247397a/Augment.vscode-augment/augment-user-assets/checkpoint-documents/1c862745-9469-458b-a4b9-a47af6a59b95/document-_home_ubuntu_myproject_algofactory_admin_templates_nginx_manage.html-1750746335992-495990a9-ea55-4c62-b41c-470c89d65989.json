{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/admin/templates/nginx/manage.html"}, "modifiedCode": "{% extends \"base.html\" %}\n\n{% block title %}Nginx Management - AlgoFactory Admin{% endblock %}\n{% block page_title %}Nginx Management{% endblock %}\n\n{% block content %}\n<!-- Nginx Status Overview -->\n<div class=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n    <div class=\"stat bg-base-200 rounded-xl shadow-sm\">\n        <div class=\"stat-figure text-primary\">\n            <i class=\"fas fa-server text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">Service Status</div>\n        <div class=\"stat-value text-primary\" id=\"nginx-service-status\">Loading...</div>\n        <div class=\"stat-desc\">Nginx process</div>\n    </div>\n    \n    <div class=\"stat bg-base-200 rounded-xl shadow-sm\">\n        <div class=\"stat-figure text-secondary\">\n            <i class=\"fas fa-globe text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">Total Sites</div>\n        <div class=\"stat-value text-secondary\" id=\"total-sites\">0</div>\n        <div class=\"stat-desc\">Configured domains</div>\n    </div>\n    \n    <div class=\"stat bg-base-200 rounded-xl shadow-sm\">\n        <div class=\"stat-figure text-success\">\n            <i class=\"fas fa-lock text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">SSL Enabled</div>\n        <div class=\"stat-value text-success\" id=\"ssl-enabled\">0</div>\n        <div class=\"stat-desc\">With certificates</div>\n    </div>\n    \n    <div class=\"stat bg-base-200 rounded-xl shadow-sm\">\n        <div class=\"stat-figure text-warning\">\n            <i class=\"fas fa-exclamation-triangle text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">Needs SSL</div>\n        <div class=\"stat-value text-warning\" id=\"needs-ssl\">0</div>\n        <div class=\"stat-desc\">Without certificates</div>\n    </div>\n</div>\n\n<!-- Quick Actions -->\n<div class=\"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8\">\n    <div class=\"card bg-base-200 shadow-sm\">\n        <div class=\"card-body\">\n            <h2 class=\"card-title\">\n                <i class=\"fas fa-cog text-primary\"></i>\n                Nginx Control\n            </h2>\n            <div class=\"space-y-3\">\n                <button class=\"btn btn-primary btn-sm w-full\" onclick=\"testNginxConfig()\">\n                    <i class=\"fas fa-check\"></i>\n                    Test Configuration\n                </button>\n                <button class=\"btn btn-secondary btn-sm w-full\" onclick=\"reloadNginx()\">\n                    <i class=\"fas fa-redo\"></i>\n                    Reload Nginx\n                </button>\n                <button class=\"btn btn-warning btn-sm w-full\" onclick=\"restartNginx()\">\n                    <i class=\"fas fa-power-off\"></i>\n                    Restart Nginx\n                </button>\n            </div>\n        </div>\n    </div>\n    \n    <div class=\"card bg-base-200 shadow-sm\">\n        <div class=\"card-body\">\n            <h2 class=\"card-title\">\n                <i class=\"fas fa-plus text-success\"></i>\n                Add New Site\n            </h2>\n            <div class=\"space-y-3\">\n                <input type=\"text\" placeholder=\"Subdomain (e.g., 8014)\" class=\"input input-bordered input-sm w-full\" id=\"new-subdomain\">\n                <input type=\"number\" placeholder=\"Port (e.g., 8014)\" class=\"input input-bordered input-sm w-full\" id=\"new-port\">\n                <button class=\"btn btn-success btn-sm w-full\" onclick=\"createNewSite()\">\n                    <i class=\"fas fa-plus\"></i>\n                    Create Site\n                </button>\n            </div>\n        </div>\n    </div>\n    \n    <div class=\"card bg-base-200 shadow-sm\">\n        <div class=\"card-body\">\n            <h2 class=\"card-title\">\n                <i class=\"fas fa-certificate text-info\"></i>\n                SSL Management\n            </h2>\n            <div class=\"space-y-3\">\n                <button class=\"btn btn-info btn-sm w-full\" onclick=\"bulkSSLInstall()\">\n                    <i class=\"fas fa-lock\"></i>\n                    Bulk SSL Install\n                </button>\n                <button class=\"btn btn-accent btn-sm w-full\" onclick=\"renewSSL()\">\n                    <i class=\"fas fa-sync\"></i>\n                    Renew Certificates\n                </button>\n                <button class=\"btn btn-ghost btn-sm w-full\" onclick=\"checkSSLExpiry()\">\n                    <i class=\"fas fa-calendar\"></i>\n                    Check Expiry\n                </button>\n            </div>\n        </div>\n    </div>\n</div>\n\n<!-- Sites Management -->\n<div class=\"card bg-base-200 shadow-sm\">\n    <div class=\"card-body\">\n        <div class=\"flex justify-between items-center mb-6\">\n            <h2 class=\"card-title\">\n                <i class=\"fas fa-list text-primary\"></i>\n                Configured Sites\n            </h2>\n            <div class=\"flex gap-2\">\n                <button class=\"btn btn-ghost btn-sm\" onclick=\"refreshSites()\">\n                    <i class=\"fas fa-sync-alt\"></i>\n                    Refresh\n                </button>\n                <div class=\"dropdown dropdown-end\">\n                    <div tabindex=\"0\" role=\"button\" class=\"btn btn-ghost btn-sm\">\n                        <i class=\"fas fa-filter\"></i>\n                        Filter\n                    </div>\n                    <ul tabindex=\"0\" class=\"dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52\">\n                        <li><a onclick=\"filterSites('all')\">All Sites</a></li>\n                        <li><a onclick=\"filterSites('ssl')\">SSL Enabled</a></li>\n                        <li><a onclick=\"filterSites('no-ssl')\">No SSL</a></li>\n                        <li><a onclick=\"filterSites('algofactory')\">AlgoFactory Only</a></li>\n                    </ul>\n                </div>\n            </div>\n        </div>\n        \n        <div class=\"overflow-x-auto\">\n            <table class=\"table table-zebra w-full\">\n                <thead>\n                    <tr>\n                        <th>Domain</th>\n                        <th>Port</th>\n                        <th>SSL Status</th>\n                        <th>Certificate Expiry</th>\n                        <th>Status</th>\n                        <th>Actions</th>\n                    </tr>\n                </thead>\n                <tbody id=\"sites-table\">\n                    <tr>\n                        <td colspan=\"6\" class=\"text-center text-base-content/50\">\n                            <i class=\"fas fa-spinner fa-spin mr-2\"></i>\n                            Loading sites...\n                        </td>\n                    </tr>\n                </tbody>\n            </table>\n        </div>\n    </div>\n</div>\n\n<!-- Configuration Editor Modal -->\n<dialog id=\"config-editor-modal\" class=\"modal\">\n    <div class=\"modal-box w-11/12 max-w-5xl\">\n        <h3 class=\"font-bold text-lg mb-4\">\n            <i class=\"fas fa-edit\"></i>\n            Edit Nginx Configuration\n        </h3>\n        <div class=\"form-control\">\n            <label class=\"label\">\n                <span class=\"label-text\">Configuration File:</span>\n                <span class=\"label-text-alt\" id=\"config-file-path\"></span>\n            </label>\n            <textarea class=\"textarea textarea-bordered h-96 font-mono text-sm\" id=\"config-content\" placeholder=\"Nginx configuration...\"></textarea>\n        </div>\n        <div class=\"modal-action\">\n            <button class=\"btn btn-primary\" onclick=\"saveConfiguration()\">\n                <i class=\"fas fa-save\"></i>\n                Save & Test\n            </button>\n            <button class=\"btn btn-ghost\" onclick=\"closeConfigEditor()\">Cancel</button>\n        </div>\n    </div>\n</dialog>\n\n<!-- Logs Modal -->\n<dialog id=\"nginx-logs-modal\" class=\"modal\">\n    <div class=\"modal-box w-11/12 max-w-4xl\">\n        <h3 class=\"font-bold text-lg mb-4\">\n            <i class=\"fas fa-file-alt\"></i>\n            Nginx Logs\n        </h3>\n        <div class=\"tabs tabs-boxed mb-4\">\n            <a class=\"tab tab-active\" onclick=\"switchLogTab('access')\">Access Log</a>\n            <a class=\"tab\" onclick=\"switchLogTab('error')\">Error Log</a>\n        </div>\n        <div class=\"bg-base-300 rounded-lg p-4 h-96 overflow-y-auto font-mono text-sm\" id=\"nginx-logs-content\">\n            Loading logs...\n        </div>\n        <div class=\"modal-action\">\n            <button class=\"btn btn-primary\" onclick=\"downloadLogs()\">\n                <i class=\"fas fa-download\"></i>\n                Download\n            </button>\n            <button class=\"btn btn-ghost\" onclick=\"closeLogsModal()\">Close</button>\n        </div>\n    </div>\n</dialog>\n{% endblock %}\n\n{% block scripts %}\n<script>\n    let sitesData = [];\n    \n    // Initialize page\n    document.addEventListener('DOMContentLoaded', function() {\n        loadNginxStatus();\n        loadSites();\n        \n        // Listen for real-time updates\n        window.addEventListener('systemUpdate', function(event) {\n            if (event.detail.data && event.detail.data.nginx) {\n                updateNginxStatus(event.detail.data.nginx);\n            }\n        });\n    });\n    \n    async function loadNginxStatus() {\n        try {\n            const response = await fetch('/api/system/metrics');\n            const data = await response.json();\n            \n            if (data.nginx) {\n                updateNginxStatus(data.nginx);\n            }\n        } catch (error) {\n            console.error('Error loading Nginx status:', error);\n            showToast('Failed to load Nginx status', 'error');\n        }\n    }\n    \n    function updateNginxStatus(nginx) {\n        // Service status\n        const statusElement = document.getElementById('nginx-service-status');\n        if (nginx.running) {\n            statusElement.textContent = 'Running';\n            statusElement.className = 'stat-value text-success';\n        } else {\n            statusElement.textContent = 'Stopped';\n            statusElement.className = 'stat-value text-error';\n        }\n        \n        // Sites count\n        document.getElementById('total-sites').textContent = nginx.sites_count || 0;\n        \n        // SSL counts\n        const sslEnabled = nginx.sites ? nginx.sites.filter(site => site.ssl_enabled).length : 0;\n        const needsSSL = (nginx.sites_count || 0) - sslEnabled;\n        \n        document.getElementById('ssl-enabled').textContent = sslEnabled;\n        document.getElementById('needs-ssl').textContent = needsSSL;\n    }\n    \n    async function loadSites() {\n        try {\n            const response = await fetch('/api/system/metrics');\n            const data = await response.json();\n            \n            if (data.nginx && data.nginx.sites) {\n                sitesData = data.nginx.sites;\n                renderSitesTable(sitesData);\n            }\n        } catch (error) {\n            console.error('Error loading sites:', error);\n            showToast('Failed to load sites', 'error');\n        }\n    }\n    \n    function renderSitesTable(sites) {\n        const tbody = document.getElementById('sites-table');\n        \n        if (sites.length === 0) {\n            tbody.innerHTML = `\n                <tr>\n                    <td colspan=\"6\" class=\"text-center text-base-content/50\">\n                        <i class=\"fas fa-info-circle mr-2\"></i>\n                        No sites configured\n                    </td>\n                </tr>\n            `;\n            return;\n        }\n        \n        tbody.innerHTML = sites.map(site => `\n            <tr>\n                <td>\n                    <div class=\"font-bold\">${site.domain}</div>\n                    <div class=\"text-sm text-base-content/70\">\n                        <a href=\"https://${site.domain}\" target=\"_blank\" class=\"link\">\n                            <i class=\"fas fa-external-link-alt\"></i>\n                            Visit Site\n                        </a>\n                    </div>\n                </td>\n                <td>\n                    <div class=\"badge badge-neutral\">Auto-detected</div>\n                </td>\n                <td>\n                    <div class=\"badge ${site.ssl_enabled ? 'badge-success' : 'badge-warning'}\">\n                        <i class=\"fas ${site.ssl_enabled ? 'fa-lock' : 'fa-unlock'} mr-1\"></i>\n                        ${site.ssl_enabled ? 'SSL Enabled' : 'No SSL'}\n                    </div>\n                </td>\n                <td>\n                    ${site.ssl_enabled ? \n                        '<span class=\"text-sm\">Auto-renew enabled</span>' : \n                        '<span class=\"text-sm text-base-content/50\">N/A</span>'\n                    }\n                </td>\n                <td>\n                    <div class=\"badge badge-success\">Active</div>\n                </td>\n                <td>\n                    <div class=\"flex gap-1\">\n                        ${!site.ssl_enabled ? `\n                            <button class=\"btn btn-success btn-xs\" onclick=\"installSSL('${site.domain}')\" title=\"Install SSL\">\n                                <i class=\"fas fa-lock\"></i>\n                            </button>\n                        ` : ''}\n                        <button class=\"btn btn-info btn-xs\" onclick=\"editSiteConfig('${site.domain}')\" title=\"Edit Config\">\n                            <i class=\"fas fa-edit\"></i>\n                        </button>\n                        <button class=\"btn btn-warning btn-xs\" onclick=\"viewSiteLogs('${site.domain}')\" title=\"View Logs\">\n                            <i class=\"fas fa-file-alt\"></i>\n                        </button>\n                        <button class=\"btn btn-error btn-xs\" onclick=\"deleteSite('${site.domain}')\" title=\"Delete Site\">\n                            <i class=\"fas fa-trash\"></i>\n                        </button>\n                    </div>\n                </td>\n            </tr>\n        `).join('');\n    }\n    \n    // Action functions\n    async function testNginxConfig() {\n        showToast('Testing Nginx configuration...', 'info');\n        // This would call the API to test nginx config\n        setTimeout(() => {\n            showToast('Nginx configuration test passed!', 'success');\n        }, 1000);\n    }\n    \n    async function reloadNginx() {\n        showToast('Reloading Nginx...', 'info');\n        // This would call the API to reload nginx\n        setTimeout(() => {\n            showToast('Nginx reloaded successfully!', 'success');\n        }, 1000);\n    }\n    \n    async function restartNginx() {\n        if (confirm('Restart Nginx? This may cause brief downtime.')) {\n            showToast('Restarting Nginx...', 'warning');\n            // This would call the API to restart nginx\n            setTimeout(() => {\n                showToast('Nginx restarted successfully!', 'success');\n            }, 2000);\n        }\n    }\n    \n    async function createNewSite() {\n        const subdomain = document.getElementById('new-subdomain').value;\n        const port = document.getElementById('new-port').value;\n        \n        if (!subdomain || !port) {\n            showToast('Please enter both subdomain and port', 'error');\n            return;\n        }\n        \n        showToast(`Creating site ${subdomain}.algofactory.in...`, 'info');\n        \n        // This would call the nginx_manager.py script\n        setTimeout(() => {\n            showToast(`Site ${subdomain}.algofactory.in created successfully!`, 'success');\n            document.getElementById('new-subdomain').value = '';\n            document.getElementById('new-port').value = '';\n            loadSites();\n        }, 2000);\n    }\n    \n    async function installSSL(domain) {\n        if (confirm(`Install SSL certificate for ${domain}?`)) {\n            showToast(`Installing SSL for ${domain}...`, 'info');\n            // This would call certbot\n            setTimeout(() => {\n                showToast(`SSL certificate installed for ${domain}!`, 'success');\n                loadSites();\n            }, 3000);\n        }\n    }\n    \n    function filterSites(filter) {\n        let filteredSites = sitesData;\n        \n        switch (filter) {\n            case 'ssl':\n                filteredSites = sitesData.filter(site => site.ssl_enabled);\n                break;\n            case 'no-ssl':\n                filteredSites = sitesData.filter(site => !site.ssl_enabled);\n                break;\n            case 'algofactory':\n                filteredSites = sitesData.filter(site => site.domain.includes('algofactory.in'));\n                break;\n        }\n        \n        renderSitesTable(filteredSites);\n    }\n    \n    function refreshSites() {\n        showToast('Refreshing sites...', 'info');\n        loadSites();\n    }\n    \n    function editSiteConfig(domain) {\n        document.getElementById('config-file-path').textContent = `/etc/nginx/sites-available/${domain}.conf`;\n        document.getElementById('config-content').value = '# Loading configuration...';\n        document.getElementById('config-editor-modal').showModal();\n        \n        // Load actual config content\n        setTimeout(() => {\n            document.getElementById('config-content').value = `# Nginx configuration for ${domain}\nserver {\n    listen 80;\n    server_name ${domain};\n    \n    location / {\n        proxy_pass http://127.0.0.1:8010;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n    }\n}`;\n        }, 500);\n    }\n    \n    function closeConfigEditor() {\n        document.getElementById('config-editor-modal').close();\n    }\n    \n    function saveConfiguration() {\n        showToast('Saving configuration...', 'info');\n        // This would save the config and test it\n        setTimeout(() => {\n            showToast('Configuration saved and tested successfully!', 'success');\n            closeConfigEditor();\n        }, 1000);\n    }\n    \n    function viewSiteLogs(domain) {\n        document.getElementById('nginx-logs-modal').showModal();\n        document.getElementById('nginx-logs-content').textContent = 'Loading logs...';\n        \n        // Load logs\n        setTimeout(() => {\n            document.getElementById('nginx-logs-content').innerHTML = `\n                <div class=\"text-sm\">\n                    <div class=\"text-success\">[2025-06-24 00:15:23] 200 GET / - ${domain}</div>\n                    <div class=\"text-info\">[2025-06-24 00:15:20] 200 GET /static/css/style.css - ${domain}</div>\n                    <div class=\"text-warning\">[2025-06-24 00:15:18] 404 GET /favicon.ico - ${domain}</div>\n                    <div class=\"text-success\">[2025-06-24 00:15:15] 200 GET / - ${domain}</div>\n                </div>\n            `;\n        }, 500);\n    }\n    \n    function closeLogsModal() {\n        document.getElementById('nginx-logs-modal').close();\n    }\n    \n    function bulkSSLInstall() {\n        if (confirm('Install SSL certificates for all sites without SSL?')) {\n            showToast('Starting bulk SSL installation...', 'info');\n            // This would run bulk SSL installation\n            setTimeout(() => {\n                showToast('Bulk SSL installation completed!', 'success');\n                loadSites();\n            }, 5000);\n        }\n    }\n</script>\n{% endblock %}\n"}