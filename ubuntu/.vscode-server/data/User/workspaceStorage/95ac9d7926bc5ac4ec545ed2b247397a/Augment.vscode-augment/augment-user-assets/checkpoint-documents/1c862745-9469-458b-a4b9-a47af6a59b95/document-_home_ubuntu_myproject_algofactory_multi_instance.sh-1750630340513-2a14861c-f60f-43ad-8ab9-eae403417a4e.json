{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/multi_instance.sh"}, "originalCode": "#!/bin/bash\n\n# Multi-Instance AlgoFactory Manager\n# Creates separate instances with different ports while keeping original templates unchanged\n\nset -e\n\nSCRIPT_DIR=\"$(cd \"$(dirname \"${BASH_SOURCE[0]}\")\" && pwd)\"\nINSTANCES_DIR=\"/home/<USER>/algofactory-multi\"\n\n# Function to display usage\nusage() {\n    echo \"Multi-Instance AlgoFactory Manager\"\n    echo \"\"\n    echo \"Usage: $0 <command> <instance_id>\"\n    echo \"\"\n    echo \"Commands:\"\n    echo \"  create <id>    Create new instance (e.g., create 1010)\"\n    echo \"  start <id>     Start instance\"\n    echo \"  stop <id>      Stop instance\"\n    echo \"  list           List all instances\"\n    echo \"  nginx <id>     Create Nginx config for instance\"\n    echo \"  remove <id>    Remove instance completely\"\n    echo \"\"\n    echo \"Examples:\"\n    echo \"  $0 create 1010     # Creates algofactory-1010 instance\"\n    echo \"  $0 start 1010      # Starts the 1010 instance\"\n    echo \"  $0 nginx 1010      # Creates Nginx config for 1010.algofactory.in\"\n    echo \"\"\n    exit 1\n}\n\n# Function to create instance\ncreate_instance() {\n    local instance_id=$1\n    local instance_dir=\"$INSTANCES_DIR/algofactory-$instance_id\"\n    \n    echo \"🚀 Creating AlgoFactory instance: $instance_id\"\n    \n    # Create instances directory\n    mkdir -p \"$INSTANCES_DIR\"\n    \n    # Remove existing instance if it exists\n    if [ -d \"$instance_dir\" ]; then\n        echo \"⚠️  Instance $instance_id already exists. Removing...\"\n        rm -rf \"$instance_dir\"\n    fi\n    \n    # Copy original algofactory to new instance\n    echo \"📁 Copying original AlgoFactory...\"\n    cp -r \"$SCRIPT_DIR\" \"$instance_dir\"\n    \n    # Remove the multi_instance.sh script from the copy to avoid confusion\n    rm -f \"$instance_dir/multi_instance.sh\"\n    \n    # Calculate ports\n    local flask_port=$instance_id\n    local websocket_port=$((instance_id + 12000))\n    local zmq_port=$((instance_id + 15000))\n    \n    echo \"🔧 Configuring ports:\"\n    echo \"   Flask: $flask_port\"\n    echo \"   WebSocket: $websocket_port\"\n    echo \"   ZMQ: $zmq_port\"\n    \n    # Create .env file with instance-specific configuration\n    cat > \"$instance_dir/.env\" << EOF\n# Broker Configuration\nBROKER_API_KEY = 'MZA0cLWq'\nBROKER_API_SECRET = 'XIA6RJ3HPG4ZRKKYJLIZ6ROKAM'\nBROKER_API_KEY_MARKET = 'YOUR_BROKER_MARKET_API_KEY'\nBROKER_API_SECRET_MARKET = 'YOUR_BROKER_MARKET_API_SECRET'\n\n# Application Configuration\nAPP_KEY = '3daa0403ce2501ee7432b75bf100048e3cf510d63d2754f952e93d88bf07ea84'\nAPI_KEY_PEPPER = 'a25d94718479b170c16278e321ea6c989358bf499a658fd20c90033cef8ce772'\n\n# Flask Configuration\nFLASK_ENV = 'production'\nFLASK_DEBUG = 'False'\nFLASK_HOST_IP = '0.0.0.0'\nFLASK_PORT = '$flask_port'\n\n# Database Configuration\nDATABASE_URL = 'sqlite:///db/algofactory-$instance_id.db'\n\n# WebSocket Configuration\nWEBSOCKET_HOST = 'localhost'\nWEBSOCKET_PORT = '$websocket_port'\nWEBSOCKET_URL = 'ws://localhost:$websocket_port'\n\n# ZMQ Configuration\nZMQ_HOST = 'localhost'\nZMQ_PORT = '$zmq_port'\n\n# Server Configuration\nHOST_SERVER = 'https://$instance_id.algofactory.in'\nREDIRECT_URL = 'https://$instance_id.algofactory.in/angel/callback'\n\n# Security Configuration\nCORS_ENABLED = 'TRUE'\nCORS_ALLOWED_ORIGINS = 'https://$instance_id.algofactory.in'\nCORS_ALLOWED_METHODS = 'GET,POST,DELETE,PUT,PATCH'\nCORS_ALLOWED_HEADERS = 'Content-Type,Authorization,X-Requested-With'\nCORS_ALLOW_CREDENTIALS = 'FALSE'\nCORS_EXPOSED_HEADERS = ''\nCORS_MAX_AGE = '86400'\n\n# CSP Configuration\nCSP_ENABLED = 'TRUE'\nCSP_DEFAULT_SRC = \"'self'\"\nCSP_SCRIPT_SRC = \"'self' 'unsafe-inline' https://cdn.socket.io https://static.cloudflareinsights.com\"\nCSP_STYLE_SRC = \"'self' 'unsafe-inline'\"\nCSP_IMG_SRC = \"'self' data:\"\nCSP_FONT_SRC = \"'self'\"\nCSP_CONNECT_SRC = \"'self' wss: ws:\"\nCSP_MEDIA_SRC = \"'self' data: https://*.amazonaws.com https://*.cloudfront.net\"\nCSP_OBJECT_SRC = \"'none'\"\nCSP_BASE_URI = \"'self'\"\nCSP_FORM_ACTION = \"'self'\"\nCSP_FRAME_ANCESTORS = \"'self'\"\nCSP_FRAME_SRC = \"'self'\"\nCSP_UPGRADE_INSECURE_REQUESTS = 'FALSE'\nCSP_REPORT_ONLY = 'FALSE'\nCSP_REPORT_URI = ''\n\n# CSRF Configuration\nCSRF_ENABLED = 'TRUE'\n\n# Rate Limiting\nAPI_RATE_LIMIT = '10 per second'\nLOGIN_RATE_LIMIT_MIN = '5 per minute'\nLOGIN_RATE_LIMIT_HOUR = '25 per hour'\n\n# Session Configuration\nSESSION_EXPIRY_TIME = '03:00'\n\n# Trading Configuration\nSMART_ORDER_DELAY = '0.5'\n\n# Broker List\nVALID_BROKERS = 'fivepaisa,fivepaisaxts,aliceblue,angel,compositedge,dhan,dhan_sandbox,firstock,flattrade,fyers,groww,iifl,kotak,jainam,jainampro,paytm,pocketful,shoonya,tradejini,upstox,wisdom,zebu,zerodha'\n\n# Development Configuration\nNGROK_ALLOW = 'FALSE'\nEOF\n    \n    # Create instance-specific database directory\n    mkdir -p \"$instance_dir/db\"\n    \n    # Create instance-specific logs directory\n    mkdir -p \"$instance_dir/logs\"\n    \n    echo \"✅ Instance $instance_id created successfully!\"\n    echo \"📁 Location: $instance_dir\"\n    echo \"🌐 Domain: $instance_id.algofactory.in\"\n}\n\n# Function to start instance\nstart_instance() {\n    local instance_id=$1\n    local instance_dir=\"$INSTANCES_DIR/algofactory-$instance_id\"\n    \n    if [ ! -d \"$instance_dir\" ]; then\n        echo \"❌ Instance $instance_id does not exist. Create it first with: $0 create $instance_id\"\n        exit 1\n    fi\n    \n    echo \"🚀 Starting AlgoFactory instance: $instance_id\"\n    \n    # Stop any existing process\n    stop_instance \"$instance_id\" 2>/dev/null || true\n    \n    # Change to instance directory and start\n    cd \"$instance_dir\"\n\n    # Extract key environment variables from .env file\n    local flask_port=$(grep \"^FLASK_PORT\" .env | cut -d\"'\" -f2)\n    local websocket_port=$(grep \"^WEBSOCKET_PORT\" .env | cut -d\"'\" -f2)\n    local zmq_port=$(grep \"^ZMQ_PORT\" .env | cut -d\"'\" -f2)\n    local database_url=$(grep \"^DATABASE_URL\" .env | cut -d\"'\" -f2)\n    local host_server=$(grep \"^HOST_SERVER\" .env | cut -d\"'\" -f2)\n\n    echo \"🔧 Environment loaded:\"\n    echo \"   FLASK_PORT=$flask_port\"\n    echo \"   WEBSOCKET_PORT=$websocket_port\"\n    echo \"   ZMQ_PORT=$zmq_port\"\n    echo \"   DATABASE_URL=$database_url\"\n    echo \"   HOST_SERVER=$host_server\"\n\n    # Start the application in background\n    nohup python3 app.py > logs/app.log 2>&1 &\n    echo $! > app.pid\n    \n    echo \"✅ Instance $instance_id started!\"\n    echo \"📊 Check logs: tail -f $instance_dir/logs/app.log\"\n}\n\n# Function to stop instance\nstop_instance() {\n    local instance_id=$1\n    local instance_dir=\"$INSTANCES_DIR/algofactory-$instance_id\"\n    \n    if [ ! -d \"$instance_dir\" ]; then\n        echo \"❌ Instance $instance_id does not exist\"\n        return 1\n    fi\n    \n    echo \"🛑 Stopping AlgoFactory instance: $instance_id\"\n    \n    # Kill by PID file\n    if [ -f \"$instance_dir/app.pid\" ]; then\n        local pid=$(cat \"$instance_dir/app.pid\")\n        if kill -0 \"$pid\" 2>/dev/null; then\n            kill \"$pid\"\n            echo \"✅ Stopped process $pid\"\n        fi\n        rm -f \"$instance_dir/app.pid\"\n    fi\n    \n    # Kill by port\n    local flask_port=$instance_id\n    pkill -f \"python.*app.py.*$flask_port\" || true\n    \n    echo \"✅ Instance $instance_id stopped\"\n}\n\n# Function to list instances\nlist_instances() {\n    echo \"📋 AlgoFactory Instances:\"\n    echo \"\"\n    \n    if [ ! -d \"$INSTANCES_DIR\" ]; then\n        echo \"   No instances found\"\n        return\n    fi\n    \n    for instance_path in \"$INSTANCES_DIR\"/algofactory-*; do\n        if [ -d \"$instance_path\" ]; then\n            local instance_name=$(basename \"$instance_path\")\n            local instance_id=${instance_name#algofactory-}\n            local pid_file=\"$instance_path/app.pid\"\n            local status=\"STOPPED\"\n            \n            if [ -f \"$pid_file\" ]; then\n                local pid=$(cat \"$pid_file\")\n                if kill -0 \"$pid\" 2>/dev/null; then\n                    status=\"RUNNING (PID: $pid)\"\n                fi\n            fi\n            \n            echo \"   $instance_id - $status\"\n            echo \"     Domain: $instance_id.algofactory.in\"\n            echo \"     Path: $instance_path\"\n            echo \"\"\n        fi\n    done\n}\n\n# Function to create Nginx configuration\ncreate_nginx_config() {\n    local instance_id=$1\n    local flask_port=$instance_id\n    \n    echo \"🌐 Creating Nginx configuration for instance: $instance_id\"\n    \n    # Create Nginx config\n    sudo tee \"/etc/nginx/sites-available/$instance_id.algofactory.in.conf\" > /dev/null << EOF\nserver {\n    listen 80;\n    server_name $instance_id.algofactory.in;\n    \n    location / {\n        proxy_pass http://127.0.0.1:$flask_port;\n        proxy_set_header Host \\$host;\n        proxy_set_header X-Real-IP \\$remote_addr;\n        proxy_set_header X-Forwarded-For \\$proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto \\$scheme;\n        \n        # WebSocket support\n        proxy_http_version 1.1;\n        proxy_set_header Upgrade \\$http_upgrade;\n        proxy_set_header Connection \"upgrade\";\n        \n        # Timeouts\n        proxy_connect_timeout 60s;\n        proxy_send_timeout 60s;\n        proxy_read_timeout 60s;\n    }\n}\nEOF\n    \n    # Enable the site\n    sudo ln -sf \"/etc/nginx/sites-available/$instance_id.algofactory.in.conf\" \"/etc/nginx/sites-enabled/\"\n    \n    # Test and reload Nginx\n    sudo nginx -t && sudo systemctl reload nginx\n    \n    echo \"✅ Nginx configuration created for $instance_id.algofactory.in\"\n    echo \"🔒 To get SSL certificate, run:\"\n    echo \"   sudo certbot --nginx -d $instance_id.algofactory.in\"\n}\n\n# Function to remove instance\nremove_instance() {\n    local instance_id=$1\n    local instance_dir=\"$INSTANCES_DIR/algofactory-$instance_id\"\n    \n    if [ ! -d \"$instance_dir\" ]; then\n        echo \"❌ Instance $instance_id does not exist\"\n        exit 1\n    fi\n    \n    echo \"🗑️  Removing AlgoFactory instance: $instance_id\"\n    \n    # Stop the instance first\n    stop_instance \"$instance_id\" 2>/dev/null || true\n    \n    # Remove instance directory\n    rm -rf \"$instance_dir\"\n    \n    # Remove Nginx config\n    sudo rm -f \"/etc/nginx/sites-enabled/$instance_id.algofactory.in.conf\"\n    sudo rm -f \"/etc/nginx/sites-available/$instance_id.algofactory.in.conf\"\n    sudo nginx -t && sudo systemctl reload nginx 2>/dev/null || true\n    \n    echo \"✅ Instance $instance_id removed completely\"\n}\n\n# Main script logic\ncase \"${1:-}\" in\n    create)\n        if [ -z \"${2:-}\" ]; then\n            echo \"❌ Error: Instance ID required\"\n            usage\n        fi\n        create_instance \"$2\"\n        ;;\n    start)\n        if [ -z \"${2:-}\" ]; then\n            echo \"❌ Error: Instance ID required\"\n            usage\n        fi\n        start_instance \"$2\"\n        ;;\n    stop)\n        if [ -z \"${2:-}\" ]; then\n            echo \"❌ Error: Instance ID required\"\n            usage\n        fi\n        stop_instance \"$2\"\n        ;;\n    list)\n        list_instances\n        ;;\n    nginx)\n        if [ -z \"${2:-}\" ]; then\n            echo \"❌ Error: Instance ID required\"\n            usage\n        fi\n        create_nginx_config \"$2\"\n        ;;\n    remove)\n        if [ -z \"${2:-}\" ]; then\n            echo \"❌ Error: Instance ID required\"\n            usage\n        fi\n        remove_instance \"$2\"\n        ;;\n    *)\n        usage\n        ;;\nesac\n", "modifiedCode": "#!/bin/bash\n\n# Multi-Instance AlgoFactory Manager\n# Creates separate instances with different ports while keeping original templates unchanged\n\nset -e\n\nSCRIPT_DIR=\"$(cd \"$(dirname \"${BASH_SOURCE[0]}\")\" && pwd)\"\nINSTANCES_DIR=\"/home/<USER>/algofactory-multi\"\n\n# Function to display usage\nusage() {\n    echo \"Multi-Instance AlgoFactory Manager\"\n    echo \"\"\n    echo \"Usage: $0 <command> <instance_id>\"\n    echo \"\"\n    echo \"Commands:\"\n    echo \"  create <id>    Create new instance (e.g., create 1010)\"\n    echo \"  start <id>     Start instance\"\n    echo \"  stop <id>      Stop instance\"\n    echo \"  list           List all instances\"\n    echo \"  nginx <id>     Create Nginx config for instance\"\n    echo \"  remove <id>    Remove instance completely\"\n    echo \"\"\n    echo \"Examples:\"\n    echo \"  $0 create 1010     # Creates algofactory-1010 instance\"\n    echo \"  $0 start 1010      # Starts the 1010 instance\"\n    echo \"  $0 nginx 1010      # Creates Nginx config for 1010.algofactory.in\"\n    echo \"\"\n    exit 1\n}\n\n# Function to create instance\ncreate_instance() {\n    local instance_id=$1\n    local instance_dir=\"$INSTANCES_DIR/algofactory-$instance_id\"\n    \n    echo \"🚀 Creating AlgoFactory instance: $instance_id\"\n    \n    # Create instances directory\n    mkdir -p \"$INSTANCES_DIR\"\n    \n    # Remove existing instance if it exists\n    if [ -d \"$instance_dir\" ]; then\n        echo \"⚠️  Instance $instance_id already exists. Removing...\"\n        rm -rf \"$instance_dir\"\n    fi\n    \n    # Copy original algofactory to new instance\n    echo \"📁 Copying original AlgoFactory...\"\n    cp -r \"$SCRIPT_DIR\" \"$instance_dir\"\n    \n    # Remove the multi_instance.sh script from the copy to avoid confusion\n    rm -f \"$instance_dir/multi_instance.sh\"\n    \n    # Calculate ports\n    local flask_port=$instance_id\n    local websocket_port=$((instance_id + 12000))\n    local zmq_port=$((instance_id + 15000))\n    \n    echo \"🔧 Configuring ports:\"\n    echo \"   Flask: $flask_port\"\n    echo \"   WebSocket: $websocket_port\"\n    echo \"   ZMQ: $zmq_port\"\n    \n    # Create .env file with instance-specific configuration\n    cat > \"$instance_dir/.env\" << EOF\n# Broker Configuration\nBROKER_API_KEY = 'MZA0cLWq'\nBROKER_API_SECRET = 'XIA6RJ3HPG4ZRKKYJLIZ6ROKAM'\nBROKER_API_KEY_MARKET = 'YOUR_BROKER_MARKET_API_KEY'\nBROKER_API_SECRET_MARKET = 'YOUR_BROKER_MARKET_API_SECRET'\n\n# Application Configuration\nAPP_KEY = '3daa0403ce2501ee7432b75bf100048e3cf510d63d2754f952e93d88bf07ea84'\nAPI_KEY_PEPPER = 'a25d94718479b170c16278e321ea6c989358bf499a658fd20c90033cef8ce772'\n\n# Flask Configuration\nFLASK_ENV = 'production'\nFLASK_DEBUG = 'False'\nFLASK_HOST_IP = '0.0.0.0'\nFLASK_PORT = '$flask_port'\n\n# Database Configuration\nDATABASE_URL = 'sqlite:///db/algofactory-$instance_id.db'\n\n# WebSocket Configuration\nWEBSOCKET_HOST = 'localhost'\nWEBSOCKET_PORT = '$websocket_port'\nWEBSOCKET_URL = 'ws://localhost:$websocket_port'\n\n# ZMQ Configuration\nZMQ_HOST = 'localhost'\nZMQ_PORT = '$zmq_port'\n\n# Server Configuration\nHOST_SERVER = 'https://$instance_id.algofactory.in'\nREDIRECT_URL = 'https://$instance_id.algofactory.in/angel/callback'\n\n# Security Configuration\nCORS_ENABLED = 'TRUE'\nCORS_ALLOWED_ORIGINS = 'https://$instance_id.algofactory.in'\nCORS_ALLOWED_METHODS = 'GET,POST,DELETE,PUT,PATCH'\nCORS_ALLOWED_HEADERS = 'Content-Type,Authorization,X-Requested-With'\nCORS_ALLOW_CREDENTIALS = 'FALSE'\nCORS_EXPOSED_HEADERS = ''\nCORS_MAX_AGE = '86400'\n\n# CSP Configuration\nCSP_ENABLED = 'TRUE'\nCSP_DEFAULT_SRC = \"'self'\"\nCSP_SCRIPT_SRC = \"'self' 'unsafe-inline' https://cdn.socket.io https://static.cloudflareinsights.com\"\nCSP_STYLE_SRC = \"'self' 'unsafe-inline'\"\nCSP_IMG_SRC = \"'self' data:\"\nCSP_FONT_SRC = \"'self'\"\nCSP_CONNECT_SRC = \"'self' wss: ws:\"\nCSP_MEDIA_SRC = \"'self' data: https://*.amazonaws.com https://*.cloudfront.net\"\nCSP_OBJECT_SRC = \"'none'\"\nCSP_BASE_URI = \"'self'\"\nCSP_FORM_ACTION = \"'self'\"\nCSP_FRAME_ANCESTORS = \"'self'\"\nCSP_FRAME_SRC = \"'self'\"\nCSP_UPGRADE_INSECURE_REQUESTS = 'FALSE'\nCSP_REPORT_ONLY = 'FALSE'\nCSP_REPORT_URI = ''\n\n# CSRF Configuration\nCSRF_ENABLED = 'TRUE'\n\n# Rate Limiting\nAPI_RATE_LIMIT = '10 per second'\nLOGIN_RATE_LIMIT_MIN = '5 per minute'\nLOGIN_RATE_LIMIT_HOUR = '25 per hour'\n\n# Session Configuration\nSESSION_EXPIRY_TIME = '03:00'\n\n# Trading Configuration\nSMART_ORDER_DELAY = '0.5'\n\n# Broker List\nVALID_BROKERS = 'fivepaisa,fivepaisaxts,aliceblue,angel,compositedge,dhan,dhan_sandbox,firstock,flattrade,fyers,groww,iifl,kotak,jainam,jainampro,paytm,pocketful,shoonya,tradejini,upstox,wisdom,zebu,zerodha'\n\n# Development Configuration\nNGROK_ALLOW = 'FALSE'\nEOF\n    \n    # Create instance-specific database directory\n    mkdir -p \"$instance_dir/db\"\n    \n    # Create instance-specific logs directory\n    mkdir -p \"$instance_dir/logs\"\n    \n    echo \"✅ Instance $instance_id created successfully!\"\n    echo \"📁 Location: $instance_dir\"\n    echo \"🌐 Domain: $instance_id.algofactory.in\"\n}\n\n# Function to start instance\nstart_instance() {\n    local instance_id=$1\n    local instance_dir=\"$INSTANCES_DIR/algofactory-$instance_id\"\n    \n    if [ ! -d \"$instance_dir\" ]; then\n        echo \"❌ Instance $instance_id does not exist. Create it first with: $0 create $instance_id\"\n        exit 1\n    fi\n    \n    echo \"🚀 Starting AlgoFactory instance: $instance_id\"\n    \n    # Stop any existing process\n    stop_instance \"$instance_id\" 2>/dev/null || true\n    \n    # Change to instance directory and start\n    cd \"$instance_dir\"\n\n    # Extract key environment variables from .env file\n    local flask_port=$(grep \"^FLASK_PORT\" .env | cut -d\"'\" -f2)\n    local websocket_port=$(grep \"^WEBSOCKET_PORT\" .env | cut -d\"'\" -f2)\n    local zmq_port=$(grep \"^ZMQ_PORT\" .env | cut -d\"'\" -f2)\n    local database_url=$(grep \"^DATABASE_URL\" .env | cut -d\"'\" -f2)\n    local host_server=$(grep \"^HOST_SERVER\" .env | cut -d\"'\" -f2)\n\n    echo \"🔧 Environment loaded:\"\n    echo \"   FLASK_PORT=$flask_port\"\n    echo \"   WEBSOCKET_PORT=$websocket_port\"\n    echo \"   ZMQ_PORT=$zmq_port\"\n    echo \"   DATABASE_URL=$database_url\"\n    echo \"   HOST_SERVER=$host_server\"\n\n    # Start the application in background\n    nohup python3 app.py > logs/app.log 2>&1 &\n    echo $! > app.pid\n    \n    echo \"✅ Instance $instance_id started!\"\n    echo \"📊 Check logs: tail -f $instance_dir/logs/app.log\"\n}\n\n# Function to stop instance\nstop_instance() {\n    local instance_id=$1\n    local instance_dir=\"$INSTANCES_DIR/algofactory-$instance_id\"\n    \n    if [ ! -d \"$instance_dir\" ]; then\n        echo \"❌ Instance $instance_id does not exist\"\n        return 1\n    fi\n    \n    echo \"🛑 Stopping AlgoFactory instance: $instance_id\"\n    \n    # Kill by PID file\n    if [ -f \"$instance_dir/app.pid\" ]; then\n        local pid=$(cat \"$instance_dir/app.pid\")\n        if kill -0 \"$pid\" 2>/dev/null; then\n            kill \"$pid\"\n            echo \"✅ Stopped process $pid\"\n        fi\n        rm -f \"$instance_dir/app.pid\"\n    fi\n    \n    # Kill by port\n    local flask_port=$instance_id\n    pkill -f \"python.*app.py.*$flask_port\" || true\n    \n    echo \"✅ Instance $instance_id stopped\"\n}\n\n# Function to list instances\nlist_instances() {\n    echo \"📋 AlgoFactory Instances:\"\n    echo \"\"\n    \n    if [ ! -d \"$INSTANCES_DIR\" ]; then\n        echo \"   No instances found\"\n        return\n    fi\n    \n    for instance_path in \"$INSTANCES_DIR\"/algofactory-*; do\n        if [ -d \"$instance_path\" ]; then\n            local instance_name=$(basename \"$instance_path\")\n            local instance_id=${instance_name#algofactory-}\n            local pid_file=\"$instance_path/app.pid\"\n            local status=\"STOPPED\"\n            \n            if [ -f \"$pid_file\" ]; then\n                local pid=$(cat \"$pid_file\")\n                if kill -0 \"$pid\" 2>/dev/null; then\n                    status=\"RUNNING (PID: $pid)\"\n                fi\n            fi\n            \n            echo \"   $instance_id - $status\"\n            echo \"     Domain: $instance_id.algofactory.in\"\n            echo \"     Path: $instance_path\"\n            echo \"\"\n        fi\n    done\n}\n\n# Function to create Nginx configuration\ncreate_nginx_config() {\n    local instance_id=$1\n    local flask_port=$instance_id\n    \n    echo \"🌐 Creating Nginx configuration for instance: $instance_id\"\n    \n    # Create Nginx config\n    sudo tee \"/etc/nginx/sites-available/$instance_id.algofactory.in.conf\" > /dev/null << EOF\nserver {\n    listen 80;\n    server_name $instance_id.algofactory.in;\n    \n    location / {\n        proxy_pass http://127.0.0.1:$flask_port;\n        proxy_set_header Host \\$host;\n        proxy_set_header X-Real-IP \\$remote_addr;\n        proxy_set_header X-Forwarded-For \\$proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto \\$scheme;\n        \n        # WebSocket support\n        proxy_http_version 1.1;\n        proxy_set_header Upgrade \\$http_upgrade;\n        proxy_set_header Connection \"upgrade\";\n        \n        # Timeouts\n        proxy_connect_timeout 60s;\n        proxy_send_timeout 60s;\n        proxy_read_timeout 60s;\n    }\n}\nEOF\n    \n    # Enable the site\n    sudo ln -sf \"/etc/nginx/sites-available/$instance_id.algofactory.in.conf\" \"/etc/nginx/sites-enabled/\"\n    \n    # Test and reload Nginx\n    sudo nginx -t && sudo systemctl reload nginx\n    \n    echo \"✅ Nginx configuration created for $instance_id.algofactory.in\"\n    echo \"🔒 To get SSL certificate, run:\"\n    echo \"   sudo certbot --nginx -d $instance_id.algofactory.in\"\n}\n\n# Function to remove instance\nremove_instance() {\n    local instance_id=$1\n    local instance_dir=\"$INSTANCES_DIR/algofactory-$instance_id\"\n    \n    if [ ! -d \"$instance_dir\" ]; then\n        echo \"❌ Instance $instance_id does not exist\"\n        exit 1\n    fi\n    \n    echo \"🗑️  Removing AlgoFactory instance: $instance_id\"\n    \n    # Stop the instance first\n    stop_instance \"$instance_id\" 2>/dev/null || true\n    \n    # Remove instance directory\n    rm -rf \"$instance_dir\"\n    \n    # Remove Nginx config\n    sudo rm -f \"/etc/nginx/sites-enabled/$instance_id.algofactory.in.conf\"\n    sudo rm -f \"/etc/nginx/sites-available/$instance_id.algofactory.in.conf\"\n    sudo nginx -t && sudo systemctl reload nginx 2>/dev/null || true\n    \n    echo \"✅ Instance $instance_id removed completely\"\n}\n\n# Main script logic\ncase \"${1:-}\" in\n    create)\n        if [ -z \"${2:-}\" ]; then\n            echo \"❌ Error: Instance ID required\"\n            usage\n        fi\n        create_instance \"$2\"\n        ;;\n    start)\n        if [ -z \"${2:-}\" ]; then\n            echo \"❌ Error: Instance ID required\"\n            usage\n        fi\n        start_instance \"$2\"\n        ;;\n    stop)\n        if [ -z \"${2:-}\" ]; then\n            echo \"❌ Error: Instance ID required\"\n            usage\n        fi\n        stop_instance \"$2\"\n        ;;\n    list)\n        list_instances\n        ;;\n    nginx)\n        if [ -z \"${2:-}\" ]; then\n            echo \"❌ Error: Instance ID required\"\n            usage\n        fi\n        create_nginx_config \"$2\"\n        ;;\n    remove)\n        if [ -z \"${2:-}\" ]; then\n            echo \"❌ Error: Instance ID required\"\n            usage\n        fi\n        remove_instance \"$2\"\n        ;;\n    *)\n        usage\n        ;;\nesac\n"}