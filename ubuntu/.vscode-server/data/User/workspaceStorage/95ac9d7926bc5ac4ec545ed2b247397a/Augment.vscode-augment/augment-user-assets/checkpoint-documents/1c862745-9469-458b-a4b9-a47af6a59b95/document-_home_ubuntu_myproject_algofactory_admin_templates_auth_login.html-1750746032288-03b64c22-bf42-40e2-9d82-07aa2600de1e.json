{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/admin/templates/auth/login.html"}, "originalCode": "<!DOCTYPE html>\n<html lang=\"en\" data-theme=\"light\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>AlgoFactory Admin - Login</title>\n    \n    <!-- DaisyUI + Tailwind CSS -->\n    <link href=\"https://cdn.jsdelivr.net/npm/daisyui@4.4.24/dist/full.min.css\" rel=\"stylesheet\" type=\"text/css\" />\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    \n    <!-- Font Awesome -->\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css\">\n    \n    <style>\n        .login-bg {\n            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n        }\n        \n        .glass-effect {\n            background: rgba(255, 255, 255, 0.1);\n            backdrop-filter: blur(10px);\n            border: 1px solid rgba(255, 255, 255, 0.2);\n        }\n        \n        .floating-animation {\n            animation: float 6s ease-in-out infinite;\n        }\n        \n        @keyframes float {\n            0%, 100% { transform: translateY(0px); }\n            50% { transform: translateY(-20px); }\n        }\n        \n        .pulse-slow {\n            animation: pulse 3s infinite;\n        }\n    </style>\n</head>\n<body class=\"min-h-screen login-bg\">\n    <div class=\"min-h-screen flex items-center justify-center p-4\">\n        <!-- Background Elements -->\n        <div class=\"absolute inset-0 overflow-hidden\">\n            <div class=\"absolute top-1/4 left-1/4 w-64 h-64 bg-white/10 rounded-full blur-3xl floating-animation\"></div>\n            <div class=\"absolute bottom-1/4 right-1/4 w-96 h-96 bg-white/5 rounded-full blur-3xl floating-animation\" style=\"animation-delay: -3s;\"></div>\n        </div>\n        \n        <!-- Login Card -->\n        <div class=\"relative z-10 w-full max-w-md\">\n            <div class=\"card bg-base-100 shadow-2xl\">\n                <div class=\"card-body p-8\">\n                    <!-- Logo and Title -->\n                    <div class=\"text-center mb-8\">\n                        <div class=\"w-16 h-16 bg-primary rounded-2xl flex items-center justify-center mx-auto mb-4 pulse-slow\">\n                            <i class=\"fas fa-cogs text-primary-content text-2xl\"></i>\n                        </div>\n                        <h1 class=\"text-3xl font-bold text-base-content\">AlgoFactory Admin</h1>\n                        <p class=\"text-base-content/70 mt-2\">Server Management Dashboard</p>\n                    </div>\n                    \n                    <!-- Flash Messages -->\n                    {% with messages = get_flashed_messages(with_categories=true) %}\n                        {% if messages %}\n                            <div class=\"mb-6 space-y-2\">\n                                {% for category, message in messages %}\n                                    <div class=\"alert alert-{{ 'success' if category == 'success' else 'error' }} shadow-lg\">\n                                        <div class=\"flex items-center\">\n                                            {% if category == 'success' %}\n                                                <i class=\"fas fa-check-circle\"></i>\n                                            {% else %}\n                                                <i class=\"fas fa-exclamation-circle\"></i>\n                                            {% endif %}\n                                            <span>{{ message }}</span>\n                                        </div>\n                                    </div>\n                                {% endfor %}\n                            </div>\n                        {% endif %}\n                    {% endwith %}\n                    \n                    <!-- Login Form -->\n                    <form method=\"POST\" class=\"space-y-6\">\n                        <div class=\"form-control\">\n                            <label class=\"label\">\n                                <span class=\"label-text font-medium\">\n                                    <i class=\"fas fa-user mr-2\"></i>Username\n                                </span>\n                            </label>\n                            <input \n                                type=\"text\" \n                                name=\"username\" \n                                placeholder=\"Enter your username\" \n                                class=\"input input-bordered w-full focus:input-primary\" \n                                required \n                                autocomplete=\"username\"\n                            />\n                        </div>\n                        \n                        <div class=\"form-control\">\n                            <label class=\"label\">\n                                <span class=\"label-text font-medium\">\n                                    <i class=\"fas fa-lock mr-2\"></i>Password\n                                </span>\n                            </label>\n                            <div class=\"relative\">\n                                <input \n                                    type=\"password\" \n                                    name=\"password\" \n                                    placeholder=\"Enter your password\" \n                                    class=\"input input-bordered w-full focus:input-primary pr-12\" \n                                    required \n                                    autocomplete=\"current-password\"\n                                    id=\"password-input\"\n                                />\n                                <button \n                                    type=\"button\" \n                                    class=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-base-content/50 hover:text-base-content\"\n                                    onclick=\"togglePassword()\"\n                                >\n                                    <i class=\"fas fa-eye\" id=\"password-toggle-icon\"></i>\n                                </button>\n                            </div>\n                        </div>\n                        \n                        <div class=\"form-control\">\n                            <label class=\"label cursor-pointer justify-start gap-3\">\n                                <input type=\"checkbox\" class=\"checkbox checkbox-primary\" />\n                                <span class=\"label-text\">Remember me</span>\n                            </label>\n                        </div>\n                        \n                        <button type=\"submit\" class=\"btn btn-primary w-full\">\n                            <i class=\"fas fa-sign-in-alt mr-2\"></i>\n                            Sign In\n                        </button>\n                    </form>\n                    \n                    <!-- Additional Info -->\n                    <div class=\"divider\">System Information</div>\n                    \n                    <div class=\"grid grid-cols-2 gap-4 text-sm\">\n                        <div class=\"stat bg-base-200 rounded-lg p-3\">\n                            <div class=\"stat-title text-xs\">Server Status</div>\n                            <div class=\"stat-value text-sm text-success\">\n                                <i class=\"fas fa-circle text-xs mr-1\"></i>Online\n                            </div>\n                        </div>\n                        <div class=\"stat bg-base-200 rounded-lg p-3\">\n                            <div class=\"stat-title text-xs\">Last Update</div>\n                            <div class=\"stat-value text-sm\" id=\"last-update\">\n                                <i class=\"fas fa-clock text-xs mr-1\"></i>--\n                            </div>\n                        </div>\n                    </div>\n                    \n                    <!-- Default Credentials Info -->\n                    <div class=\"alert alert-info mt-6\">\n                        <i class=\"fas fa-info-circle\"></i>\n                        <div>\n                            <div class=\"font-bold\">Default Credentials</div>\n                            <div class=\"text-sm\">Username: <code>admin</code> | Password: <code>admin123</code></div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n            \n            <!-- Footer -->\n            <div class=\"text-center mt-6 text-white/80\">\n                <p class=\"text-sm\">\n                    <i class=\"fas fa-shield-alt mr-1\"></i>\n                    Secure Admin Access\n                </p>\n                <p class=\"text-xs mt-1\">\n                    AlgoFactory Server Management System v2.0\n                </p>\n            </div>\n        </div>\n    </div>\n    \n    <script>\n        // Password toggle functionality\n        function togglePassword() {\n            const passwordInput = document.getElementById('password-input');\n            const toggleIcon = document.getElementById('password-toggle-icon');\n            \n            if (passwordInput.type === 'password') {\n                passwordInput.type = 'text';\n                toggleIcon.className = 'fas fa-eye-slash';\n            } else {\n                passwordInput.type = 'password';\n                toggleIcon.className = 'fas fa-eye';\n            }\n        }\n        \n        // Update last update time\n        function updateLastUpdate() {\n            const now = new Date();\n            const timeString = now.toLocaleTimeString();\n            document.getElementById('last-update').innerHTML = `<i class=\"fas fa-clock text-xs mr-1\"></i>${timeString}`;\n        }\n        \n        // Initialize\n        document.addEventListener('DOMContentLoaded', function() {\n            updateLastUpdate();\n            setInterval(updateLastUpdate, 60000); // Update every minute\n            \n            // Focus on username field\n            document.querySelector('input[name=\"username\"]').focus();\n        });\n        \n        // Add some interactive effects\n        document.addEventListener('DOMContentLoaded', function() {\n            // Add ripple effect to login button\n            const loginBtn = document.querySelector('button[type=\"submit\"]');\n            loginBtn.addEventListener('click', function(e) {\n                const ripple = document.createElement('span');\n                const rect = this.getBoundingClientRect();\n                const size = Math.max(rect.width, rect.height);\n                const x = e.clientX - rect.left - size / 2;\n                const y = e.clientY - rect.top - size / 2;\n                \n                ripple.style.width = ripple.style.height = size + 'px';\n                ripple.style.left = x + 'px';\n                ripple.style.top = y + 'px';\n                ripple.classList.add('ripple');\n                \n                this.appendChild(ripple);\n                \n                setTimeout(() => {\n                    ripple.remove();\n                }, 600);\n            });\n        });\n    </script>\n    \n    <style>\n        .ripple {\n            position: absolute;\n            border-radius: 50%;\n            background: rgba(255, 255, 255, 0.3);\n            transform: scale(0);\n            animation: ripple-animation 0.6s linear;\n            pointer-events: none;\n        }\n        \n        @keyframes ripple-animation {\n            to {\n                transform: scale(4);\n                opacity: 0;\n            }\n        }\n    </style>\n</body>\n</html>\n", "modifiedCode": "<!DOCTYPE html>\n<html lang=\"en\" data-theme=\"light\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>AlgoFactory Admin - Login</title>\n    \n    <!-- DaisyUI + Tailwind CSS -->\n    <link href=\"https://cdn.jsdelivr.net/npm/daisyui@4.4.24/dist/full.min.css\" rel=\"stylesheet\" type=\"text/css\" />\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    \n    <!-- Font Awesome -->\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css\">\n    \n    <style>\n        .login-bg {\n            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n        }\n        \n        .glass-effect {\n            background: rgba(255, 255, 255, 0.1);\n            backdrop-filter: blur(10px);\n            border: 1px solid rgba(255, 255, 255, 0.2);\n        }\n        \n        .floating-animation {\n            animation: float 6s ease-in-out infinite;\n        }\n        \n        @keyframes float {\n            0%, 100% { transform: translateY(0px); }\n            50% { transform: translateY(-20px); }\n        }\n        \n        .pulse-slow {\n            animation: pulse 3s infinite;\n        }\n    </style>\n</head>\n<body class=\"min-h-screen login-bg\">\n    <div class=\"min-h-screen flex items-center justify-center p-4\">\n        <!-- Background Elements -->\n        <div class=\"absolute inset-0 overflow-hidden\">\n            <div class=\"absolute top-1/4 left-1/4 w-64 h-64 bg-white/10 rounded-full blur-3xl floating-animation\"></div>\n            <div class=\"absolute bottom-1/4 right-1/4 w-96 h-96 bg-white/5 rounded-full blur-3xl floating-animation\" style=\"animation-delay: -3s;\"></div>\n        </div>\n        \n        <!-- Login Card -->\n        <div class=\"relative z-10 w-full max-w-md\">\n            <div class=\"card bg-base-100 shadow-2xl\">\n                <div class=\"card-body p-8\">\n                    <!-- Logo and Title -->\n                    <div class=\"text-center mb-8\">\n                        <div class=\"w-16 h-16 bg-primary rounded-2xl flex items-center justify-center mx-auto mb-4 pulse-slow\">\n                            <i class=\"fas fa-cogs text-primary-content text-2xl\"></i>\n                        </div>\n                        <h1 class=\"text-3xl font-bold text-base-content\">AlgoFactory Admin</h1>\n                        <p class=\"text-base-content/70 mt-2\">Server Management Dashboard</p>\n                    </div>\n                    \n                    <!-- Flash Messages -->\n                    {% with messages = get_flashed_messages(with_categories=true) %}\n                        {% if messages %}\n                            <div class=\"mb-6 space-y-2\">\n                                {% for category, message in messages %}\n                                    <div class=\"alert alert-{{ 'success' if category == 'success' else 'error' }} shadow-lg\">\n                                        <div class=\"flex items-center\">\n                                            {% if category == 'success' %}\n                                                <i class=\"fas fa-check-circle\"></i>\n                                            {% else %}\n                                                <i class=\"fas fa-exclamation-circle\"></i>\n                                            {% endif %}\n                                            <span>{{ message }}</span>\n                                        </div>\n                                    </div>\n                                {% endfor %}\n                            </div>\n                        {% endif %}\n                    {% endwith %}\n                    \n                    <!-- Login Form -->\n                    <form method=\"POST\" class=\"space-y-6\">\n                        <div class=\"form-control\">\n                            <label class=\"label\">\n                                <span class=\"label-text font-medium\">\n                                    <i class=\"fas fa-user mr-2\"></i>Username\n                                </span>\n                            </label>\n                            <input \n                                type=\"text\" \n                                name=\"username\" \n                                placeholder=\"Enter your username\" \n                                class=\"input input-bordered w-full focus:input-primary\" \n                                required \n                                autocomplete=\"username\"\n                            />\n                        </div>\n                        \n                        <div class=\"form-control\">\n                            <label class=\"label\">\n                                <span class=\"label-text font-medium\">\n                                    <i class=\"fas fa-lock mr-2\"></i>Password\n                                </span>\n                            </label>\n                            <div class=\"relative\">\n                                <input \n                                    type=\"password\" \n                                    name=\"password\" \n                                    placeholder=\"Enter your password\" \n                                    class=\"input input-bordered w-full focus:input-primary pr-12\" \n                                    required \n                                    autocomplete=\"current-password\"\n                                    id=\"password-input\"\n                                />\n                                <button \n                                    type=\"button\" \n                                    class=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-base-content/50 hover:text-base-content\"\n                                    onclick=\"togglePassword()\"\n                                >\n                                    <i class=\"fas fa-eye\" id=\"password-toggle-icon\"></i>\n                                </button>\n                            </div>\n                        </div>\n                        \n                        <div class=\"form-control\">\n                            <label class=\"label cursor-pointer justify-start gap-3\">\n                                <input type=\"checkbox\" class=\"checkbox checkbox-primary\" />\n                                <span class=\"label-text\">Remember me</span>\n                            </label>\n                        </div>\n                        \n                        <button type=\"submit\" class=\"btn btn-primary w-full\">\n                            <i class=\"fas fa-sign-in-alt mr-2\"></i>\n                            Sign In\n                        </button>\n                    </form>\n                    \n                    <!-- Additional Info -->\n                    <div class=\"divider\">System Information</div>\n                    \n                    <div class=\"grid grid-cols-2 gap-4 text-sm\">\n                        <div class=\"stat bg-base-200 rounded-lg p-3\">\n                            <div class=\"stat-title text-xs\">Server Status</div>\n                            <div class=\"stat-value text-sm text-success\">\n                                <i class=\"fas fa-circle text-xs mr-1\"></i>Online\n                            </div>\n                        </div>\n                        <div class=\"stat bg-base-200 rounded-lg p-3\">\n                            <div class=\"stat-title text-xs\">Last Update</div>\n                            <div class=\"stat-value text-sm\" id=\"last-update\">\n                                <i class=\"fas fa-clock text-xs mr-1\"></i>--\n                            </div>\n                        </div>\n                    </div>\n                    \n                    <!-- Default Credentials Info (Hidden by default) -->\n                    <div class=\"alert alert-info mt-6\" id=\"credentials-info\" style=\"display: none;\">\n                        <i class=\"fas fa-info-circle\"></i>\n                        <div>\n                            <div class=\"font-bold\">Default Credentials</div>\n                            <div class=\"text-sm\">Username: <code>admin</code> | Password: <code>admin123</code></div>\n                        </div>\n                    </div>\n\n                    <!-- Show credentials button -->\n                    <div class=\"text-center mt-4\">\n                        <button type=\"button\" class=\"btn btn-ghost btn-sm\" onclick=\"toggleCredentials()\">\n                            <i class=\"fas fa-eye mr-1\"></i>\n                            <span id=\"credentials-btn-text\">Show Default Credentials</span>\n                        </button>\n                    </div>\n                </div>\n            </div>\n            \n            <!-- Footer -->\n            <div class=\"text-center mt-6 text-white/80\">\n                <p class=\"text-sm\">\n                    <i class=\"fas fa-shield-alt mr-1\"></i>\n                    Secure Admin Access\n                </p>\n                <p class=\"text-xs mt-1\">\n                    AlgoFactory Server Management System v2.0\n                </p>\n            </div>\n        </div>\n    </div>\n    \n    <script>\n        // Password toggle functionality\n        function togglePassword() {\n            const passwordInput = document.getElementById('password-input');\n            const toggleIcon = document.getElementById('password-toggle-icon');\n            \n            if (passwordInput.type === 'password') {\n                passwordInput.type = 'text';\n                toggleIcon.className = 'fas fa-eye-slash';\n            } else {\n                passwordInput.type = 'password';\n                toggleIcon.className = 'fas fa-eye';\n            }\n        }\n        \n        // Update last update time\n        function updateLastUpdate() {\n            const now = new Date();\n            const timeString = now.toLocaleTimeString();\n            document.getElementById('last-update').innerHTML = `<i class=\"fas fa-clock text-xs mr-1\"></i>${timeString}`;\n        }\n        \n        // Initialize\n        document.addEventListener('DOMContentLoaded', function() {\n            updateLastUpdate();\n            setInterval(updateLastUpdate, 60000); // Update every minute\n            \n            // Focus on username field\n            document.querySelector('input[name=\"username\"]').focus();\n        });\n        \n        // Toggle credentials visibility\n        function toggleCredentials() {\n            const credentialsInfo = document.getElementById('credentials-info');\n            const btnText = document.getElementById('credentials-btn-text');\n            const btn = event.target.closest('button');\n            const icon = btn.querySelector('i');\n\n            if (credentialsInfo.style.display === 'none') {\n                credentialsInfo.style.display = 'block';\n                btnText.textContent = 'Hide Default Credentials';\n                icon.className = 'fas fa-eye-slash mr-1';\n            } else {\n                credentialsInfo.style.display = 'none';\n                btnText.textContent = 'Show Default Credentials';\n                icon.className = 'fas fa-eye mr-1';\n            }\n        }\n\n        // Add some interactive effects\n        document.addEventListener('DOMContentLoaded', function() {\n            // Add ripple effect to login button\n            const loginBtn = document.querySelector('button[type=\"submit\"]');\n            loginBtn.addEventListener('click', function(e) {\n                const ripple = document.createElement('span');\n                const rect = this.getBoundingClientRect();\n                const size = Math.max(rect.width, rect.height);\n                const x = e.clientX - rect.left - size / 2;\n                const y = e.clientY - rect.top - size / 2;\n\n                ripple.style.width = ripple.style.height = size + 'px';\n                ripple.style.left = x + 'px';\n                ripple.style.top = y + 'px';\n                ripple.classList.add('ripple');\n\n                this.appendChild(ripple);\n\n                setTimeout(() => {\n                    ripple.remove();\n                }, 600);\n            });\n        });\n    </script>\n    \n    <style>\n        .ripple {\n            position: absolute;\n            border-radius: 50%;\n            background: rgba(255, 255, 255, 0.3);\n            transform: scale(0);\n            animation: ripple-animation 0.6s linear;\n            pointer-events: none;\n        }\n        \n        @keyframes ripple-animation {\n            to {\n                transform: scale(4);\n                opacity: 0;\n            }\n        }\n    </style>\n</body>\n</html>\n"}