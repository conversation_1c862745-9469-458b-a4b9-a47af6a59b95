{"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/setup.html"}, "originalCode": "{% extends \"layout.html\" %}\r\n\r\n{% block title %}Setup AlgoFactory Account{% endblock %}\r\n\r\n{% block head %}\r\n<style>\r\n    .requirement-item {\r\n        @apply flex items-center gap-2 text-sm py-1;\r\n    }\r\n\r\n    .requirement-met {\r\n        @apply text-success;\r\n    }\r\n\r\n    .requirement-met svg {\r\n        @apply opacity-100;\r\n    }\r\n\r\n    .requirement-not-met {\r\n        @apply text-base-content/60;\r\n    }\r\n\r\n    .requirement-not-met svg {\r\n        @apply opacity-0;\r\n    }\r\n\r\n    .password-strength-text {\r\n        @apply text-xs mt-1 font-medium;\r\n    }\r\n</style>\r\n\r\n<script>\r\n    document.addEventListener('DOMContentLoaded', function() {\r\n        // Handle username prefixing\r\n        const usernameSuffix = document.getElementById('username_suffix');\r\n        const usernameHidden = document.getElementById('username');\r\n        const instanceId = '{{ instance_id or \"8010\" }}';\r\n\r\n        function updateUsername() {\r\n            const suffix = usernameSuffix.value.trim();\r\n            usernameHidden.value = suffix ? `${instanceId}_${suffix}` : '';\r\n        }\r\n\r\n        usernameSuffix.addEventListener('input', updateUsername);\r\n        usernameSuffix.addEventListener('blur', updateUsername);\r\n\r\n        // Password validation\r\n        const password = document.getElementById('password');\r\n        const confirmPassword = document.getElementById('confirm_password');\r\n        const passwordMatchMessage = document.getElementById('passwordMatchMessage');\r\n        const submitButton = document.getElementById('submitButton');\r\n        const strengthMeter = document.getElementById('strengthMeter');\r\n        const strengthText = document.getElementById('strengthText');\r\n        const requirements = {\r\n            length: document.getElementById('req-length'),\r\n            uppercase: document.getElementById('req-uppercase'),\r\n            lowercase: document.getElementById('req-lowercase'),\r\n            number: document.getElementById('req-number'),\r\n            special: document.getElementById('req-special')\r\n        };\r\n\r\n        function calculatePasswordStrength(password) {\r\n            let score = 0;\r\n            \r\n            if (password.length >= 8) score += 20;\r\n            if (password.length >= 12) score += 10;\r\n            if (password.length >= 16) score += 10;\r\n            if (/[A-Z]/.test(password)) score += 15;\r\n            if (/[a-z]/.test(password)) score += 15;\r\n            if (/[0-9]/.test(password)) score += 15;\r\n            if (/[!@#$%^&*]/.test(password)) score += 15;\r\n            \r\n            return score;\r\n        }\r\n\r\n        function updateStrengthMeter(password) {\r\n            const score = calculatePasswordStrength(password);\r\n            let strength, className;\r\n\r\n            if (score >= 80) {\r\n                strength = 'Strong';\r\n                className = 'progress-success';\r\n            } else if (score >= 50) {\r\n                strength = 'Medium';\r\n                className = 'progress-warning';\r\n            } else if (score > 0) {\r\n                strength = 'Weak';\r\n                className = 'progress-error';\r\n            } else {\r\n                strength = '';\r\n                className = '';\r\n            }\r\n\r\n            strengthMeter.className = `progress ${className}`;\r\n            strengthMeter.value = score;\r\n            strengthText.textContent = strength;\r\n            strengthText.className = `password-strength-text ${className ? 'text-' + className.replace('progress-', '') : ''}`;\r\n        }\r\n\r\n        function checkPasswordRequirements(password) {\r\n            const checks = {\r\n                length: password.length >= 8,\r\n                uppercase: /[A-Z]/.test(password),\r\n                lowercase: /[a-z]/.test(password),\r\n                number: /[0-9]/.test(password),\r\n                special: /[!@#$%^&*]/.test(password)\r\n            };\r\n\r\n            Object.keys(checks).forEach(req => {\r\n                const element = requirements[req];\r\n                if (checks[req]) {\r\n                    element.classList.remove('requirement-not-met');\r\n                    element.classList.add('requirement-met');\r\n                } else {\r\n                    element.classList.remove('requirement-met');\r\n                    element.classList.add('requirement-not-met');\r\n                }\r\n            });\r\n\r\n            return Object.values(checks).every(Boolean);\r\n        }\r\n\r\n        function validateForm() {\r\n            const passwordsMatch = password.value === confirmPassword.value;\r\n            const meetsRequirements = checkPasswordRequirements(password.value);\r\n            const allFieldsFilled = Array.from(document.querySelectorAll('input[required]'))\r\n                .every(input => input.value.trim() !== '');\r\n\r\n            if (passwordsMatch && meetsRequirements && allFieldsFilled) {\r\n                submitButton.disabled = false;\r\n                passwordMatchMessage.textContent = 'Passwords match';\r\n                passwordMatchMessage.className = 'label-text-alt mt-1 text-success';\r\n            } else {\r\n                submitButton.disabled = true;\r\n                if (!passwordsMatch && confirmPassword.value) {\r\n                    passwordMatchMessage.textContent = 'Passwords do not match';\r\n                    passwordMatchMessage.className = 'label-text-alt mt-1 text-error';\r\n                } else if (!meetsRequirements) {\r\n                    passwordMatchMessage.textContent = 'Password does not meet requirements';\r\n                    passwordMatchMessage.className = 'label-text-alt mt-1 text-warning';\r\n                } else {\r\n                    passwordMatchMessage.textContent = '';\r\n                }\r\n            }\r\n        }\r\n\r\n        password.addEventListener('input', function() {\r\n            validateForm();\r\n            updateStrengthMeter(this.value);\r\n        });\r\n        confirmPassword.addEventListener('input', validateForm);\r\n        document.querySelectorAll('input[required]').forEach(input => {\r\n            input.addEventListener('input', validateForm);\r\n        });\r\n\r\n        document.getElementById('setupForm').addEventListener('submit', function(event) {\r\n            if (!checkPasswordRequirements(password.value) || password.value !== confirmPassword.value) {\r\n                event.preventDefault();\r\n                validateForm();\r\n            }\r\n        });\r\n    });\r\n</script>\r\n{% endblock %}\r\n\r\n{% block content %}\r\n<div class=\"min-h-[calc(100vh-8rem)] flex items-center justify-center\">\r\n    <div class=\"container mx-auto px-4\">\r\n        <div class=\"flex flex-col lg:flex-row items-center justify-between gap-16\">\r\n            <!-- Left side content -->\r\n            <div class=\"flex-1 max-w-xl\">\r\n                <h1 class=\"text-5xl font-bold mb-6\">Initial <span class=\"text-primary\">Setup</span></h1>\r\n                <p class=\"text-xl mb-8 opacity-80\">\r\n                    Welcome to AlgoFactory! Create your administrator account to get started with algorithmic trading. This account will have full access to manage the platform.\r\n                </p>\r\n                <div class=\"alert alert-info shadow-lg\">\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" class=\"stroke-current shrink-0 w-6 h-6\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                    </svg>\r\n                    <div>\r\n                        <h3 class=\"font-bold\">First Time Setup</h3>\r\n                        <div class=\"text-sm\">This form will create the initial administrator account. You'll receive a TOTP QR code for password resets after setup.</div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- Right side setup form -->\r\n            <div class=\"card flex-shrink-0 w-full max-w-lg shadow-2xl bg-base-100\">\r\n                <div class=\"card-body\">\r\n                    <form id=\"setupForm\" action=\"{{ url_for('core_bp.setup') }}\" method=\"post\" class=\"space-y-4\">\r\n                        <input type=\"hidden\" name=\"csrf_token\" value=\"{{ csrf_token() }}\"/>\r\n                        <!-- Username -->\r\n                        <div class=\"form-control\">\r\n                            <label class=\"label\">\r\n                                <span class=\"label-text\">Username</span>\r\n                                <span class=\"label-text-alt\">Instance: {{ instance_id or '8010' }}</span>\r\n                            </label>\r\n                            <div class=\"flex\">\r\n                                <span class=\"inline-flex items-center px-3 text-sm bg-base-200 border border-r-0 border-base-300 rounded-l-md font-mono\">\r\n                                    {{ instance_id or '8010' }}_\r\n                                </span>\r\n                                <input type=\"text\"\r\n                                       id=\"username_suffix\"\r\n                                       required\r\n                                       class=\"input input-bordered w-full rounded-l-none\"\r\n                                       placeholder=\"Choose username\"\r\n                                       autocomplete=\"username\">\r\n                                <input type=\"hidden\"\r\n                                       id=\"username\"\r\n                                       name=\"username\" />\r\n                            </div>\r\n                            <label class=\"label\">\r\n                                <span class=\"label-text-alt\">Your username will be: {{ instance_id or '8010' }}_[your_input]</span>\r\n                            </label>\r\n                        </div>\r\n\r\n                        <!-- Email -->\r\n                        <div class=\"form-control\">\r\n                            <label class=\"label\">\r\n                                <span class=\"label-text\">Email</span>\r\n                            </label>\r\n                            <input type=\"email\" \r\n                                   id=\"email\" \r\n                                   name=\"email\" \r\n                                   required \r\n                                   class=\"input input-bordered\" \r\n                                   placeholder=\"Enter your email\">\r\n                        </div>\r\n\r\n                        <!-- Password -->\r\n                        <div class=\"form-control\">\r\n                            <label class=\"label\">\r\n                                <span class=\"label-text\">Password</span>\r\n                            </label>\r\n                            <input type=\"password\" \r\n                                   id=\"password\" \r\n                                   name=\"password\" \r\n                                   required \r\n                                   class=\"input input-bordered w-full\" \r\n                                   placeholder=\"Choose a password\"\r\n                                   autocomplete=\"new-password\">\r\n                            \r\n                            <!-- Password Strength Meter -->\r\n                            <progress id=\"strengthMeter\" class=\"progress w-full mt-2\" value=\"0\" max=\"100\"></progress>\r\n                            <div id=\"strengthText\" class=\"password-strength-text\"></div>\r\n                        </div>\r\n\r\n                        <!-- Confirm Password -->\r\n                        <div class=\"form-control\">\r\n                            <label class=\"label\">\r\n                                <span class=\"label-text\">Confirm Password</span>\r\n                            </label>\r\n                            <input type=\"password\" \r\n                                   id=\"confirm_password\" \r\n                                   name=\"confirm_password\" \r\n                                   required \r\n                                   class=\"input input-bordered\" \r\n                                   placeholder=\"Confirm your password\"\r\n                                   autocomplete=\"new-password\">\r\n                            <div id=\"passwordMatchMessage\" class=\"label-text-alt mt-1\"></div>\r\n                        </div>\r\n\r\n                        <!-- Password Requirements -->\r\n                        <div class=\"space-y-1\">\r\n                            <div id=\"req-length\" class=\"requirement-item requirement-not-met\">\r\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\r\n                                </svg>\r\n                                <span>Minimum 8 characters</span>\r\n                            </div>\r\n                            <div id=\"req-uppercase\" class=\"requirement-item requirement-not-met\">\r\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\r\n                                </svg>\r\n                                <span>At least 1 uppercase letter (A-Z)</span>\r\n                            </div>\r\n                            <div id=\"req-lowercase\" class=\"requirement-item requirement-not-met\">\r\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\r\n                                </svg>\r\n                                <span>At least 1 lowercase letter (a-z)</span>\r\n                            </div>\r\n                            <div id=\"req-number\" class=\"requirement-item requirement-not-met\">\r\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\r\n                                </svg>\r\n                                <span>At least 1 number (0-9)</span>\r\n                            </div>\r\n                            <div id=\"req-special\" class=\"requirement-item requirement-not-met\">\r\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\r\n                                </svg>\r\n                                <span>At least 1 special character (!@#$%^&*)</span>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <!-- Submit Button -->\r\n                        <div class=\"form-control mt-6\">\r\n                            <button type=\"submit\" class=\"btn btn-primary w-full\" id=\"submitButton\" disabled>\r\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\r\n                                </svg>\r\n                                Create Account\r\n                            </button>\r\n                        </div>\r\n                    </form>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n{% endblock %}\r\n", "modifiedCode": "{% extends \"layout.html\" %}\r\n\r\n{% block title %}Setup AlgoFactory Account{% endblock %}\r\n\r\n{% block head %}\r\n<style>\r\n    .requirement-item {\r\n        @apply flex items-center gap-2 text-sm py-1;\r\n    }\r\n\r\n    .requirement-met {\r\n        @apply text-success;\r\n    }\r\n\r\n    .requirement-met svg {\r\n        @apply opacity-100;\r\n    }\r\n\r\n    .requirement-not-met {\r\n        @apply text-base-content/60;\r\n    }\r\n\r\n    .requirement-not-met svg {\r\n        @apply opacity-0;\r\n    }\r\n\r\n    .password-strength-text {\r\n        @apply text-xs mt-1 font-medium;\r\n    }\r\n</style>\r\n\r\n<script>\r\n    document.addEventListener('DOMContentLoaded', function() {\r\n        // Handle username prefixing\r\n        const usernameSuffix = document.getElementById('username_suffix');\r\n        const usernameHidden = document.getElementById('username');\r\n        const instanceId = '{{ instance_id or \"8010\" }}';\r\n\r\n        function updateUsername() {\r\n            const suffix = usernameSuffix.value.trim();\r\n            usernameHidden.value = suffix ? `${instanceId}_${suffix}` : '';\r\n        }\r\n\r\n        usernameSuffix.addEventListener('input', updateUsername);\r\n        usernameSuffix.addEventListener('blur', updateUsername);\r\n\r\n        // Password validation\r\n        const password = document.getElementById('password');\r\n        const confirmPassword = document.getElementById('confirm_password');\r\n        const passwordMatchMessage = document.getElementById('passwordMatchMessage');\r\n        const submitButton = document.getElementById('submitButton');\r\n        const strengthMeter = document.getElementById('strengthMeter');\r\n        const strengthText = document.getElementById('strengthText');\r\n        const requirements = {\r\n            length: document.getElementById('req-length'),\r\n            uppercase: document.getElementById('req-uppercase'),\r\n            lowercase: document.getElementById('req-lowercase'),\r\n            number: document.getElementById('req-number'),\r\n            special: document.getElementById('req-special')\r\n        };\r\n\r\n        function calculatePasswordStrength(password) {\r\n            let score = 0;\r\n            \r\n            if (password.length >= 8) score += 20;\r\n            if (password.length >= 12) score += 10;\r\n            if (password.length >= 16) score += 10;\r\n            if (/[A-Z]/.test(password)) score += 15;\r\n            if (/[a-z]/.test(password)) score += 15;\r\n            if (/[0-9]/.test(password)) score += 15;\r\n            if (/[!@#$%^&*]/.test(password)) score += 15;\r\n            \r\n            return score;\r\n        }\r\n\r\n        function updateStrengthMeter(password) {\r\n            const score = calculatePasswordStrength(password);\r\n            let strength, className;\r\n\r\n            if (score >= 80) {\r\n                strength = 'Strong';\r\n                className = 'progress-success';\r\n            } else if (score >= 50) {\r\n                strength = 'Medium';\r\n                className = 'progress-warning';\r\n            } else if (score > 0) {\r\n                strength = 'Weak';\r\n                className = 'progress-error';\r\n            } else {\r\n                strength = '';\r\n                className = '';\r\n            }\r\n\r\n            strengthMeter.className = `progress ${className}`;\r\n            strengthMeter.value = score;\r\n            strengthText.textContent = strength;\r\n            strengthText.className = `password-strength-text ${className ? 'text-' + className.replace('progress-', '') : ''}`;\r\n        }\r\n\r\n        function checkPasswordRequirements(password) {\r\n            const checks = {\r\n                length: password.length >= 8,\r\n                uppercase: /[A-Z]/.test(password),\r\n                lowercase: /[a-z]/.test(password),\r\n                number: /[0-9]/.test(password),\r\n                special: /[!@#$%^&*]/.test(password)\r\n            };\r\n\r\n            Object.keys(checks).forEach(req => {\r\n                const element = requirements[req];\r\n                if (checks[req]) {\r\n                    element.classList.remove('requirement-not-met');\r\n                    element.classList.add('requirement-met');\r\n                } else {\r\n                    element.classList.remove('requirement-met');\r\n                    element.classList.add('requirement-not-met');\r\n                }\r\n            });\r\n\r\n            return Object.values(checks).every(Boolean);\r\n        }\r\n\r\n        function validateForm() {\r\n            const passwordsMatch = password.value === confirmPassword.value;\r\n            const meetsRequirements = checkPasswordRequirements(password.value);\r\n            const allFieldsFilled = Array.from(document.querySelectorAll('input[required]'))\r\n                .every(input => input.value.trim() !== '');\r\n\r\n            if (passwordsMatch && meetsRequirements && allFieldsFilled) {\r\n                submitButton.disabled = false;\r\n                passwordMatchMessage.textContent = 'Passwords match';\r\n                passwordMatchMessage.className = 'label-text-alt mt-1 text-success';\r\n            } else {\r\n                submitButton.disabled = true;\r\n                if (!passwordsMatch && confirmPassword.value) {\r\n                    passwordMatchMessage.textContent = 'Passwords do not match';\r\n                    passwordMatchMessage.className = 'label-text-alt mt-1 text-error';\r\n                } else if (!meetsRequirements) {\r\n                    passwordMatchMessage.textContent = 'Password does not meet requirements';\r\n                    passwordMatchMessage.className = 'label-text-alt mt-1 text-warning';\r\n                } else {\r\n                    passwordMatchMessage.textContent = '';\r\n                }\r\n            }\r\n        }\r\n\r\n        password.addEventListener('input', function() {\r\n            validateForm();\r\n            updateStrengthMeter(this.value);\r\n        });\r\n        confirmPassword.addEventListener('input', validateForm);\r\n        document.querySelectorAll('input[required]').forEach(input => {\r\n            input.addEventListener('input', validateForm);\r\n        });\r\n\r\n        document.getElementById('setupForm').addEventListener('submit', function(event) {\r\n            if (!checkPasswordRequirements(password.value) || password.value !== confirmPassword.value) {\r\n                event.preventDefault();\r\n                validateForm();\r\n            }\r\n        });\r\n    });\r\n</script>\r\n{% endblock %}\r\n\r\n{% block content %}\r\n<div class=\"min-h-[calc(100vh-8rem)] flex items-center justify-center\">\r\n    <div class=\"container mx-auto px-4\">\r\n        <div class=\"flex flex-col lg:flex-row items-center justify-between gap-16\">\r\n            <!-- Left side content -->\r\n            <div class=\"flex-1 max-w-xl\">\r\n                <h1 class=\"text-5xl font-bold mb-6\">Initial <span class=\"text-primary\">Setup</span></h1>\r\n                <p class=\"text-xl mb-8 opacity-80\">\r\n                    Welcome to AlgoFactory! Create your administrator account to get started with algorithmic trading. This account will have full access to manage the platform.\r\n                </p>\r\n                <div class=\"alert alert-info shadow-lg\">\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" class=\"stroke-current shrink-0 w-6 h-6\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                    </svg>\r\n                    <div>\r\n                        <h3 class=\"font-bold\">First Time Setup</h3>\r\n                        <div class=\"text-sm\">This form will create the initial administrator account. You'll receive a TOTP QR code for password resets after setup.</div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- Right side setup form -->\r\n            <div class=\"card flex-shrink-0 w-full max-w-lg shadow-2xl bg-base-100\">\r\n                <div class=\"card-body\">\r\n                    <form id=\"setupForm\" action=\"{{ url_for('core_bp.setup') }}\" method=\"post\" class=\"space-y-4\">\r\n                        <input type=\"hidden\" name=\"csrf_token\" value=\"{{ csrf_token() }}\"/>\r\n                        <!-- Username -->\r\n                        <div class=\"form-control\">\r\n                            <label class=\"label\">\r\n                                <span class=\"label-text\">Username</span>\r\n                                <span class=\"label-text-alt\">Instance: {{ instance_id or '8010' }}</span>\r\n                            </label>\r\n                            <div class=\"flex\">\r\n                                <span class=\"inline-flex items-center px-3 text-sm bg-base-200 border border-r-0 border-base-300 rounded-l-md font-mono\">\r\n                                    {{ instance_id or '8010' }}_\r\n                                </span>\r\n                                <input type=\"text\"\r\n                                       id=\"username_suffix\"\r\n                                       required\r\n                                       class=\"input input-bordered w-full rounded-l-none\"\r\n                                       placeholder=\"Choose username\"\r\n                                       autocomplete=\"username\">\r\n                                <input type=\"hidden\"\r\n                                       id=\"username\"\r\n                                       name=\"username\" />\r\n                            </div>\r\n                            <label class=\"label\">\r\n                                <span class=\"label-text-alt\">Your username will be: {{ instance_id or '8010' }}_[your_input]</span>\r\n                            </label>\r\n                        </div>\r\n\r\n                        <!-- Email -->\r\n                        <div class=\"form-control\">\r\n                            <label class=\"label\">\r\n                                <span class=\"label-text\">Email</span>\r\n                            </label>\r\n                            <input type=\"email\" \r\n                                   id=\"email\" \r\n                                   name=\"email\" \r\n                                   required \r\n                                   class=\"input input-bordered\" \r\n                                   placeholder=\"Enter your email\">\r\n                        </div>\r\n\r\n                        <!-- Password -->\r\n                        <div class=\"form-control\">\r\n                            <label class=\"label\">\r\n                                <span class=\"label-text\">Password</span>\r\n                            </label>\r\n                            <input type=\"password\" \r\n                                   id=\"password\" \r\n                                   name=\"password\" \r\n                                   required \r\n                                   class=\"input input-bordered w-full\" \r\n                                   placeholder=\"Choose a password\"\r\n                                   autocomplete=\"new-password\">\r\n                            \r\n                            <!-- Password Strength Meter -->\r\n                            <progress id=\"strengthMeter\" class=\"progress w-full mt-2\" value=\"0\" max=\"100\"></progress>\r\n                            <div id=\"strengthText\" class=\"password-strength-text\"></div>\r\n                        </div>\r\n\r\n                        <!-- Confirm Password -->\r\n                        <div class=\"form-control\">\r\n                            <label class=\"label\">\r\n                                <span class=\"label-text\">Confirm Password</span>\r\n                            </label>\r\n                            <input type=\"password\" \r\n                                   id=\"confirm_password\" \r\n                                   name=\"confirm_password\" \r\n                                   required \r\n                                   class=\"input input-bordered\" \r\n                                   placeholder=\"Confirm your password\"\r\n                                   autocomplete=\"new-password\">\r\n                            <div id=\"passwordMatchMessage\" class=\"label-text-alt mt-1\"></div>\r\n                        </div>\r\n\r\n                        <!-- Password Requirements -->\r\n                        <div class=\"space-y-1\">\r\n                            <div id=\"req-length\" class=\"requirement-item requirement-not-met\">\r\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\r\n                                </svg>\r\n                                <span>Minimum 8 characters</span>\r\n                            </div>\r\n                            <div id=\"req-uppercase\" class=\"requirement-item requirement-not-met\">\r\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\r\n                                </svg>\r\n                                <span>At least 1 uppercase letter (A-Z)</span>\r\n                            </div>\r\n                            <div id=\"req-lowercase\" class=\"requirement-item requirement-not-met\">\r\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\r\n                                </svg>\r\n                                <span>At least 1 lowercase letter (a-z)</span>\r\n                            </div>\r\n                            <div id=\"req-number\" class=\"requirement-item requirement-not-met\">\r\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\r\n                                </svg>\r\n                                <span>At least 1 number (0-9)</span>\r\n                            </div>\r\n                            <div id=\"req-special\" class=\"requirement-item requirement-not-met\">\r\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\r\n                                </svg>\r\n                                <span>At least 1 special character (!@#$%^&*)</span>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <!-- Submit Button -->\r\n                        <div class=\"form-control mt-6\">\r\n                            <button type=\"submit\" class=\"btn btn-primary w-full\" id=\"submitButton\" disabled>\r\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\r\n                                </svg>\r\n                                Create Account\r\n                            </button>\r\n                        </div>\r\n                    </form>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n{% endblock %}\r\n"}