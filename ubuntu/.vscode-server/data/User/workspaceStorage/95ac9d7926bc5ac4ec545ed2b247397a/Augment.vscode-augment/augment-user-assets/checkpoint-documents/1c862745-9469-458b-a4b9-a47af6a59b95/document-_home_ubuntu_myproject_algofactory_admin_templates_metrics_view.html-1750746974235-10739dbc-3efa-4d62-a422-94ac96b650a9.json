{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/admin/templates/metrics/view.html"}, "modifiedCode": "{% extends \"base.html\" %}\n\n{% block title %}Performance Metrics - AlgoFactory Admin{% endblock %}\n{% block page_title %}Performance Metrics{% endblock %}\n\n{% block content %}\n<!-- Metrics Overview -->\n<div class=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n    <div class=\"stat bg-base-200 rounded-xl shadow-sm\">\n        <div class=\"stat-figure text-primary\">\n            <i class=\"fas fa-tachometer-alt text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">Avg Response Time</div>\n        <div class=\"stat-value text-primary\">245ms</div>\n        <div class=\"stat-desc\">Last 24 hours</div>\n    </div>\n    \n    <div class=\"stat bg-base-200 rounded-xl shadow-sm\">\n        <div class=\"stat-figure text-secondary\">\n            <i class=\"fas fa-users text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">Active Users</div>\n        <div class=\"stat-value text-secondary\">12</div>\n        <div class=\"stat-desc\">Currently online</div>\n    </div>\n    \n    <div class=\"stat bg-base-200 rounded-xl shadow-sm\">\n        <div class=\"stat-figure text-accent\">\n            <i class=\"fas fa-exchange-alt text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">Requests/min</div>\n        <div class=\"stat-value text-accent\">1,234</div>\n        <div class=\"stat-desc\">Peak: 2,456</div>\n    </div>\n    \n    <div class=\"stat bg-base-200 rounded-xl shadow-sm\">\n        <div class=\"stat-figure text-info\">\n            <i class=\"fas fa-check-circle text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">Uptime</div>\n        <div class=\"stat-value text-info\">99.9%</div>\n        <div class=\"stat-desc\">This month</div>\n    </div>\n</div>\n\n<!-- Performance Charts -->\n<div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\">\n    <div class=\"card bg-base-200 shadow-sm\">\n        <div class=\"card-body\">\n            <h2 class=\"card-title\">\n                <i class=\"fas fa-chart-line text-primary\"></i>\n                Response Time Trends\n            </h2>\n            <div class=\"h-64\">\n                <canvas id=\"response-time-chart\"></canvas>\n            </div>\n        </div>\n    </div>\n    \n    <div class=\"card bg-base-200 shadow-sm\">\n        <div class=\"card-body\">\n            <h2 class=\"card-title\">\n                <i class=\"fas fa-chart-bar text-secondary\"></i>\n                Request Volume\n            </h2>\n            <div class=\"h-64\">\n                <canvas id=\"request-volume-chart\"></canvas>\n            </div>\n        </div>\n    </div>\n</div>\n\n<!-- Detailed Metrics -->\n<div class=\"card bg-base-200 shadow-sm\">\n    <div class=\"card-body\">\n        <h2 class=\"card-title mb-6\">\n            <i class=\"fas fa-table text-primary\"></i>\n            Detailed Performance Metrics\n        </h2>\n        \n        <div class=\"overflow-x-auto\">\n            <table class=\"table table-zebra w-full\">\n                <thead>\n                    <tr>\n                        <th>Metric</th>\n                        <th>Current</th>\n                        <th>Average</th>\n                        <th>Peak</th>\n                        <th>Status</th>\n                    </tr>\n                </thead>\n                <tbody>\n                    <tr>\n                        <td>CPU Usage</td>\n                        <td>15.2%</td>\n                        <td>12.8%</td>\n                        <td>89.5%</td>\n                        <td><div class=\"badge badge-success\">Good</div></td>\n                    </tr>\n                    <tr>\n                        <td>Memory Usage</td>\n                        <td>78.4%</td>\n                        <td>75.2%</td>\n                        <td>94.1%</td>\n                        <td><div class=\"badge badge-warning\">High</div></td>\n                    </tr>\n                    <tr>\n                        <td>Disk I/O</td>\n                        <td>2.1 MB/s</td>\n                        <td>1.8 MB/s</td>\n                        <td>15.6 MB/s</td>\n                        <td><div class=\"badge badge-success\">Good</div></td>\n                    </tr>\n                    <tr>\n                        <td>Network I/O</td>\n                        <td>5.4 MB/s</td>\n                        <td>4.2 MB/s</td>\n                        <td>25.8 MB/s</td>\n                        <td><div class=\"badge badge-success\">Good</div></td>\n                    </tr>\n                    <tr>\n                        <td>Active Connections</td>\n                        <td>156</td>\n                        <td>142</td>\n                        <td>892</td>\n                        <td><div class=\"badge badge-success\">Good</div></td>\n                    </tr>\n                </tbody>\n            </table>\n        </div>\n    </div>\n</div>\n{% endblock %}\n\n{% block scripts %}\n<script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n<script>\n    document.addEventListener('DOMContentLoaded', function() {\n        initializeCharts();\n    });\n    \n    function initializeCharts() {\n        // Response Time Chart\n        const responseTimeCtx = document.getElementById('response-time-chart').getContext('2d');\n        new Chart(responseTimeCtx, {\n            type: 'line',\n            data: {\n                labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],\n                datasets: [{\n                    label: 'Response Time (ms)',\n                    data: [180, 220, 245, 280, 320, 290, 245],\n                    borderColor: 'rgb(59, 130, 246)',\n                    backgroundColor: 'rgba(59, 130, 246, 0.1)',\n                    tension: 0.4\n                }]\n            },\n            options: {\n                responsive: true,\n                maintainAspectRatio: false,\n                scales: {\n                    y: {\n                        beginAtZero: true\n                    }\n                }\n            }\n        });\n        \n        // Request Volume Chart\n        const requestVolumeCtx = document.getElementById('request-volume-chart').getContext('2d');\n        new Chart(requestVolumeCtx, {\n            type: 'bar',\n            data: {\n                labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],\n                datasets: [{\n                    label: 'Requests (thousands)',\n                    data: [45, 52, 48, 61, 58, 42, 38],\n                    backgroundColor: 'rgba(16, 185, 129, 0.8)',\n                    borderColor: 'rgb(16, 185, 129)',\n                    borderWidth: 1\n                }]\n            },\n            options: {\n                responsive: true,\n                maintainAspectRatio: false,\n                scales: {\n                    y: {\n                        beginAtZero: true\n                    }\n                }\n            }\n        });\n    }\n</script>\n{% endblock %}\n"}