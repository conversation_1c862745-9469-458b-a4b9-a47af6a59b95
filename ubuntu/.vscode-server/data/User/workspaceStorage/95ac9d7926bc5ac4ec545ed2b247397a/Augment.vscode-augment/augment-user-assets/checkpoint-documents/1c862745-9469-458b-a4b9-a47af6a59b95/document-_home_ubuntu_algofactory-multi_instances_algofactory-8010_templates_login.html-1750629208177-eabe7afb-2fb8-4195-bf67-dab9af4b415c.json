{"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/login.html"}, "originalCode": "{% extends \"layout.html\" %}\r\n\r\n{% block title %}AlgoFactory Login{% endblock %}\r\n\r\n{% block head %}\r\n<script>\r\n    async function login(event) {\r\n        event.preventDefault();\r\n\r\n        const form = document.getElementById('loginForm');\r\n        const formData = new FormData(form);\r\n        const loginStatus = document.getElementById('loginStatus');\r\n        \r\n        loginStatus.classList.remove('alert-error', 'alert-success', 'hidden');\r\n        loginStatus.classList.add('alert-info');\r\n        loginStatus.querySelector('span').textContent = 'Logging in...';\r\n\r\n        try {\r\n            const response = await fetch('/auth/login', {\r\n                method: 'POST',\r\n                body: formData\r\n            });\r\n            const data = await response.json();\r\n\r\n            if (data.status === 'error') {\r\n                loginStatus.classList.remove('alert-info');\r\n                loginStatus.classList.add('alert-error');\r\n                loginStatus.querySelector('span').textContent = data.message || 'Login failed';\r\n            } else {\r\n                loginStatus.classList.remove('alert-info');\r\n                loginStatus.classList.add('alert-success');\r\n                loginStatus.querySelector('span').textContent = 'Login successful';\r\n                window.location.href = '/auth/broker';\r\n            }\r\n        } catch (error) {\r\n            loginStatus.classList.remove('alert-info');\r\n            loginStatus.classList.add('alert-error');\r\n            loginStatus.querySelector('span').textContent = 'Login failed: Please try again';\r\n        }\r\n    }\r\n</script>\r\n{% endblock %}\r\n\r\n{% block content %}\r\n<div class=\"min-h-[calc(100vh-8rem)] flex items-center justify-center py-8\">\r\n    <div class=\"container mx-auto px-4\">\r\n        <div class=\"flex flex-col lg:flex-row items-center justify-between gap-8 lg:gap-16\">\r\n            <!-- Right side login form - Shown first on mobile -->\r\n            <div class=\"card flex-shrink-0 w-full max-w-md shadow-2xl bg-base-100 order-1 lg:order-2\">\r\n                <div class=\"card-body\">\r\n                    <div class=\"flex justify-center mb-6\">\r\n                        <img class=\"h-20 w-auto\" src=\"{{ url_for('static', filename='favicon/apple-touch-icon.png') }}\" alt=\"AlgoFactory\">\r\n                    </div>\r\n\r\n                    <form id=\"loginForm\" onsubmit=\"login(event)\" class=\"space-y-4\">\r\n                        <input type=\"hidden\" name=\"csrf_token\" value=\"{{ csrf_token() }}\"/>\r\n                        <div class=\"form-control\">\r\n                            <label class=\"label\">\r\n                                <span class=\"label-text\">Username</span>\r\n                            </label>\r\n                            <input type=\"text\" \r\n                                   id=\"username\" \r\n                                   name=\"username\" \r\n                                   required \r\n                                   class=\"input input-bordered\" \r\n                                   placeholder=\"Enter your username\" />\r\n                        </div>\r\n\r\n                        <div class=\"form-control\">\r\n                            <label class=\"label\">\r\n                                <span class=\"label-text\">Password</span>\r\n                            </label>\r\n                            <input type=\"password\" \r\n                                   id=\"password\" \r\n                                   name=\"password\" \r\n                                   required \r\n                                   class=\"input input-bordered\" \r\n                                   placeholder=\"Enter your password\" />\r\n                            <label class=\"label\">\r\n                                <a href=\"{{ url_for('auth.reset_password') }}\" class=\"label-text-alt link link-hover\">Forgot password?</a>\r\n                            </label>\r\n                        </div>\r\n\r\n                        <div id=\"loginStatus\" class=\"alert hidden\">\r\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"stroke-current shrink-0 h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\" />\r\n                            </svg>\r\n                            <span></span>\r\n                        </div>\r\n\r\n                        <div class=\"form-control mt-6\">\r\n                            <button type=\"submit\" class=\"btn btn-primary w-full\">\r\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1\" />\r\n                                </svg>\r\n                                Sign in\r\n                            </button>\r\n                        </div>\r\n                    </form>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- Left side content - Shown second on mobile -->\r\n            <div class=\"flex-1 max-w-xl text-center lg:text-left order-2 lg:order-1\">\r\n                <h1 class=\"text-4xl lg:text-5xl font-bold mb-6\">Welcome to <span class=\"text-primary\">AlgoFactory</span></h1>\r\n                <p class=\"text-lg lg:text-xl mb-8 opacity-80\">\r\n                    Sign in to your account to access your trading dashboard and manage your algorithmic trading strategies.\r\n                </p>\r\n                <div class=\"flex flex-col gap-4\">\r\n                    <div class=\"alert alert-info shadow-lg\">\r\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" class=\"stroke-current shrink-0 w-6 h-6\">\r\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                        </svg>\r\n                        <div>\r\n                            <h3 class=\"font-bold\">First Time User?</h3>\r\n                            <div class=\"text-sm\">Contact your administrator to set up your account.</div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"flex justify-center lg:justify-start gap-4\">\r\n                        <a href=\"https://github.com/algofactory.in\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"btn btn-outline gap-2\">\r\n                            <svg class=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                                <path d=\"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z\"/>\r\n                            </svg>\r\n                            GitHub\r\n                        </a>\r\n                        <a href=\"https://www.algofactory.in\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"btn btn-outline gap-2\">\r\n                            <svg class=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                                <path d=\"M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.946 2.4189-2.1568 2.4189Z\"/>\r\n                            </svg>\r\n                            Discord\r\n                        </a>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n{% endblock %}\r\n", "modifiedCode": "{% extends \"layout.html\" %}\r\n\r\n{% block title %}AlgoFactory Login{% endblock %}\r\n\r\n{% block head %}\r\n<script>\r\n    async function login(event) {\r\n        event.preventDefault();\r\n\r\n        const form = document.getElementById('loginForm');\r\n        const formData = new FormData(form);\r\n        const loginStatus = document.getElementById('loginStatus');\r\n        \r\n        loginStatus.classList.remove('alert-error', 'alert-success', 'hidden');\r\n        loginStatus.classList.add('alert-info');\r\n        loginStatus.querySelector('span').textContent = 'Logging in...';\r\n\r\n        try {\r\n            const response = await fetch('/auth/login', {\r\n                method: 'POST',\r\n                body: formData\r\n            });\r\n            const data = await response.json();\r\n\r\n            if (data.status === 'error') {\r\n                loginStatus.classList.remove('alert-info');\r\n                loginStatus.classList.add('alert-error');\r\n                loginStatus.querySelector('span').textContent = data.message || 'Login failed';\r\n            } else {\r\n                loginStatus.classList.remove('alert-info');\r\n                loginStatus.classList.add('alert-success');\r\n                loginStatus.querySelector('span').textContent = 'Login successful';\r\n                window.location.href = '/auth/broker';\r\n            }\r\n        } catch (error) {\r\n            loginStatus.classList.remove('alert-info');\r\n            loginStatus.classList.add('alert-error');\r\n            loginStatus.querySelector('span').textContent = 'Login failed: Please try again';\r\n        }\r\n    }\r\n</script>\r\n{% endblock %}\r\n\r\n{% block content %}\r\n<div class=\"min-h-[calc(100vh-8rem)] flex items-center justify-center py-8\">\r\n    <div class=\"container mx-auto px-4\">\r\n        <div class=\"flex flex-col lg:flex-row items-center justify-between gap-8 lg:gap-16\">\r\n            <!-- Right side login form - Shown first on mobile -->\r\n            <div class=\"card flex-shrink-0 w-full max-w-md shadow-2xl bg-base-100 order-1 lg:order-2\">\r\n                <div class=\"card-body\">\r\n                    <div class=\"flex justify-center mb-6\">\r\n                        <img class=\"h-20 w-auto\" src=\"{{ url_for('static', filename='favicon/apple-touch-icon.png') }}\" alt=\"AlgoFactory\">\r\n                    </div>\r\n\r\n                    <form id=\"loginForm\" onsubmit=\"login(event)\" class=\"space-y-4\">\r\n                        <input type=\"hidden\" name=\"csrf_token\" value=\"{{ csrf_token() }}\"/>\r\n                        <div class=\"form-control\">\r\n                            <label class=\"label\">\r\n                                <span class=\"label-text\">Username</span>\r\n                                <span class=\"label-text-alt\">Instance: {{ instance_id or '8010' }}</span>\r\n                            </label>\r\n                            <div class=\"flex\">\r\n                                <span class=\"inline-flex items-center px-3 text-sm bg-base-200 border border-r-0 border-base-300 rounded-l-md font-mono\">\r\n                                    {{ instance_id or '8010' }}_\r\n                                </span>\r\n                                <input type=\"text\"\r\n                                       id=\"username_suffix\"\r\n                                       required\r\n                                       class=\"input input-bordered w-full rounded-l-none\"\r\n                                       placeholder=\"Enter username\" />\r\n                                <input type=\"hidden\"\r\n                                       id=\"username\"\r\n                                       name=\"username\" />\r\n                            </div>\r\n                            <label class=\"label\">\r\n                                <span class=\"label-text-alt\">Your username will be: {{ instance_id or '8010' }}_[your_input]</span>\r\n                            </label>\r\n                        </div>\r\n\r\n                        <div class=\"form-control\">\r\n                            <label class=\"label\">\r\n                                <span class=\"label-text\">Password</span>\r\n                            </label>\r\n                            <input type=\"password\" \r\n                                   id=\"password\" \r\n                                   name=\"password\" \r\n                                   required \r\n                                   class=\"input input-bordered\" \r\n                                   placeholder=\"Enter your password\" />\r\n                            <label class=\"label\">\r\n                                <a href=\"{{ url_for('auth.reset_password') }}\" class=\"label-text-alt link link-hover\">Forgot password?</a>\r\n                            </label>\r\n                        </div>\r\n\r\n                        <div id=\"loginStatus\" class=\"alert hidden\">\r\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"stroke-current shrink-0 h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\" />\r\n                            </svg>\r\n                            <span></span>\r\n                        </div>\r\n\r\n                        <div class=\"form-control mt-6\">\r\n                            <button type=\"submit\" class=\"btn btn-primary w-full\">\r\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1\" />\r\n                                </svg>\r\n                                Sign in\r\n                            </button>\r\n                        </div>\r\n                    </form>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- Left side content - Shown second on mobile -->\r\n            <div class=\"flex-1 max-w-xl text-center lg:text-left order-2 lg:order-1\">\r\n                <h1 class=\"text-4xl lg:text-5xl font-bold mb-6\">Welcome to <span class=\"text-primary\">AlgoFactory</span></h1>\r\n                <p class=\"text-lg lg:text-xl mb-8 opacity-80\">\r\n                    Sign in to your account to access your trading dashboard and manage your algorithmic trading strategies.\r\n                </p>\r\n                <div class=\"flex flex-col gap-4\">\r\n                    <div class=\"alert alert-info shadow-lg\">\r\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" class=\"stroke-current shrink-0 w-6 h-6\">\r\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                        </svg>\r\n                        <div>\r\n                            <h3 class=\"font-bold\">First Time User?</h3>\r\n                            <div class=\"text-sm\">Contact your administrator to set up your account.</div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"flex justify-center lg:justify-start gap-4\">\r\n                        <a href=\"https://github.com/algofactory.in\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"btn btn-outline gap-2\">\r\n                            <svg class=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                                <path d=\"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z\"/>\r\n                            </svg>\r\n                            GitHub\r\n                        </a>\r\n                        <a href=\"https://www.algofactory.in\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"btn btn-outline gap-2\">\r\n                            <svg class=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                                <path d=\"M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.946 2.4189-2.1568 2.4189Z\"/>\r\n                            </svg>\r\n                            Discord\r\n                        </a>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n{% endblock %}\r\n"}