{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/admin/templates/terminal/index.html"}, "modifiedCode": "{% extends \"base.html\" %}\n\n{% block title %}Web Terminal - AlgoFactory Admin{% endblock %}\n{% block page_title %}Web Terminal{% endblock %}\n\n{% block content %}\n<div class=\"card bg-base-200 shadow-sm\">\n    <div class=\"card-body\">\n        <div class=\"flex justify-between items-center mb-6\">\n            <h2 class=\"card-title\">\n                <i class=\"fas fa-terminal text-primary\"></i>\n                Web Terminal\n            </h2>\n            <div class=\"flex gap-2\">\n                <button class=\"btn btn-ghost btn-sm\" onclick=\"clearTerminal()\">\n                    <i class=\"fas fa-trash\"></i>\n                    Clear\n                </button>\n                <button class=\"btn btn-primary btn-sm\" onclick=\"newSession()\">\n                    <i class=\"fas fa-plus\"></i>\n                    New Session\n                </button>\n            </div>\n        </div>\n        \n        <div class=\"bg-black text-green-400 p-4 rounded-lg h-96 overflow-y-auto font-mono text-sm\" id=\"terminal\">\n            <div class=\"mb-2\">AlgoFactory Web Terminal v1.0</div>\n            <div class=\"mb-2\">Type 'help' for available commands</div>\n            <div class=\"mb-4\">ubuntu@algofactory:~$ <span class=\"animate-pulse\">_</span></div>\n        </div>\n        \n        <div class=\"mt-4\">\n            <div class=\"flex gap-2\">\n                <input type=\"text\" placeholder=\"Enter command...\" class=\"input input-bordered flex-1\" id=\"command-input\" onkeypress=\"handleKeyPress(event)\">\n                <button class=\"btn btn-primary\" onclick=\"executeCommand()\">\n                    <i class=\"fas fa-play\"></i>\n                    Execute\n                </button>\n            </div>\n        </div>\n        \n        <div class=\"mt-4\">\n            <div class=\"alert alert-warning\">\n                <i class=\"fas fa-exclamation-triangle\"></i>\n                <span>Web terminal is for demonstration purposes. Use with caution in production.</span>\n            </div>\n        </div>\n    </div>\n</div>\n{% endblock %}\n\n{% block scripts %}\n<script>\n    function handleKeyPress(event) {\n        if (event.key === 'Enter') {\n            executeCommand();\n        }\n    }\n    \n    function executeCommand() {\n        const input = document.getElementById('command-input');\n        const terminal = document.getElementById('terminal');\n        const command = input.value.trim();\n        \n        if (!command) return;\n        \n        // Add command to terminal\n        terminal.innerHTML += `<div>ubuntu@algofactory:~$ ${command}</div>`;\n        \n        // Mock command execution\n        setTimeout(() => {\n            let output = '';\n            \n            switch (command.toLowerCase()) {\n                case 'help':\n                    output = `Available commands:\n- ls: List files\n- ps: Show processes\n- df: Show disk usage\n- free: Show memory usage\n- uptime: Show system uptime\n- clear: Clear terminal`;\n                    break;\n                case 'ls':\n                    output = 'admin  algofactory-1010  algofactory-1011  algofactory-1012';\n                    break;\n                case 'ps':\n                    output = `PID   COMMAND\n1234  nginx\n5678  python3 app.py\n9012  systemd`;\n                    break;\n                case 'df':\n                    output = `Filesystem     Size  Used Avail Use%\n/dev/root      6.8G  5.3G  1.5G  78%`;\n                    break;\n                case 'free':\n                    output = `              total        used        free\nMem:           957         750          70\nSwap:         2048         744        1304`;\n                    break;\n                case 'uptime':\n                    output = '06:35:12 up 2 days, 14:32, 1 user, load average: 0.15, 0.12, 0.08';\n                    break;\n                case 'clear':\n                    clearTerminal();\n                    input.value = '';\n                    return;\n                default:\n                    output = `bash: ${command}: command not found`;\n            }\n            \n            terminal.innerHTML += `<div class=\"mb-2\">${output}</div>`;\n            terminal.innerHTML += `<div>ubuntu@algofactory:~$ <span class=\"animate-pulse\">_</span></div>`;\n            terminal.scrollTop = terminal.scrollHeight;\n        }, 500);\n        \n        input.value = '';\n    }\n    \n    function clearTerminal() {\n        const terminal = document.getElementById('terminal');\n        terminal.innerHTML = `\n            <div class=\"mb-2\">AlgoFactory Web Terminal v1.0</div>\n            <div class=\"mb-2\">Type 'help' for available commands</div>\n            <div class=\"mb-4\">ubuntu@algofactory:~$ <span class=\"animate-pulse\">_</span></div>\n        `;\n    }\n    \n    function newSession() {\n        showToast('New terminal session created', 'info');\n        clearTerminal();\n    }\n</script>\n{% endblock %}\n"}