{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/admin/templates/ssl/manage.html"}, "modifiedCode": "{% extends \"base.html\" %}\n\n{% block title %}SSL Certificates - AlgoFactory Admin{% endblock %}\n{% block page_title %}SSL Certificate Management{% endblock %}\n\n{% block content %}\n<!-- SSL Overview -->\n<div class=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n    <div class=\"stat bg-base-200 rounded-xl shadow-sm\">\n        <div class=\"stat-figure text-success\">\n            <i class=\"fas fa-shield-alt text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">Active Certificates</div>\n        <div class=\"stat-value text-success\" id=\"active-certs\">0</div>\n        <div class=\"stat-desc\">Valid SSL certificates</div>\n    </div>\n    \n    <div class=\"stat bg-base-200 rounded-xl shadow-sm\">\n        <div class=\"stat-figure text-warning\">\n            <i class=\"fas fa-exclamation-triangle text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">Expiring Soon</div>\n        <div class=\"stat-value text-warning\" id=\"expiring-certs\">0</div>\n        <div class=\"stat-desc\">Within 30 days</div>\n    </div>\n    \n    <div class=\"stat bg-base-200 rounded-xl shadow-sm\">\n        <div class=\"stat-figure text-error\">\n            <i class=\"fas fa-times-circle text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">No SSL</div>\n        <div class=\"stat-value text-error\" id=\"no-ssl-sites\">0</div>\n        <div class=\"stat-desc\">Sites without SSL</div>\n    </div>\n    \n    <div class=\"stat bg-base-200 rounded-xl shadow-sm\">\n        <div class=\"stat-figure text-info\">\n            <i class=\"fas fa-sync-alt text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">Auto-Renewal</div>\n        <div class=\"stat-value text-info\">ON</div>\n        <div class=\"stat-desc\">Certbot enabled</div>\n    </div>\n</div>\n\n<!-- Quick Actions -->\n<div class=\"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8\">\n    <div class=\"card bg-base-200 shadow-sm\">\n        <div class=\"card-body\">\n            <h2 class=\"card-title\">\n                <i class=\"fas fa-plus text-success\"></i>\n                Install New Certificate\n            </h2>\n            <div class=\"space-y-3\">\n                <input type=\"text\" placeholder=\"Domain (e.g., 8014.algofactory.in)\" class=\"input input-bordered input-sm w-full\" id=\"new-ssl-domain\">\n                <select class=\"select select-bordered select-sm w-full\" id=\"ssl-method\">\n                    <option value=\"webroot\">Webroot</option>\n                    <option value=\"nginx\">Nginx Plugin</option>\n                    <option value=\"standalone\">Standalone</option>\n                </select>\n                <button class=\"btn btn-success btn-sm w-full\" onclick=\"installSSL()\">\n                    <i class=\"fas fa-lock\"></i>\n                    Install SSL\n                </button>\n            </div>\n        </div>\n    </div>\n    \n    <div class=\"card bg-base-200 shadow-sm\">\n        <div class=\"card-body\">\n            <h2 class=\"card-title\">\n                <i class=\"fas fa-sync text-primary\"></i>\n                Bulk Operations\n            </h2>\n            <div class=\"space-y-3\">\n                <button class=\"btn btn-primary btn-sm w-full\" onclick=\"renewAllCertificates()\">\n                    <i class=\"fas fa-sync\"></i>\n                    Renew All\n                </button>\n                <button class=\"btn btn-info btn-sm w-full\" onclick=\"bulkInstallSSL()\">\n                    <i class=\"fas fa-certificate\"></i>\n                    Bulk Install\n                </button>\n                <button class=\"btn btn-warning btn-sm w-full\" onclick=\"checkAllExpiry()\">\n                    <i class=\"fas fa-calendar-check\"></i>\n                    Check Expiry\n                </button>\n            </div>\n        </div>\n    </div>\n    \n    <div class=\"card bg-base-200 shadow-sm\">\n        <div class=\"card-body\">\n            <h2 class=\"card-title\">\n                <i class=\"fas fa-cog text-accent\"></i>\n                Certbot Settings\n            </h2>\n            <div class=\"space-y-3\">\n                <button class=\"btn btn-accent btn-sm w-full\" onclick=\"configureCertbot()\">\n                    <i class=\"fas fa-cog\"></i>\n                    Configure\n                </button>\n                <button class=\"btn btn-ghost btn-sm w-full\" onclick=\"viewCertbotLogs()\">\n                    <i class=\"fas fa-file-alt\"></i>\n                    View Logs\n                </button>\n                <button class=\"btn btn-ghost btn-sm w-full\" onclick=\"testCertbot()\">\n                    <i class=\"fas fa-vial\"></i>\n                    Test Setup\n                </button>\n            </div>\n        </div>\n    </div>\n</div>\n\n<!-- Certificates Table -->\n<div class=\"card bg-base-200 shadow-sm\">\n    <div class=\"card-body\">\n        <div class=\"flex justify-between items-center mb-6\">\n            <h2 class=\"card-title\">\n                <i class=\"fas fa-certificate text-primary\"></i>\n                SSL Certificates\n            </h2>\n            <div class=\"flex gap-2\">\n                <button class=\"btn btn-ghost btn-sm\" onclick=\"refreshCertificates()\">\n                    <i class=\"fas fa-sync-alt\"></i>\n                    Refresh\n                </button>\n                <div class=\"dropdown dropdown-end\">\n                    <div tabindex=\"0\" role=\"button\" class=\"btn btn-ghost btn-sm\">\n                        <i class=\"fas fa-filter\"></i>\n                        Filter\n                    </div>\n                    <ul tabindex=\"0\" class=\"dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52\">\n                        <li><a onclick=\"filterCertificates('all')\">All Certificates</a></li>\n                        <li><a onclick=\"filterCertificates('valid')\">Valid Only</a></li>\n                        <li><a onclick=\"filterCertificates('expiring')\">Expiring Soon</a></li>\n                        <li><a onclick=\"filterCertificates('expired')\">Expired</a></li>\n                    </ul>\n                </div>\n            </div>\n        </div>\n        \n        <div class=\"overflow-x-auto\">\n            <table class=\"table table-zebra w-full\">\n                <thead>\n                    <tr>\n                        <th>Domain</th>\n                        <th>Status</th>\n                        <th>Issued Date</th>\n                        <th>Expiry Date</th>\n                        <th>Days Left</th>\n                        <th>Issuer</th>\n                        <th>Actions</th>\n                    </tr>\n                </thead>\n                <tbody id=\"certificates-table\">\n                    <tr>\n                        <td colspan=\"7\" class=\"text-center text-base-content/50\">\n                            <i class=\"fas fa-spinner fa-spin mr-2\"></i>\n                            Loading certificates...\n                        </td>\n                    </tr>\n                </tbody>\n            </table>\n        </div>\n    </div>\n</div>\n\n<!-- Sites Without SSL -->\n<div class=\"card bg-base-200 shadow-sm mt-8\">\n    <div class=\"card-body\">\n        <div class=\"flex justify-between items-center mb-6\">\n            <h2 class=\"card-title\">\n                <i class=\"fas fa-unlock text-warning\"></i>\n                Sites Without SSL\n            </h2>\n            <button class=\"btn btn-success btn-sm\" onclick=\"installAllSSL()\">\n                <i class=\"fas fa-lock\"></i>\n                Install SSL for All\n            </button>\n        </div>\n        \n        <div class=\"overflow-x-auto\">\n            <table class=\"table table-zebra w-full\">\n                <thead>\n                    <tr>\n                        <th>Domain</th>\n                        <th>Port</th>\n                        <th>Status</th>\n                        <th>Actions</th>\n                    </tr>\n                </thead>\n                <tbody id=\"no-ssl-table\">\n                    <tr>\n                        <td colspan=\"4\" class=\"text-center text-base-content/50\">\n                            <i class=\"fas fa-spinner fa-spin mr-2\"></i>\n                            Loading sites...\n                        </td>\n                    </tr>\n                </tbody>\n            </table>\n        </div>\n    </div>\n</div>\n\n<!-- Certificate Details Modal -->\n<dialog id=\"cert-details-modal\" class=\"modal\">\n    <div class=\"modal-box w-11/12 max-w-4xl\">\n        <h3 class=\"font-bold text-lg mb-4\">\n            <i class=\"fas fa-certificate\"></i>\n            Certificate Details - <span id=\"cert-domain\"></span>\n        </h3>\n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div class=\"space-y-4\">\n                <div>\n                    <label class=\"label\">\n                        <span class=\"label-text font-bold\">Subject</span>\n                    </label>\n                    <div class=\"bg-base-300 p-3 rounded font-mono text-sm\" id=\"cert-subject\">Loading...</div>\n                </div>\n                <div>\n                    <label class=\"label\">\n                        <span class=\"label-text font-bold\">Issuer</span>\n                    </label>\n                    <div class=\"bg-base-300 p-3 rounded font-mono text-sm\" id=\"cert-issuer\">Loading...</div>\n                </div>\n                <div>\n                    <label class=\"label\">\n                        <span class=\"label-text font-bold\">Serial Number</span>\n                    </label>\n                    <div class=\"bg-base-300 p-3 rounded font-mono text-sm\" id=\"cert-serial\">Loading...</div>\n                </div>\n            </div>\n            <div class=\"space-y-4\">\n                <div>\n                    <label class=\"label\">\n                        <span class=\"label-text font-bold\">Valid From</span>\n                    </label>\n                    <div class=\"bg-base-300 p-3 rounded font-mono text-sm\" id=\"cert-valid-from\">Loading...</div>\n                </div>\n                <div>\n                    <label class=\"label\">\n                        <span class=\"label-text font-bold\">Valid Until</span>\n                    </label>\n                    <div class=\"bg-base-300 p-3 rounded font-mono text-sm\" id=\"cert-valid-until\">Loading...</div>\n                </div>\n                <div>\n                    <label class=\"label\">\n                        <span class=\"label-text font-bold\">Fingerprint (SHA256)</span>\n                    </label>\n                    <div class=\"bg-base-300 p-3 rounded font-mono text-sm break-all\" id=\"cert-fingerprint\">Loading...</div>\n                </div>\n            </div>\n        </div>\n        <div class=\"modal-action\">\n            <button class=\"btn btn-primary\" onclick=\"downloadCertificate()\">\n                <i class=\"fas fa-download\"></i>\n                Download\n            </button>\n            <button class=\"btn btn-ghost\" onclick=\"closeCertDetailsModal()\">Close</button>\n        </div>\n    </div>\n</dialog>\n\n<!-- Certbot Configuration Modal -->\n<dialog id=\"certbot-config-modal\" class=\"modal\">\n    <div class=\"modal-box\">\n        <h3 class=\"font-bold text-lg mb-4\">\n            <i class=\"fas fa-cog\"></i>\n            Certbot Configuration\n        </h3>\n        <div class=\"space-y-4\">\n            <div class=\"form-control\">\n                <label class=\"label\">\n                    <span class=\"label-text\">Email Address</span>\n                </label>\n                <input type=\"email\" placeholder=\"<EMAIL>\" class=\"input input-bordered w-full\" id=\"certbot-email\">\n            </div>\n            <div class=\"form-control\">\n                <label class=\"label cursor-pointer\">\n                    <span class=\"label-text\">Agree to Terms of Service</span>\n                    <input type=\"checkbox\" class=\"checkbox\" id=\"certbot-agree-tos\" checked>\n                </label>\n            </div>\n            <div class=\"form-control\">\n                <label class=\"label cursor-pointer\">\n                    <span class=\"label-text\">Enable automatic renewal</span>\n                    <input type=\"checkbox\" class=\"checkbox\" id=\"certbot-auto-renew\" checked>\n                </label>\n            </div>\n            <div class=\"form-control\">\n                <label class=\"label\">\n                    <span class=\"label-text\">Renewal Check Frequency</span>\n                </label>\n                <select class=\"select select-bordered w-full\" id=\"certbot-frequency\">\n                    <option value=\"daily\">Daily</option>\n                    <option value=\"weekly\" selected>Weekly</option>\n                    <option value=\"monthly\">Monthly</option>\n                </select>\n            </div>\n        </div>\n        <div class=\"modal-action\">\n            <button class=\"btn btn-primary\" onclick=\"saveCertbotConfig()\">\n                <i class=\"fas fa-save\"></i>\n                Save Configuration\n            </button>\n            <button class=\"btn btn-ghost\" onclick=\"closeCertbotConfigModal()\">Cancel</button>\n        </div>\n    </div>\n</dialog>\n{% endblock %}\n\n{% block scripts %}\n<script>\n    let certificatesData = [];\n    let noSSLSites = [];\n    \n    // Initialize page\n    document.addEventListener('DOMContentLoaded', function() {\n        loadCertificates();\n        loadNoSSLSites();\n    });\n    \n    async function loadCertificates() {\n        // Mock certificate data - in real implementation, this would come from certbot\n        certificatesData = [\n            {\n                domain: 'admin.algofactory.in',\n                status: 'valid',\n                issued_date: '2025-03-24',\n                expiry_date: '2025-06-24',\n                days_left: 30,\n                issuer: \"Let's Encrypt\",\n                subject: 'CN=admin.algofactory.in',\n                serial: '03:A1:B2:C3:D4:E5:F6:07:08:09',\n                fingerprint: 'SHA256:1234567890ABCDEF...'\n            },\n            {\n                domain: '8010.algofactory.in',\n                status: 'valid',\n                issued_date: '2025-03-20',\n                expiry_date: '2025-06-20',\n                days_left: 26,\n                issuer: \"Let's Encrypt\",\n                subject: 'CN=8010.algofactory.in',\n                serial: '03:A1:B2:C3:D4:E5:F6:07:08:10',\n                fingerprint: 'SHA256:ABCDEF1234567890...'\n            },\n            {\n                domain: '8011.algofactory.in',\n                status: 'expiring',\n                issued_date: '2025-03-15',\n                expiry_date: '2025-06-15',\n                days_left: 21,\n                issuer: \"Let's Encrypt\",\n                subject: 'CN=8011.algofactory.in',\n                serial: '03:A1:B2:C3:D4:E5:F6:07:08:11',\n                fingerprint: 'SHA256:567890ABCDEF1234...'\n            },\n            {\n                domain: '8012.algofactory.in',\n                status: 'valid',\n                issued_date: '2025-04-01',\n                expiry_date: '2025-07-01',\n                days_left: 37,\n                issuer: \"Let's Encrypt\",\n                subject: 'CN=8012.algofactory.in',\n                serial: '03:A1:B2:C3:D4:E5:F6:07:08:12',\n                fingerprint: 'SHA256:DEF1234567890ABC...'\n            }\n        ];\n        \n        updateCertificatesData(certificatesData);\n    }\n    \n    function updateCertificatesData(certificates) {\n        certificatesData = certificates;\n        \n        // Update overview stats\n        const activeCerts = certificates.filter(cert => cert.status === 'valid').length;\n        const expiringCerts = certificates.filter(cert => cert.days_left <= 30).length;\n        \n        document.getElementById('active-certs').textContent = activeCerts;\n        document.getElementById('expiring-certs').textContent = expiringCerts;\n        \n        // Update table\n        renderCertificatesTable(certificates);\n    }\n    \n    function renderCertificatesTable(certificates) {\n        const tbody = document.getElementById('certificates-table');\n        \n        if (certificates.length === 0) {\n            tbody.innerHTML = `\n                <tr>\n                    <td colspan=\"7\" class=\"text-center text-base-content/50\">\n                        <i class=\"fas fa-info-circle mr-2\"></i>\n                        No SSL certificates found\n                    </td>\n                </tr>\n            `;\n            return;\n        }\n        \n        tbody.innerHTML = certificates.map(cert => `\n            <tr>\n                <td>\n                    <div class=\"font-bold\">${cert.domain}</div>\n                    <div class=\"text-sm text-base-content/70\">\n                        <a href=\"https://${cert.domain}\" target=\"_blank\" class=\"link\">\n                            <i class=\"fas fa-external-link-alt\"></i>\n                            Test SSL\n                        </a>\n                    </div>\n                </td>\n                <td>\n                    <div class=\"badge ${getCertStatusBadge(cert.status, cert.days_left)}\">\n                        <i class=\"fas ${getCertStatusIcon(cert.status)} mr-1\"></i>\n                        ${getCertStatusText(cert.status, cert.days_left)}\n                    </div>\n                </td>\n                <td>\n                    <div class=\"text-sm\">${formatDate(cert.issued_date)}</div>\n                </td>\n                <td>\n                    <div class=\"text-sm\">${formatDate(cert.expiry_date)}</div>\n                </td>\n                <td>\n                    <div class=\"text-sm ${cert.days_left <= 30 ? 'text-warning font-bold' : ''}\">${cert.days_left} days</div>\n                </td>\n                <td>\n                    <div class=\"text-sm\">${cert.issuer}</div>\n                </td>\n                <td>\n                    <div class=\"flex gap-1\">\n                        <button class=\"btn btn-info btn-xs\" onclick=\"viewCertificateDetails('${cert.domain}')\" title=\"View Details\">\n                            <i class=\"fas fa-info-circle\"></i>\n                        </button>\n                        <button class=\"btn btn-primary btn-xs\" onclick=\"renewCertificate('${cert.domain}')\" title=\"Renew\">\n                            <i class=\"fas fa-sync\"></i>\n                        </button>\n                        <button class=\"btn btn-warning btn-xs\" onclick=\"testCertificate('${cert.domain}')\" title=\"Test\">\n                            <i class=\"fas fa-vial\"></i>\n                        </button>\n                        <button class=\"btn btn-error btn-xs\" onclick=\"revokeCertificate('${cert.domain}')\" title=\"Revoke\">\n                            <i class=\"fas fa-ban\"></i>\n                        </button>\n                    </div>\n                </td>\n            </tr>\n        `).join('');\n    }\n    \n    async function loadNoSSLSites() {\n        // Mock data for sites without SSL\n        noSSLSites = [\n            { domain: '8013.algofactory.in', port: 8013, status: 'No SSL' },\n            { domain: '8014.algofactory.in', port: 8014, status: 'No SSL' },\n            { domain: '8015.algofactory.in', port: 8015, status: 'No SSL' }\n        ];\n        \n        document.getElementById('no-ssl-sites').textContent = noSSLSites.length;\n        renderNoSSLTable(noSSLSites);\n    }\n    \n    function renderNoSSLTable(sites) {\n        const tbody = document.getElementById('no-ssl-table');\n        \n        if (sites.length === 0) {\n            tbody.innerHTML = `\n                <tr>\n                    <td colspan=\"4\" class=\"text-center text-base-content/50\">\n                        <i class=\"fas fa-check-circle mr-2\"></i>\n                        All sites have SSL certificates!\n                    </td>\n                </tr>\n            `;\n            return;\n        }\n        \n        tbody.innerHTML = sites.map(site => `\n            <tr>\n                <td>\n                    <div class=\"font-bold\">${site.domain}</div>\n                    <div class=\"text-sm text-base-content/70\">\n                        <a href=\"http://${site.domain}\" target=\"_blank\" class=\"link\">\n                            <i class=\"fas fa-external-link-alt\"></i>\n                            Visit Site\n                        </a>\n                    </div>\n                </td>\n                <td>\n                    <div class=\"badge badge-neutral\">${site.port}</div>\n                </td>\n                <td>\n                    <div class=\"badge badge-warning\">\n                        <i class=\"fas fa-unlock mr-1\"></i>\n                        ${site.status}\n                    </div>\n                </td>\n                <td>\n                    <button class=\"btn btn-success btn-xs\" onclick=\"installSSLForSite('${site.domain}')\">\n                        <i class=\"fas fa-lock\"></i>\n                        Install SSL\n                    </button>\n                </td>\n            </tr>\n        `).join('');\n    }\n    \n    // Action functions\n    async function installSSL() {\n        const domain = document.getElementById('new-ssl-domain').value;\n        const method = document.getElementById('ssl-method').value;\n        \n        if (!domain) {\n            showToast('Please enter a domain name', 'error');\n            return;\n        }\n        \n        showToast(`Installing SSL certificate for ${domain}...`, 'info');\n        \n        // This would call certbot\n        setTimeout(() => {\n            showToast(`SSL certificate installed for ${domain}!`, 'success');\n            document.getElementById('new-ssl-domain').value = '';\n            loadCertificates();\n            loadNoSSLSites();\n        }, 3000);\n    }\n    \n    async function installSSLForSite(domain) {\n        showToast(`Installing SSL certificate for ${domain}...`, 'info');\n        \n        // This would call certbot for the specific domain\n        setTimeout(() => {\n            showToast(`SSL certificate installed for ${domain}!`, 'success');\n            loadCertificates();\n            loadNoSSLSites();\n        }, 3000);\n    }\n    \n    async function renewCertificate(domain) {\n        if (confirm(`Renew SSL certificate for ${domain}?`)) {\n            showToast(`Renewing certificate for ${domain}...`, 'info');\n            \n            // This would call certbot renew\n            setTimeout(() => {\n                showToast(`Certificate renewed for ${domain}!`, 'success');\n                loadCertificates();\n            }, 2000);\n        }\n    }\n    \n    async function renewAllCertificates() {\n        if (confirm('Renew all SSL certificates?')) {\n            showToast('Renewing all certificates...', 'info');\n            \n            // This would call certbot renew --force-renewal\n            setTimeout(() => {\n                showToast('All certificates renewed successfully!', 'success');\n                loadCertificates();\n            }, 5000);\n        }\n    }\n    \n    function viewCertificateDetails(domain) {\n        const cert = certificatesData.find(c => c.domain === domain);\n        if (!cert) return;\n        \n        document.getElementById('cert-domain').textContent = domain;\n        document.getElementById('cert-subject').textContent = cert.subject;\n        document.getElementById('cert-issuer').textContent = cert.issuer;\n        document.getElementById('cert-serial').textContent = cert.serial;\n        document.getElementById('cert-valid-from').textContent = cert.issued_date;\n        document.getElementById('cert-valid-until').textContent = cert.expiry_date;\n        document.getElementById('cert-fingerprint').textContent = cert.fingerprint;\n        \n        document.getElementById('cert-details-modal').showModal();\n    }\n    \n    function closeCertDetailsModal() {\n        document.getElementById('cert-details-modal').close();\n    }\n    \n    function configureCertbot() {\n        document.getElementById('certbot-config-modal').showModal();\n    }\n    \n    function closeCertbotConfigModal() {\n        document.getElementById('certbot-config-modal').close();\n    }\n    \n    function saveCertbotConfig() {\n        showToast('Certbot configuration saved!', 'success');\n        closeCertbotConfigModal();\n    }\n    \n    // Utility functions\n    function getCertStatusBadge(status, daysLeft) {\n        if (daysLeft <= 7) return 'badge-error';\n        if (daysLeft <= 30) return 'badge-warning';\n        return 'badge-success';\n    }\n    \n    function getCertStatusIcon(status) {\n        switch (status) {\n            case 'valid': return 'fa-check-circle';\n            case 'expiring': return 'fa-exclamation-triangle';\n            case 'expired': return 'fa-times-circle';\n            default: return 'fa-question-circle';\n        }\n    }\n    \n    function getCertStatusText(status, daysLeft) {\n        if (daysLeft <= 7) return 'Expires Soon';\n        if (daysLeft <= 30) return 'Expiring';\n        return 'Valid';\n    }\n    \n    function formatDate(dateString) {\n        return new Date(dateString).toLocaleDateString();\n    }\n    \n    function filterCertificates(filter) {\n        let filteredCerts = certificatesData;\n        \n        switch (filter) {\n            case 'valid':\n                filteredCerts = certificatesData.filter(cert => cert.status === 'valid' && cert.days_left > 30);\n                break;\n            case 'expiring':\n                filteredCerts = certificatesData.filter(cert => cert.days_left <= 30);\n                break;\n            case 'expired':\n                filteredCerts = certificatesData.filter(cert => cert.days_left <= 0);\n                break;\n        }\n        \n        renderCertificatesTable(filteredCerts);\n    }\n    \n    function refreshCertificates() {\n        showToast('Refreshing certificates...', 'info');\n        loadCertificates();\n    }\n</script>\n{% endblock %}\n"}