{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/admin/app.py"}, "originalCode": "#!/usr/bin/env python3\n\n\"\"\"\nAlgoFactory Advanced Admin Dashboard\nScalable server management system with modular architecture\n\"\"\"\n\nfrom flask import Flask, render_template, request, jsonify, session, redirect, url_for, flash\nfrom flask_socketio import Socket<PERSON>, emit, join_room, leave_room\nfrom werkzeug.security import generate_password_hash, check_password_hash\nimport os\nimport sys\nimport json\nimport sqlite3\nimport psutil\nimport subprocess\nfrom datetime import datetime, timedelta\nfrom pathlib import Path\nimport logging\nimport asyncio\nfrom threading import Thread\nimport time\n\n# Add parent directory to path for imports\nsys.path.append(str(Path(__file__).parent.parent))\n\n# Configure logging\nlogging.basicConfig(level=logging.INFO)\nlogger = logging.getLogger(__name__)\n\n# Initialize Flask app\napp = Flask(__name__)\napp.config['SECRET_KEY'] = 'algofactory-admin-secret-key-change-in-production'\napp.config['TEMPLATES_AUTO_RELOAD'] = True\n\n# Initialize SocketIO\nsocketio = SocketIO(app, cors_allowed_origins=\"*\", async_mode='threading')\n\n# Database setup\nBASE_DIR = Path(__file__).parent\nDB_PATH = BASE_DIR / \"admin.db\"\n\nclass DatabaseManager:\n    \"\"\"Centralized database management\"\"\"\n    \n    def __init__(self, db_path):\n        self.db_path = db_path\n        self.init_database()\n    \n    def get_connection(self):\n        conn = sqlite3.connect(str(self.db_path))\n        conn.row_factory = sqlite3.Row\n        return conn\n    \n    def init_database(self):\n        \"\"\"Initialize all database tables\"\"\"\n        conn = self.get_connection()\n        cursor = conn.cursor()\n        \n        # Admin users table\n        cursor.execute('''\n            CREATE TABLE IF NOT EXISTS admin_users (\n                id INTEGER PRIMARY KEY AUTOINCREMENT,\n                username TEXT UNIQUE NOT NULL,\n                password_hash TEXT NOT NULL,\n                email TEXT,\n                role TEXT DEFAULT 'admin',\n                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n                last_login DATETIME\n            )\n        ''')\n        \n        # System metrics table\n        cursor.execute('''\n            CREATE TABLE IF NOT EXISTS system_metrics (\n                id INTEGER PRIMARY KEY AUTOINCREMENT,\n                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,\n                cpu_percent REAL,\n                memory_percent REAL,\n                memory_used_gb REAL,\n                memory_total_gb REAL,\n                disk_percent REAL,\n                disk_used_gb REAL,\n                disk_total_gb REAL,\n                load_average REAL,\n                network_bytes_sent INTEGER,\n                network_bytes_recv INTEGER,\n                active_connections INTEGER,\n                process_count INTEGER\n            )\n        ''')\n        \n        # AlgoFactory instances table\n        cursor.execute('''\n            CREATE TABLE IF NOT EXISTS algofactory_instances (\n                id INTEGER PRIMARY KEY AUTOINCREMENT,\n                instance_id TEXT UNIQUE NOT NULL,\n                port INTEGER NOT NULL,\n                websocket_port INTEGER,\n                zmq_port INTEGER,\n                status TEXT DEFAULT 'stopped',\n                pid INTEGER,\n                memory_mb REAL,\n                cpu_percent REAL,\n                uptime_seconds INTEGER DEFAULT 0,\n                last_check DATETIME DEFAULT CURRENT_TIMESTAMP,\n                auto_restart BOOLEAN DEFAULT 1,\n                created_at DATETIME DEFAULT CURRENT_TIMESTAMP\n            )\n        ''')\n        \n        # Nginx sites table\n        cursor.execute('''\n            CREATE TABLE IF NOT EXISTS nginx_sites (\n                id INTEGER PRIMARY KEY AUTOINCREMENT,\n                domain TEXT UNIQUE NOT NULL,\n                port INTEGER NOT NULL,\n                ssl_enabled BOOLEAN DEFAULT 0,\n                ssl_cert_path TEXT,\n                ssl_expiry_date DATETIME,\n                status TEXT DEFAULT 'active',\n                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n                last_check DATETIME DEFAULT CURRENT_TIMESTAMP\n            )\n        ''')\n        \n        # System events/logs table\n        cursor.execute('''\n            CREATE TABLE IF NOT EXISTS system_events (\n                id INTEGER PRIMARY KEY AUTOINCREMENT,\n                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,\n                event_type TEXT NOT NULL,\n                category TEXT DEFAULT 'system',\n                message TEXT NOT NULL,\n                severity TEXT DEFAULT 'info',\n                source TEXT,\n                details TEXT,\n                user_id INTEGER,\n                FOREIGN KEY (user_id) REFERENCES admin_users (id)\n            )\n        ''')\n        \n        # Tasks/jobs table\n        cursor.execute('''\n            CREATE TABLE IF NOT EXISTS admin_tasks (\n                id INTEGER PRIMARY KEY AUTOINCREMENT,\n                task_name TEXT NOT NULL,\n                task_type TEXT NOT NULL,\n                status TEXT DEFAULT 'pending',\n                progress INTEGER DEFAULT 0,\n                result TEXT,\n                error_message TEXT,\n                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n                started_at DATETIME,\n                completed_at DATETIME,\n                created_by INTEGER,\n                FOREIGN KEY (created_by) REFERENCES admin_users (id)\n            )\n        ''')\n        \n        # Settings table\n        cursor.execute('''\n            CREATE TABLE IF NOT EXISTS admin_settings (\n                id INTEGER PRIMARY KEY AUTOINCREMENT,\n                setting_key TEXT UNIQUE NOT NULL,\n                setting_value TEXT,\n                setting_type TEXT DEFAULT 'string',\n                description TEXT,\n                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n                updated_by INTEGER,\n                FOREIGN KEY (updated_by) REFERENCES admin_users (id)\n            )\n        ''')\n        \n        conn.commit()\n        \n        # Create default admin user if none exists\n        cursor.execute('SELECT COUNT(*) FROM admin_users')\n        if cursor.fetchone()[0] == 0:\n            default_password = generate_password_hash('admin123')\n            cursor.execute('''\n                INSERT INTO admin_users (username, password_hash, email, role)\n                VALUES (?, ?, ?, ?)\n            ''', ('admin', default_password, '<EMAIL>', 'superadmin'))\n            conn.commit()\n            logger.info(\"Created default admin user: admin/admin123\")\n        \n        # Insert default settings\n        default_settings = [\n            ('monitoring_interval', '5', 'integer', 'System monitoring interval in seconds'),\n            ('max_log_entries', '1000', 'integer', 'Maximum log entries to keep'),\n            ('auto_cleanup_days', '30', 'integer', 'Auto cleanup logs older than X days'),\n            ('alert_email', '<EMAIL>', 'string', 'Email for system alerts'),\n            ('maintenance_mode', 'false', 'boolean', 'Enable maintenance mode'),\n        ]\n        \n        for key, value, type_, desc in default_settings:\n            cursor.execute('''\n                INSERT OR IGNORE INTO admin_settings (setting_key, setting_value, setting_type, description)\n                VALUES (?, ?, ?, ?)\n            ''', (key, value, type_, desc))\n        \n        conn.commit()\n        conn.close()\n\n# Initialize database\ndb_manager = DatabaseManager(DB_PATH)\n\nclass SystemMonitor:\n    \"\"\"Real-time system monitoring\"\"\"\n    \n    def __init__(self, socketio_instance):\n        self.socketio = socketio_instance\n        self.monitoring = False\n        self.monitor_thread = None\n    \n    def start_monitoring(self):\n        \"\"\"Start the monitoring thread\"\"\"\n        if not self.monitoring:\n            self.monitoring = True\n            self.monitor_thread = Thread(target=self._monitor_loop, daemon=True)\n            self.monitor_thread.start()\n            logger.info(\"System monitoring started\")\n    \n    def stop_monitoring(self):\n        \"\"\"Stop the monitoring thread\"\"\"\n        self.monitoring = False\n        if self.monitor_thread:\n            self.monitor_thread.join(timeout=5)\n        logger.info(\"System monitoring stopped\")\n    \n    def _monitor_loop(self):\n        \"\"\"Main monitoring loop\"\"\"\n        while self.monitoring:\n            try:\n                # Collect system metrics\n                metrics = self.collect_system_metrics()\n                \n                # Store in database\n                self.store_metrics(metrics)\n                \n                # Emit to connected clients\n                self.socketio.emit('system_update', {\n                    'type': 'metrics',\n                    'data': metrics,\n                    'timestamp': datetime.now().isoformat()\n                }, namespace='/admin')\n                \n                # Check for alerts\n                self.check_alerts(metrics)\n                \n                # Sleep for monitoring interval\n                time.sleep(5)  # 5 seconds default\n                \n            except Exception as e:\n                logger.error(f\"Error in monitoring loop: {e}\")\n                time.sleep(10)  # Wait longer on error\n    \n    def collect_system_metrics(self):\n        \"\"\"Collect comprehensive system metrics\"\"\"\n        try:\n            # CPU and Memory\n            cpu_percent = psutil.cpu_percent(interval=1)\n            memory = psutil.virtual_memory()\n            disk = psutil.disk_usage('/')\n            load_avg = os.getloadavg()[0] if hasattr(os, 'getloadavg') else 0\n            \n            # Network\n            network = psutil.net_io_counters()\n            \n            # Process information\n            process_count = len(psutil.pids())\n            \n            # AlgoFactory instances\n            instances = self.get_algofactory_instances()\n            \n            # Nginx status\n            nginx_status = self.get_nginx_status()\n            \n            return {\n                'system': {\n                    'timestamp': datetime.now().isoformat(),\n                    'cpu': {\n                        'percent': round(cpu_percent, 1),\n                        'count': psutil.cpu_count(),\n                        'load_average': round(load_avg, 2)\n                    },\n                    'memory': {\n                        'percent': round(memory.percent, 1),\n                        'used_gb': round(memory.used / (1024**3), 2),\n                        'total_gb': round(memory.total / (1024**3), 2),\n                        'available_gb': round(memory.available / (1024**3), 2)\n                    },\n                    'disk': {\n                        'percent': round((disk.used / disk.total) * 100, 1),\n                        'used_gb': round(disk.used / (1024**3), 2),\n                        'total_gb': round(disk.total / (1024**3), 2),\n                        'free_gb': round(disk.free / (1024**3), 2)\n                    },\n                    'network': {\n                        'bytes_sent': network.bytes_sent,\n                        'bytes_recv': network.bytes_recv\n                    },\n                    'processes': process_count\n                },\n                'instances': instances,\n                'nginx': nginx_status\n            }\n        except Exception as e:\n            logger.error(f\"Error collecting system metrics: {e}\")\n            return {}\n    \n    def get_algofactory_instances(self):\n        \"\"\"Get status of AlgoFactory instances\"\"\"\n        instances = []\n        try:\n            # Check running processes\n            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'memory_info', 'cpu_percent', 'create_time']):\n                try:\n                    if 'python' in proc.info['name'] and proc.info['cmdline']:\n                        cmdline = ' '.join(proc.info['cmdline'])\n                        if 'app.py' in cmdline and any(port in cmdline for port in ['8010', '8011', '8012']):\n                            # Extract port from command line\n                            port = None\n                            for arg in proc.info['cmdline']:\n                                if arg.isdigit() and 8000 <= int(arg) <= 9999:\n                                    port = int(arg)\n                                    break\n                            \n                            if port:\n                                uptime = datetime.now() - datetime.fromtimestamp(proc.create_time())\n                                instances.append({\n                                    'instance_id': str(port),\n                                    'port': port,\n                                    'websocket_port': port + 12000,\n                                    'zmq_port': port + 15000,\n                                    'pid': proc.info['pid'],\n                                    'status': 'running',\n                                    'uptime_seconds': int(uptime.total_seconds()),\n                                    'memory_mb': round(proc.memory_info().rss / (1024**2), 1),\n                                    'cpu_percent': round(proc.cpu_percent(), 1)\n                                })\n                except (psutil.NoSuchProcess, psutil.AccessDenied):\n                    continue\n        except Exception as e:\n            logger.error(f\"Error getting AlgoFactory instances: {e}\")\n        \n        return instances\n    \n    def get_nginx_status(self):\n        \"\"\"Get Nginx status and sites\"\"\"\n        try:\n            # Check if Nginx is running\n            nginx_running = False\n            nginx_pid = None\n            \n            for proc in psutil.process_iter(['pid', 'name']):\n                if proc.info['name'] == 'nginx':\n                    nginx_running = True\n                    nginx_pid = proc.info['pid']\n                    break\n            \n            # Get configured sites\n            sites = []\n            sites_enabled_dir = Path('/etc/nginx/sites-enabled')\n            if sites_enabled_dir.exists():\n                for site_file in sites_enabled_dir.glob('*.algofactory.in.conf'):\n                    domain = site_file.stem\n                    ssl_cert_path = Path(f'/etc/letsencrypt/live/{domain}')\n                    \n                    sites.append({\n                        'domain': domain,\n                        'ssl_enabled': ssl_cert_path.exists(),\n                        'config_file': str(site_file)\n                    })\n            \n            return {\n                'running': nginx_running,\n                'pid': nginx_pid,\n                'sites_count': len(sites),\n                'sites': sites\n            }\n        except Exception as e:\n            logger.error(f\"Error getting Nginx status: {e}\")\n            return {'running': False, 'error': str(e)}\n    \n    def store_metrics(self, metrics):\n        \"\"\"Store metrics in database\"\"\"\n        try:\n            if not metrics or 'system' not in metrics:\n                return\n            \n            system = metrics['system']\n            conn = db_manager.get_connection()\n            cursor = conn.cursor()\n            \n            cursor.execute('''\n                INSERT INTO system_metrics \n                (cpu_percent, memory_percent, memory_used_gb, memory_total_gb,\n                 disk_percent, disk_used_gb, disk_total_gb, load_average,\n                 network_bytes_sent, network_bytes_recv, process_count)\n                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n            ''', (\n                system.get('cpu', {}).get('percent', 0),\n                system.get('memory', {}).get('percent', 0),\n                system.get('memory', {}).get('used_gb', 0),\n                system.get('memory', {}).get('total_gb', 0),\n                system.get('disk', {}).get('percent', 0),\n                system.get('disk', {}).get('used_gb', 0),\n                system.get('disk', {}).get('total_gb', 0),\n                system.get('cpu', {}).get('load_average', 0),\n                system.get('network', {}).get('bytes_sent', 0),\n                system.get('network', {}).get('bytes_recv', 0),\n                system.get('processes', 0)\n            ))\n            \n            conn.commit()\n            conn.close()\n        except Exception as e:\n            logger.error(f\"Error storing metrics: {e}\")\n    \n    def check_alerts(self, metrics):\n        \"\"\"Check for system alerts\"\"\"\n        try:\n            if not metrics or 'system' not in metrics:\n                return\n            \n            system = metrics['system']\n            alerts = []\n            \n            # CPU alert\n            cpu_percent = system.get('cpu', {}).get('percent', 0)\n            if cpu_percent > 90:\n                alerts.append({\n                    'type': 'cpu_high',\n                    'severity': 'critical',\n                    'message': f'High CPU usage: {cpu_percent}%'\n                })\n            \n            # Memory alert\n            memory_percent = system.get('memory', {}).get('percent', 0)\n            if memory_percent > 85:\n                alerts.append({\n                    'type': 'memory_high',\n                    'severity': 'warning' if memory_percent < 95 else 'critical',\n                    'message': f'High memory usage: {memory_percent}%'\n                })\n            \n            # Disk alert\n            disk_percent = system.get('disk', {}).get('percent', 0)\n            if disk_percent > 85:\n                alerts.append({\n                    'type': 'disk_high',\n                    'severity': 'warning' if disk_percent < 95 else 'critical',\n                    'message': f'High disk usage: {disk_percent}%'\n                })\n            \n            # Emit alerts\n            for alert in alerts:\n                self.socketio.emit('system_alert', alert, namespace='/admin')\n                \n        except Exception as e:\n            logger.error(f\"Error checking alerts: {e}\")\n\n# Initialize system monitor\nsystem_monitor = SystemMonitor(socketio)\n\n# Authentication decorator\ndef login_required(f):\n    def decorated_function(*args, **kwargs):\n        if 'user_id' not in session:\n            return redirect(url_for('login'))\n        return f(*args, **kwargs)\n    decorated_function.__name__ = f.__name__\n    return decorated_function\n\n# Routes\***********('/login', methods=['GET', 'POST'])\ndef login():\n    if request.method == 'POST':\n        username = request.form.get('username')\n        password = request.form.get('password')\n        \n        conn = db_manager.get_connection()\n        cursor = conn.cursor()\n        cursor.execute('SELECT * FROM admin_users WHERE username = ?', (username,))\n        user = cursor.fetchone()\n        \n        if user and check_password_hash(user['password_hash'], password):\n            session['user_id'] = user['id']\n            session['username'] = user['username']\n            session['role'] = user['role']\n            \n            # Update last login\n            cursor.execute('UPDATE admin_users SET last_login = ? WHERE id = ?', \n                         (datetime.now(), user['id']))\n            conn.commit()\n            conn.close()\n            \n            flash('Login successful!', 'success')\n            return redirect(url_for('dashboard'))\n        else:\n            flash('Invalid username or password', 'error')\n            conn.close()\n    \n    return render_template('auth/login.html')\n\***********('/logout')\ndef logout():\n    session.clear()\n    flash('Logged out successfully', 'success')\n    return redirect(url_for('login'))\n\***********('/')\n@login_required\ndef dashboard():\n    return render_template('dashboard/index.html')\n\n# API Routes\***********('/api/system/metrics')\n@login_required\ndef api_system_metrics():\n    metrics = system_monitor.collect_system_metrics()\n    return jsonify(metrics)\n\***********('/api/system/metrics/history')\n@login_required\ndef api_metrics_history():\n    hours = request.args.get('hours', 24, type=int)\n\n    conn = db_manager.get_connection()\n    cursor = conn.cursor()\n\n    since = datetime.now() - timedelta(hours=hours)\n    cursor.execute('''\n        SELECT * FROM system_metrics\n        WHERE timestamp > ?\n        ORDER BY timestamp DESC\n        LIMIT 1000\n    ''', (since,))\n\n    rows = cursor.fetchall()\n    conn.close()\n\n    return jsonify({\n        'metrics': [dict(row) for row in rows]\n    })\n\***********('/change-password', methods=['GET', 'POST'])\n@login_required\ndef change_password():\n    if request.method == 'POST':\n        current_password = request.form.get('current_password')\n        new_password = request.form.get('new_password')\n        confirm_password = request.form.get('confirm_password')\n\n        if not all([current_password, new_password, confirm_password]):\n            flash('All fields are required', 'error')\n            return redirect(url_for('change_password'))\n\n        if new_password != confirm_password:\n            flash('New passwords do not match', 'error')\n            return redirect(url_for('change_password'))\n\n        if len(new_password) < 6:\n            flash('Password must be at least 6 characters long', 'error')\n            return redirect(url_for('change_password'))\n\n        # Verify current password\n        conn = db_manager.get_connection()\n        cursor = conn.cursor()\n        cursor.execute('SELECT password_hash FROM admin_users WHERE id = ?', (session['user_id'],))\n        user = cursor.fetchone()\n\n        if not user or not check_password_hash(user['password_hash'], current_password):\n            flash('Current password is incorrect', 'error')\n            conn.close()\n            return redirect(url_for('change_password'))\n\n        # Update password\n        new_password_hash = generate_password_hash(new_password)\n        cursor.execute('UPDATE admin_users SET password_hash = ? WHERE id = ?',\n                      (new_password_hash, session['user_id']))\n        conn.commit()\n        conn.close()\n\n        flash('Password changed successfully!', 'success')\n        return redirect(url_for('dashboard'))\n\n    return render_template('auth/change_password.html')\n\***********('/api/instances/manage', methods=['POST'])\n@login_required\ndef manage_instances():\n    action = request.json.get('action')\n    instance_id = request.json.get('instance_id')\n\n    if action == 'stop' and instance_id:\n        try:\n            # Find and stop the instance\n            for proc in psutil.process_iter(['pid', 'cmdline']):\n                if 'python' in proc.info['cmdline'][0] and str(instance_id) in ' '.join(proc.info['cmdline']):\n                    proc.terminate()\n                    return jsonify({'success': True, 'message': f'Instance {instance_id} stopped'})\n            return jsonify({'success': False, 'message': 'Instance not found'})\n        except Exception as e:\n            return jsonify({'success': False, 'message': str(e)})\n\n    elif action == 'restart' and instance_id:\n        try:\n            # This would need to be implemented based on your instance management\n            return jsonify({'success': True, 'message': f'Instance {instance_id} restart initiated'})\n        except Exception as e:\n            return jsonify({'success': False, 'message': str(e)})\n\n    return jsonify({'success': False, 'message': 'Invalid action'})\n\***********('/api/system/cleanup', methods=['POST'])\n@login_required\ndef system_cleanup():\n    try:\n        # Run the simple cleanup script\n        result = subprocess.run(['/home/<USER>/myproject/algofactory/simple_cleanup.sh'],\n                              capture_output=True, text=True)\n\n        if result.returncode == 0:\n            return jsonify({'success': True, 'message': 'System cleanup completed', 'output': result.stdout})\n        else:\n            return jsonify({'success': False, 'message': 'Cleanup failed', 'error': result.stderr})\n    except Exception as e:\n        return jsonify({'success': False, 'message': str(e)})\n\n# SocketIO Events\*************('connect', namespace='/admin')\ndef handle_connect():\n    if 'user_id' not in session:\n        return False\n    \n    join_room('admin_room')\n    emit('connected', {'message': 'Connected to admin dashboard'})\n    logger.info(f\"Admin user {session.get('username')} connected\")\n\*************('disconnect', namespace='/admin')\ndef handle_disconnect():\n    leave_room('admin_room')\n    logger.info(f\"Admin user {session.get('username')} disconnected\")\n\nif __name__ == '__main__':\n    # Start system monitoring\n    system_monitor.start_monitoring()\n    \n    # Run the app with production settings\n    try:\n        # Use threading mode for better compatibility\n        socketio.run(app,\n                    host='0.0.0.0',\n                    port=9001,\n                    debug=False,\n                    use_reloader=False,\n                    log_output=False,\n                    allow_unsafe_werkzeug=True)\n    except Exception as e:\n        logger.error(f\"Failed to start admin dashboard: {e}\")\n        import traceback\n        traceback.print_exc()\n    finally:\n        system_monitor.stop_monitoring()\n", "modifiedCode": "#!/usr/bin/env python3\n\n\"\"\"\nAlgoFactory Advanced Admin Dashboard\nScalable server management system with modular architecture\n\"\"\"\n\nfrom flask import Flask, render_template, request, jsonify, session, redirect, url_for, flash\nfrom flask_socketio import Socket<PERSON>, emit, join_room, leave_room\nfrom werkzeug.security import generate_password_hash, check_password_hash\nimport os\nimport sys\nimport json\nimport sqlite3\nimport psutil\nimport subprocess\nfrom datetime import datetime, timedelta\nfrom pathlib import Path\nimport logging\nimport asyncio\nfrom threading import Thread\nimport time\n\n# Add parent directory to path for imports\nsys.path.append(str(Path(__file__).parent.parent))\n\n# Configure logging\nlogging.basicConfig(level=logging.INFO)\nlogger = logging.getLogger(__name__)\n\n# Initialize Flask app\napp = Flask(__name__)\napp.config['SECRET_KEY'] = 'algofactory-admin-secret-key-change-in-production'\napp.config['TEMPLATES_AUTO_RELOAD'] = True\n\n# Initialize SocketIO\nsocketio = SocketIO(app, cors_allowed_origins=\"*\", async_mode='threading')\n\n# Database setup\nBASE_DIR = Path(__file__).parent\nDB_PATH = BASE_DIR / \"admin.db\"\n\nclass DatabaseManager:\n    \"\"\"Centralized database management\"\"\"\n    \n    def __init__(self, db_path):\n        self.db_path = db_path\n        self.init_database()\n    \n    def get_connection(self):\n        conn = sqlite3.connect(str(self.db_path))\n        conn.row_factory = sqlite3.Row\n        return conn\n    \n    def init_database(self):\n        \"\"\"Initialize all database tables\"\"\"\n        conn = self.get_connection()\n        cursor = conn.cursor()\n        \n        # Admin users table\n        cursor.execute('''\n            CREATE TABLE IF NOT EXISTS admin_users (\n                id INTEGER PRIMARY KEY AUTOINCREMENT,\n                username TEXT UNIQUE NOT NULL,\n                password_hash TEXT NOT NULL,\n                email TEXT,\n                role TEXT DEFAULT 'admin',\n                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n                last_login DATETIME\n            )\n        ''')\n        \n        # System metrics table\n        cursor.execute('''\n            CREATE TABLE IF NOT EXISTS system_metrics (\n                id INTEGER PRIMARY KEY AUTOINCREMENT,\n                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,\n                cpu_percent REAL,\n                memory_percent REAL,\n                memory_used_gb REAL,\n                memory_total_gb REAL,\n                disk_percent REAL,\n                disk_used_gb REAL,\n                disk_total_gb REAL,\n                load_average REAL,\n                network_bytes_sent INTEGER,\n                network_bytes_recv INTEGER,\n                active_connections INTEGER,\n                process_count INTEGER\n            )\n        ''')\n        \n        # AlgoFactory instances table\n        cursor.execute('''\n            CREATE TABLE IF NOT EXISTS algofactory_instances (\n                id INTEGER PRIMARY KEY AUTOINCREMENT,\n                instance_id TEXT UNIQUE NOT NULL,\n                port INTEGER NOT NULL,\n                websocket_port INTEGER,\n                zmq_port INTEGER,\n                status TEXT DEFAULT 'stopped',\n                pid INTEGER,\n                memory_mb REAL,\n                cpu_percent REAL,\n                uptime_seconds INTEGER DEFAULT 0,\n                last_check DATETIME DEFAULT CURRENT_TIMESTAMP,\n                auto_restart BOOLEAN DEFAULT 1,\n                created_at DATETIME DEFAULT CURRENT_TIMESTAMP\n            )\n        ''')\n        \n        # Nginx sites table\n        cursor.execute('''\n            CREATE TABLE IF NOT EXISTS nginx_sites (\n                id INTEGER PRIMARY KEY AUTOINCREMENT,\n                domain TEXT UNIQUE NOT NULL,\n                port INTEGER NOT NULL,\n                ssl_enabled BOOLEAN DEFAULT 0,\n                ssl_cert_path TEXT,\n                ssl_expiry_date DATETIME,\n                status TEXT DEFAULT 'active',\n                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n                last_check DATETIME DEFAULT CURRENT_TIMESTAMP\n            )\n        ''')\n        \n        # System events/logs table\n        cursor.execute('''\n            CREATE TABLE IF NOT EXISTS system_events (\n                id INTEGER PRIMARY KEY AUTOINCREMENT,\n                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,\n                event_type TEXT NOT NULL,\n                category TEXT DEFAULT 'system',\n                message TEXT NOT NULL,\n                severity TEXT DEFAULT 'info',\n                source TEXT,\n                details TEXT,\n                user_id INTEGER,\n                FOREIGN KEY (user_id) REFERENCES admin_users (id)\n            )\n        ''')\n        \n        # Tasks/jobs table\n        cursor.execute('''\n            CREATE TABLE IF NOT EXISTS admin_tasks (\n                id INTEGER PRIMARY KEY AUTOINCREMENT,\n                task_name TEXT NOT NULL,\n                task_type TEXT NOT NULL,\n                status TEXT DEFAULT 'pending',\n                progress INTEGER DEFAULT 0,\n                result TEXT,\n                error_message TEXT,\n                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n                started_at DATETIME,\n                completed_at DATETIME,\n                created_by INTEGER,\n                FOREIGN KEY (created_by) REFERENCES admin_users (id)\n            )\n        ''')\n        \n        # Settings table\n        cursor.execute('''\n            CREATE TABLE IF NOT EXISTS admin_settings (\n                id INTEGER PRIMARY KEY AUTOINCREMENT,\n                setting_key TEXT UNIQUE NOT NULL,\n                setting_value TEXT,\n                setting_type TEXT DEFAULT 'string',\n                description TEXT,\n                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n                updated_by INTEGER,\n                FOREIGN KEY (updated_by) REFERENCES admin_users (id)\n            )\n        ''')\n        \n        conn.commit()\n        \n        # Create default admin user if none exists\n        cursor.execute('SELECT COUNT(*) FROM admin_users')\n        if cursor.fetchone()[0] == 0:\n            default_password = generate_password_hash('admin123')\n            cursor.execute('''\n                INSERT INTO admin_users (username, password_hash, email, role)\n                VALUES (?, ?, ?, ?)\n            ''', ('admin', default_password, '<EMAIL>', 'superadmin'))\n            conn.commit()\n            logger.info(\"Created default admin user: admin/admin123\")\n        \n        # Insert default settings\n        default_settings = [\n            ('monitoring_interval', '5', 'integer', 'System monitoring interval in seconds'),\n            ('max_log_entries', '1000', 'integer', 'Maximum log entries to keep'),\n            ('auto_cleanup_days', '30', 'integer', 'Auto cleanup logs older than X days'),\n            ('alert_email', '<EMAIL>', 'string', 'Email for system alerts'),\n            ('maintenance_mode', 'false', 'boolean', 'Enable maintenance mode'),\n        ]\n        \n        for key, value, type_, desc in default_settings:\n            cursor.execute('''\n                INSERT OR IGNORE INTO admin_settings (setting_key, setting_value, setting_type, description)\n                VALUES (?, ?, ?, ?)\n            ''', (key, value, type_, desc))\n        \n        conn.commit()\n        conn.close()\n\n# Initialize database\ndb_manager = DatabaseManager(DB_PATH)\n\nclass SystemMonitor:\n    \"\"\"Real-time system monitoring\"\"\"\n    \n    def __init__(self, socketio_instance):\n        self.socketio = socketio_instance\n        self.monitoring = False\n        self.monitor_thread = None\n    \n    def start_monitoring(self):\n        \"\"\"Start the monitoring thread\"\"\"\n        if not self.monitoring:\n            self.monitoring = True\n            self.monitor_thread = Thread(target=self._monitor_loop, daemon=True)\n            self.monitor_thread.start()\n            logger.info(\"System monitoring started\")\n    \n    def stop_monitoring(self):\n        \"\"\"Stop the monitoring thread\"\"\"\n        self.monitoring = False\n        if self.monitor_thread:\n            self.monitor_thread.join(timeout=5)\n        logger.info(\"System monitoring stopped\")\n    \n    def _monitor_loop(self):\n        \"\"\"Main monitoring loop\"\"\"\n        while self.monitoring:\n            try:\n                # Collect system metrics\n                metrics = self.collect_system_metrics()\n                \n                # Store in database\n                self.store_metrics(metrics)\n                \n                # Emit to connected clients\n                self.socketio.emit('system_update', {\n                    'type': 'metrics',\n                    'data': metrics,\n                    'timestamp': datetime.now().isoformat()\n                }, namespace='/admin')\n                \n                # Check for alerts\n                self.check_alerts(metrics)\n                \n                # Sleep for monitoring interval\n                time.sleep(5)  # 5 seconds default\n                \n            except Exception as e:\n                logger.error(f\"Error in monitoring loop: {e}\")\n                time.sleep(10)  # Wait longer on error\n    \n    def collect_system_metrics(self):\n        \"\"\"Collect comprehensive system metrics\"\"\"\n        try:\n            # CPU and Memory\n            cpu_percent = psutil.cpu_percent(interval=1)\n            memory = psutil.virtual_memory()\n            disk = psutil.disk_usage('/')\n            load_avg = os.getloadavg()[0] if hasattr(os, 'getloadavg') else 0\n            \n            # Network\n            network = psutil.net_io_counters()\n            \n            # Process information\n            process_count = len(psutil.pids())\n            \n            # AlgoFactory instances\n            instances = self.get_algofactory_instances()\n            \n            # Nginx status\n            nginx_status = self.get_nginx_status()\n            \n            return {\n                'system': {\n                    'timestamp': datetime.now().isoformat(),\n                    'cpu': {\n                        'percent': round(cpu_percent, 1),\n                        'count': psutil.cpu_count(),\n                        'load_average': round(load_avg, 2)\n                    },\n                    'memory': {\n                        'percent': round(memory.percent, 1),\n                        'used_gb': round(memory.used / (1024**3), 2),\n                        'total_gb': round(memory.total / (1024**3), 2),\n                        'available_gb': round(memory.available / (1024**3), 2)\n                    },\n                    'disk': {\n                        'percent': round((disk.used / disk.total) * 100, 1),\n                        'used_gb': round(disk.used / (1024**3), 2),\n                        'total_gb': round(disk.total / (1024**3), 2),\n                        'free_gb': round(disk.free / (1024**3), 2)\n                    },\n                    'network': {\n                        'bytes_sent': network.bytes_sent,\n                        'bytes_recv': network.bytes_recv\n                    },\n                    'processes': process_count\n                },\n                'instances': instances,\n                'nginx': nginx_status\n            }\n        except Exception as e:\n            logger.error(f\"Error collecting system metrics: {e}\")\n            return {}\n    \n    def get_algofactory_instances(self):\n        \"\"\"Get status of AlgoFactory instances\"\"\"\n        instances = []\n        try:\n            # Check running processes\n            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'memory_info', 'cpu_percent', 'create_time']):\n                try:\n                    if 'python' in proc.info['name'] and proc.info['cmdline']:\n                        cmdline = ' '.join(proc.info['cmdline'])\n                        if 'app.py' in cmdline and any(port in cmdline for port in ['8010', '8011', '8012']):\n                            # Extract port from command line\n                            port = None\n                            for arg in proc.info['cmdline']:\n                                if arg.isdigit() and 8000 <= int(arg) <= 9999:\n                                    port = int(arg)\n                                    break\n                            \n                            if port:\n                                uptime = datetime.now() - datetime.fromtimestamp(proc.create_time())\n                                instances.append({\n                                    'instance_id': str(port),\n                                    'port': port,\n                                    'websocket_port': port + 12000,\n                                    'zmq_port': port + 15000,\n                                    'pid': proc.info['pid'],\n                                    'status': 'running',\n                                    'uptime_seconds': int(uptime.total_seconds()),\n                                    'memory_mb': round(proc.memory_info().rss / (1024**2), 1),\n                                    'cpu_percent': round(proc.cpu_percent(), 1)\n                                })\n                except (psutil.NoSuchProcess, psutil.AccessDenied):\n                    continue\n        except Exception as e:\n            logger.error(f\"Error getting AlgoFactory instances: {e}\")\n        \n        return instances\n    \n    def get_nginx_status(self):\n        \"\"\"Get Nginx status and sites\"\"\"\n        try:\n            # Check if Nginx is running\n            nginx_running = False\n            nginx_pid = None\n            \n            for proc in psutil.process_iter(['pid', 'name']):\n                if proc.info['name'] == 'nginx':\n                    nginx_running = True\n                    nginx_pid = proc.info['pid']\n                    break\n            \n            # Get configured sites\n            sites = []\n            sites_enabled_dir = Path('/etc/nginx/sites-enabled')\n            if sites_enabled_dir.exists():\n                for site_file in sites_enabled_dir.glob('*.algofactory.in.conf'):\n                    domain = site_file.stem\n                    ssl_cert_path = Path(f'/etc/letsencrypt/live/{domain}')\n                    \n                    sites.append({\n                        'domain': domain,\n                        'ssl_enabled': ssl_cert_path.exists(),\n                        'config_file': str(site_file)\n                    })\n            \n            return {\n                'running': nginx_running,\n                'pid': nginx_pid,\n                'sites_count': len(sites),\n                'sites': sites\n            }\n        except Exception as e:\n            logger.error(f\"Error getting Nginx status: {e}\")\n            return {'running': False, 'error': str(e)}\n    \n    def store_metrics(self, metrics):\n        \"\"\"Store metrics in database\"\"\"\n        try:\n            if not metrics or 'system' not in metrics:\n                return\n            \n            system = metrics['system']\n            conn = db_manager.get_connection()\n            cursor = conn.cursor()\n            \n            cursor.execute('''\n                INSERT INTO system_metrics \n                (cpu_percent, memory_percent, memory_used_gb, memory_total_gb,\n                 disk_percent, disk_used_gb, disk_total_gb, load_average,\n                 network_bytes_sent, network_bytes_recv, process_count)\n                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n            ''', (\n                system.get('cpu', {}).get('percent', 0),\n                system.get('memory', {}).get('percent', 0),\n                system.get('memory', {}).get('used_gb', 0),\n                system.get('memory', {}).get('total_gb', 0),\n                system.get('disk', {}).get('percent', 0),\n                system.get('disk', {}).get('used_gb', 0),\n                system.get('disk', {}).get('total_gb', 0),\n                system.get('cpu', {}).get('load_average', 0),\n                system.get('network', {}).get('bytes_sent', 0),\n                system.get('network', {}).get('bytes_recv', 0),\n                system.get('processes', 0)\n            ))\n            \n            conn.commit()\n            conn.close()\n        except Exception as e:\n            logger.error(f\"Error storing metrics: {e}\")\n    \n    def check_alerts(self, metrics):\n        \"\"\"Check for system alerts\"\"\"\n        try:\n            if not metrics or 'system' not in metrics:\n                return\n            \n            system = metrics['system']\n            alerts = []\n            \n            # CPU alert\n            cpu_percent = system.get('cpu', {}).get('percent', 0)\n            if cpu_percent > 90:\n                alerts.append({\n                    'type': 'cpu_high',\n                    'severity': 'critical',\n                    'message': f'High CPU usage: {cpu_percent}%'\n                })\n            \n            # Memory alert\n            memory_percent = system.get('memory', {}).get('percent', 0)\n            if memory_percent > 85:\n                alerts.append({\n                    'type': 'memory_high',\n                    'severity': 'warning' if memory_percent < 95 else 'critical',\n                    'message': f'High memory usage: {memory_percent}%'\n                })\n            \n            # Disk alert\n            disk_percent = system.get('disk', {}).get('percent', 0)\n            if disk_percent > 85:\n                alerts.append({\n                    'type': 'disk_high',\n                    'severity': 'warning' if disk_percent < 95 else 'critical',\n                    'message': f'High disk usage: {disk_percent}%'\n                })\n            \n            # Emit alerts\n            for alert in alerts:\n                self.socketio.emit('system_alert', alert, namespace='/admin')\n                \n        except Exception as e:\n            logger.error(f\"Error checking alerts: {e}\")\n\n# Initialize system monitor\nsystem_monitor = SystemMonitor(socketio)\n\n# Authentication decorator\ndef login_required(f):\n    def decorated_function(*args, **kwargs):\n        if 'user_id' not in session:\n            return redirect(url_for('login'))\n        return f(*args, **kwargs)\n    decorated_function.__name__ = f.__name__\n    return decorated_function\n\n# Routes\***********('/login', methods=['GET', 'POST'])\ndef login():\n    if request.method == 'POST':\n        username = request.form.get('username')\n        password = request.form.get('password')\n        \n        conn = db_manager.get_connection()\n        cursor = conn.cursor()\n        cursor.execute('SELECT * FROM admin_users WHERE username = ?', (username,))\n        user = cursor.fetchone()\n        \n        if user and check_password_hash(user['password_hash'], password):\n            session['user_id'] = user['id']\n            session['username'] = user['username']\n            session['role'] = user['role']\n            \n            # Update last login\n            cursor.execute('UPDATE admin_users SET last_login = ? WHERE id = ?', \n                         (datetime.now(), user['id']))\n            conn.commit()\n            conn.close()\n            \n            flash('Login successful!', 'success')\n            return redirect(url_for('dashboard'))\n        else:\n            flash('Invalid username or password', 'error')\n            conn.close()\n    \n    return render_template('auth/login.html')\n\***********('/logout')\ndef logout():\n    session.clear()\n    flash('Logged out successfully', 'success')\n    return redirect(url_for('login'))\n\***********('/')\n@login_required\ndef dashboard():\n    return render_template('dashboard/index.html')\n\***********('/system')\n@login_required\ndef system_monitor():\n    return render_template('system/monitor.html')\n\***********('/instances')\n@login_required\ndef instances():\n    return render_template('instances/manage.html')\n\***********('/nginx')\n@login_required\ndef nginx_management():\n    return render_template('nginx/manage.html')\n\***********('/ssl')\n@login_required\ndef ssl_certificates():\n    return render_template('ssl/manage.html')\n\***********('/logs')\n@login_required\ndef system_logs():\n    return render_template('logs/view.html')\n\***********('/metrics')\n@login_required\ndef performance_metrics():\n    return render_template('metrics/view.html')\n\***********('/alerts')\n@login_required\ndef alerts():\n    return render_template('alerts/manage.html')\n\***********('/backup')\n@login_required\ndef backup():\n    return render_template('backup/manage.html')\n\***********('/tasks')\n@login_required\ndef tasks():\n    return render_template('tasks/manage.html')\n\***********('/terminal')\n@login_required\ndef web_terminal():\n    return render_template('terminal/index.html')\n\***********('/settings')\n@login_required\ndef settings():\n    return render_template('settings/index.html')\n\***********('/users')\n@login_required\ndef users():\n    return render_template('users/manage.html')\n\n# API Routes\***********('/api/system/metrics')\n@login_required\ndef api_system_metrics():\n    metrics = system_monitor.collect_system_metrics()\n    return jsonify(metrics)\n\***********('/api/system/metrics/history')\n@login_required\ndef api_metrics_history():\n    hours = request.args.get('hours', 24, type=int)\n\n    conn = db_manager.get_connection()\n    cursor = conn.cursor()\n\n    since = datetime.now() - timedelta(hours=hours)\n    cursor.execute('''\n        SELECT * FROM system_metrics\n        WHERE timestamp > ?\n        ORDER BY timestamp DESC\n        LIMIT 1000\n    ''', (since,))\n\n    rows = cursor.fetchall()\n    conn.close()\n\n    return jsonify({\n        'metrics': [dict(row) for row in rows]\n    })\n\***********('/change-password', methods=['GET', 'POST'])\n@login_required\ndef change_password():\n    if request.method == 'POST':\n        current_password = request.form.get('current_password')\n        new_password = request.form.get('new_password')\n        confirm_password = request.form.get('confirm_password')\n\n        if not all([current_password, new_password, confirm_password]):\n            flash('All fields are required', 'error')\n            return redirect(url_for('change_password'))\n\n        if new_password != confirm_password:\n            flash('New passwords do not match', 'error')\n            return redirect(url_for('change_password'))\n\n        if len(new_password) < 6:\n            flash('Password must be at least 6 characters long', 'error')\n            return redirect(url_for('change_password'))\n\n        # Verify current password\n        conn = db_manager.get_connection()\n        cursor = conn.cursor()\n        cursor.execute('SELECT password_hash FROM admin_users WHERE id = ?', (session['user_id'],))\n        user = cursor.fetchone()\n\n        if not user or not check_password_hash(user['password_hash'], current_password):\n            flash('Current password is incorrect', 'error')\n            conn.close()\n            return redirect(url_for('change_password'))\n\n        # Update password\n        new_password_hash = generate_password_hash(new_password)\n        cursor.execute('UPDATE admin_users SET password_hash = ? WHERE id = ?',\n                      (new_password_hash, session['user_id']))\n        conn.commit()\n        conn.close()\n\n        flash('Password changed successfully!', 'success')\n        return redirect(url_for('dashboard'))\n\n    return render_template('auth/change_password.html')\n\***********('/api/instances/manage', methods=['POST'])\n@login_required\ndef manage_instances():\n    action = request.json.get('action')\n    instance_id = request.json.get('instance_id')\n\n    if action == 'stop' and instance_id:\n        try:\n            # Find and stop the instance\n            for proc in psutil.process_iter(['pid', 'cmdline']):\n                if 'python' in proc.info['cmdline'][0] and str(instance_id) in ' '.join(proc.info['cmdline']):\n                    proc.terminate()\n                    return jsonify({'success': True, 'message': f'Instance {instance_id} stopped'})\n            return jsonify({'success': False, 'message': 'Instance not found'})\n        except Exception as e:\n            return jsonify({'success': False, 'message': str(e)})\n\n    elif action == 'restart' and instance_id:\n        try:\n            # This would need to be implemented based on your instance management\n            return jsonify({'success': True, 'message': f'Instance {instance_id} restart initiated'})\n        except Exception as e:\n            return jsonify({'success': False, 'message': str(e)})\n\n    return jsonify({'success': False, 'message': 'Invalid action'})\n\***********('/api/system/cleanup', methods=['POST'])\n@login_required\ndef system_cleanup():\n    try:\n        # Run the simple cleanup script\n        result = subprocess.run(['/home/<USER>/myproject/algofactory/simple_cleanup.sh'],\n                              capture_output=True, text=True)\n\n        if result.returncode == 0:\n            return jsonify({'success': True, 'message': 'System cleanup completed', 'output': result.stdout})\n        else:\n            return jsonify({'success': False, 'message': 'Cleanup failed', 'error': result.stderr})\n    except Exception as e:\n        return jsonify({'success': False, 'message': str(e)})\n\n# SocketIO Events\*************('connect', namespace='/admin')\ndef handle_connect():\n    if 'user_id' not in session:\n        return False\n    \n    join_room('admin_room')\n    emit('connected', {'message': 'Connected to admin dashboard'})\n    logger.info(f\"Admin user {session.get('username')} connected\")\n\*************('disconnect', namespace='/admin')\ndef handle_disconnect():\n    leave_room('admin_room')\n    logger.info(f\"Admin user {session.get('username')} disconnected\")\n\nif __name__ == '__main__':\n    # Start system monitoring\n    system_monitor.start_monitoring()\n    \n    # Run the app with production settings\n    try:\n        # Use threading mode for better compatibility\n        socketio.run(app,\n                    host='0.0.0.0',\n                    port=9001,\n                    debug=False,\n                    use_reloader=False,\n                    log_output=False,\n                    allow_unsafe_werkzeug=True)\n    except Exception as e:\n        logger.error(f\"Failed to start admin dashboard: {e}\")\n        import traceback\n        traceback.print_exc()\n    finally:\n        system_monitor.stop_monitoring()\n"}