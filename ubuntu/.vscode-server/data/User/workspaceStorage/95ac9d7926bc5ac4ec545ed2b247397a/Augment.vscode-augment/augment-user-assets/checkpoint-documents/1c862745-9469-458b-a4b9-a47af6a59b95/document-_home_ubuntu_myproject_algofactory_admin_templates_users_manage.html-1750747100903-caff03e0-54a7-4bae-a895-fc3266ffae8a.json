{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/admin/templates/users/manage.html"}, "modifiedCode": "{% extends \"base.html\" %}\n\n{% block title %}User Management - AlgoFactory Admin{% endblock %}\n{% block page_title %}User Management{% endblock %}\n\n{% block content %}\n<div class=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n    <div class=\"stat bg-base-200 rounded-xl shadow-sm\">\n        <div class=\"stat-figure text-primary\">\n            <i class=\"fas fa-users text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">Total Users</div>\n        <div class=\"stat-value text-primary\">3</div>\n        <div class=\"stat-desc\">Registered users</div>\n    </div>\n    \n    <div class=\"stat bg-base-200 rounded-xl shadow-sm\">\n        <div class=\"stat-figure text-success\">\n            <i class=\"fas fa-user-check text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">Active Users</div>\n        <div class=\"stat-value text-success\">2</div>\n        <div class=\"stat-desc\">Currently online</div>\n    </div>\n    \n    <div class=\"stat bg-base-200 rounded-xl shadow-sm\">\n        <div class=\"stat-figure text-info\">\n            <i class=\"fas fa-user-shield text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">Administrators</div>\n        <div class=\"stat-value text-info\">1</div>\n        <div class=\"stat-desc\">Admin privileges</div>\n    </div>\n</div>\n\n<div class=\"card bg-base-200 shadow-sm\">\n    <div class=\"card-body\">\n        <div class=\"flex justify-between items-center mb-6\">\n            <h2 class=\"card-title\">\n                <i class=\"fas fa-list text-primary\"></i>\n                User List\n            </h2>\n            <button class=\"btn btn-primary\">\n                <i class=\"fas fa-plus\"></i>\n                Add User\n            </button>\n        </div>\n        \n        <div class=\"overflow-x-auto\">\n            <table class=\"table table-zebra w-full\">\n                <thead>\n                    <tr>\n                        <th>User</th>\n                        <th>Role</th>\n                        <th>Status</th>\n                        <th>Last Login</th>\n                        <th>Actions</th>\n                    </tr>\n                </thead>\n                <tbody>\n                    <tr>\n                        <td>\n                            <div class=\"flex items-center gap-3\">\n                                <div class=\"avatar\">\n                                    <div class=\"mask mask-squircle w-12 h-12 bg-primary text-primary-content flex items-center justify-center\">\n                                        <i class=\"fas fa-user\"></i>\n                                    </div>\n                                </div>\n                                <div>\n                                    <div class=\"font-bold\">admin</div>\n                                    <div class=\"text-sm text-base-content/70\"><EMAIL></div>\n                                </div>\n                            </div>\n                        </td>\n                        <td>\n                            <div class=\"badge badge-primary\">Administrator</div>\n                        </td>\n                        <td>\n                            <div class=\"badge badge-success\">Online</div>\n                        </td>\n                        <td>Just now</td>\n                        <td>\n                            <div class=\"flex gap-1\">\n                                <button class=\"btn btn-info btn-xs\">\n                                    <i class=\"fas fa-edit\"></i>\n                                </button>\n                                <button class=\"btn btn-warning btn-xs\">\n                                    <i class=\"fas fa-key\"></i>\n                                </button>\n                            </div>\n                        </td>\n                    </tr>\n                    <tr>\n                        <td>\n                            <div class=\"flex items-center gap-3\">\n                                <div class=\"avatar\">\n                                    <div class=\"mask mask-squircle w-12 h-12 bg-secondary text-secondary-content flex items-center justify-center\">\n                                        <i class=\"fas fa-user\"></i>\n                                    </div>\n                                </div>\n                                <div>\n                                    <div class=\"font-bold\">operator</div>\n                                    <div class=\"text-sm text-base-content/70\"><EMAIL></div>\n                                </div>\n                            </div>\n                        </td>\n                        <td>\n                            <div class=\"badge badge-secondary\">Operator</div>\n                        </td>\n                        <td>\n                            <div class=\"badge badge-warning\">Away</div>\n                        </td>\n                        <td>2 hours ago</td>\n                        <td>\n                            <div class=\"flex gap-1\">\n                                <button class=\"btn btn-info btn-xs\">\n                                    <i class=\"fas fa-edit\"></i>\n                                </button>\n                                <button class=\"btn btn-warning btn-xs\">\n                                    <i class=\"fas fa-key\"></i>\n                                </button>\n                                <button class=\"btn btn-error btn-xs\">\n                                    <i class=\"fas fa-trash\"></i>\n                                </button>\n                            </div>\n                        </td>\n                    </tr>\n                </tbody>\n            </table>\n        </div>\n    </div>\n</div>\n{% endblock %}\n"}