{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/admin/templates/logs/view.html"}, "modifiedCode": "{% extends \"base.html\" %}\n\n{% block title %}System Logs - AlgoFactory Admin{% endblock %}\n{% block page_title %}System Logs{% endblock %}\n\n{% block content %}\n<!-- Log Sources -->\n<div class=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n    <div class=\"stat bg-base-200 rounded-xl shadow-sm cursor-pointer\" onclick=\"loadLogs('admin')\">\n        <div class=\"stat-figure text-primary\">\n            <i class=\"fas fa-user-shield text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">Admin Logs</div>\n        <div class=\"stat-value text-primary\" id=\"admin-log-count\">0</div>\n        <div class=\"stat-desc\">Recent entries</div>\n    </div>\n    \n    <div class=\"stat bg-base-200 rounded-xl shadow-sm cursor-pointer\" onclick=\"loadLogs('nginx')\">\n        <div class=\"stat-figure text-secondary\">\n            <i class=\"fas fa-globe text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">Nginx Logs</div>\n        <div class=\"stat-value text-secondary\" id=\"nginx-log-count\">0</div>\n        <div class=\"stat-desc\">Access & Error</div>\n    </div>\n    \n    <div class=\"stat bg-base-200 rounded-xl shadow-sm cursor-pointer\" onclick=\"loadLogs('system')\">\n        <div class=\"stat-figure text-accent\">\n            <i class=\"fas fa-server text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">System Logs</div>\n        <div class=\"stat-value text-accent\" id=\"system-log-count\">0</div>\n        <div class=\"stat-desc\">Syslog entries</div>\n    </div>\n    \n    <div class=\"stat bg-base-200 rounded-xl shadow-sm cursor-pointer\" onclick=\"loadLogs('algofactory')\">\n        <div class=\"stat-figure text-info\">\n            <i class=\"fas fa-cubes text-3xl\"></i>\n        </div>\n        <div class=\"stat-title\">AlgoFactory Logs</div>\n        <div class=\"stat-value text-info\" id=\"algofactory-log-count\">0</div>\n        <div class=\"stat-desc\">Instance logs</div>\n    </div>\n</div>\n\n<!-- Log Viewer -->\n<div class=\"card bg-base-200 shadow-sm\">\n    <div class=\"card-body\">\n        <div class=\"flex justify-between items-center mb-6\">\n            <h2 class=\"card-title\">\n                <i class=\"fas fa-file-alt text-primary\"></i>\n                Log Viewer - <span id=\"current-log-source\">Select a log source</span>\n            </h2>\n            <div class=\"flex gap-2\">\n                <select class=\"select select-bordered select-sm\" id=\"log-level-filter\">\n                    <option value=\"all\">All Levels</option>\n                    <option value=\"error\">Error</option>\n                    <option value=\"warning\">Warning</option>\n                    <option value=\"info\">Info</option>\n                    <option value=\"debug\">Debug</option>\n                </select>\n                <button class=\"btn btn-ghost btn-sm\" onclick=\"refreshLogs()\">\n                    <i class=\"fas fa-sync-alt\"></i>\n                    Refresh\n                </button>\n                <button class=\"btn btn-primary btn-sm\" onclick=\"downloadLogs()\">\n                    <i class=\"fas fa-download\"></i>\n                    Download\n                </button>\n            </div>\n        </div>\n        \n        <div class=\"bg-base-300 rounded-lg p-4 h-96 overflow-y-auto font-mono text-sm\" id=\"log-content\">\n            <div class=\"text-center text-base-content/50 mt-20\">\n                <i class=\"fas fa-file-alt text-4xl mb-4\"></i>\n                <div>Select a log source above to view logs</div>\n            </div>\n        </div>\n        \n        <div class=\"flex justify-between items-center mt-4\">\n            <div class=\"text-sm text-base-content/70\">\n                <span id=\"log-info\">Ready to load logs</span>\n            </div>\n            <div class=\"flex gap-2\">\n                <label class=\"label cursor-pointer\">\n                    <span class=\"label-text mr-2\">Auto-refresh</span>\n                    <input type=\"checkbox\" class=\"toggle toggle-sm\" id=\"auto-refresh-toggle\" onchange=\"toggleAutoRefresh()\">\n                </label>\n            </div>\n        </div>\n    </div>\n</div>\n{% endblock %}\n\n{% block scripts %}\n<script>\n    let currentLogSource = null;\n    let autoRefreshInterval = null;\n    \n    document.addEventListener('DOMContentLoaded', function() {\n        loadLogCounts();\n    });\n    \n    async function loadLogCounts() {\n        // Mock log counts\n        document.getElementById('admin-log-count').textContent = '156';\n        document.getElementById('nginx-log-count').textContent = '2,341';\n        document.getElementById('system-log-count').textContent = '892';\n        document.getElementById('algofactory-log-count').textContent = '445';\n    }\n    \n    async function loadLogs(source) {\n        currentLogSource = source;\n        document.getElementById('current-log-source').textContent = source.charAt(0).toUpperCase() + source.slice(1) + ' Logs';\n        \n        const logContent = document.getElementById('log-content');\n        logContent.innerHTML = '<div class=\"text-center\"><i class=\"fas fa-spinner fa-spin mr-2\"></i>Loading logs...</div>';\n        \n        // Mock log data based on source\n        setTimeout(() => {\n            let logs = '';\n            const now = new Date();\n            \n            switch (source) {\n                case 'admin':\n                    logs = generateAdminLogs();\n                    break;\n                case 'nginx':\n                    logs = generateNginxLogs();\n                    break;\n                case 'system':\n                    logs = generateSystemLogs();\n                    break;\n                case 'algofactory':\n                    logs = generateAlgoFactoryLogs();\n                    break;\n            }\n            \n            logContent.innerHTML = logs;\n            logContent.scrollTop = logContent.scrollHeight;\n            \n            document.getElementById('log-info').textContent = `Loaded ${source} logs - Last updated: ${now.toLocaleTimeString()}`;\n        }, 1000);\n    }\n    \n    function generateAdminLogs() {\n        return `\n            <div class=\"space-y-1\">\n                <div class=\"text-info\">[2025-06-24 06:32:50] INFO: Admin dashboard started successfully</div>\n                <div class=\"text-success\">[2025-06-24 06:32:49] INFO: Database connection established</div>\n                <div class=\"text-info\">[2025-06-24 06:32:48] INFO: System monitoring initialized</div>\n                <div class=\"text-success\">[2025-06-24 06:30:15] INFO: User admin logged in from 172.31.14.191</div>\n                <div class=\"text-warning\">[2025-06-24 06:25:30] WARNING: High memory usage detected: 85%</div>\n                <div class=\"text-info\">[2025-06-24 06:20:45] INFO: System optimization completed</div>\n                <div class=\"text-success\">[2025-06-24 06:15:20] INFO: SSL certificate renewed for admin.algofactory.in</div>\n                <div class=\"text-info\">[2025-06-24 06:10:10] INFO: Nginx configuration reloaded</div>\n                <div class=\"text-warning\">[2025-06-24 06:05:55] WARNING: Instance 8012 stopped unexpectedly</div>\n                <div class=\"text-success\">[2025-06-24 06:05:56] INFO: Instance 8012 restarted automatically</div>\n            </div>\n        `;\n    }\n    \n    function generateNginxLogs() {\n        return `\n            <div class=\"space-y-1\">\n                <div class=\"text-success\">[2025-06-24 06:32:45] 200 GET /api/system/metrics - admin.algofactory.in</div>\n                <div class=\"text-success\">[2025-06-24 06:32:40] 200 GET /dashboard - admin.algofactory.in</div>\n                <div class=\"text-success\">[2025-06-24 06:32:35] 200 POST /login - admin.algofactory.in</div>\n                <div class=\"text-success\">[2025-06-24 06:30:20] 200 GET / - 8010.algofactory.in</div>\n                <div class=\"text-success\">[2025-06-24 06:30:15] 200 GET / - 8011.algofactory.in</div>\n                <div class=\"text-warning\">[2025-06-24 06:25:30] 404 GET /favicon.ico - 8012.algofactory.in</div>\n                <div class=\"text-success\">[2025-06-24 06:25:25] 200 GET /static/css/style.css - 8012.algofactory.in</div>\n                <div class=\"text-success\">[2025-06-24 06:25:20] 200 GET / - 8012.algofactory.in</div>\n                <div class=\"text-error\">[2025-06-24 06:20:15] 502 Bad Gateway - 8013.algofactory.in</div>\n                <div class=\"text-success\">[2025-06-24 06:15:10] 200 GET / - admin.algofactory.in</div>\n            </div>\n        `;\n    }\n    \n    function generateSystemLogs() {\n        return `\n            <div class=\"space-y-1\">\n                <div class=\"text-info\">[2025-06-24 06:32:50] systemd[1]: Started algofactory-admin.service</div>\n                <div class=\"text-warning\">[2025-06-24 06:31:25] systemd[1]: algofactory-admin.service: Failed with result 'exit-code'</div>\n                <div class=\"text-info\">[2025-06-24 06:30:00] systemd[1]: Started Daily apt download activities</div>\n                <div class=\"text-info\">[2025-06-24 06:25:30] kernel: [12345.678] TCP: request_sock_TCP: Possible SYN flooding</div>\n                <div class=\"text-success\">[2025-06-24 06:20:15] systemd[1]: Reloaded nginx.service</div>\n                <div class=\"text-info\">[2025-06-24 06:15:45] cron[1234]: (root) CMD (test -x /usr/sbin/anacron)</div>\n                <div class=\"text-warning\">[2025-06-24 06:10:30] systemd[1]: snapd.service: Killing process 5678</div>\n                <div class=\"text-info\">[2025-06-24 06:05:20] systemd[1]: Started Session 123 of user ubuntu</div>\n                <div class=\"text-success\">[2025-06-24 06:00:15] systemd[1]: Reached target Timers</div>\n                <div class=\"text-info\">[2025-06-24 05:55:10] systemd[1]: Starting Daily apt upgrade activities</div>\n            </div>\n        `;\n    }\n    \n    function generateAlgoFactoryLogs() {\n        return `\n            <div class=\"space-y-1\">\n                <div class=\"text-success\">[2025-06-24 06:32:30] Instance 8010: WebSocket connection established</div>\n                <div class=\"text-info\">[2025-06-24 06:30:45] Instance 8011: Processing trading signal</div>\n                <div class=\"text-success\">[2025-06-24 06:30:40] Instance 8012: Database sync completed</div>\n                <div class=\"text-warning\">[2025-06-24 06:25:55] Instance 8010: High CPU usage detected: 95%</div>\n                <div class=\"text-info\">[2025-06-24 06:25:30] Instance 8011: Market data updated</div>\n                <div class=\"text-error\">[2025-06-24 06:20:15] Instance 8012: Connection to external API failed</div>\n                <div class=\"text-success\">[2025-06-24 06:20:16] Instance 8012: Retrying API connection</div>\n                <div class=\"text-success\">[2025-06-24 06:20:18] Instance 8012: API connection restored</div>\n                <div class=\"text-info\">[2025-06-24 06:15:45] Instance 8010: Scheduled backup completed</div>\n                <div class=\"text-warning\">[2025-06-24 06:10:30] Instance 8011: Memory usage above threshold: 80%</div>\n            </div>\n        `;\n    }\n    \n    function refreshLogs() {\n        if (currentLogSource) {\n            showToast('Refreshing logs...', 'info');\n            loadLogs(currentLogSource);\n        } else {\n            showToast('Please select a log source first', 'warning');\n        }\n    }\n    \n    function downloadLogs() {\n        if (!currentLogSource) {\n            showToast('Please select a log source first', 'warning');\n            return;\n        }\n        \n        showToast(`Downloading ${currentLogSource} logs...`, 'info');\n        \n        // Create download link\n        const logContent = document.getElementById('log-content').textContent;\n        const blob = new Blob([logContent], { type: 'text/plain' });\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `${currentLogSource}-logs-${new Date().toISOString().split('T')[0]}.txt`;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        window.URL.revokeObjectURL(url);\n        \n        showToast('Logs downloaded successfully', 'success');\n    }\n    \n    function toggleAutoRefresh() {\n        const toggle = document.getElementById('auto-refresh-toggle');\n        \n        if (toggle.checked) {\n            if (currentLogSource) {\n                autoRefreshInterval = setInterval(() => {\n                    loadLogs(currentLogSource);\n                }, 10000); // Refresh every 10 seconds\n                showToast('Auto-refresh enabled (10s interval)', 'info');\n            } else {\n                toggle.checked = false;\n                showToast('Please select a log source first', 'warning');\n            }\n        } else {\n            if (autoRefreshInterval) {\n                clearInterval(autoRefreshInterval);\n                autoRefreshInterval = null;\n                showToast('Auto-refresh disabled', 'info');\n            }\n        }\n    }\n    \n    // Filter logs by level\n    document.getElementById('log-level-filter').addEventListener('change', function() {\n        const level = this.value;\n        const logContent = document.getElementById('log-content');\n        const lines = logContent.querySelectorAll('div > div');\n        \n        lines.forEach(line => {\n            if (level === 'all') {\n                line.style.display = 'block';\n            } else {\n                const text = line.textContent.toLowerCase();\n                if (text.includes(level.toLowerCase())) {\n                    line.style.display = 'block';\n                } else {\n                    line.style.display = 'none';\n                }\n            }\n        });\n    });\n</script>\n{% endblock %}\n"}