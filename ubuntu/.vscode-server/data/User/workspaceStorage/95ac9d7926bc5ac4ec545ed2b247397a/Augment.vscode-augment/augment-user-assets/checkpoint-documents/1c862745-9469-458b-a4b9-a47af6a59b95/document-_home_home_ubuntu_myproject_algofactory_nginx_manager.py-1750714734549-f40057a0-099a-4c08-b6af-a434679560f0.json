{"path": {"rootPath": "/home", "relPath": "home/ubuntu/myproject/algofactory/nginx_manager.py"}, "modifiedCode": "#!/usr/bin/env python3\n\n\"\"\"\nCentralized Nginx Configuration Manager for AlgoFactory\nAutomates Nginx setup, SSL certificates, and reverse proxy configuration\n\"\"\"\n\nimport os\nimport sys\nimport subprocess\nimport argparse\nimport json\nfrom pathlib import Path\nimport time\n\nclass NginxManager:\n    def __init__(self):\n        self.nginx_sites_available = \"/etc/nginx/sites-available\"\n        self.nginx_sites_enabled = \"/etc/nginx/sites-enabled\"\n        self.base_domain = \"algofactory.in\"\n        self.email = \"<EMAIL>\"\n        \n    def create_nginx_config(self, subdomain, port):\n        \"\"\"Create Nginx configuration for a subdomain\"\"\"\n        domain = f\"{subdomain}.{self.base_domain}\"\n        config_file = f\"{self.nginx_sites_available}/{domain}.conf\"\n        \n        config_content = f\"\"\"# AlgoFactory Nginx Configuration for {domain}\n# Auto-generated by nginx_manager.py\n\nserver {{\n    listen 80;\n    server_name {domain};\n    \n    # Security headers\n    add_header X-Frame-Options DENY;\n    add_header X-Content-Type-Options nosniff;\n    add_header X-XSS-Protection \"1; mode=block\";\n    add_header Referrer-Policy strict-origin-when-cross-origin;\n    \n    # Rate limiting\n    limit_req_zone $binary_remote_addr zone={subdomain}_limit:10m rate=10r/s;\n    limit_req zone={subdomain}_limit burst=20 nodelay;\n    \n    # Main location block\n    location / {{\n        # Proxy settings\n        proxy_pass http://127.0.0.1:{port};\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n        proxy_set_header X-Forwarded-Host $host;\n        proxy_set_header X-Forwarded-Port $server_port;\n        \n        # WebSocket support\n        proxy_http_version 1.1;\n        proxy_set_header Upgrade $http_upgrade;\n        proxy_set_header Connection \"upgrade\";\n        \n        # Timeouts\n        proxy_connect_timeout 60s;\n        proxy_send_timeout 60s;\n        proxy_read_timeout 60s;\n        proxy_buffering off;\n        \n        # Buffer settings\n        proxy_buffer_size 4k;\n        proxy_buffers 8 4k;\n        proxy_busy_buffers_size 8k;\n        \n        # Client settings\n        client_max_body_size 50M;\n        client_body_timeout 60s;\n        client_header_timeout 60s;\n    }}\n    \n    # Static files optimization\n    location /static/ {{\n        proxy_pass http://127.0.0.1:{port};\n        proxy_cache_valid 200 1d;\n        expires 1d;\n        add_header Cache-Control \"public, immutable\";\n    }}\n    \n    # API endpoints with specific settings\n    location /api/ {{\n        proxy_pass http://127.0.0.1:{port};\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n        \n        # API specific timeouts\n        proxy_connect_timeout 30s;\n        proxy_send_timeout 30s;\n        proxy_read_timeout 30s;\n    }}\n    \n    # Health check endpoint\n    location /health {{\n        proxy_pass http://127.0.0.1:{port};\n        access_log off;\n    }}\n    \n    # Security - block common attack patterns\n    location ~ /\\\\. {{\n        deny all;\n        access_log off;\n        log_not_found off;\n    }}\n    \n    location ~ /(config|logs|tmp)/ {{\n        deny all;\n        access_log off;\n        log_not_found off;\n    }}\n}}\n\n# HTTPS redirect will be added by Certbot\n\"\"\"\n        \n        try:\n            with open(config_file, 'w') as f:\n                f.write(config_content)\n            print(f\"✅ Created Nginx config: {config_file}\")\n            return True\n        except Exception as e:\n            print(f\"❌ Error creating Nginx config: {e}\")\n            return False\n    \n    def enable_site(self, subdomain):\n        \"\"\"Enable Nginx site\"\"\"\n        domain = f\"{subdomain}.{self.base_domain}\"\n        available_file = f\"{self.nginx_sites_available}/{domain}.conf\"\n        enabled_file = f\"{self.nginx_sites_enabled}/{domain}.conf\"\n        \n        try:\n            # Create symlink\n            if os.path.exists(enabled_file):\n                os.remove(enabled_file)\n            os.symlink(available_file, enabled_file)\n            print(f\"✅ Enabled site: {domain}\")\n            return True\n        except Exception as e:\n            print(f\"❌ Error enabling site: {e}\")\n            return False\n    \n    def test_nginx_config(self):\n        \"\"\"Test Nginx configuration\"\"\"\n        try:\n            result = subprocess.run(['nginx', '-t'], capture_output=True, text=True)\n            if result.returncode == 0:\n                print(\"✅ Nginx configuration test passed\")\n                return True\n            else:\n                print(f\"❌ Nginx configuration test failed: {result.stderr}\")\n                return False\n        except Exception as e:\n            print(f\"❌ Error testing Nginx config: {e}\")\n            return False\n    \n    def reload_nginx(self):\n        \"\"\"Reload Nginx configuration\"\"\"\n        try:\n            subprocess.run(['systemctl', 'reload', 'nginx'], check=True)\n            print(\"✅ Nginx reloaded successfully\")\n            return True\n        except Exception as e:\n            print(f\"❌ Error reloading Nginx: {e}\")\n            return False\n    \n    def install_ssl(self, subdomain):\n        \"\"\"Install SSL certificate using Certbot\"\"\"\n        domain = f\"{subdomain}.{self.base_domain}\"\n        \n        try:\n            print(f\"🔒 Installing SSL certificate for {domain}...\")\n            \n            # Run certbot\n            cmd = [\n                'certbot', '--nginx',\n                '-d', domain,\n                '--non-interactive',\n                '--agree-tos',\n                '--email', self.email,\n                '--redirect'\n            ]\n            \n            result = subprocess.run(cmd, capture_output=True, text=True)\n            \n            if result.returncode == 0:\n                print(f\"✅ SSL certificate installed for {domain}\")\n                return True\n            else:\n                print(f\"❌ SSL installation failed: {result.stderr}\")\n                return False\n                \n        except Exception as e:\n            print(f\"❌ Error installing SSL: {e}\")\n            return False\n    \n    def check_domain_dns(self, subdomain):\n        \"\"\"Check if domain DNS is properly configured\"\"\"\n        domain = f\"{subdomain}.{self.base_domain}\"\n        \n        try:\n            import socket\n            ip = socket.gethostbyname(domain)\n            print(f\"🌐 DNS check for {domain}: {ip}\")\n            return True\n        except Exception as e:\n            print(f\"⚠️  DNS check failed for {domain}: {e}\")\n            return False\n    \n    def setup_subdomain(self, subdomain, port, skip_ssl=False):\n        \"\"\"Complete setup for a subdomain\"\"\"\n        domain = f\"{subdomain}.{self.base_domain}\"\n        \n        print(f\"🚀 Setting up {domain} -> port {port}\")\n        print(\"=\" * 50)\n        \n        # Step 1: Check if port is available\n        try:\n            import socket\n            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)\n            result = sock.connect_ex(('127.0.0.1', port))\n            sock.close()\n            \n            if result != 0:\n                print(f\"⚠️  Warning: Port {port} is not responding\")\n                response = input(f\"Continue anyway? (y/N): \")\n                if response.lower() != 'y':\n                    return False\n        except Exception as e:\n            print(f\"⚠️  Could not check port {port}: {e}\")\n        \n        # Step 2: Create Nginx config\n        if not self.create_nginx_config(subdomain, port):\n            return False\n        \n        # Step 3: Enable site\n        if not self.enable_site(subdomain):\n            return False\n        \n        # Step 4: Test Nginx config\n        if not self.test_nginx_config():\n            return False\n        \n        # Step 5: Reload Nginx\n        if not self.reload_nginx():\n            return False\n        \n        # Step 6: Check DNS (optional)\n        self.check_domain_dns(subdomain)\n        \n        # Step 7: Install SSL\n        if not skip_ssl:\n            print(\"\\n🔒 Installing SSL certificate...\")\n            if self.install_ssl(subdomain):\n                print(f\"🎉 Complete setup finished for https://{domain}\")\n            else:\n                print(f\"⚠️  Setup completed but SSL failed. You can retry SSL later with:\")\n                print(f\"   sudo certbot --nginx -d {domain}\")\n        else:\n            print(f\"✅ Basic setup completed for http://{domain}\")\n            print(f\"   To add SSL later: sudo certbot --nginx -d {domain}\")\n        \n        return True\n    \n    def list_configured_sites(self):\n        \"\"\"List all configured AlgoFactory sites\"\"\"\n        print(\"📋 Configured AlgoFactory Sites:\")\n        print(\"=\" * 40)\n        \n        try:\n            sites = []\n            for file in os.listdir(self.nginx_sites_available):\n                if file.endswith('.algofactory.in.conf'):\n                    subdomain = file.replace('.algofactory.in.conf', '')\n                    enabled = os.path.exists(f\"{self.nginx_sites_enabled}/{file}\")\n                    \n                    # Check SSL\n                    ssl_status = \"❌\"\n                    cert_path = f\"/etc/letsencrypt/live/{subdomain}.algofactory.in\"\n                    if os.path.exists(cert_path):\n                        ssl_status = \"✅\"\n                    \n                    # Check if enabled\n                    status = \"✅ Enabled\" if enabled else \"❌ Disabled\"\n                    \n                    sites.append({\n                        'subdomain': subdomain,\n                        'domain': f\"{subdomain}.algofactory.in\",\n                        'status': status,\n                        'ssl': ssl_status\n                    })\n            \n            if sites:\n                for site in sorted(sites, key=lambda x: x['subdomain']):\n                    print(f\"🔹 {site['domain']}\")\n                    print(f\"   Status: {site['status']}\")\n                    print(f\"   SSL: {site['ssl']}\")\n                    print()\n            else:\n                print(\"   No AlgoFactory sites configured\")\n                \n        except Exception as e:\n            print(f\"❌ Error listing sites: {e}\")\n    \n    def bulk_setup(self, start_subdomain, end_subdomain):\n        \"\"\"Setup multiple subdomains in bulk\"\"\"\n        start_num = int(start_subdomain)\n        end_num = int(end_subdomain)\n        \n        print(f\"🚀 Bulk setup: {start_subdomain}.algofactory.in to {end_subdomain}.algofactory.in\")\n        print(\"=\" * 60)\n        \n        success_count = 0\n        failed_domains = []\n        \n        for num in range(start_num, end_num + 1):\n            subdomain = str(num)\n            port = num  # Use subdomain number as port\n            \n            print(f\"\\n📍 Setting up {subdomain}.algofactory.in...\")\n            \n            if self.setup_subdomain(subdomain, port, skip_ssl=True):\n                success_count += 1\n                print(f\"✅ {subdomain}.algofactory.in setup completed\")\n            else:\n                failed_domains.append(f\"{subdomain}.algofactory.in\")\n                print(f\"❌ {subdomain}.algofactory.in setup failed\")\n        \n        # Summary\n        print(f\"\\n📊 Bulk Setup Summary:\")\n        print(\"=\" * 30)\n        print(f\"✅ Successful: {success_count}\")\n        print(f\"❌ Failed: {len(failed_domains)}\")\n        \n        if failed_domains:\n            print(f\"\\nFailed domains:\")\n            for domain in failed_domains:\n                print(f\"   - {domain}\")\n        \n        print(f\"\\n🔒 To install SSL for all domains:\")\n        print(f\"   python3 nginx_manager.py --bulk-ssl {start_subdomain} {end_subdomain}\")\n    \n    def bulk_ssl_install(self, start_subdomain, end_subdomain):\n        \"\"\"Install SSL for multiple subdomains\"\"\"\n        start_num = int(start_subdomain)\n        end_num = int(end_subdomain)\n        \n        print(f\"🔒 Bulk SSL installation: {start_subdomain} to {end_subdomain}\")\n        print(\"=\" * 50)\n        \n        success_count = 0\n        failed_domains = []\n        \n        for num in range(start_num, end_num + 1):\n            subdomain = str(num)\n            domain = f\"{subdomain}.algofactory.in\"\n            \n            print(f\"\\n🔒 Installing SSL for {domain}...\")\n            \n            if self.install_ssl(subdomain):\n                success_count += 1\n            else:\n                failed_domains.append(domain)\n            \n            # Small delay between SSL installations\n            time.sleep(2)\n        \n        # Summary\n        print(f\"\\n📊 SSL Installation Summary:\")\n        print(\"=\" * 35)\n        print(f\"✅ Successful: {success_count}\")\n        print(f\"❌ Failed: {len(failed_domains)}\")\n        \n        if failed_domains:\n            print(f\"\\nFailed SSL installations:\")\n            for domain in failed_domains:\n                print(f\"   - {domain}\")\n\ndef main():\n    parser = argparse.ArgumentParser(description=\"AlgoFactory Nginx Configuration Manager\")\n    parser.add_argument('--setup', nargs=2, metavar=('SUBDOMAIN', 'PORT'), \n                       help='Setup single subdomain (e.g., --setup 8013 8013)')\n    parser.add_argument('--bulk', nargs=2, metavar=('START', 'END'),\n                       help='Bulk setup subdomains (e.g., --bulk 1010 1020)')\n    parser.add_argument('--bulk-ssl', nargs=2, metavar=('START', 'END'),\n                       help='Bulk SSL installation (e.g., --bulk-ssl 1010 1020)')\n    parser.add_argument('--list', action='store_true', help='List configured sites')\n    parser.add_argument('--ssl', metavar='SUBDOMAIN', help='Install SSL for subdomain')\n    \n    args = parser.parse_args()\n    \n    # Check if running as root\n    if os.geteuid() != 0:\n        print(\"❌ This script must be run as root (use sudo)\")\n        sys.exit(1)\n    \n    manager = NginxManager()\n    \n    if args.setup:\n        subdomain, port = args.setup\n        manager.setup_subdomain(subdomain, int(port))\n    elif args.bulk:\n        start, end = args.bulk\n        manager.bulk_setup(start, end)\n    elif args.bulk_ssl:\n        start, end = args.bulk_ssl\n        manager.bulk_ssl_install(start, end)\n    elif args.list:\n        manager.list_configured_sites()\n    elif args.ssl:\n        manager.install_ssl(args.ssl)\n    else:\n        print(\"AlgoFactory Nginx Configuration Manager\")\n        print(\"=\" * 40)\n        print()\n        print(\"Usage examples:\")\n        print(\"  sudo python3 nginx_manager.py --setup 8013 8013\")\n        print(\"  sudo python3 nginx_manager.py --bulk 1010 1020\")\n        print(\"  sudo python3 nginx_manager.py --bulk-ssl 1010 1020\")\n        print(\"  sudo python3 nginx_manager.py --list\")\n        print(\"  sudo python3 nginx_manager.py --ssl 8013\")\n\nif __name__ == \"__main__\":\n    main()\n"}