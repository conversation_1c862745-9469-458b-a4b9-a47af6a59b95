{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/change_port.sh"}, "originalCode": "#!/bin/bash\n\n# Simple Port Changer for AlgoFactory\n# This script only changes ports, keeping all original templates and functionality intact\n\nset -e\n\n# Function to display usage\nusage() {\n    echo \"Usage: $0 <new_port>\"\n    echo \"Example: $0 8011\"\n    echo \"\"\n    echo \"This will change the Flask port to the specified port\"\n    echo \"WebSocket port will be: new_port + 12000\"\n    echo \"ZMQ port will be: new_port + 15000\"\n    exit 1\n}\n\n# Check if port argument is provided\nif [ $# -ne 1 ]; then\n    usage\nfi\n\nNEW_PORT=$1\n\n# Validate port number\nif ! [[ \"$NEW_PORT\" =~ ^[0-9]+$ ]] || [ \"$NEW_PORT\" -lt 1024 ] || [ \"$NEW_PORT\" -gt 65535 ]; then\n    echo \"Error: Port must be a number between 1024 and 65535\"\n    exit 1\nfi\n\n# Calculate other ports\nWEBSOCKET_PORT=$((NEW_PORT + 12000))\nZMQ_PORT=$((NEW_PORT + 15000))\n\necho \"🔧 Changing AlgoFactory ports...\"\necho \"   Flask Port: $NEW_PORT\"\necho \"   WebSocket Port: $WEBSOCKET_PORT\"\necho \"   ZMQ Port: $ZMQ_PORT\"\n\n# Check if .env file exists\nif [ ! -f \".env\" ]; then\n    echo \"Error: .env file not found in current directory\"\n    echo \"Please run this script from the algofactory directory\"\n    exit 1\nfi\n\n# Backup original .env file\ncp .env .env.backup.$(date +%Y%m%d_%H%M%S)\necho \"✅ Backed up .env file\"\n\n# Update .env file\necho \"🔧 Updating .env file...\"\n\n# Update Flask port\nsed -i \"s/^FLASK_PORT=.*/FLASK_PORT=\\\"$NEW_PORT\\\"/\" .env\n\n# Update WebSocket port\nsed -i \"s/^WEBSOCKET_PORT=.*/WEBSOCKET_PORT=\\\"$WEBSOCKET_PORT\\\"/\" .env\n\n# Update ZMQ port\nsed -i \"s/^ZMQ_PORT=.*/ZMQ_PORT=\\\"$ZMQ_PORT\\\"/\" .env\n\n# Update database URL\nsed -i \"s/^DATABASE_URL=.*/DATABASE_URL=\\\"sqlite:\\/\\/\\/db\\/algofactory-$NEW_PORT.db\\\"/\" .env\n\n# Update host server URL\nsed -i \"s/^HOST_SERVER=.*/HOST_SERVER=\\\"https:\\/\\/$NEW_PORT.algofactory.in\\\"/\" .env\n\n# Update redirect URL\nsed -i \"s/^REDIRECT_URL=.*/REDIRECT_URL=\\\"https:\\/\\/$NEW_PORT.algofactory.in\\/angel\\/callback\\\"/\" .env\n\n# Update CORS origins\nsed -i \"s/^CORS_ALLOWED_ORIGINS=.*/CORS_ALLOWED_ORIGINS=\\\"https:\\/\\/$NEW_PORT.algofactory.in\\\"/\" .env\n\necho \"✅ Updated .env file with new ports\"\n\n# Stop any running instance\necho \"🛑 Stopping any running AlgoFactory instance...\"\npkill -f \"python.*app.py\" || true\npkill -f \"gunicorn.*app:app\" || true\n\necho \"\"\necho \"🎉 Port change completed successfully!\"\necho \"\"\necho \"📋 Summary:\"\necho \"   Flask Port: $NEW_PORT\"\necho \"   WebSocket Port: $WEBSOCKET_PORT\"\necho \"   ZMQ Port: $ZMQ_PORT\"\necho \"   Database: algofactory-$NEW_PORT.db\"\necho \"   Domain: $NEW_PORT.algofactory.in\"\necho \"\"\necho \"🚀 To start the application:\"\necho \"   python3 app.py\"\necho \"\"\necho \"🌐 To set up Nginx and SSL:\"\necho \"   1. Add DNS record: $NEW_PORT.algofactory.in → your_server_ip\"\necho \"   2. Create Nginx config for $NEW_PORT.algofactory.in\"\necho \"   3. Get SSL certificate with certbot\"\necho \"\"\necho \"📁 Original .env backed up as: .env.backup.$(date +%Y%m%d_%H%M%S)\"\n"}