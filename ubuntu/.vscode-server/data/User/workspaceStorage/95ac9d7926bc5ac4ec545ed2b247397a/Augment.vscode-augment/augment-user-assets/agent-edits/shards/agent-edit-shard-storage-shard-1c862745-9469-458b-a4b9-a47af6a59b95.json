{"id": "shard-1c862745-9469-458b-a4b9-a47af6a59b95", "checkpoints": {"1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/start.sh": [{"sourceToolCallRequestId": "9cbd8c41-b244-4503-85ff-50e8e30fa4a2", "timestamp": 0, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/start.sh"}}}, {"sourceToolCallRequestId": "5f597a77-328a-4ba7-96a8-ee3ab83859f9", "timestamp": 1750620830153, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/start.sh"}}}, {"sourceToolCallRequestId": "43f95f61-9573-4837-a073-26cb3aa49661", "timestamp": 1750620830217, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/start.sh"}}}, {"sourceToolCallRequestId": "755cf555-adfd-42b2-a5c9-5070100e1c9c", "timestamp": 1750620830217, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "lastIncludedInRequestId": "ec5e9754-dab5-41bf-8aef-f3e919f5a45b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/start.sh"}}}, {"sourceToolCallRequestId": "d8b3091f-f39a-4b55-b364-883e5568fe1a", "timestamp": 1750620962985, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "lastIncludedInRequestId": "89d4c718-ce8f-4bb8-b777-8be6619441bd", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/start.sh"}}}, {"sourceToolCallRequestId": "6e304281-9056-4c69-ab29-291cb60f274b", "timestamp": 1750621616391, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/start.sh"}}}, {"sourceToolCallRequestId": "f70cd2c1-9720-4e34-8698-67227bf1959e", "timestamp": 1750621616441, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/start.sh"}}}, {"sourceToolCallRequestId": "4dd3cea1-67e0-4dce-a128-13e199a7e175", "timestamp": 1750621616441, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "lastIncludedInRequestId": "bf5be829-fc9d-435d-a586-b197aefc65dc", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/start.sh"}}}], "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/algofactory.service": [{"sourceToolCallRequestId": "6aa15fe5-4a3d-4c2a-87f6-f5e486f3d176", "timestamp": 1750621608482, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/algofactory.service"}}}, {"sourceToolCallRequestId": "452fe139-8e3f-4cd9-ab83-c81299df6532", "timestamp": 1750621807792, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/algofactory.service"}}}, {"sourceToolCallRequestId": "39975aca-7f6c-48a7-9ce2-a72b8032ed95", "timestamp": 1750621807889, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/algofactory.service"}}}, {"sourceToolCallRequestId": "a1635cd6-86e4-4996-b54d-395061ea0523", "timestamp": 1750621807889, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/algofactory.service"}}}, {"sourceToolCallRequestId": "6046b7a9-29b1-4494-9d80-65fd4cbfe19a", "timestamp": 1750621847795, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/algofactory.service"}}}, {"sourceToolCallRequestId": "63d6e499-8fa6-4c71-8e90-3872c9a521e8", "timestamp": 1750621847815, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/algofactory.service"}}}, {"sourceToolCallRequestId": "6493688a-3083-4e65-8362-6c78acd73635", "timestamp": 1750621847815, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "lastIncludedInRequestId": "bf5be829-fc9d-435d-a586-b197aefc65dc", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/algofactory.service"}}}], "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/nginx-algofactory.conf": [{"sourceToolCallRequestId": "1e9f8105-ebfc-4e0e-9436-d0f535fae589", "timestamp": 1750621703159, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/nginx-algofactory.conf"}}}], "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/monitoring.py": [{"sourceToolCallRequestId": "ea3f87de-7572-4267-bdb1-a19cab0ba47b", "timestamp": 1750621942421, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/monitoring.py"}}}], "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/setup_24x7.sh": [{"sourceToolCallRequestId": "c42cd372-fa42-4e3e-b47f-687f728905a3", "timestamp": 1750622991211, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/setup_24x7.sh"}}}], "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/multi-instance-roadmap.md": [{"sourceToolCallRequestId": "06c58153-4235-4ad0-b95e-d19c34cb8c01", "timestamp": 1750626552777, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/multi-instance-roadmap.md"}}}], "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/instance_manager.py": [{"sourceToolCallRequestId": "fe36752a-fec5-46a3-8b66-1a0424d6db04", "timestamp": 1750626610366, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/instance_manager.py"}}}, {"sourceToolCallRequestId": "0cf9e4a5-0f48-4f2e-8137-1e6485037659", "timestamp": 1750626791807, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/instance_manager.py"}}}, {"sourceToolCallRequestId": "9b6e01bf-b122-4aec-bad9-ff264cc47cef", "timestamp": 1750626791878, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/instance_manager.py"}}}, {"sourceToolCallRequestId": "74204394-9303-4d8b-aa8d-5a80ed3d20a6", "timestamp": 1750626791879, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/instance_manager.py"}}}, {"sourceToolCallRequestId": "7fb0ebdd-9117-46af-b3f2-b2dadcefb8fa", "timestamp": 1750626804008, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/instance_manager.py"}}}, {"sourceToolCallRequestId": "ae1f7747-ef51-472f-a730-970f6bd8085b", "timestamp": 1750626804037, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/instance_manager.py"}}}, {"sourceToolCallRequestId": "83037b44-521a-45b6-a5bc-f40f83e525fe", "timestamp": 1750626804037, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "lastIncludedInRequestId": "de7457f0-82ef-4a9e-b420-b022cfe7e49f", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/instance_manager.py"}}}], "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/optimize_memory.sh": [{"sourceToolCallRequestId": "de7457f0-82ef-4a9e-b420-b022cfe7e49f", "timestamp": 1750627486325, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/optimize_memory.sh"}}}], "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/quick_instance.sh": [{"sourceToolCallRequestId": "a0df8be4-5bb5-45db-a683-1dcfa68c2f3e", "timestamp": 1750627571107, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "c2fcc578-2802-4345-8258-31573c60e6df", "timestamp": 1750627716252, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "99495545-0100-459e-bd6e-acd7eb9b6b22", "timestamp": 1750627716286, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "a10f8f4c-9735-457c-bea2-fb4840b74f82", "timestamp": 1750627716286, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "2557ce08-646e-471e-bd23-0295c38b82bb", "timestamp": 1750627756843, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "e65e5d44-a1b5-45f2-9172-fd52f79a3bd6", "timestamp": 1750627756920, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "b73a0912-4222-4b0c-9aa3-0f520c5876c1", "timestamp": 1750627756920, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "6327771a-6639-4a7a-98dd-5a2e6bef0cfb", "timestamp": 1750627767216, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "557b12b3-9579-44e0-aff3-1c7d9f0a3439", "timestamp": 1750627767233, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "86ad5ae6-17b4-4323-8ef9-de9931e7a24d", "timestamp": 1750627767233, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "143661f5-e921-49cb-b453-6a6423533b43", "timestamp": 1750627831268, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "f2bfdeef-3b9a-4a3c-b776-3da445ebd9d3", "timestamp": 1750627831287, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "3c47a06e-77b5-431c-9595-5544a3998165", "timestamp": 1750627831288, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "lastIncludedInRequestId": "9c0bbf48-67ae-48af-bdfc-6b769c667f89", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "b9f64887-ea27-4ec1-a752-6317ed0219d7", "timestamp": 1750628170000, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "690c8044-7e75-47cf-b7cd-ca3e9ab0144d", "timestamp": 1750628170055, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "e43bd0aa-266a-4268-9f02-c94bba0ba746", "timestamp": 1750628170055, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "7f5687f7-b9fb-42c8-80c6-b4c84beb87ff", "timestamp": 1750628219072, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "c08b5458-5bd5-4be2-87d7-7fb0829a7ccc", "timestamp": 1750628219101, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "6c5a3ce2-dc72-457f-865e-90a3e23f97ce", "timestamp": 1750628219101, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "lastIncludedInRequestId": "2dc06a65-62be-4899-bcba-a7bc4719935d", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "e30374d5-6333-4656-a3cb-b1bad699bce9", "timestamp": 1750628464876, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "3f2ba2ea-109a-4bec-b0c8-9ef1929ea4f2", "timestamp": 1750628464938, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "fb228910-8c7f-4179-92a6-1ad17d3c3eeb", "timestamp": 1750628464938, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "5b43e4f2-c84f-4552-84f9-375ce8933175", "timestamp": 1750628657151, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "7e1e7707-d868-4167-8043-277408c76781", "timestamp": 1750628657249, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "38132063-2b76-4a47-a779-61d48c7aeb21", "timestamp": 1750628657249, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "lastIncludedInRequestId": "9c1bfd2a-ae5a-447c-a7f2-c70c97983871", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}], "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/MULTI_INSTANCE_COMPLETE_GUIDE.md": [{"sourceToolCallRequestId": "0305ed16-3833-49e8-8ca2-e00adabb5aaa", "timestamp": 1750627929422, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/MULTI_INSTANCE_COMPLETE_GUIDE.md"}}}], "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/algofactory-multi/instances/algofactory-8010/templates/login.html": [{"sourceToolCallRequestId": "631d515a-3b45-402d-b936-19c1209a8103", "timestamp": 0, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/login.html"}}}, {"sourceToolCallRequestId": "eabe7afb-2fb8-4195-bf67-dab9af4b415c", "timestamp": 1750629208177, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/login.html"}}}, {"sourceToolCallRequestId": "c5ca67b4-1c1d-4fe0-ab04-28b0aa9ea597", "timestamp": 1750629208456, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/login.html"}}}, {"sourceToolCallRequestId": "12207380-7024-488b-ae12-dbf42e5d24a8", "timestamp": 1750629208456, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/login.html"}}}, {"sourceToolCallRequestId": "a66c07f9-4e8e-48d1-b4bd-d926932fd078", "timestamp": 1750629223058, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/login.html"}}}, {"sourceToolCallRequestId": "071b1123-4c24-4578-a0fc-7b296a2a02ba", "timestamp": 1750629223137, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/login.html"}}}, {"sourceToolCallRequestId": "8c3a306e-10bb-4269-98ec-8c97d9981171", "timestamp": 1750629223137, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/login.html"}}}]}, "metadata": {"checkpointDocumentIds": ["1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/start.sh", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/algofactory.service", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/nginx-algofactory.conf", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/monitoring.py", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/setup_24x7.sh", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/multi-instance-roadmap.md", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/instance_manager.py", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/optimize_memory.sh", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/quick_instance.sh", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/MULTI_INSTANCE_COMPLETE_GUIDE.md", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/algofactory-multi/instances/algofactory-8010/templates/login.html"], "size": 1013535, "checkpointCount": 60, "lastModified": 1750629223440}}