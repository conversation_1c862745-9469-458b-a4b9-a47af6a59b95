{"id": "shard-1c862745-9469-458b-a4b9-a47af6a59b95", "checkpoints": {"1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/start.sh": [{"sourceToolCallRequestId": "9cbd8c41-b244-4503-85ff-50e8e30fa4a2", "timestamp": 0, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/start.sh"}}}, {"sourceToolCallRequestId": "5f597a77-328a-4ba7-96a8-ee3ab83859f9", "timestamp": 1750620830153, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/start.sh"}}}, {"sourceToolCallRequestId": "43f95f61-9573-4837-a073-26cb3aa49661", "timestamp": 1750620830217, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/start.sh"}}}, {"sourceToolCallRequestId": "755cf555-adfd-42b2-a5c9-5070100e1c9c", "timestamp": 1750620830217, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "lastIncludedInRequestId": "ec5e9754-dab5-41bf-8aef-f3e919f5a45b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/start.sh"}}}, {"sourceToolCallRequestId": "d8b3091f-f39a-4b55-b364-883e5568fe1a", "timestamp": 1750620962985, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "lastIncludedInRequestId": "89d4c718-ce8f-4bb8-b777-8be6619441bd", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/start.sh"}}}, {"sourceToolCallRequestId": "6e304281-9056-4c69-ab29-291cb60f274b", "timestamp": 1750621616391, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/start.sh"}}}, {"sourceToolCallRequestId": "f70cd2c1-9720-4e34-8698-67227bf1959e", "timestamp": 1750621616441, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/start.sh"}}}, {"sourceToolCallRequestId": "4dd3cea1-67e0-4dce-a128-13e199a7e175", "timestamp": 1750621616441, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "lastIncludedInRequestId": "bf5be829-fc9d-435d-a586-b197aefc65dc", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/start.sh"}}}, {"sourceToolCallRequestId": "42488140-1495-4307-8e6c-2874c36d8e7f", "timestamp": 1750629760980, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/start.sh"}}}, {"sourceToolCallRequestId": "c86b3b16-4a43-4390-93f2-0fa6b15b5576", "timestamp": 1750629776563, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/start.sh"}}}, {"sourceToolCallRequestId": "8e0319f9-6ab2-4027-b7bd-8c0f9c1d3bc0", "timestamp": 1750629805754, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/start.sh"}}}, {"sourceToolCallRequestId": "ad872fd4-912d-416b-b39b-c54b8e13621c", "timestamp": 1750629810703, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/start.sh"}}}, {"sourceToolCallRequestId": "1c93a322-93a8-4611-a956-928d4742c9ed", "timestamp": 1750630158517, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/start.sh"}}}], "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/algofactory.service": [{"sourceToolCallRequestId": "6aa15fe5-4a3d-4c2a-87f6-f5e486f3d176", "timestamp": 1750621608482, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/algofactory.service"}}}, {"sourceToolCallRequestId": "452fe139-8e3f-4cd9-ab83-c81299df6532", "timestamp": 1750621807792, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/algofactory.service"}}}, {"sourceToolCallRequestId": "39975aca-7f6c-48a7-9ce2-a72b8032ed95", "timestamp": 1750621807889, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/algofactory.service"}}}, {"sourceToolCallRequestId": "a1635cd6-86e4-4996-b54d-395061ea0523", "timestamp": 1750621807889, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/algofactory.service"}}}, {"sourceToolCallRequestId": "6046b7a9-29b1-4494-9d80-65fd4cbfe19a", "timestamp": 1750621847795, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/algofactory.service"}}}, {"sourceToolCallRequestId": "63d6e499-8fa6-4c71-8e90-3872c9a521e8", "timestamp": 1750621847815, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/algofactory.service"}}}, {"sourceToolCallRequestId": "6493688a-3083-4e65-8362-6c78acd73635", "timestamp": 1750621847815, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "lastIncludedInRequestId": "bf5be829-fc9d-435d-a586-b197aefc65dc", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/algofactory.service"}}}, {"sourceToolCallRequestId": "fc1c154f-f531-4100-9f3a-e1358d064c0f", "timestamp": 1750629760980, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/algofactory.service"}}}, {"sourceToolCallRequestId": "a1aa4e93-a16d-4650-aebb-d8c5bebab824", "timestamp": 1750629776563, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/algofactory.service"}}}, {"sourceToolCallRequestId": "50abbc9e-d707-4a74-8f44-8963bb99cc96", "timestamp": 1750629805754, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/algofactory.service"}}}, {"sourceToolCallRequestId": "416e78f0-0755-40d5-89de-c25048c9ced5", "timestamp": 1750629810703, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/algofactory.service"}}}, {"sourceToolCallRequestId": "ab25d809-0c67-44d8-a8c1-f7e04afa5785", "timestamp": 1750630158517, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/algofactory.service"}}}], "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/nginx-algofactory.conf": [{"sourceToolCallRequestId": "1e9f8105-ebfc-4e0e-9436-d0f535fae589", "timestamp": 1750621703159, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/nginx-algofactory.conf"}}}, {"sourceToolCallRequestId": "1fde434e-2a19-4641-99f3-154166fa4ade", "timestamp": 1750629760980, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/nginx-algofactory.conf"}}}, {"sourceToolCallRequestId": "f65f1313-a5be-42a1-9a93-51a90e93a84d", "timestamp": 1750629776563, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/nginx-algofactory.conf"}}}, {"sourceToolCallRequestId": "ff679388-f759-4a0e-9844-c3478e0b1f74", "timestamp": 1750629805754, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/nginx-algofactory.conf"}}}, {"sourceToolCallRequestId": "9fc62616-941f-48a7-9f4b-6616b672b3c9", "timestamp": 1750629810703, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/nginx-algofactory.conf"}}}, {"sourceToolCallRequestId": "aa3d43bd-0597-4b88-a2f3-e9ff016de7ca", "timestamp": 1750630158517, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/nginx-algofactory.conf"}}}], "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/monitoring.py": [{"sourceToolCallRequestId": "ea3f87de-7572-4267-bdb1-a19cab0ba47b", "timestamp": 1750621942421, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/monitoring.py"}}}, {"sourceToolCallRequestId": "854c12f8-88a2-4c98-9cc5-77c51f24f576", "timestamp": 1750629760980, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/monitoring.py"}}}, {"sourceToolCallRequestId": "06b10b89-e352-4df2-9040-1b3aac6c42c8", "timestamp": 1750629776563, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/monitoring.py"}}}, {"sourceToolCallRequestId": "5e3128c3-97c6-4589-9ccf-5a4b46ca291b", "timestamp": 1750629805754, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/monitoring.py"}}}, {"sourceToolCallRequestId": "6e206c14-b16e-4a08-ad17-81b725a32b29", "timestamp": 1750629810703, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/monitoring.py"}}}, {"sourceToolCallRequestId": "7451e371-927e-4ba4-94a5-c177f997ee21", "timestamp": 1750630158517, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/monitoring.py"}}}], "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/setup_24x7.sh": [{"sourceToolCallRequestId": "c42cd372-fa42-4e3e-b47f-687f728905a3", "timestamp": 1750622991211, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/setup_24x7.sh"}}}, {"sourceToolCallRequestId": "9b8015b2-187c-40b8-be6e-1213070258c2", "timestamp": 1750629760980, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/setup_24x7.sh"}}}, {"sourceToolCallRequestId": "b6e797f2-baa6-4d56-9b60-81884e0768c5", "timestamp": 1750629776563, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/setup_24x7.sh"}}}, {"sourceToolCallRequestId": "9ed3db87-2b6f-4ed7-b295-ce7d76ad4ca6", "timestamp": 1750629805754, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/setup_24x7.sh"}}}, {"sourceToolCallRequestId": "53269616-0178-4a17-9f93-77b32eaf88c9", "timestamp": 1750629810703, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/setup_24x7.sh"}}}, {"sourceToolCallRequestId": "f21b559e-21b2-4eaa-b313-496dd6f21a14", "timestamp": 1750630158517, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/setup_24x7.sh"}}}], "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/multi-instance-roadmap.md": [{"sourceToolCallRequestId": "06c58153-4235-4ad0-b95e-d19c34cb8c01", "timestamp": 1750626552777, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/multi-instance-roadmap.md"}}}, {"sourceToolCallRequestId": "368a2197-b7b6-43a2-864b-05cf3529fb18", "timestamp": 1750629760980, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/multi-instance-roadmap.md"}}}, {"sourceToolCallRequestId": "392cc6e5-3ecd-431f-9e8a-782798d5be06", "timestamp": 1750629776563, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/multi-instance-roadmap.md"}}}, {"sourceToolCallRequestId": "b3419033-cd47-4a9e-affc-89a8eed798ec", "timestamp": 1750629805754, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/multi-instance-roadmap.md"}}}, {"sourceToolCallRequestId": "ea1f2616-b13c-438f-a09e-90e34b380b80", "timestamp": 1750629810703, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/multi-instance-roadmap.md"}}}, {"sourceToolCallRequestId": "6f117c7b-569c-4256-89b1-fcb2dc3cbdb6", "timestamp": 1750630158517, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/multi-instance-roadmap.md"}}}], "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/instance_manager.py": [{"sourceToolCallRequestId": "fe36752a-fec5-46a3-8b66-1a0424d6db04", "timestamp": 1750626610366, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/instance_manager.py"}}}, {"sourceToolCallRequestId": "0cf9e4a5-0f48-4f2e-8137-1e6485037659", "timestamp": 1750626791807, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/instance_manager.py"}}}, {"sourceToolCallRequestId": "9b6e01bf-b122-4aec-bad9-ff264cc47cef", "timestamp": 1750626791878, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/instance_manager.py"}}}, {"sourceToolCallRequestId": "74204394-9303-4d8b-aa8d-5a80ed3d20a6", "timestamp": 1750626791879, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/instance_manager.py"}}}, {"sourceToolCallRequestId": "7fb0ebdd-9117-46af-b3f2-b2dadcefb8fa", "timestamp": 1750626804008, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/instance_manager.py"}}}, {"sourceToolCallRequestId": "ae1f7747-ef51-472f-a730-970f6bd8085b", "timestamp": 1750626804037, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/instance_manager.py"}}}, {"sourceToolCallRequestId": "83037b44-521a-45b6-a5bc-f40f83e525fe", "timestamp": 1750626804037, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "lastIncludedInRequestId": "de7457f0-82ef-4a9e-b420-b022cfe7e49f", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/instance_manager.py"}}}, {"sourceToolCallRequestId": "7877f781-b337-4f9f-acee-25390bfbc226", "timestamp": 1750629760980, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/instance_manager.py"}}}, {"sourceToolCallRequestId": "79da31f1-1a21-4714-8a1f-4398d1be76ba", "timestamp": 1750629776563, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/instance_manager.py"}}}, {"sourceToolCallRequestId": "21775fd6-3789-4975-b9bc-2b13c7ec7530", "timestamp": 1750629805754, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/instance_manager.py"}}}, {"sourceToolCallRequestId": "d5d95b3e-42d3-472e-b150-f6a9bf514a86", "timestamp": 1750629810703, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/instance_manager.py"}}}, {"sourceToolCallRequestId": "b51ac199-cf0b-44f2-a310-71fede4fd437", "timestamp": 1750630158517, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/instance_manager.py"}}}], "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/optimize_memory.sh": [{"sourceToolCallRequestId": "de7457f0-82ef-4a9e-b420-b022cfe7e49f", "timestamp": 1750627486325, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/optimize_memory.sh"}}}, {"sourceToolCallRequestId": "1206080b-4610-49dd-973d-baa019624466", "timestamp": 1750629760980, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/optimize_memory.sh"}}}, {"sourceToolCallRequestId": "6c57971e-1e63-454c-a04d-a40c6c6a0e36", "timestamp": 1750629776563, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/optimize_memory.sh"}}}, {"sourceToolCallRequestId": "e77a1f27-023f-4d66-a3b9-a85d77ec3cce", "timestamp": 1750629805754, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/optimize_memory.sh"}}}, {"sourceToolCallRequestId": "a5c0ddc0-7ef2-491a-ab67-985f31e95589", "timestamp": 1750629810703, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/optimize_memory.sh"}}}, {"sourceToolCallRequestId": "c1848ea4-61f7-4ec8-bdac-ff0b406cfeab", "timestamp": 1750630158517, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/optimize_memory.sh"}}}], "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/quick_instance.sh": [{"sourceToolCallRequestId": "a0df8be4-5bb5-45db-a683-1dcfa68c2f3e", "timestamp": 1750627571107, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "c2fcc578-2802-4345-8258-31573c60e6df", "timestamp": 1750627716252, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "99495545-0100-459e-bd6e-acd7eb9b6b22", "timestamp": 1750627716286, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "a10f8f4c-9735-457c-bea2-fb4840b74f82", "timestamp": 1750627716286, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "2557ce08-646e-471e-bd23-0295c38b82bb", "timestamp": 1750627756843, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "e65e5d44-a1b5-45f2-9172-fd52f79a3bd6", "timestamp": 1750627756920, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "b73a0912-4222-4b0c-9aa3-0f520c5876c1", "timestamp": 1750627756920, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "6327771a-6639-4a7a-98dd-5a2e6bef0cfb", "timestamp": 1750627767216, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "557b12b3-9579-44e0-aff3-1c7d9f0a3439", "timestamp": 1750627767233, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "86ad5ae6-17b4-4323-8ef9-de9931e7a24d", "timestamp": 1750627767233, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "143661f5-e921-49cb-b453-6a6423533b43", "timestamp": 1750627831268, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "f2bfdeef-3b9a-4a3c-b776-3da445ebd9d3", "timestamp": 1750627831287, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "3c47a06e-77b5-431c-9595-5544a3998165", "timestamp": 1750627831288, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "lastIncludedInRequestId": "9c0bbf48-67ae-48af-bdfc-6b769c667f89", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "b9f64887-ea27-4ec1-a752-6317ed0219d7", "timestamp": 1750628170000, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "690c8044-7e75-47cf-b7cd-ca3e9ab0144d", "timestamp": 1750628170055, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "e43bd0aa-266a-4268-9f02-c94bba0ba746", "timestamp": 1750628170055, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "7f5687f7-b9fb-42c8-80c6-b4c84beb87ff", "timestamp": 1750628219072, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "c08b5458-5bd5-4be2-87d7-7fb0829a7ccc", "timestamp": 1750628219101, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "6c5a3ce2-dc72-457f-865e-90a3e23f97ce", "timestamp": 1750628219101, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "lastIncludedInRequestId": "2dc06a65-62be-4899-bcba-a7bc4719935d", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "e30374d5-6333-4656-a3cb-b1bad699bce9", "timestamp": 1750628464876, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "3f2ba2ea-109a-4bec-b0c8-9ef1929ea4f2", "timestamp": 1750628464938, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "fb228910-8c7f-4179-92a6-1ad17d3c3eeb", "timestamp": 1750628464938, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "5b43e4f2-c84f-4552-84f9-375ce8933175", "timestamp": 1750628657151, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "7e1e7707-d868-4167-8043-277408c76781", "timestamp": 1750628657249, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "38132063-2b76-4a47-a779-61d48c7aeb21", "timestamp": 1750628657249, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "lastIncludedInRequestId": "9c1bfd2a-ae5a-447c-a7f2-c70c97983871", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "c04c370e-b662-4b1f-aeb3-687269cfb74c", "timestamp": 1750629388660, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "c53edc17-6808-47f6-a461-a1c87f1767f2", "timestamp": 1750629388715, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "dbac467c-1fab-4dae-b322-fa5abf821c83", "timestamp": 1750629388715, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "lastIncludedInRequestId": "2ecef2ce-fa8e-4e94-a612-f6c8f0996732", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "0ae7d150-0d79-48b0-9c9e-2507639dd3c9", "timestamp": 1750629760980, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "cc5980ff-8ca6-4d8a-b539-ba5298a48f73", "timestamp": 1750629761173, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "254aa18c-3ccd-4166-9671-13cde619fa1d", "timestamp": 1750629761173, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "6e51ddb0-6102-4518-bf41-1c11ed193801", "timestamp": 1750629776563, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "1ffb5119-3fbd-43e8-a53e-2410e889bb62", "timestamp": 1750629805754, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "a3744215-1571-467b-ae12-6495bfa15013", "timestamp": 1750629810703, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "b0d1644b-c95e-4fea-a8ee-22bec61ac982", "timestamp": 1750630100164, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "2da8c85e-74d9-426b-8b90-f52a7dda17b0", "timestamp": 1750630100265, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}, {"sourceToolCallRequestId": "c420a099-71ec-4897-a01b-a0e2ec0c65b8", "timestamp": 1750630158517, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/quick_instance.sh"}}}], "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/MULTI_INSTANCE_COMPLETE_GUIDE.md": [{"sourceToolCallRequestId": "0305ed16-3833-49e8-8ca2-e00adabb5aaa", "timestamp": 1750627929422, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/MULTI_INSTANCE_COMPLETE_GUIDE.md"}}}, {"sourceToolCallRequestId": "bf8424fb-68ea-4486-b608-5950da7edfd0", "timestamp": 1750629760980, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/MULTI_INSTANCE_COMPLETE_GUIDE.md"}}}, {"sourceToolCallRequestId": "b32c7c25-953c-4454-bc04-6f2359435918", "timestamp": 1750629776563, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/MULTI_INSTANCE_COMPLETE_GUIDE.md"}}}, {"sourceToolCallRequestId": "5639b077-002c-45a4-b6d8-c9dbda51b1fa", "timestamp": 1750629805754, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/MULTI_INSTANCE_COMPLETE_GUIDE.md"}}}, {"sourceToolCallRequestId": "6d82880b-466a-4ccd-bba3-96110fa3699d", "timestamp": 1750629810703, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/MULTI_INSTANCE_COMPLETE_GUIDE.md"}}}, {"sourceToolCallRequestId": "107e0c18-7ae1-40bb-b357-000e2623dfd5", "timestamp": 1750630158517, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/MULTI_INSTANCE_COMPLETE_GUIDE.md"}}}], "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/algofactory-multi/instances/algofactory-8010/templates/login.html": [{"sourceToolCallRequestId": "631d515a-3b45-402d-b936-19c1209a8103", "timestamp": 0, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/login.html"}}}, {"sourceToolCallRequestId": "eabe7afb-2fb8-4195-bf67-dab9af4b415c", "timestamp": 1750629208177, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/login.html"}}}, {"sourceToolCallRequestId": "c5ca67b4-1c1d-4fe0-ab04-28b0aa9ea597", "timestamp": 1750629208456, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/login.html"}}}, {"sourceToolCallRequestId": "12207380-7024-488b-ae12-dbf42e5d24a8", "timestamp": 1750629208456, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/login.html"}}}, {"sourceToolCallRequestId": "a66c07f9-4e8e-48d1-b4bd-d926932fd078", "timestamp": 1750629223058, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/login.html"}}}, {"sourceToolCallRequestId": "071b1123-4c24-4578-a0fc-7b296a2a02ba", "timestamp": 1750629223137, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/login.html"}}}, {"sourceToolCallRequestId": "8c3a306e-10bb-4269-98ec-8c97d9981171", "timestamp": 1750629223137, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "lastIncludedInRequestId": "1179c4f6-80de-4640-b946-6c2295cdf5d0", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/login.html"}}}, {"sourceToolCallRequestId": "035bcd8e-3c0e-4ae9-928b-7ef4769fa25b", "timestamp": 1750629629777, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/login.html"}}}, {"sourceToolCallRequestId": "0d3bb77e-1a5b-472e-aca1-adce7e2dcfeb", "timestamp": 1750629629934, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/login.html"}}}, {"sourceToolCallRequestId": "e4d50c76-1533-4b58-987d-ecec88b9aa12", "timestamp": 1750629629934, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/login.html"}}}, {"sourceToolCallRequestId": "8a43bb3e-57d1-4591-97e9-9f46c1bb3fdc", "timestamp": 1750629760980, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/login.html"}}}, {"sourceToolCallRequestId": "0a75bd57-8927-4007-8b27-6f36a6e7dcba", "timestamp": 1750629761014, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/login.html"}}}, {"sourceToolCallRequestId": "47fa80db-3ce1-49ae-bca9-2eaf9c3a398f", "timestamp": 1750629761014, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/login.html"}}}, {"sourceToolCallRequestId": "283b826f-f457-491b-8cc1-0f27b2b92f82", "timestamp": 1750629776563, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/login.html"}}}, {"sourceToolCallRequestId": "c819c175-022f-4f31-8d4f-20dfd1cb0e34", "timestamp": 1750629776598, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/login.html"}}}, {"sourceToolCallRequestId": "895d86ec-dc23-4855-b1d4-8684b5078b06", "timestamp": 1750629776598, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/login.html"}}}, {"sourceToolCallRequestId": "9285c9cf-41e2-4952-9801-5af2e7fff7d9", "timestamp": 1750629805754, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/login.html"}}}, {"sourceToolCallRequestId": "de5bb9c6-4df6-4dcb-b7c5-2bfd15b6d6dd", "timestamp": 1750629805821, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/login.html"}}}, {"sourceToolCallRequestId": "70397803-a51c-474d-89e5-9725871a51c3", "timestamp": 1750629805821, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/login.html"}}}, {"sourceToolCallRequestId": "54497faf-25ec-4117-89ed-8a95055801b8", "timestamp": 1750629810703, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/login.html"}}}, {"sourceToolCallRequestId": "d61d0534-cc65-4fc7-9104-736375be<PERSON><PERSON>c", "timestamp": 1750629810798, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/login.html"}}}, {"sourceToolCallRequestId": "a4b75a32-27e8-49d8-af69-ba851b0b1cfb", "timestamp": 1750629810798, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "lastIncludedInRequestId": "2cd07966-6643-4b6f-afec-7b684a809727", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/login.html"}}}, {"sourceToolCallRequestId": "00908a67-12da-4544-9196-b50477b53361", "timestamp": 1750630158517, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/login.html"}}}], "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/algofactory-multi/instances/algofactory-8010/templates/setup.html": [{"sourceToolCallRequestId": "6a001d27-af0f-41e7-828c-30ef00e2afa0", "timestamp": 0, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/setup.html"}}}, {"sourceToolCallRequestId": "740c150f-c973-4477-ba41-df59ccea7cac", "timestamp": 1750629312273, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/setup.html"}}}, {"sourceToolCallRequestId": "709897d2-d4a7-4aec-ad7e-6b4846694a65", "timestamp": 1750629312380, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/setup.html"}}}, {"sourceToolCallRequestId": "229e4964-f456-4f91-9855-981ebc676bd2", "timestamp": 1750629312380, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/setup.html"}}}, {"sourceToolCallRequestId": "84d12e17-8b4f-464e-9f8a-195448fc6727", "timestamp": 1750629335770, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/setup.html"}}}, {"sourceToolCallRequestId": "325c6ce1-4e0e-450d-9089-685f205e4219", "timestamp": 1750629335790, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/setup.html"}}}, {"sourceToolCallRequestId": "358c1c7e-3976-421d-89b9-e993c00e6be6", "timestamp": 1750629335790, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "lastIncludedInRequestId": "2ecef2ce-fa8e-4e94-a612-f6c8f0996732", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/setup.html"}}}, {"sourceToolCallRequestId": "ffbc985d-cc93-456d-9e24-76c8b0b0412d", "timestamp": 1750629760980, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/setup.html"}}}, {"sourceToolCallRequestId": "8d3ce379-7f0f-4d67-95ab-895f4f3a42a6", "timestamp": 1750629761173, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/setup.html"}}}, {"sourceToolCallRequestId": "25763c67-8258-437c-8453-247f8af15f19", "timestamp": 1750629761173, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/setup.html"}}}, {"sourceToolCallRequestId": "88def98d-8f66-442b-abc7-f68470f5993d", "timestamp": 1750629776563, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/setup.html"}}}, {"sourceToolCallRequestId": "18c34d4f-4349-4d3b-85e1-8a7c20d6b891", "timestamp": 1750629805754, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/setup.html"}}}, {"sourceToolCallRequestId": "534f48cc-2dda-4c88-91ef-d22f731ec4b9", "timestamp": 1750629810703, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/setup.html"}}}, {"sourceToolCallRequestId": "e6186448-a8a4-422a-87ab-93317fa6b707", "timestamp": 1750630158517, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/templates/setup.html"}}}], "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/algofactory-multi/instances/algofactory-8010/app.py": [{"sourceToolCallRequestId": "b01ae459-963e-48c8-8bb1-dd88ea4e6e67", "timestamp": 0, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/app.py"}}}, {"sourceToolCallRequestId": "ddb18b66-6449-4734-9e96-2dcd8bc1e778", "timestamp": 1750629355488, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/app.py"}}}, {"sourceToolCallRequestId": "6030372d-fa95-40d3-8cec-6ae6aa783e8e", "timestamp": 1750629355639, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/app.py"}}}, {"sourceToolCallRequestId": "17a443ef-8544-42b8-bbb8-6f140ffa2d72", "timestamp": 1750629355639, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "lastIncludedInRequestId": "2ecef2ce-fa8e-4e94-a612-f6c8f0996732", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/app.py"}}}, {"sourceToolCallRequestId": "a1c219be-9f2d-4b1e-bfaa-e6f31ffd014c", "timestamp": 1750629760980, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/app.py"}}}, {"sourceToolCallRequestId": "b0b69cd3-0a4f-4082-9fa2-aba9cafead10", "timestamp": 1750629761184, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/app.py"}}}, {"sourceToolCallRequestId": "eb34a137-0b9e-4c76-b9cd-2f095f47432d", "timestamp": 1750629761184, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/app.py"}}}, {"sourceToolCallRequestId": "c9be67f6-157e-4118-bdef-1cb593d9f5db", "timestamp": 1750629776563, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/app.py"}}}, {"sourceToolCallRequestId": "4305321a-5cfc-447b-9349-737ce6431ca1", "timestamp": 1750629805756, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/app.py"}}}, {"sourceToolCallRequestId": "804b332e-6372-4429-a919-6669085d6d2a", "timestamp": 1750629810703, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/app.py"}}}, {"sourceToolCallRequestId": "ce4fb843-2c3e-4a58-ae24-de2d5e1f0ff0", "timestamp": 1750630158517, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/app.py"}}}], "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/algofactory-multi/instances/algofactory-8010/.env": [{"sourceToolCallRequestId": "30e45f0a-2e5a-4243-859f-e23878434fa2", "timestamp": 0, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/.env"}}}, {"sourceToolCallRequestId": "3bb99b9c-8cd2-4cee-9032-33a1b034d82b", "timestamp": 1750629375990, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/.env"}}}, {"sourceToolCallRequestId": "aa2e2381-510c-43ba-b3da-35d417106a9f", "timestamp": 1750629376088, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/.env"}}}, {"sourceToolCallRequestId": "528f3c20-eb35-491b-b93f-f5205e9f8643", "timestamp": 1750629376088, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "lastIncludedInRequestId": "2ecef2ce-fa8e-4e94-a612-f6c8f0996732", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/.env"}}}, {"sourceToolCallRequestId": "ff25ee02-3f16-4803-a70f-4edfa15ef506", "timestamp": 1750629760980, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/.env"}}}, {"sourceToolCallRequestId": "b0eab556-47e2-4395-b257-f4bcac635726", "timestamp": 1750629761037, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/.env"}}}, {"sourceToolCallRequestId": "a8dcbb97-b41b-4d5e-945c-d220874b7054", "timestamp": 1750629761037, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/.env"}}}, {"sourceToolCallRequestId": "22637202-6432-4afc-972c-7d4aac3a6401", "timestamp": 1750629776563, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/.env"}}}, {"sourceToolCallRequestId": "13248ab7-5229-414c-86ec-2a61aa8c55c9", "timestamp": 1750629805756, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/.env"}}}, {"sourceToolCallRequestId": "6482c37d-9687-4165-b017-43da2a44fcea", "timestamp": 1750629810703, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/.env"}}}, {"sourceToolCallRequestId": "8afe4030-a7b9-40d7-ac20-fac976b6a76c", "timestamp": 1750630158517, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/.env"}}}], "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/change_port.sh": [{"sourceToolCallRequestId": "dc9cbf06-efc6-422b-a12b-20df5a9367c5", "timestamp": 1750630143024, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/change_port.sh"}}}, {"sourceToolCallRequestId": "67d5622c-4ee7-4537-824f-d1cfe23f57e8", "timestamp": 1750630158517, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 2, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/change_port.sh"}}}, {"sourceToolCallRequestId": "70988eb2-a565-45fd-8afb-309dbf1bdc7e", "timestamp": 1750630158751, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "lastIncludedInRequestId": "b68fdf1a-6c02-4195-80d1-0c10a5bc7c40", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/change_port.sh"}}}], "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/multi_instance.sh": [{"sourceToolCallRequestId": "b68fdf1a-6c02-4195-80d1-0c10a5bc7c40", "timestamp": 1750630284285, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/multi_instance.sh"}}}, {"sourceToolCallRequestId": "64909de5-9142-4b2f-9d10-7e1ef56aa235", "timestamp": 1750630340489, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/multi_instance.sh"}}}, {"sourceToolCallRequestId": "2a14861c-f60f-43ad-8ab9-eae403417a4e", "timestamp": 1750630340513, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/multi_instance.sh"}}}, {"sourceToolCallRequestId": "5ae40e79-e04e-4fc5-a093-860928e55f39", "timestamp": 1750630340513, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "lastIncludedInRequestId": "89fece37-3e89-476d-8c13-9f2d4ea62198", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/multi_instance.sh"}}}, {"sourceToolCallRequestId": "89fece37-3e89-476d-8c13-9f2d4ea62198", "timestamp": 1750630469599, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/multi_instance.sh"}}}, {"sourceToolCallRequestId": "5706381b-b303-4b51-85d4-8ffe8479ab6e", "timestamp": 1750630469711, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/multi_instance.sh"}}}, {"sourceToolCallRequestId": "fbcc9400-4d4a-41d0-bb41-a19f1397a87d", "timestamp": 1750630469711, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/multi_instance.sh"}}}, {"sourceToolCallRequestId": "8a556bf0-479f-4ce2-9935-277a088ada66", "timestamp": 1750630530223, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/multi_instance.sh"}}}, {"sourceToolCallRequestId": "cc632ae2-75d2-41c1-9fe6-8fc99fac4ae6", "timestamp": 1750630530334, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/multi_instance.sh"}}}, {"sourceToolCallRequestId": "39d8fd22-8c28-4aa5-94b0-97b9e2d829e0", "timestamp": 1750630530334, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "editSource": 1, "lastIncludedInRequestId": "859d82a6-b6ef-4d73-93cc-9f7fb5b456ba", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/multi_instance.sh"}}}], "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/monitor_instances.sh": [{"sourceToolCallRequestId": "9040e935-b7c0-4ba1-980f-df299ef60d1d", "timestamp": 1750631373166, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/monitor_instances.sh"}}}], "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/dashboard.sh": [{"sourceToolCallRequestId": "1547f032-77da-4d4b-a2d8-6ac1a6a27631", "timestamp": 1750631583703, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/dashboard.sh"}}}], "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/test_websocket.py": [{"sourceToolCallRequestId": "94529a32-8f42-4ea3-9410-28752cbbc9fc", "timestamp": 1750632617844, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/test_websocket.py"}}}], "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/simple_websocket_test.py": [{"sourceToolCallRequestId": "456df114-99cb-4d67-86ff-ff1ce872c753", "timestamp": 1750632678276, "conversationId": "1c862745-9469-458b-a4b9-a47af6a59b95", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/simple_websocket_test.py"}}}]}, "metadata": {"checkpointDocumentIds": ["1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/start.sh", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/algofactory.service", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/nginx-algofactory.conf", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/monitoring.py", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/setup_24x7.sh", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/multi-instance-roadmap.md", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/instance_manager.py", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/optimize_memory.sh", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/quick_instance.sh", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/MULTI_INSTANCE_COMPLETE_GUIDE.md", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/algofactory-multi/instances/algofactory-8010/templates/login.html", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/algofactory-multi/instances/algofactory-8010/templates/setup.html", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/algofactory-multi/instances/algofactory-8010/app.py", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/algofactory-multi/instances/algofactory-8010/.env", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/change_port.sh", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/multi_instance.sh", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/monitor_instances.sh", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/dashboard.sh", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/test_websocket.py", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/simple_websocket_test.py"], "size": 3195056, "checkpointCount": 186, "lastModified": 1750632678313}}