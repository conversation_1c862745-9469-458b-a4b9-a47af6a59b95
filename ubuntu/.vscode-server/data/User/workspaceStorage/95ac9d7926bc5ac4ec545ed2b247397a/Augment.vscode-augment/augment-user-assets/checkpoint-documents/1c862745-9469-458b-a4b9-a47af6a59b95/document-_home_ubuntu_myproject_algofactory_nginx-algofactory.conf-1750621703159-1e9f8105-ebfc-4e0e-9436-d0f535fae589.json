{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/nginx-algofactory.conf"}, "modifiedCode": "# Rate limiting zones\nlimit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;\nlimit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;\nlimit_req_zone $binary_remote_addr zone=general:10m rate=30r/m;\n\n# Upstream configuration for AlgoFactory\nupstream algofactory_backend {\n    server 127.0.0.1:5000 fail_timeout=30s max_fails=3;\n    keepalive 32;\n}\n\n# HTTP to HTTPS redirect\nserver {\n    listen 80;\n    listen [::]:80;\n    server_name algo.algofactory.in;\n    \n    # Security headers\n    add_header X-Frame-Options DENY always;\n    add_header X-Content-Type-Options nosniff always;\n    add_header X-XSS-Protection \"1; mode=block\" always;\n    \n    # Redirect all HTTP traffic to HTTPS\n    return 301 https://$server_name$request_uri;\n}\n\n# HTTPS server configuration\nserver {\n    listen 443 ssl http2;\n    listen [::]:443 ssl http2;\n    server_name algo.algofactory.in;\n    \n    # SSL Configuration (will be updated after SSL certificate setup)\n    ssl_certificate /etc/ssl/certs/algofactory.crt;\n    ssl_certificate_key /etc/ssl/private/algofactory.key;\n    ssl_protocols TLSv1.2 TLSv1.3;\n    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;\n    ssl_prefer_server_ciphers off;\n    ssl_session_cache shared:SSL:10m;\n    ssl_session_timeout 10m;\n    \n    # Security headers\n    add_header Strict-Transport-Security \"max-age=31536000; includeSubDomains; preload\" always;\n    add_header X-Frame-Options DENY always;\n    add_header X-Content-Type-Options nosniff always;\n    add_header X-XSS-Protection \"1; mode=block\" always;\n    add_header Referrer-Policy \"strict-origin-when-cross-origin\" always;\n    add_header Permissions-Policy \"camera=(), microphone=(), geolocation=(), payment=(), usb=()\" always;\n    \n    # Gzip compression\n    gzip on;\n    gzip_vary on;\n    gzip_min_length 1024;\n    gzip_proxied any;\n    gzip_comp_level 6;\n    gzip_types\n        text/plain\n        text/css\n        text/xml\n        text/javascript\n        application/json\n        application/javascript\n        application/xml+rss\n        application/atom+xml\n        image/svg+xml;\n    \n    # Client settings\n    client_max_body_size 10M;\n    client_body_timeout 60s;\n    client_header_timeout 60s;\n    \n    # Logging\n    access_log /var/log/nginx/algofactory_access.log;\n    error_log /var/log/nginx/algofactory_error.log;\n    \n    # Root location - main application\n    location / {\n        # Rate limiting\n        limit_req zone=general burst=10 nodelay;\n        \n        # Proxy settings\n        proxy_pass http://algofactory_backend;\n        proxy_http_version 1.1;\n        proxy_set_header Upgrade $http_upgrade;\n        proxy_set_header Connection 'upgrade';\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n        proxy_set_header X-Forwarded-Host $server_name;\n        proxy_cache_bypass $http_upgrade;\n        \n        # Timeouts\n        proxy_connect_timeout 30s;\n        proxy_send_timeout 60s;\n        proxy_read_timeout 60s;\n        \n        # Buffer settings\n        proxy_buffering on;\n        proxy_buffer_size 4k;\n        proxy_buffers 8 4k;\n        proxy_busy_buffers_size 8k;\n    }\n    \n    # API endpoints with stricter rate limiting\n    location /api/ {\n        limit_req zone=api burst=20 nodelay;\n        \n        proxy_pass http://algofactory_backend;\n        proxy_http_version 1.1;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n        \n        # API specific timeouts\n        proxy_connect_timeout 10s;\n        proxy_send_timeout 30s;\n        proxy_read_timeout 30s;\n    }\n    \n    # Login endpoints with very strict rate limiting\n    location /auth/login {\n        limit_req zone=login burst=3 nodelay;\n        \n        proxy_pass http://algofactory_backend;\n        proxy_http_version 1.1;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n    }\n    \n    # WebSocket support for real-time features\n    location /socket.io/ {\n        proxy_pass http://algofactory_backend;\n        proxy_http_version 1.1;\n        proxy_set_header Upgrade $http_upgrade;\n        proxy_set_header Connection \"upgrade\";\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n        \n        # WebSocket specific settings\n        proxy_connect_timeout 7d;\n        proxy_send_timeout 7d;\n        proxy_read_timeout 7d;\n    }\n    \n    # Static files caching\n    location /static/ {\n        proxy_pass http://algofactory_backend;\n        proxy_cache_valid 200 1h;\n        expires 1h;\n        add_header Cache-Control \"public, immutable\";\n    }\n    \n    # Health check endpoint for monitoring\n    location /health {\n        proxy_pass http://algofactory_backend;\n        access_log off;\n    }\n    \n    # Status endpoint for monitoring\n    location /status {\n        proxy_pass http://algofactory_backend;\n        access_log off;\n        allow 127.0.0.1;\n        allow ::1;\n        # Add your monitoring server IPs here\n        # allow YOUR_MONITORING_IP;\n        deny all;\n    }\n    \n    # Block access to sensitive files\n    location ~ /\\. {\n        deny all;\n        access_log off;\n        log_not_found off;\n    }\n    \n    location ~ \\.(env|log|ini|conf)$ {\n        deny all;\n        access_log off;\n        log_not_found off;\n    }\n}\n\n# Server block for monitoring from external services\nserver {\n    listen 8080;\n    server_name algo.algofactory.in;\n    \n    # Simple status page for external monitoring\n    location /monitor {\n        access_log off;\n        return 200 \"AlgoFactory Status: OK\\nTimestamp: $time_iso8601\\nServer: $hostname\\n\";\n        add_header Content-Type text/plain;\n    }\n    \n    # Detailed status (restricted access)\n    location /monitor/detailed {\n        access_log off;\n        allow 127.0.0.1;\n        # Add your monitoring service IPs here\n        deny all;\n        \n        proxy_pass http://algofactory_backend/health;\n    }\n}\n"}