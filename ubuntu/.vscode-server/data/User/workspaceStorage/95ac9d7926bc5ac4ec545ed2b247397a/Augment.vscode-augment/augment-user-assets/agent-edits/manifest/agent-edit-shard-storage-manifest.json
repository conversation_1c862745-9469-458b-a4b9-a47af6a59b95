{"version": 1, "lastUpdated": 1750627576131, "shards": {"shard-1c862745-9469-458b-a4b9-a47af6a59b95": {"checkpointDocumentIds": ["1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/start.sh", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/algofactory.service", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/nginx-algofactory.conf", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/monitoring.py", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/setup_24x7.sh", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/multi-instance-roadmap.md", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/instance_manager.py", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/optimize_memory.sh", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/quick_instance.sh"], "size": 336162, "checkpointCount": 28, "lastModified": 1750627571131}}}