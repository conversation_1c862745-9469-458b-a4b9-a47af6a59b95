{"version": 1, "lastUpdated": 1750630940661, "shards": {"shard-1c862745-9469-458b-a4b9-a47af6a59b95": {"checkpointDocumentIds": ["1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/start.sh", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/algofactory.service", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/nginx-algofactory.conf", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/monitoring.py", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/setup_24x7.sh", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/multi-instance-roadmap.md", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/instance_manager.py", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/optimize_memory.sh", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/quick_instance.sh", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/MULTI_INSTANCE_COMPLETE_GUIDE.md", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/algofactory-multi/instances/algofactory-8010/templates/login.html", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/algofactory-multi/instances/algofactory-8010/templates/setup.html", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/algofactory-multi/instances/algofactory-8010/app.py", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/algofactory-multi/instances/algofactory-8010/.env", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/change_port.sh", "1c862745-9469-458b-a4b9-a47af6a59b95:/home/<USER>/myproject/algofactory/multi_instance.sh"], "size": 3174479, "checkpointCount": 182, "lastModified": 1750630935659}}}