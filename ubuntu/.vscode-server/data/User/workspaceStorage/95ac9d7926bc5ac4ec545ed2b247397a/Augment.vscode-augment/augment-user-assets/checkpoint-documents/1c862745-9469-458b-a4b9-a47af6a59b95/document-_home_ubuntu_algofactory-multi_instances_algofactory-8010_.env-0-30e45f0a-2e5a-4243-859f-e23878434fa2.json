{"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-8010/.env"}, "originalCode": "# Broker Configuration\nBROKER_API_KEY = 'MZA0cLWq'\nBROKER_API_SECRET = 'XIA6RJ3HPG4ZRKKYJLIZ6ROKAM'\n\n# Market Data Configuration (Optional and Required only for XTS API Supported Brokers)\n\nBROKER_API_KEY_MARKET = 'YOUR_BROKER_MARKET_API_KEY'\nBROKER_API_SECRET_MARKET = 'YOUR_BROKER_MARKET_API_SECRET'\n\nREDIRECT_URL = 'https://8010.algofactory.in/angel/callback'  # Change if different\n\n# Valid Brokers Configuration\nVALID_BROKERS = 'fivepaisa,fivepaisaxts,aliceblue,angel,compositedge,dhan,dhan_sandbox,firstock,flattrade,fyers,groww,iifl,kotak,jainam,jainampro,paytm,pocketful,shoonya,tradejini,upstox,wisdom,zebu,zerodha'\n\n# Security Configuration\n# IMPORTANT: Generate new random values for both keys during setup!\n\n# AlgoFactory Application Key\nAPP_KEY = '3daa0403ce2501ee7432b75bf100048e3cf510d63d2754f952e93d88bf07ea84'\n\n# Security Pepper - Used for hashing/encryption of sensitive data\n# This is used for:\n# 1. API key hashing\n# 2. User password hashing\n# 3. Broker auth token encryption\n# Generate a new random string during setup using: python -c \"import secrets; print(secrets.token_hex(32))\"\nAPI_KEY_PEPPER = 'a25d94718479b170c16278e321ea6c989358bf499a658fd20c90033cef8ce772'\n\n# AlgoFactory Database Configuration\nDATABASE_URL = 'sqlite:///db/algofactory-8010.db'\n\n# AlgoFactory Ngrok Configuration\nNGROK_ALLOW = 'FALSE'\n\n# AlgoFactory Hosted Server (Custom Domain Name) or Ngrok Domain Configuration\n# Change to your custom domain or Ngrok domain\nHOST_SERVER = 'https://8010.algofactory.in'\n\n# AlgoFactory Flask App Host and Port Configuration\n# For 0.0.0.0 (accessible from other devices on the network)\n# Flask Environment - development or production\nFLASK_HOST_IP='0.0.0.0'\nFLASK_PORT='8010'\nFLASK_DEBUG='False'\nFLASK_ENV='production'\n\n# WebSocket Configuration\nWEBSOCKET_HOST='localhost'\nWEBSOCKET_PORT='20010'\nWEBSOCKET_URL='ws://localhost:20010'\n\n# ZeroMQ Configuration\nZMQ_HOST='localhost'\nZMQ_PORT='23010'\n\n# AlgoFactory Rate Limit Settings\nLOGIN_RATE_LIMIT_MIN = \"5 per minute\"\nLOGIN_RATE_LIMIT_HOUR = \"25 per hour\"\nAPI_RATE_LIMIT=\"10 per second\"\n\n# AlgoFactory API Configuration\n\n# Required to give 0.5 second to 1 second delay between multi-legged option strategies\n# Single legged orders are not affected by this setting.\nSMART_ORDER_DELAY = '0.5'\n\n# Session Expiry Time (24-hour format, IST)\n# All user sessions will automatically expire at this time daily\nSESSION_EXPIRY_TIME = '03:00'\n\n# AlgoFactory CORS (Cross-Origin Resource Sharing) Configuration\n# Set to TRUE to enable CORS support, FALSE to disable\nCORS_ENABLED = 'TRUE'\n\n# Comma-separated list of allowed origins (domains)\n# Example: http://localhost:3000,https://example.com\n# Use '*' to allow all origins (not recommended for production)\nCORS_ALLOWED_ORIGINS = 'https://8010.algofactory.in'\n\n# Comma-separated list of allowed HTTP methods\n# Default: GET,POST\nCORS_ALLOWED_METHODS = 'GET,POST,DELETE,PUT,PATCH'\n\n# Comma-separated list of allowed headers\n# Default Flask-CORS values will be used if not specified\nCORS_ALLOWED_HEADERS = 'Content-Type,Authorization,X-Requested-With'\n\n# Comma-separated list of headers exposed to the browser\nCORS_EXPOSED_HEADERS = ''\n\n# Whether to allow credentials (cookies, authorization headers)\n# Set to TRUE only if you need to support credentials\nCORS_ALLOW_CREDENTIALS = 'FALSE'\n\n# Max age (in seconds) for browser to cache preflight requests\n# Default: 86400 (24 hours)\nCORS_MAX_AGE = '86400'\n\n# AlgoFactory Content Security Policy (CSP) Configuration\n# Set to TRUE to enable CSP, FALSE to disable\nCSP_ENABLED = 'TRUE'\n\n# Set to TRUE to use Content-Security-Policy-Report-Only mode (testing without blocking)\n# This will report violations but not block content\nCSP_REPORT_ONLY = 'FALSE'\n\n# Default source directive - restricts all resource types by default\nCSP_DEFAULT_SRC = \"'self'\"\n\n# Script source directive - controls where scripts can be loaded from\n# Includes Socket.IO CDN which is required by the application\n# 'unsafe-inline' is needed for Socket.IO to function properly\n# Cloudflare Insights is used for analytics\nCSP_SCRIPT_SRC = \"'self' 'unsafe-inline' https://cdn.socket.io https://static.cloudflareinsights.com\"\n\n# Style source directive - controls where styles can be loaded from\n# 'unsafe-inline' is needed for some inline styles in the application\nCSP_STYLE_SRC = \"'self' 'unsafe-inline'\"\n\n# Image source directive - controls where images can be loaded from\n# 'data:' allows base64 encoded images\nCSP_IMG_SRC = \"'self' data:\"\n\n# Connect source directive - controls what network connections are allowed\n# Includes WebSocket connections needed for real-time updates\nCSP_CONNECT_SRC = \"'self' wss: ws:\"\n\n# Font source directive - controls where fonts can be loaded from\nCSP_FONT_SRC = \"'self'\"\n\n# Object source directive - controls where plugins can be loaded from\n# 'none' disables all object, embed, and applet elements\nCSP_OBJECT_SRC = \"'none'\"\n\n# Media source directive - controls where audio and video can be loaded from\n# Allows audio alerts from your domain and potentially CDN sources in the future\nCSP_MEDIA_SRC = \"'self' data: https://*.amazonaws.com https://*.cloudfront.net\"\n\n# Frame source directive - controls where iframes can be loaded from\n# If you integrate with TradingView or other platforms, you may need to add their domains\nCSP_FRAME_SRC = \"'self'\"\n\n# Form action directive - restricts where forms can be submitted to\nCSP_FORM_ACTION = \"'self'\"\n\n# Frame ancestors directive - controls which sites can embed your site in frames\n# This helps prevent clickjacking attacks\nCSP_FRAME_ANCESTORS = \"'self'\"\n\n# Base URI directive - restricts what base URIs can be used\nCSP_BASE_URI = \"'self'\"\n\n# Set to TRUE to upgrade insecure (HTTP) requests to HTTPS\n# Recommended for production environments\nCSP_UPGRADE_INSECURE_REQUESTS = 'FALSE'\n\n# URI to report CSP violations to (optional)\n# Example: /csp-report\nCSP_REPORT_URI = ''\n\n# CSRF (Cross-Site Request Forgery) Protection Configuration\n# Set to TRUE to enable CSRF protection, FALSE to disable\nCSRF_ENABLED = 'TRUE'\n\n# CSRF Token Time Limit (in seconds)\n# Leave empty for no time limit (tokens valid for entire session)\n# Example: 3600 = 1 hour, 86400 = 24 hours\nCSRF_TIME_LIMIT = ''\n", "modifiedCode": "# Broker Configuration\nBROKER_API_KEY = 'MZA0cLWq'\nBROKER_API_SECRET = 'XIA6RJ3HPG4ZRKKYJLIZ6ROKAM'\n\n# Market Data Configuration (Optional and Required only for XTS API Supported Brokers)\n\nBROKER_API_KEY_MARKET = 'YOUR_BROKER_MARKET_API_KEY'\nBROKER_API_SECRET_MARKET = 'YOUR_BROKER_MARKET_API_SECRET'\n\nREDIRECT_URL = 'https://8010.algofactory.in/angel/callback'  # Change if different\n\n# Valid Brokers Configuration\nVALID_BROKERS = 'fivepaisa,fivepaisaxts,aliceblue,angel,compositedge,dhan,dhan_sandbox,firstock,flattrade,fyers,groww,iifl,kotak,jainam,jainampro,paytm,pocketful,shoonya,tradejini,upstox,wisdom,zebu,zerodha'\n\n# Security Configuration\n# IMPORTANT: Generate new random values for both keys during setup!\n\n# AlgoFactory Application Key\nAPP_KEY = '3daa0403ce2501ee7432b75bf100048e3cf510d63d2754f952e93d88bf07ea84'\n\n# Security Pepper - Used for hashing/encryption of sensitive data\n# This is used for:\n# 1. API key hashing\n# 2. User password hashing\n# 3. Broker auth token encryption\n# Generate a new random string during setup using: python -c \"import secrets; print(secrets.token_hex(32))\"\nAPI_KEY_PEPPER = 'a25d94718479b170c16278e321ea6c989358bf499a658fd20c90033cef8ce772'\n\n# AlgoFactory Database Configuration\nDATABASE_URL = 'sqlite:///db/algofactory-8010.db'\n\n# AlgoFactory Ngrok Configuration\nNGROK_ALLOW = 'FALSE'\n\n# AlgoFactory Hosted Server (Custom Domain Name) or Ngrok Domain Configuration\n# Change to your custom domain or Ngrok domain\nHOST_SERVER = 'https://8010.algofactory.in'\n\n# AlgoFactory Flask App Host and Port Configuration\n# For 0.0.0.0 (accessible from other devices on the network)\n# Flask Environment - development or production\nFLASK_HOST_IP='0.0.0.0'\nFLASK_PORT='8010'\nFLASK_DEBUG='False'\nFLASK_ENV='production'\n\n# WebSocket Configuration\nWEBSOCKET_HOST='localhost'\nWEBSOCKET_PORT='20010'\nWEBSOCKET_URL='ws://localhost:20010'\n\n# ZeroMQ Configuration\nZMQ_HOST='localhost'\nZMQ_PORT='23010'\n\n# AlgoFactory Rate Limit Settings\nLOGIN_RATE_LIMIT_MIN = \"5 per minute\"\nLOGIN_RATE_LIMIT_HOUR = \"25 per hour\"\nAPI_RATE_LIMIT=\"10 per second\"\n\n# AlgoFactory API Configuration\n\n# Required to give 0.5 second to 1 second delay between multi-legged option strategies\n# Single legged orders are not affected by this setting.\nSMART_ORDER_DELAY = '0.5'\n\n# Session Expiry Time (24-hour format, IST)\n# All user sessions will automatically expire at this time daily\nSESSION_EXPIRY_TIME = '03:00'\n\n# AlgoFactory CORS (Cross-Origin Resource Sharing) Configuration\n# Set to TRUE to enable CORS support, FALSE to disable\nCORS_ENABLED = 'TRUE'\n\n# Comma-separated list of allowed origins (domains)\n# Example: http://localhost:3000,https://example.com\n# Use '*' to allow all origins (not recommended for production)\nCORS_ALLOWED_ORIGINS = 'https://8010.algofactory.in'\n\n# Comma-separated list of allowed HTTP methods\n# Default: GET,POST\nCORS_ALLOWED_METHODS = 'GET,POST,DELETE,PUT,PATCH'\n\n# Comma-separated list of allowed headers\n# Default Flask-CORS values will be used if not specified\nCORS_ALLOWED_HEADERS = 'Content-Type,Authorization,X-Requested-With'\n\n# Comma-separated list of headers exposed to the browser\nCORS_EXPOSED_HEADERS = ''\n\n# Whether to allow credentials (cookies, authorization headers)\n# Set to TRUE only if you need to support credentials\nCORS_ALLOW_CREDENTIALS = 'FALSE'\n\n# Max age (in seconds) for browser to cache preflight requests\n# Default: 86400 (24 hours)\nCORS_MAX_AGE = '86400'\n\n# AlgoFactory Content Security Policy (CSP) Configuration\n# Set to TRUE to enable CSP, FALSE to disable\nCSP_ENABLED = 'TRUE'\n\n# Set to TRUE to use Content-Security-Policy-Report-Only mode (testing without blocking)\n# This will report violations but not block content\nCSP_REPORT_ONLY = 'FALSE'\n\n# Default source directive - restricts all resource types by default\nCSP_DEFAULT_SRC = \"'self'\"\n\n# Script source directive - controls where scripts can be loaded from\n# Includes Socket.IO CDN which is required by the application\n# 'unsafe-inline' is needed for Socket.IO to function properly\n# Cloudflare Insights is used for analytics\nCSP_SCRIPT_SRC = \"'self' 'unsafe-inline' https://cdn.socket.io https://static.cloudflareinsights.com\"\n\n# Style source directive - controls where styles can be loaded from\n# 'unsafe-inline' is needed for some inline styles in the application\nCSP_STYLE_SRC = \"'self' 'unsafe-inline'\"\n\n# Image source directive - controls where images can be loaded from\n# 'data:' allows base64 encoded images\nCSP_IMG_SRC = \"'self' data:\"\n\n# Connect source directive - controls what network connections are allowed\n# Includes WebSocket connections needed for real-time updates\nCSP_CONNECT_SRC = \"'self' wss: ws:\"\n\n# Font source directive - controls where fonts can be loaded from\nCSP_FONT_SRC = \"'self'\"\n\n# Object source directive - controls where plugins can be loaded from\n# 'none' disables all object, embed, and applet elements\nCSP_OBJECT_SRC = \"'none'\"\n\n# Media source directive - controls where audio and video can be loaded from\n# Allows audio alerts from your domain and potentially CDN sources in the future\nCSP_MEDIA_SRC = \"'self' data: https://*.amazonaws.com https://*.cloudfront.net\"\n\n# Frame source directive - controls where iframes can be loaded from\n# If you integrate with TradingView or other platforms, you may need to add their domains\nCSP_FRAME_SRC = \"'self'\"\n\n# Form action directive - restricts where forms can be submitted to\nCSP_FORM_ACTION = \"'self'\"\n\n# Frame ancestors directive - controls which sites can embed your site in frames\n# This helps prevent clickjacking attacks\nCSP_FRAME_ANCESTORS = \"'self'\"\n\n# Base URI directive - restricts what base URIs can be used\nCSP_BASE_URI = \"'self'\"\n\n# Set to TRUE to upgrade insecure (HTTP) requests to HTTPS\n# Recommended for production environments\nCSP_UPGRADE_INSECURE_REQUESTS = 'FALSE'\n\n# URI to report CSP violations to (optional)\n# Example: /csp-report\nCSP_REPORT_URI = ''\n\n# CSRF (Cross-Site Request Forgery) Protection Configuration\n# Set to TRUE to enable CSRF protection, FALSE to disable\nCSRF_ENABLED = 'TRUE'\n\n# CSRF Token Time Limit (in seconds)\n# Leave empty for no time limit (tokens valid for entire session)\n# Example: 3600 = 1 hour, 86400 = 24 hours\nCSRF_TIME_LIMIT = ''\n"}