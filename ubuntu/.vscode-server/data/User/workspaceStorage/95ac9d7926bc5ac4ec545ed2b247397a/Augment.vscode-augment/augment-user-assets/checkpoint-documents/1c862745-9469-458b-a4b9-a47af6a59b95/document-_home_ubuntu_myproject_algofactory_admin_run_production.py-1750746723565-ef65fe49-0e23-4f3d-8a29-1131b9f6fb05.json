{"path": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/admin/run_production.py"}, "originalCode": "#!/usr/bin/env python3\n\n\"\"\"\nProduction runner for AlgoFactory Admin Dashboard\nUses Gunicorn for production deployment\n\"\"\"\n\nimport os\nimport sys\nimport signal\nimport time\nfrom pathlib import Path\n\n# Add current directory to Python path\nsys.path.insert(0, str(Path(__file__).parent))\n\ndef signal_handler(signum, frame):\n    print(f\"\\n🛑 Received signal {signum}, shutting down gracefully...\")\n    sys.exit(0)\n\ndef main():\n    # Set up signal handlers\n    signal.signal(signal.SIGTERM, signal_handler)\n    signal.signal(signal.SIGINT, signal_handler)\n    \n    print(\"🚀 Starting AlgoFactory Admin Dashboard (Production)\")\n    print(\"=\" * 55)\n    print(f\"Time: {time.strftime('%Y-%m-%d %H:%M:%S UTC')}\")\n    print(f\"Directory: {os.getcwd()}\")\n    print(f\"Python: {sys.executable}\")\n    print(\"\")\n    \n    try:\n        # Import and run the app\n        from app import app, socketio\n\n        print(\"✅ System monitoring will start with app\")\n        print(\"🌐 Starting web server on http://0.0.0.0:9001\")\n        print(\"📊 Admin Dashboard: https://admin.algofactory.in\")\n        print(\"🔐 Login: admin / admin123\")\n        print(\"\")\n\n        # Run with SocketIO\n        socketio.run(app,\n                    host='0.0.0.0',\n                    port=9001,\n                    debug=False,\n                    use_reloader=False,\n                    log_output=True,\n                    allow_unsafe_werkzeug=True)\n\n    except KeyboardInterrupt:\n        print(\"\\n🛑 Shutdown requested by user\")\n    except Exception as e:\n        print(f\"❌ Error starting admin dashboard: {e}\")\n        import traceback\n        traceback.print_exc()\n        return 1\n    finally:\n        print(\"✅ Admin dashboard stopped\")\n    \n    return 0\n\nif __name__ == \"__main__\":\n    sys.exit(main())\n", "modifiedCode": "#!/usr/bin/env python3\n\n\"\"\"\nProduction runner for AlgoFactory Admin Dashboard\nUses Gunicorn for production deployment\n\"\"\"\n\nimport os\nimport sys\nimport signal\nimport time\nfrom pathlib import Path\n\n# Add current directory to Python path\nsys.path.insert(0, str(Path(__file__).parent))\n\ndef signal_handler(signum, frame):\n    print(f\"\\n🛑 Received signal {signum}, shutting down gracefully...\")\n    sys.exit(0)\n\ndef main():\n    # Set up signal handlers\n    signal.signal(signal.SIGTERM, signal_handler)\n    signal.signal(signal.SIGINT, signal_handler)\n    \n    print(\"🚀 Starting AlgoFactory Admin Dashboard (Production)\")\n    print(\"=\" * 55)\n    print(f\"Time: {time.strftime('%Y-%m-%d %H:%M:%S UTC')}\")\n    print(f\"Directory: {os.getcwd()}\")\n    print(f\"Python: {sys.executable}\")\n    print(\"\")\n    \n    try:\n        # Import and run the app\n        from app import app, socketio\n\n        print(\"✅ System monitoring will start with app\")\n        print(\"🌐 Starting web server on http://0.0.0.0:9001\")\n        print(\"📊 Admin Dashboard: https://admin.algofactory.in\")\n        print(\"🔐 Login: admin / admin123\")\n        print(\"\")\n\n        # Run with SocketIO\n        socketio.run(app,\n                    host='0.0.0.0',\n                    port=9001,\n                    debug=False,\n                    use_reloader=False,\n                    log_output=True,\n                    allow_unsafe_werkzeug=True)\n\n    except KeyboardInterrupt:\n        print(\"\\n🛑 Shutdown requested by user\")\n    except Exception as e:\n        print(f\"❌ Error starting admin dashboard: {e}\")\n        import traceback\n        traceback.print_exc()\n        return 1\n    finally:\n        print(\"✅ Admin dashboard stopped\")\n    \n    return 0\n\nif __name__ == \"__main__\":\n    sys.exit(main())\n"}