[["/home/<USER>/myproject/algofactory/algofactory.service", {"value": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/algofactory.service"}}], ["/home/<USER>/myproject/algofactory/nginx-algofactory.conf", {"value": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/nginx-algofactory.conf"}}], ["/home/<USER>/myproject/algofactory/multi-instance-roadmap.md", {"value": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/multi-instance-roadmap.md"}}], ["/home/<USER>/myproject/algofactory/app.pid", {"value": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/app.pid"}}], ["/home/<USER>/myproject/algofactory/multi_instance.sh", {"value": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/multi_instance.sh"}}], ["/home/<USER>/myproject/algofactory/start.sh", {"value": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/start.sh"}}], ["/home/<USER>/algofactory-multi/algofactory-8010/test/simple_depth_test.py", {"value": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/algofactory-8010/test/simple_depth_test.py"}}], ["/home/<USER>/algofactory-multi/algofactory-8010/test/simple_ltp_test.py", {"value": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/algofactory-8010/test/simple_ltp_test.py"}}], ["/home/<USER>/algofactory-multi/algofactory-8010/test/simple_quote_test.py", {"value": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/algofactory-8010/test/simple_quote_test.py"}}], ["/home/<USER>/algofactory-multi/algofactory-8010/test/test_broker.py", {"value": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/algofactory-8010/test/test_broker.py"}}], ["/home/<USER>/algofactory-multi/algofactory-8010/test/test_csrf.py", {"value": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/algofactory-8010/test/test_csrf.py"}}], ["/home/<USER>/algofactory-multi/algofactory-8010/websocket_proxy/app_integration.py", {"value": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/algofactory-8010/websocket_proxy/app_integration.py"}}], ["/home/<USER>/algofactory-multi/algofactory-8010/test/iteration_test.py", {"value": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/algofactory-8010/test/iteration_test.py"}}]]