[["/home/<USER>/myproject/algofactory/algofactory.service", {"value": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/algofactory.service"}}], ["/home/<USER>/myproject/algofactory/nginx-algofactory.conf", {"value": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/nginx-algofactory.conf"}}], ["/home/<USER>/myproject/algofactory/multi-instance-roadmap.md", {"value": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/multi-instance-roadmap.md"}}], ["/home/<USER>/myproject/algofactory/start.sh", {"value": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/start.sh"}}], ["/home/<USER>/algofactory-multi/algofactory-8010/test/simple_depth_test.py", {"value": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/algofactory-8010/test/simple_depth_test.py"}}], ["/home/<USER>/algofactory-multi/algofactory-8010/test/simple_ltp_test.py", {"value": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/algofactory-8010/test/simple_ltp_test.py"}}], ["/home/<USER>/algofactory-multi/algofactory-8010/test/simple_quote_test.py", {"value": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/algofactory-8010/test/simple_quote_test.py"}}], ["/home/<USER>/algofactory-multi/algofactory-8010/test/test_broker.py", {"value": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/algofactory-8010/test/test_broker.py"}}], ["/home/<USER>/algofactory-multi/algofactory-8010/test/test_csrf.py", {"value": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/algofactory-8010/test/test_csrf.py"}}], ["/home/<USER>/algofactory-multi/algofactory-8010/websocket_proxy/app_integration.py", {"value": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/algofactory-8010/websocket_proxy/app_integration.py"}}], ["/home/<USER>/myproject/algofactory/dashboard.sh", {"value": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/dashboard.sh"}}], ["/home/<USER>/myproject/algofactory/instance_manager.py", {"value": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/instance_manager.py"}}], ["/home/<USER>/myproject/algofactory/monitor_dashboard.html", {"value": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/monitor_dashboard.html"}}], ["/home/<USER>/myproject/algofactory/monitor_instances.sh", {"value": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/monitor_instances.sh"}}], ["/home/<USER>/myproject/algofactory/monitoring.py", {"value": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/monitoring.py"}}], ["/home/<USER>/myproject/algofactory/MULTI_INSTANCE_COMPLETE_GUIDE.md", {"value": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/MULTI_INSTANCE_COMPLETE_GUIDE.md"}}], ["/home/<USER>/myproject/algofactory/multi_instance.sh", {"value": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/multi_instance.sh"}}], ["/home/<USER>/algofactory-multi/algofactory-8010/algofactory.service", {"value": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/algofactory-8010/algofactory.service"}}], ["/home/<USER>/algofactory-multi/algofactory-8010/app.pid", {"value": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/algofactory-8010/app.pid"}}], ["/home/<USER>/algofactory-multi/algofactory-8010/instance_manager.py", {"value": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/algofactory-8010/instance_manager.py"}}], ["/home/<USER>/algofactory-multi/algofactory-8010/monitor_dashboard.html", {"value": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/algofactory-8010/monitor_dashboard.html"}}], ["/home/<USER>/algofactory-multi/algofactory-8010/monitoring.py", {"value": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/algofactory-8010/monitoring.py"}}], ["/home/<USER>/algofactory-multi/algofactory-8010/MULTI_INSTANCE_COMPLETE_GUIDE.md", {"value": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/algofactory-8010/MULTI_INSTANCE_COMPLETE_GUIDE.md"}}], ["/home/<USER>/algofactory-multi/algofactory-8010/nginx-algofactory.conf", {"value": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/algofactory-8010/nginx-algofactory.conf"}}], ["/home/<USER>/algofactory-multi/algofactory-8010/optimize_memory.sh", {"value": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/algofactory-8010/optimize_memory.sh"}}], ["/home/<USER>/algofactory-multi/algofactory-8010/package-lock.json", {"value": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/algofactory-8010/package-lock.json"}}], ["/home/<USER>/algofactory-multi/algofactory-8010/setup_24x7.sh", {"value": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/algofactory-8010/setup_24x7.sh"}}], ["/home/<USER>/.vscode-server/extensions/extensions.json", {"value": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/extensions/extensions.json"}}], ["/home/<USER>/.bashrc", {"value": {"rootPath": "/home", "relPath": "ubuntu/.bashrc"}}], ["/home/<USER>/algofactory-multi/algofactory-8010/start.sh", {"value": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/algofactory-8010/start.sh"}}], ["/home/<USER>/.sudo_as_admin_successful", {"value": {"rootPath": "/home", "relPath": "ubuntu/.sudo_as_admin_successful"}}], ["/home/<USER>/.wget-hsts", {"value": {"rootPath": "/home", "relPath": "ubuntu/.wget-hsts"}}], ["/home/<USER>/algofactory-multi/algofactory-8012/nginx-algofactory.conf", {"value": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/algofactory-8012/nginx-algofactory.conf"}}], ["/home/<USER>/myproject/algofactory/admin_dashboard/requirements.txt", {"value": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/admin_dashboard/requirements.txt"}}], ["/home/<USER>/myproject/algofactory/app.py", {"value": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/app.py"}}], ["/home/<USER>/myproject/algofactory/app.pid", {"value": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/app.pid"}}], ["/home/<USER>/myproject/algofactory/uv.lock", {"value": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/uv.lock"}}], ["/home/<USER>/algofactory-multi/algofactory-8010/test/iteration_test.py", {"value": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/algofactory-8010/test/iteration_test.py"}}], ["/home/<USER>/.bash_history", {"value": {"rootPath": "/home", "relPath": "ubuntu/.bash_history"}}]]