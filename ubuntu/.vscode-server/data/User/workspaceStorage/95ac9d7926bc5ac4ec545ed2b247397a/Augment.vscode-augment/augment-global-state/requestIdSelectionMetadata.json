[["f0419baa-40db-4065-ae2b-3d9e5bc9fd88", {"value": {"selectedCode": "", "prefix": "# Broker Configuration\r\nBROKER_API_KEY = 'MZA0cLWq'\r\nBROKER_API_SECRET = 'XIA6RJ3HPG4ZRKKYJLIZ6ROKAM'\r\n\r\n# Market Data Configuration (Optional and Required only for XTS API Supported Brokers)\r\n\r\nBROKER_API_KEY_MARKET = 'YOUR_BROKER_MARKET_API_KEY'\r\nBROKER_API_SECRET_MARKET = 'YOUR_BROKER_MARKET_API_SECRET'\r\n\r\nREDIRECT_URL = 'https://algo.algofactory.in/angel/callback'  # Change if different\r\n\r\n# Valid Brokers Configuration\r\nVALID_BROKERS = 'fivepaisa,fivepaisaxts,aliceblue,angel,compositedge,dhan,dhan_sandbox,firstock,flattrade,fyers,groww,iifl,kotak,jainam,jainampro,paytm,pocketful,shoonya,tradejini,upstox,wisdom,zebu,zerodha'\r\n\r\n# Security Configuration\r\n# IMPORTANT: Generate new random values for both keys during setup!\r\n\r\n# AlgoFactory Application Key\r\nAPP_KEY = '3daa0403ce2501ee7432b75bf100048e3cf510d63d2754f952e93d88bf07ea84'\r\n\r\n# Security Pepper - Used for hashing/encryption of sensitive data\r\n# This is used for:\r\n# 1. API key hashing\r\n# 2. User password hashing\r\n# 3. Broker auth token encryption\r\n# Generate a new random string during setup using: python -c \"import secrets; print(secrets.token_hex(32))\"\r\nAPI_KEY_PEPPER = 'a25d94718479b170c16278e321ea6c989358bf499a658fd20c90033cef8ce772'\r\n\r\n# AlgoFactory Database Configuration\r\nDATABASE_URL = 'sqlite:///db/openalgo.db' \r\n\r\n# AlgoFactory Ngrok Configuration\r\nNGROK_ALLOW = 'FALSE' \r\n\r\n# AlgoFactory Hosted Server (Custom Domain Name) or Ngrok Domain Configuration\r\n# Change to your custom domain or Ngrok domain\r\nHOST_SERVER = 'https://algo.algofactory.in'  \r\n\r\n# AlgoFactory Flask App Host and Port Configuration\r\n# For 0.0.0.0 (accessible from other devices on the network)\r\n# Flask Environment - development or production\r\nFLASK_HOST_IP='0.0.0.0'  \r\nFLASK_PORT='5000' \r\nFLASK_DEBUG='False' \r\nFLASK_ENV='development'\r\n\r\n# WebSocket Configuration\r\nWEBSOCKET_HOST='localhost'\r\n", "suffix": "WEBSOCKET_PORT='8765'\r\nWEBSOCKET_URL='ws://localhost:8765'\r\n\r\n# ZeroMQ Configuration\r\nZMQ_HOST='localhost'\r\nZMQ_PORT='5555'\r\n\r\n# AlgoFactory Rate Limit Settings\r\nLOGIN_RATE_LIMIT_MIN = \"5 per minute\" \r\nLOGIN_RATE_LIMIT_HOUR = \"25 per hour\"\r\nAPI_RATE_LIMIT=\"10 per second\"\r\n\r\n# AlgoFactory API Configuration\r\n\r\n# Required to give 0.5 second to 1 second delay between multi-legged option strategies\r\n# Single legged orders are not affected by this setting.\r\nSMART_ORDER_DELAY = '0.5'\r\n\r\n# Session Expiry Time (24-hour format, IST)\r\n# All user sessions will automatically expire at this time daily\r\nSESSION_EXPIRY_TIME = '03:00'\r\n\r\n# AlgoFactory CORS (Cross-Origin Resource Sharing) Configuration\r\n# Set to TRUE to enable CORS support, FALSE to disable\r\nCORS_ENABLED = 'TRUE'\r\n\r\n# Comma-separated list of allowed origins (domains)\r\n# Example: http://localhost:3000,https://example.com\r\n# Use '*' to allow all origins (not recommended for production)\r\nCORS_ALLOWED_ORIGINS = 'https://algo.algofactory.in'\r\n\r\n# Comma-separated list of allowed HTTP methods\r\n# Default: GET,POST\r\nCORS_ALLOWED_METHODS = 'GET,POST,DELETE,PUT,PATCH'\r\n\r\n# Comma-separated list of allowed headers\r\n# Default Flask-CORS values will be used if not specified\r\nCORS_ALLOWED_HEADERS = 'Content-Type,Authorization,X-Requested-With'\r\n\r\n# Comma-separated list of headers exposed to the browser\r\nCORS_EXPOSED_HEADERS = ''\r\n\r\n# Whether to allow credentials (cookies, authorization headers)\r\n# Set to TRUE only if you need to support credentials\r\nCORS_ALLOW_CREDENTIALS = 'FALSE'\r\n\r\n# Max age (in seconds) for browser to cache preflight requests\r\n# Default: 86400 (24 hours)\r\nCORS_MAX_AGE = '86400'\r\n\r\n# AlgoFactory Content Security Policy (CSP) Configuration\r\n# Set to TRUE to enable CSP, FALSE to disable\r\nCSP_ENABLED = 'TRUE'\r\n\r\n# Set to TRUE to use Content-Security-Policy-Report-Only mode (testing without blocking)\r\n# This will report violations but not block content\r\nCSP_REPORT_ONLY = 'FALSE'\r\n\r\n# Default source directive - restricts all resource types by default\r\nCSP_DEFAULT_SRC = \"'self'\"\r\n\r\n# Script source directive - controls where scripts can be loaded from\r\n# Includes Socket.IO CDN which is required by the application\r\n# 'unsafe-inline' is needed for Socket.IO to function properly\r\n# Cloudflare Insights is used for analytics\r\nCSP_SCRIPT_SRC = \"'self' 'unsafe-inline' https://cdn.socket.io https://static.cloudflareinsights.com\"\r\n\r\n# Style source directive - controls where styles can be loaded from\r\n# 'unsafe-inline' is needed for some inline styles in the application\r\nCSP_STYLE_SRC = \"'self' 'unsafe-inline'\"\r\n\r\n# Image source directive - controls where images can be loaded from\r\n# 'data:' allows base64 encoded images\r\nCSP_IMG_SRC = \"'self' data:\"\r\n\r\n# Connect source directive - controls what network connections are allowed\r\n# Includes WebSocket connections needed for real-time updates\r\nCSP_CONNECT_SRC = \"'self' wss: ws:\"\r\n\r\n# Font source directive - controls where fonts can be loaded from\r\nCSP_FONT_SRC = \"'self'\"\r\n\r\n# Object source directive - controls where plugins can be loaded from\r\n# 'none' disables all object, embed, and applet elements\r\nCSP_OBJECT_SRC = \"'none'\"\r\n\r\n# Media source directive - controls where audio and video can be loaded from\r\n# Allows audio alerts from your domain and potentially CDN sources in the future\r\nCSP_MEDIA_SRC = \"'self' data: https://*.amazonaws.com https://*.cloudfront.net\"\r\n\r\n# Frame source directive - controls where iframes can be loaded from\r\n# If you integrate with TradingView or other platforms, you may need to add their domains\r\nCSP_FRAME_SRC = \"'self'\"\r\n\r\n# Form action directive - restricts where forms can be submitted to\r\nCSP_FORM_ACTION = \"'self'\"\r\n\r\n# Frame ancestors directive - controls which sites can embed your site in frames\r\n# This helps prevent clickjacking attacks\r\nCSP_FRAME_ANCESTORS = \"'self'\"\r\n\r\n# Base URI directive - restricts what base URIs can be used\r\nCSP_BASE_URI = \"'self'\"\r\n\r\n# Set to TRUE to upgrade insecure (HTTP) requests to HTTPS\r\n# Recommended for production environments\r\nCSP_UPGRADE_INSECURE_REQUESTS = 'FALSE'\r\n\r\n# URI to report CSP violations to (optional)\r\n# Example: /csp-report\r\nCSP_REPORT_URI = ''\r\n\r\n# CSRF (Cross-Site Request Forgery) Protection Configuration\r\n# Set to TRUE to enable CSRF protection, FALSE to disable\r\nCSRF_ENABLED = 'TRUE'\r\n\r\n# CSRF Token Time Limit (in seconds)\r\n# Leave empty for no time limit (tokens valid for entire session)\r\n# Example: 3600 = 1 hour, 86400 = 24 hours\r\nCSRF_TIME_LIMIT = ''\r\n", "path": "ubuntu/myproject/algofactory/.env", "language": "properties", "prefixBegin": 0, "suffixEnd": 0}}], ["3126c46c-deb8-4198-ad1f-060383b177c9", {"value": {"selectedCode": "", "prefix": "# Rate limiting zones\n", "suffix": "limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;\nlimit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;\nlimit_req_zone $binary_remote_addr zone=general:10m rate=30r/m;\n\n# Upstream configuration for AlgoFactory\nupstream algofactory_backend {\n    server 127.0.0.1:5000 fail_timeout=30s max_fails=3;\n    keepalive 32;\n}\n\n# HTTP to HTTPS redirect\nserver {\n    listen 80;\n    listen [::]:80;\n    server_name algo.algofactory.in;\n    \n    # Security headers\n    add_header X-Frame-Options DENY always;\n    add_header X-Content-Type-Options nosniff always;\n    add_header X-XSS-Protection \"1; mode=block\" always;\n    \n    # Redirect all HTTP traffic to HTTPS\n    return 301 https://$server_name$request_uri;\n}\n\n# HTTPS server configuration\nserver {\n    listen 443 ssl http2;\n    listen [::]:443 ssl http2;\n    server_name algo.algofactory.in;\n    \n    # SSL Configuration (will be updated after SSL certificate setup)\n    ssl_certificate /etc/ssl/certs/algofactory.crt;\n    ssl_certificate_key /etc/ssl/private/algofactory.key;\n    ssl_protocols TLSv1.2 TLSv1.3;\n    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;\n    ssl_prefer_server_ciphers off;\n    ssl_session_cache shared:SSL:10m;\n    ssl_session_timeout 10m;\n    \n    # Security headers\n    add_header Strict-Transport-Security \"max-age=31536000; includeSubDomains; preload\" always;\n    add_header X-Frame-Options DENY always;\n    add_header X-Content-Type-Options nosniff always;\n    add_header X-XSS-Protection \"1; mode=block\" always;\n    add_header Referrer-Policy \"strict-origin-when-cross-origin\" always;\n    add_header Permissions-Policy \"camera=(), microphone=(), geolocation=(), payment=(), usb=()\" always;\n    \n    # Gzip compression\n    gzip on;\n    gzip_vary on;\n    gzip_min_length 1024;\n    gzip_proxied any;\n    gzip_comp_level 6;\n    gzip_types\n        text/plain\n        text/css\n        text/xml\n        text/javascript\n        application/json\n        application/javascript\n        application/xml+rss\n        application/atom+xml\n        image/svg+xml;\n    \n    # Client settings\n    client_max_body_size 10M;\n    client_body_timeout 60s;\n    client_header_timeout 60s;\n    \n    # Logging\n    access_log /var/log/nginx/algofactory_access.log;\n    error_log /var/log/nginx/algofactory_error.log;\n    \n    # Root location - main application\n    location / {\n        # Rate limiting\n        limit_req zone=general burst=10 nodelay;\n        \n        # Proxy settings\n        proxy_pass http://algofactory_backend;\n        proxy_http_version 1.1;\n        proxy_set_header Upgrade $http_upgrade;\n        proxy_set_header Connection 'upgrade';\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n        proxy_set_header X-Forwarded-Host $server_name;\n        proxy_cache_bypass $http_upgrade;\n        \n        # Timeouts\n        proxy_connect_timeout 30s;\n        proxy_send_timeout 60s;\n        proxy_read_timeout 60s;\n        \n        # Buffer settings\n        proxy_buffering on;\n        proxy_buffer_size 4k;\n        proxy_buffers 8 4k;\n        proxy_busy_buffers_size 8k;\n    }\n    \n    # API endpoints with stricter rate limiting\n    location /api/ {\n        limit_req zone=api burst=20 nodelay;\n        \n        proxy_pass http://algofactory_backend;\n        proxy_http_version 1.1;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n        \n        # API specific timeouts\n        proxy_connect_timeout 10s;\n        proxy_send_timeout 30s;\n        proxy_read_timeout 30s;\n    }\n    \n    # Login endpoints with very strict rate limiting\n    location /auth/login {\n        limit_req zone=login burst=3 nodelay;\n        \n        proxy_pass http://algofactory_backend;\n        proxy_http_version 1.1;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n    }\n    \n    # WebSocket support for real-time features\n    location /socket.io/ {\n        proxy_pass http://algofactory_backend;\n        proxy_http_version 1.1;\n        proxy_set_header Upgrade $http_upgrade;\n        proxy_set_header Connection \"upgrade\";\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n        \n        # WebSocket specific settings\n        proxy_connect_timeout 7d;\n        proxy_send_timeout 7d;\n        proxy_read_timeout 7d;\n    }\n    \n    # Static files caching\n    location /static/ {\n        proxy_pass http://algofactory_backend;\n        proxy_cache_valid 200 1h;\n        expires 1h;\n        add_header Cache-Control \"public, immutable\";\n    }\n    \n    # Health check endpoint for monitoring\n    location /health {\n        proxy_pass http://algofactory_backend;\n        access_log off;\n    }\n    \n    # Status endpoint for monitoring\n    location /status {\n        proxy_pass http://algofactory_backend;\n        access_log off;\n        allow 127.0.0.1;\n        allow ::1;\n        # Add your monitoring server IPs here\n        # allow YOUR_MONITORING_IP;\n        deny all;\n    }\n    \n    # Block access to sensitive files\n    location ~ /\\. {\n        deny all;\n        access_log off;\n        log_not_found off;\n    }\n    \n    location ~ \\.(env|log|ini|conf)$ {\n        deny all;\n        access_log off;\n        log_not_found off;\n    }\n}\n\n# Server block for monitoring from external services\nserver {\n    listen 8080;\n    server_name algo.algofactory.in;\n    \n    # Simple status page for external monitoring\n    location /monitor {\n        access_log off;\n        return 200 \"AlgoFactory Status: OK\\nTimestamp: $time_iso8601\\nServer: $hostname\\n\";\n        add_header Content-Type text/plain;\n    }\n    \n    # Detailed status (restricted access)\n    location /monitor/detailed {\n        access_log off;\n        allow 127.0.0.1;\n        # Add your monitoring service IPs here\n        deny all;\n        \n        proxy_pass http://algofactory_backend/health;\n    }\n}\n", "path": "ubuntu/myproject/algofactory/nginx-algofactory.conf", "language": "properties", "prefixBegin": 0, "suffixEnd": 0}}], ["3ceb723e-ce23-4852-a7e7-1cde29c2fbea", {"value": {"selectedCode": "", "prefix": "# AlgoFactory Multi-Instance Architecture Roadmap\n", "suffix": "\n## 🎯 **Objective**\nCreate a scalable multi-tenant system where each user gets their own isolated AlgoFactory instance with unique ports and subdomains.\n\n## 🏗️ **Architecture Overview**\n\n### **Port Allocation Strategy**\n```\nInstance ID: 1010, 1011, 1012, etc.\n- Flask Port: 1000 + instance_id (1010, 1011, 1012...)\n- WebSocket Port: 12000 + instance_id (12010, 12011, 12012...)\n- ZMQ Port: 15000 + instance_id (15010, 15011, 15012...)\n```\n\n### **Domain Strategy**\n```\n- 1010.algofactory.in → Instance 1010 (Port 1010)\n- 1011.algofactory.in → Instance 1011 (Port 1011)\n- 1012.algofactory.in → Instance 1012 (Port 1012)\n```\n\n### **Folder Structure**\n```\n/home/<USER>/algofactory-multi/\n├── template/                    # Master template (copy of current algofactory)\n│   ├── app.py\n│   ├── requirements.txt\n│   ├── .env.template\n│   └── ... (all current files)\n├── instances/\n│   ├── algofactory-1010/       # Instance 1010\n│   │   ├── app.py\n│   │   ├── .env                # Port 1010, WebSocket 12010, ZMQ 15010\n│   │   ├── db/                 # Isolated database\n│   │   └── logs/               # Instance-specific logs\n│   ├── algofactory-1011/       # Instance 1011\n│   └── algofactory-1012/       # Instance 1012\n├── manager/                     # Management scripts\n│   ├── instance_manager.py     # Main management script\n│   ├── create_instance.sh      # Create new instance\n│   ├── start_all.sh           # Start all instances\n│   ├── stop_all.sh            # Stop all instances\n│   └── monitor_all.py         # Monitor all instances\n├── nginx/                       # Nginx configurations\n│   ├── template.conf           # Nginx template\n│   └── sites/                  # Generated configs\n│       ├── 1010.algofactory.in.conf\n│       └── 1011.algofactory.in.conf\n└── monitoring/                  # Centralized monitoring\n    ├── dashboard.html          # Multi-instance dashboard\n    └── status_api.py           # Status API for all instances\n```\n\n## 🚀 **Implementation Phases**\n\n### **Phase 1: Setup Multi-Instance Foundation**\n1. Create folder structure\n2. Copy current algofactory as template\n3. Create instance manager script\n4. Test with 2 instances (1010, 1011)\n\n### **Phase 2: Nginx & SSL Automation**\n1. Create dynamic Nginx configuration generator\n2. Setup automatic SSL certificate generation\n3. Configure wildcard DNS (*.algofactory.in)\n4. Test subdomain routing\n\n### **Phase 3: Instance Management**\n1. Build web-based instance manager\n2. Create user assignment system\n3. Implement instance lifecycle management\n4. Add resource monitoring\n\n### **Phase 4: Monitoring & Scaling**\n1. Centralized monitoring dashboard\n2. Auto-scaling based on demand\n3. Health checks and auto-restart\n4. Performance optimization\n\n## 🔧 **Key Components to Build**\n\n### **1. Instance Manager Script**\n```bash\n./instance_manager.py create 1010    # Create instance 1010\n./instance_manager.py start 1010     # Start instance 1010\n./instance_manager.py stop 1010      # Stop instance 1010\n./instance_manager.py delete 1010    # Delete instance 1010\n./instance_manager.py list           # List all instances\n./instance_manager.py status         # Status of all instances\n```\n\n### **2. Environment Template**\n```env\n# Instance-specific configuration\nINSTANCE_ID=1010\nFLASK_PORT=1010\nWEBSOCKET_PORT=12010\nZMQ_PORT=15010\nDATABASE_URL=sqlite:///db/algofactory-1010.db\nHOST_SERVER=https://1010.algofactory.in\n```\n\n### **3. Nginx Template**\n```nginx\nserver {\n    listen 443 ssl http2;\n    server_name {INSTANCE_ID}.algofactory.in;\n    \n    location / {\n        proxy_pass http://127.0.0.1:{FLASK_PORT};\n        # ... other proxy settings\n    }\n}\n```\n\n### **4. SystemD Service Template**\n```ini\n[Unit]\nDescription=AlgoFactory Instance {INSTANCE_ID}\n\n[Service]\nExecStart=/home/<USER>/algofactory-multi/instances/algofactory-{INSTANCE_ID}/start.sh monitor\nWorkingDirectory=/home/<USER>/algofactory-multi/instances/algofactory-{INSTANCE_ID}\n```\n\n## 📊 **Resource Planning**\n\n### **Port Ranges**\n- Flask: 1010-1999 (990 instances max)\n- WebSocket: 12010-12999 (990 instances max)\n- ZMQ: 15010-15999 (990 instances max)\n\n### **System Resources**\n- Each instance: ~200MB RAM\n- 10 instances: ~2GB RAM\n- 50 instances: ~10GB RAM\n\n### **Storage**\n- Each instance: ~100MB disk\n- Logs: ~10MB per day per instance\n- Database: Variable based on usage\n\n## 🔐 **Security Considerations**\n\n1. **Isolation**: Each instance has separate database and files\n2. **Firewall**: Only necessary ports exposed\n3. **SSL**: Automatic certificate generation for each subdomain\n4. **Access Control**: User-to-instance mapping\n5. **Resource Limits**: CPU and memory limits per instance\n\n## 🎯 **Success Metrics**\n\n1. **Scalability**: Ability to create 100+ instances\n2. **Performance**: <2 second instance creation\n3. **Reliability**: 99.9% uptime per instance\n4. **Management**: Web-based instance management\n5. **Monitoring**: Real-time status of all instances\n\n## 🚀 **Next Steps**\n\n1. **Start with Phase 1**: Create basic multi-instance setup\n2. **Test with 2-3 instances**: Validate the concept\n3. **Automate Nginx**: Dynamic configuration generation\n4. **Build management interface**: Web-based control panel\n5. **Scale gradually**: Add monitoring and optimization\n\n## 💡 **Benefits**\n\n1. **User Isolation**: Each user has dedicated environment\n2. **Scalability**: Easy to add new instances\n3. **Reliability**: One instance failure doesn't affect others\n4. **Customization**: Each instance can have different configurations\n5. **Resource Management**: Better resource allocation and monitoring\n", "path": "ubuntu/myproject/algofactory/multi-instance-roadmap.md", "language": "markdown", "prefixBegin": 0, "suffixEnd": 0}}], ["75cbc269-821c-493b-bb18-1294c7f568aa", {"value": {"selectedCode": "", "prefix": "4053\n", "suffix": "", "path": "ubuntu/myproject/algofactory/app.pid", "language": "plaintext", "prefixBegin": 0, "suffixEnd": 0}}], ["de7457f0-82ef-4a9e-b420-b022cfe7e49f", {"value": {"selectedCode": "", "prefix": "# AlgoFactory Instance Configuration\n", "suffix": "# This file is auto-generated for instance 1010\n\n# Instance Configuration\nINSTANCE_ID=1010\n\n# Broker Configuration\nBROKER_API_KEY = 'MZA0cLWq'\nBROKER_API_SECRET = 'XIA6RJ3HPG4ZRKKYJLIZ6ROKAM'\n\n# Market Data Configuration (Optional and Required only for XTS API Supported Brokers)\nBROKER_API_KEY_MARKET = 'YOUR_BROKER_MARKET_API_KEY'\nBROKER_API_SECRET_MARKET = 'YOUR_BROKER_MARKET_API_SECRET'\n\nREDIRECT_URL = 'https://1010.algofactory.in/angel/callback'\n\n# Valid Brokers Configuration\nVALID_BROKERS = 'fivepaisa,fivepaisaxts,aliceblue,angel,compositedge,dhan,dhan_sandbox,firstock,flattrade,fyers,groww,iifl,kotak,jainam,jainampro,paytm,pocketful,shoonya,tradejini,upstox,wisdom,zebu,zerodha'\n\n# Security Configuration\nAPP_KEY = '3daa0403ce2501ee7432b75bf100048e3cf510d63d2754f952e93d88bf07ea84'\nAPI_KEY_PEPPER = 'a25d94718479b170c16278e321ea6c989358bf499a658fd20c90033cef8ce772'\n\n# Database Configuration\nDATABASE_URL = 'sqlite:///db/algofactory-1010.db' \n\n# Ngrok Configuration\nNGROK_ALLOW = 'FALSE' \n\n# Host Server Configuration\nHOST_SERVER = 'https://1010.algofactory.in'  \n\n# Flask App Configuration\nFLASK_HOST_IP='0.0.0.0'  \nFLASK_PORT='1010' \nFLASK_DEBUG='False' \nFLASK_ENV='production'\n\n# WebSocket Configuration\nWEBSOCKET_HOST='localhost'\nWEBSOCKET_PORT='13010'\nWEBSOCKET_URL='ws://localhost:13010'\n\n# ZeroMQ Configuration\nZMQ_HOST='localhost'\nZMQ_PORT='16010'\n\n# Rate Limit Settings\nLOGIN_RATE_LIMIT_MIN = \"5 per minute\" \nLOGIN_RATE_LIMIT_HOUR = \"25 per hour\"\nAPI_RATE_LIMIT=\"10 per second\"\n\n# API Configuration\nSMART_ORDER_DELAY = '0.5'\nSESSION_EXPIRY_TIME = '03:00'\n\n# CORS Configuration\nCORS_ENABLED = 'TRUE'\nCORS_ALLOWED_ORIGINS = 'https://1010.algofactory.in'\nCORS_ALLOWED_METHODS = 'GET,POST,DELETE,PUT,PATCH'\nCORS_ALLOWED_HEADERS = 'Content-Type,Authorization,X-Requested-With'\nCORS_EXPOSED_HEADERS = ''\nCORS_ALLOW_CREDENTIALS = 'FALSE'\nCORS_MAX_AGE = '86400'\n\n# CSP Configuration\nCSP_ENABLED = 'TRUE'\nCSP_REPORT_ONLY = 'FALSE'\nCSP_DEFAULT_SRC = \"'self'\"\nCSP_SCRIPT_SRC = \"'self' 'unsafe-inline' https://cdn.socket.io https://static.cloudflareinsights.com\"\nCSP_STYLE_SRC = \"'self' 'unsafe-inline'\"\nCSP_IMG_SRC = \"'self' data:\"\nCSP_CONNECT_SRC = \"'self' wss: ws:\"\nCSP_FONT_SRC = \"'self'\"\nCSP_OBJECT_SRC = \"'none'\"\nCSP_MEDIA_SRC = \"'self' data: https://*.amazonaws.com https://*.cloudfront.net\"\nCSP_FRAME_SRC = \"'self'\"\nCSP_FORM_ACTION = \"'self'\"\nCSP_FRAME_ANCESTORS = \"'self'\"\nCSP_BASE_URI = \"'self'\"\nCSP_UPGRADE_INSECURE_REQUESTS = 'FALSE'\nCSP_REPORT_URI = ''\n\n# CSRF Configuration\nCSRF_ENABLED = 'TRUE'\nCSRF_TIME_LIMIT = ''\n", "path": "ubuntu/algofactory-multi/instances/algofactory-1010/.env", "language": "properties", "prefixBegin": 0, "suffixEnd": 0}}], ["253865cc-f19e-46f7-87a8-a615035d4dd1", {"value": {"selectedCode": "", "prefix": "# Instance 1010 Configuration\nINSTANCE_ID=1010\nBROKER_API_KEY=MZA0cLWq\nBROKER_API_SECRET=XIA6RJ3HPG4ZRKKYJLIZ6ROKAM\nREDIRECT_URL=https://1010.algofactory.in/angel/callback\nVALID_BROKERS=fivepaisa,fivepaisaxts,aliceblue,angel,compositedge,dhan,dhan_sandbox,firstock,flattrade,fyers,groww,iifl,kotak,jainam,jainampro,paytm,pocketful,shoonya,tradejini,upstox,wisdom,zebu,zerodha\nAPP_KEY=3daa0403ce2501ee7432b75bf100048e3cf510d63d2754f952e93d88bf07ea84\nAPI_KEY_PEPPER=a25d94718479b170c16278e321ea6c989358bf499a658fd20c90033cef8ce772\nDATABASE_URL=sqlite:///db/algofactory-1010.db\nNGROK_ALLOW=FALSE\nHOST_SERVER=https://1010.algofactory.in\nFLASK_HOST_IP=0.0.0.0\nFLASK_PORT=1010\nFLASK_DEBUG=False\nFLASK_ENV=production\nWEBSOCKET_HOST=localhost\nWEBSOCKET_PORT=13010\nWEBSOCKET_URL=ws://localhost:13010\nZMQ_HOST=localhost\nZMQ_PORT=16010\nLOGIN_RATE_LIMIT_MIN=5 per minute\nLOGIN_RATE_LIMIT_HOUR=25 per hour\nAPI_RATE_LIMIT=10 per second\nSMART_ORDER_DELAY=0.5\nSESSION_EXPIRY_TIME=03:00\nCORS_ENABLED=TRUE\nCORS_ALLOWED_ORIGINS=https://1010.algofactory.in\nCORS_ALLOWED_METHODS=GET,POST,DELETE,PUT,PATCH\nCORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Requested-With\nCORS_EXPOSED_HEADERS=\nCORS_ALLOW_CREDENTIALS=FALSE\nCORS_MAX_AGE=86400\nCSP_ENABLED=TRUE\nCSP_REPORT_ONLY=FALSE\nCSP_DEFAULT_SRC='self'\nCSP_SCRIPT_SRC='self' 'unsafe-inline' https://cdn.socket.io https://static.cloudflareinsights.com\nCSP_STYLE_SRC='self' 'unsafe-inline'\nCSP_IMG_SRC='self' data:\nCSP_CONNECT_SRC='self' wss: ws:\nCSP_FONT_SRC='self'\nCSP_OBJECT_SRC='none'\nCSP_MEDIA_SRC='self' data: https://*.amazonaws.com https://*.cloudfront.net\nCSP_FRAME_SRC='self'\nCSP_FORM_ACTION='self'\nCSP_FRAME_ANCESTORS='self'\nCSP_BASE_URI='self'\nCSP_UPGRADE_INSECURE_REQUESTS=FALSE\nCSP_REPORT_URI=\nCSRF_ENABLED=TRUE\nCSRF_TIME_LIMIT=\n", "suffix": "", "path": "ubuntu/algofactory-multi/instances/algofactory-1010/.env", "language": "properties", "prefixBegin": 0, "suffixEnd": 0}}], ["6319aa3e-e4a3-4805-aa21-5d73ee2777f4", {"value": {"selectedCode": "", "prefix": "# Broker Configuration\nBROKER_API_KEY = 'MZA0cLWq'\nBROKER_API_SECRET = 'XIA6RJ3HPG4ZRKKYJLIZ6ROKAM'\n\n# Market Data Configuration (Optional and Required only for XTS API Supported Brokers)\n\nBROKER_API_KEY_MARKET = 'YOUR_BROKER_MARKET_API_KEY'\nBROKER_API_SECRET_MARKET = 'YOUR_BROKER_MARKET_API_SECRET'\n\nREDIRECT_URL = 'https://1010.algofactory.in/angel/callback'  # Change if different\n\n# Valid Brokers Configuration\nVALID_BROKERS = 'fivepaisa,fivepaisaxts,aliceblue,angel,compositedge,dhan,dhan_sandbox,firstock,flattrade,fyers,groww,iifl,kotak,jainam,jainampro,paytm,pocketful,shoonya,tradejini,upstox,wisdom,zebu,zerodha'\n\n# Security Configuration\n# IMPORTANT: Generate new random values for both keys during setup!\n\n# AlgoFactory Application Key\nAPP_KEY = '3daa0403ce2501ee7432b75bf100048e3cf510d63d2754f952e93d88bf07ea84'\n\n# Security Pepper - Used for hashing/encryption of sensitive data\n# This is used for:\n# 1. API key hashing\n# 2. User password hashing\n# 3. Broker auth token encryption\n# Generate a new random string during setup using: python -c \"import secrets; print(secrets.token_hex(32))\"\nAPI_KEY_PEPPER = 'a25d94718479b170c16278e321ea6c989358bf499a658fd20c90033cef8ce772'\n\n# AlgoFactory Database Configuration\nDATABASE_URL = 'sqlite:///db/algofactory-1010.db'\n\n# AlgoFactory Ngrok Configuration\nNGROK_ALLOW = 'FALSE'\n\n# AlgoFactory Hosted Server (Custom Domain Name) or Ngrok Domain Configuration\n# Change to your custom domain or Ngrok domain\nHOST_SERVER = 'https://1010.algofactory.in'\n\n# AlgoFactory Flask App Host and Port Configuration\n# For 0.0.0.0 (accessible from other devices on the network)\n# Flask Environment - development or production\nFLASK_HOST_IP='0.0.0.0'\nFLASK_PORT='1010'\nFLASK_DEBUG='False'\nFLASK_ENV='production'\n\n# WebSocket Configuration\nWEBSOCKET_HOST='localhost'\nWEBSOCKET_PORT='13010'\nWEBSOCKET_URL='ws://localhost:13010'\n\n", "suffix": "# ZeroMQ Configuration\nZMQ_HOST='localhost'\nZMQ_PORT='16010'\n\n# AlgoFactory Rate Limit Settings\nLOGIN_RATE_LIMIT_MIN = \"5 per minute\"\nLOGIN_RATE_LIMIT_HOUR = \"25 per hour\"\nAPI_RATE_LIMIT=\"10 per second\"\n\n# AlgoFactory API Configuration\n\n# Required to give 0.5 second to 1 second delay between multi-legged option strategies\n# Single legged orders are not affected by this setting.\nSMART_ORDER_DELAY = '0.5'\n\n# Session Expiry Time (24-hour format, IST)\n# All user sessions will automatically expire at this time daily\nSESSION_EXPIRY_TIME = '03:00'\n\n# AlgoFactory CORS (Cross-Origin Resource Sharing) Configuration\n# Set to TRUE to enable CORS support, <PERSON><PERSON><PERSON> to disable\nCORS_ENABLED = 'TRUE'\n\n# Comma-separated list of allowed origins (domains)\n# Example: http://localhost:3000,https://example.com\n# Use '*' to allow all origins (not recommended for production)\nCORS_ALLOWED_ORIGINS = 'https://1010.algofactory.in'\n\n# Comma-separated list of allowed HTTP methods\n# Default: GET,POST\nCORS_ALLOWED_METHODS = 'GET,POST,DELETE,PUT,PATCH'\n\n# Comma-separated list of allowed headers\n# Default Flask-CORS values will be used if not specified\nCORS_ALLOWED_HEADERS = 'Content-Type,Authorization,X-Requested-With'\n\n# Comma-separated list of headers exposed to the browser\nCORS_EXPOSED_HEADERS = ''\n\n# Whether to allow credentials (cookies, authorization headers)\n# Set to TRUE only if you need to support credentials\nCORS_ALLOW_CREDENTIALS = 'FALSE'\n\n# Max age (in seconds) for browser to cache preflight requests\n# Default: 86400 (24 hours)\nCORS_MAX_AGE = '86400'\n\n# AlgoFactory Content Security Policy (CSP) Configuration\n# Set to TRUE to enable CSP, FALSE to disable\nCSP_ENABLED = 'TRUE'\n\n# Set to TRUE to use Content-Security-Policy-Report-Only mode (testing without blocking)\n# This will report violations but not block content\nCSP_REPORT_ONLY = 'FALSE'\n\n# Default source directive - restricts all resource types by default\nCSP_DEFAULT_SRC = \"'self'\"\n\n# Script source directive - controls where scripts can be loaded from\n# Includes Socket.IO CDN which is required by the application\n# 'unsafe-inline' is needed for Socket.IO to function properly\n# Cloudflare Insights is used for analytics\nCSP_SCRIPT_SRC = \"'self' 'unsafe-inline' https://cdn.socket.io https://static.cloudflareinsights.com\"\n\n# Style source directive - controls where styles can be loaded from\n# 'unsafe-inline' is needed for some inline styles in the application\nCSP_STYLE_SRC = \"'self' 'unsafe-inline'\"\n\n# Image source directive - controls where images can be loaded from\n# 'data:' allows base64 encoded images\nCSP_IMG_SRC = \"'self' data:\"\n\n# Connect source directive - controls what network connections are allowed\n# Includes WebSocket connections needed for real-time updates\nCSP_CONNECT_SRC = \"'self' wss: ws:\"\n\n# Font source directive - controls where fonts can be loaded from\nCSP_FONT_SRC = \"'self'\"\n\n# Object source directive - controls where plugins can be loaded from\n# 'none' disables all object, embed, and applet elements\nCSP_OBJECT_SRC = \"'none'\"\n\n# Media source directive - controls where audio and video can be loaded from\n# Allows audio alerts from your domain and potentially CDN sources in the future\nCSP_MEDIA_SRC = \"'self' data: https://*.amazonaws.com https://*.cloudfront.net\"\n\n# Frame source directive - controls where iframes can be loaded from\n# If you integrate with TradingView or other platforms, you may need to add their domains\nCSP_FRAME_SRC = \"'self'\"\n\n# Form action directive - restricts where forms can be submitted to\nCSP_FORM_ACTION = \"'self'\"\n\n# Frame ancestors directive - controls which sites can embed your site in frames\n# This helps prevent clickjacking attacks\nCSP_FRAME_ANCESTORS = \"'self'\"\n\n# Base URI directive - restricts what base URIs can be used\nCSP_BASE_URI = \"'self'\"\n\n# Set to TRUE to upgrade insecure (HTTP) requests to HTTPS\n# Recommended for production environments\nCSP_UPGRADE_INSECURE_REQUESTS = 'FALSE'\n\n# URI to report CSP violations to (optional)\n# Example: /csp-report\nCSP_REPORT_URI = ''\n\n# CSRF (Cross-Site Request Forgery) Protection Configuration\n# Set to TRUE to enable CSRF protection, FALSE to disable\nCSRF_ENABLED = 'TRUE'\n\n# CSRF Token Time Limit (in seconds)\n# Leave empty for no time limit (tokens valid for entire session)\n# Example: 3600 = 1 hour, 86400 = 24 hours\nCSRF_TIME_LIMIT = ''\n", "path": "ubuntu/algofactory-multi/instances/algofactory-1010/.env", "language": "properties", "prefixBegin": 0, "suffixEnd": 0}}], ["f52fcd17-a56e-4080-97c0-786ba187fc3f", {"value": {"selectedCode": "", "prefix": "# Instance Configuration\n", "suffix": "INSTANCE_ID = '8010'\n\n# Broker Configuration\nBROKER_API_KEY = 'MZA0cLWq'\nBROKER_API_SECRET = 'XIA6RJ3HPG4ZRKKYJLIZ6ROKAM'\n\n# Market Data Configuration (Optional and Required only for XTS API Supported Brokers)\n\nBROKER_API_KEY_MARKET = 'YOUR_BROKER_MARKET_API_KEY'\nBROKER_API_SECRET_MARKET = 'YOUR_BROKER_MARKET_API_SECRET'\n\nREDIRECT_URL = 'https://8010.algofactory.in/angel/callback'  # Change if different\n\n# Valid Brokers Configuration\nVALID_BROKERS = 'fivepaisa,fivepaisaxts,aliceblue,angel,compositedge,dhan,dhan_sandbox,firstock,flattrade,fyers,groww,iifl,kotak,jainam,jainampro,paytm,pocketful,shoonya,tradejini,upstox,wisdom,zebu,zerodha'\n\n# Security Configuration\n# IMPORTANT: Generate new random values for both keys during setup!\n\n# AlgoFactory Application Key\nAPP_KEY = '3daa0403ce2501ee7432b75bf100048e3cf510d63d2754f952e93d88bf07ea84'\n\n# Security Pepper - Used for hashing/encryption of sensitive data\n# This is used for:\n# 1. API key hashing\n# 2. User password hashing\n# 3. Broker auth token encryption\n# Generate a new random string during setup using: python -c \"import secrets; print(secrets.token_hex(32))\"\nAPI_KEY_PEPPER = 'a25d94718479b170c16278e321ea6c989358bf499a658fd20c90033cef8ce772'\n\n# AlgoFactory Database Configuration\nDATABASE_URL = 'sqlite:///db/algofactory-8010.db'\n\n# AlgoFactory Ngrok Configuration\nNGROK_ALLOW = 'FALSE'\n\n# AlgoFactory Hosted Server (Custom Domain Name) or Ngrok Domain Configuration\n# Change to your custom domain or Ngrok domain\nHOST_SERVER = 'https://8010.algofactory.in'\n\n# AlgoFactory Flask App Host and Port Configuration\n# For 0.0.0.0 (accessible from other devices on the network)\n# Flask Environment - development or production\nFLASK_HOST_IP='0.0.0.0'\nFLASK_PORT='8010'\nFLASK_DEBUG='False'\nFLASK_ENV='production'\n\n# WebSocket Configuration\nWEBSOCKET_HOST='localhost'\nWEBSOCKET_PORT='20010'\nWEBSOCKET_URL='ws://localhost:20010'\n\n# ZeroMQ Configuration\nZMQ_HOST='localhost'\nZMQ_PORT='23010'\n\n# AlgoFactory Rate Limit Settings\nLOGIN_RATE_LIMIT_MIN = \"5 per minute\"\nLOGIN_RATE_LIMIT_HOUR = \"25 per hour\"\nAPI_RATE_LIMIT=\"10 per second\"\n\n# AlgoFactory API Configuration\n\n# Required to give 0.5 second to 1 second delay between multi-legged option strategies\n# Single legged orders are not affected by this setting.\nSMART_ORDER_DELAY = '0.5'\n\n# Session Expiry Time (24-hour format, IST)\n# All user sessions will automatically expire at this time daily\nSESSION_EXPIRY_TIME = '03:00'\n\n# AlgoFactory CORS (Cross-Origin Resource Sharing) Configuration\n# Set to TRUE to enable CORS support, FALSE to disable\nCORS_ENABLED = 'TRUE'\n\n# Comma-separated list of allowed origins (domains)\n# Example: http://localhost:3000,https://example.com\n# Use '*' to allow all origins (not recommended for production)\nCORS_ALLOWED_ORIGINS = 'https://8010.algofactory.in'\n\n# Comma-separated list of allowed HTTP methods\n# Default: GET,POST\nCORS_ALLOWED_METHODS = 'GET,POST,DELETE,PUT,PATCH'\n\n# Comma-separated list of allowed headers\n# Default Flask-CORS values will be used if not specified\nCORS_ALLOWED_HEADERS = 'Content-Type,Authorization,X-Requested-With'\n\n# Comma-separated list of headers exposed to the browser\nCORS_EXPOSED_HEADERS = ''\n\n# Whether to allow credentials (cookies, authorization headers)\n# Set to TRUE only if you need to support credentials\nCORS_ALLOW_CREDENTIALS = 'FALSE'\n\n# Max age (in seconds) for browser to cache preflight requests\n# Default: 86400 (24 hours)\nCORS_MAX_AGE = '86400'\n\n# AlgoFactory Content Security Policy (CSP) Configuration\n# Set to TRUE to enable CSP, FALSE to disable\nCSP_ENABLED = 'TRUE'\n\n# Set to TRUE to use Content-Security-Policy-Report-Only mode (testing without blocking)\n# This will report violations but not block content\nCSP_REPORT_ONLY = 'FALSE'\n\n# Default source directive - restricts all resource types by default\nCSP_DEFAULT_SRC = \"'self'\"\n\n# Script source directive - controls where scripts can be loaded from\n# Includes Socket.IO CDN which is required by the application\n# 'unsafe-inline' is needed for Socket.IO to function properly\n# Cloudflare Insights is used for analytics\nCSP_SCRIPT_SRC = \"'self' 'unsafe-inline' https://cdn.socket.io https://static.cloudflareinsights.com\"\n\n# Style source directive - controls where styles can be loaded from\n# 'unsafe-inline' is needed for some inline styles in the application\nCSP_STYLE_SRC = \"'self' 'unsafe-inline'\"\n\n# Image source directive - controls where images can be loaded from\n# 'data:' allows base64 encoded images\nCSP_IMG_SRC = \"'self' data:\"\n\n# Connect source directive - controls what network connections are allowed\n# Includes WebSocket connections needed for real-time updates\nCSP_CONNECT_SRC = \"'self' wss: ws:\"\n\n# Font source directive - controls where fonts can be loaded from\nCSP_FONT_SRC = \"'self'\"\n\n# Object source directive - controls where plugins can be loaded from\n# 'none' disables all object, embed, and applet elements\nCSP_OBJECT_SRC = \"'none'\"\n\n# Media source directive - controls where audio and video can be loaded from\n# Allows audio alerts from your domain and potentially CDN sources in the future\nCSP_MEDIA_SRC = \"'self' data: https://*.amazonaws.com https://*.cloudfront.net\"\n\n# Frame source directive - controls where iframes can be loaded from\n# If you integrate with TradingView or other platforms, you may need to add their domains\nCSP_FRAME_SRC = \"'self'\"\n\n# Form action directive - restricts where forms can be submitted to\nCSP_FORM_ACTION = \"'self'\"\n\n# Frame ancestors directive - controls which sites can embed your site in frames\n# This helps prevent clickjacking attacks\nCSP_FRAME_ANCESTORS = \"'self'\"\n\n# Base URI directive - restricts what base URIs can be used\nCSP_BASE_URI = \"'self'\"\n\n# Set to TRUE to upgrade insecure (HTTP) requests to HTTPS\n# Recommended for production environments\nCSP_UPGRADE_INSECURE_REQUESTS = 'FALSE'\n\n# URI to report CSP violations to (optional)\n# Example: /csp-report\nCSP_REPORT_URI = ''\n\n# CSRF (Cross-Site Request Forgery) Protection Configuration\n# Set to TRUE to enable CSRF protection, FALSE to disable\nCSRF_ENABLED = 'TRUE'\n\n# CSRF Token Time Limit (in seconds)\n# Leave empty for no time limit (tokens valid for entire session)\n# Example: 3600 = 1 hour, 86400 = 24 hours\nCSRF_TIME_LIMIT = ''\n", "path": "ubuntu/algofactory-multi/instances/algofactory-8010/.env", "language": "properties", "prefixBegin": 0, "suffixEnd": 0}}], ["07d29db4-0fc0-4f29-a39c-658f73620b6c", {"value": {"selectedCode": "", "prefix": "# Broker Configuration\n", "suffix": "BROKER_API_KEY = 'MZA0cLWq'\nBROKER_API_SECRET = 'XIA6RJ3HPG4ZRKKYJLIZ6ROKAM'\nBROKER_API_KEY_MARKET = 'YOUR_BROKER_MARKET_API_KEY'\nBROKER_API_SECRET_MARKET = 'YOUR_BROKER_MARKET_API_SECRET'\n\n# Application Configuration\nAPP_KEY = '3daa0403ce2501ee7432b75bf100048e3cf510d63d2754f952e93d88bf07ea84'\nAPI_KEY_PEPPER = 'a25d94718479b170c16278e321ea6c989358bf499a658fd20c90033cef8ce772'\n\n# Flask Configuration\nFLASK_ENV = 'production'\nFLASK_DEBUG = 'False'\nFLASK_HOST_IP = '0.0.0.0'\nFLASK_PORT = '1010'\n\n# Database Configuration\nDATABASE_URL = 'sqlite:///db/algofactory-1010.db'\n\n# WebSocket Configuration\nWEBSOCKET_HOST = 'localhost'\nWEBSOCKET_PORT = '13010'\nWEBSOCKET_URL = 'ws://localhost:13010'\n\n# ZMQ Configuration\nZMQ_HOST = 'localhost'\nZMQ_PORT = '16010'\n\n# Server Configuration\nHOST_SERVER = 'https://1010.algofactory.in'\nREDIRECT_URL = 'https://1010.algofactory.in/angel/callback'\n\n# Security Configuration\nCORS_ENABLED = 'TRUE'\nCORS_ALLOWED_ORIGINS = 'https://1010.algofactory.in'\nCORS_ALLOWED_METHODS = 'GET,POST,DELETE,PUT,PATCH'\nCORS_ALLOWED_HEADERS = 'Content-Type,Authorization,X-Requested-With'\nCORS_ALLOW_CREDENTIALS = 'FALSE'\nCORS_EXPOSED_HEADERS = ''\nCORS_MAX_AGE = '86400'\n\n# CSP Configuration\nCSP_ENABLED = 'TRUE'\nCSP_DEFAULT_SRC = \"'self'\"\nCSP_SCRIPT_SRC = \"'self' 'unsafe-inline' https://cdn.socket.io https://static.cloudflareinsights.com\"\nCSP_STYLE_SRC = \"'self' 'unsafe-inline'\"\nCSP_IMG_SRC = \"'self' data:\"\nCSP_FONT_SRC = \"'self'\"\nCSP_CONNECT_SRC = \"'self' wss: ws:\"\nCSP_MEDIA_SRC = \"'self' data: https://*.amazonaws.com https://*.cloudfront.net\"\nCSP_OBJECT_SRC = \"'none'\"\nCSP_BASE_URI = \"'self'\"\nCSP_FORM_ACTION = \"'self'\"\nCSP_FRAME_ANCESTORS = \"'self'\"\nCSP_FRAME_SRC = \"'self'\"\nCSP_UPGRADE_INSECURE_REQUESTS = 'FALSE'\nCSP_REPORT_ONLY = 'FALSE'\nCSP_REPORT_URI = ''\n\n# CSRF Configuration\nCSRF_ENABLED = 'TRUE'\n\n# Rate Limiting\nAPI_RATE_LIMIT = '10 per second'\nLOGIN_RATE_LIMIT_MIN = '5 per minute'\nLOGIN_RATE_LIMIT_HOUR = '25 per hour'\n\n# Session Configuration\nSESSION_EXPIRY_TIME = '03:00'\n\n# Trading Configuration\nSMART_ORDER_DELAY = '0.5'\n\n# Broker List\nVALID_BROKERS = 'fivepaisa,fivepaisaxts,aliceblue,angel,compositedge,dhan,dhan_sandbox,firstock,flattrade,fyers,groww,iifl,kotak,jainam,jainampro,paytm,pocketful,shoonya,tradejini,upstox,wisdom,zebu,zerodha'\n\n# Development Configuration\nNGROK_ALLOW = 'FALSE'\n", "path": "ubuntu/algofactory-multi/algofactory-1010/.env", "language": "properties", "prefixBegin": 0, "suffixEnd": 0}}], ["ac1a1baf-d3e2-48f1-8fdb-7e2e01972abe", {"value": {"selectedCode": "", "prefix": "#!/bin/bash\n", "suffix": "\n# AlgoFactory Robust Startup Script\n# This script automatically starts app.py with full error handling and recovery\n\nset -e  # Exit on any error\n\n# Configuration\nPROJECT_DIR=\"/home/<USER>/myproject/algofactory\"\nSHARED_VENV=\"/home/<USER>/shared-venv\"\nAPP_FILE=\"app.py\"\nLOG_DIR=\"$PROJECT_DIR/logs\"\nPID_FILE=\"$PROJECT_DIR/app.pid\"\nMAX_RETRIES=5\nRETRY_DELAY=10\n\n# Colors for output\nRED='\\033[0;31m'\nGREEN='\\033[0;32m'\nYELLOW='\\033[1;33m'\nBLUE='\\033[0;34m'\nNC='\\033[0m' # No Color\n\n# Logging function\nlog() {\n    echo -e \"${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1\"\n}\n\nerror() {\n    echo -e \"${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1\" >&2\n}\n\nsuccess() {\n    echo -e \"${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1\"\n}\n\nwarning() {\n    echo -e \"${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1\"\n}\n\n# Function to check if app is running\nis_app_running() {\n    if [ -f \"$PID_FILE\" ]; then\n        local pid=$(cat \"$PID_FILE\")\n        if ps -p \"$pid\" > /dev/null 2>&1; then\n            return 0\n        else\n            rm -f \"$PID_FILE\"\n            return 1\n        fi\n    fi\n    return 1\n}\n\n# Function to stop the app\nstop_app() {\n    if [ -f \"$PID_FILE\" ]; then\n        local pid=$(cat \"$PID_FILE\")\n        log \"Stopping application (PID: $pid)...\"\n        kill -TERM \"$pid\" 2>/dev/null || true\n        sleep 5\n        if ps -p \"$pid\" > /dev/null 2>&1; then\n            warning \"Application didn't stop gracefully, forcing kill...\"\n            kill -KILL \"$pid\" 2>/dev/null || true\n        fi\n        rm -f \"$PID_FILE\"\n        success \"Application stopped\"\n    fi\n}\n\n# Function to setup environment\nsetup_environment() {\n    log \"Setting up environment...\"\n\n    # Change to project directory\n    cd \"$PROJECT_DIR\" || {\n        error \"Failed to change to project directory: $PROJECT_DIR\"\n        exit 1\n    }\n\n    # Create necessary directories with full permissions\n    log \"Creating necessary directories...\"\n    mkdir -p db logs tmp static/uploads\n\n    # Set full permissions (777) for all necessary directories\n    chmod -R 777 db logs tmp static 2>/dev/null || {\n        warning \"Could not set permissions on some directories (may be mounted volumes)\"\n    }\n\n    # Check if shared virtual environment exists\n    if [ ! -d \"$SHARED_VENV\" ]; then\n        error \"Shared virtual environment not found at: $SHARED_VENV\"\n        log \"Creating shared virtual environment...\"\n        python3 -m venv \"$SHARED_VENV\" || {\n            error \"Failed to create virtual environment\"\n            exit 1\n        }\n    fi\n\n    # Check if virtual environment has Python\n    if [ ! -f \"$SHARED_VENV/bin/python\" ]; then\n        error \"Python not found in virtual environment: $SHARED_VENV/bin/python\"\n        exit 1\n    fi\n\n    success \"Environment setup completed\"\n}\n\n# Function to install/update dependencies\ninstall_dependencies() {\n    log \"Installing/updating dependencies...\"\n\n    # Activate virtual environment\n    source \"$SHARED_VENV/bin/activate\" || {\n        error \"Failed to activate virtual environment\"\n        exit 1\n    }\n\n    # Upgrade pip\n    pip install --upgrade pip > /dev/null 2>&1 || {\n        warning \"Failed to upgrade pip\"\n    }\n\n    # Install requirements if file exists\n    if [ -f \"requirements.txt\" ]; then\n        log \"Installing requirements from requirements.txt...\"\n        pip install -r requirements.txt || {\n            error \"Failed to install requirements\"\n            exit 1\n        }\n    fi\n\n    # Install additional packages that might be needed\n    pip install gunicorn eventlet python-dotenv > /dev/null 2>&1 || {\n        warning \"Failed to install some additional packages\"\n    }\n\n    success \"Dependencies installed successfully\"\n}\n\n# Function to start the application\nstart_app() {\n    local retry_count=0\n\n    while [ $retry_count -lt $MAX_RETRIES ]; do\n        log \"Starting AlgoFactory application (attempt $((retry_count + 1))/$MAX_RETRIES)...\"\n\n        # Activate virtual environment\n        source \"$SHARED_VENV/bin/activate\" || {\n            error \"Failed to activate virtual environment\"\n            exit 1\n        }\n\n        # Check if app.py exists\n        if [ ! -f \"$APP_FILE\" ]; then\n            error \"Application file not found: $APP_FILE\"\n            exit 1\n        fi\n\n        # Start the application with gunicorn\n        nohup \"$SHARED_VENV/bin/gunicorn\" \\\n            --bind=0.0.0.0:5000 \\\n            --worker-class=eventlet \\\n            --workers=1 \\\n            --timeout=120 \\\n            --keep-alive=2 \\\n            --max-requests=1000 \\\n            --max-requests-jitter=100 \\\n            --preload \\\n            --log-level=info \\\n            --access-logfile=\"$LOG_DIR/access.log\" \\\n            --error-logfile=\"$LOG_DIR/error.log\" \\\n            --capture-output \\\n            --enable-stdio-inheritance \\\n            app:app > \"$LOG_DIR/app.log\" 2>&1 &\n\n        local app_pid=$!\n        echo $app_pid > \"$PID_FILE\"\n\n        # Wait a moment and check if the app started successfully\n        sleep 5\n\n        if ps -p \"$app_pid\" > /dev/null 2>&1; then\n            success \"AlgoFactory application started successfully (PID: $app_pid)\"\n            success \"Application is running on http://0.0.0.0:5000\"\n            success \"Logs are available in: $LOG_DIR/\"\n            return 0\n        else\n            error \"Application failed to start (attempt $((retry_count + 1)))\"\n            rm -f \"$PID_FILE\"\n\n            # Show last few lines of error log\n            if [ -f \"$LOG_DIR/error.log\" ]; then\n                error \"Last few lines from error log:\"\n                tail -10 \"$LOG_DIR/error.log\" | while read line; do\n                    error \"  $line\"\n                done\n            fi\n\n            retry_count=$((retry_count + 1))\n            if [ $retry_count -lt $MAX_RETRIES ]; then\n                warning \"Retrying in $RETRY_DELAY seconds...\"\n                sleep $RETRY_DELAY\n            fi\n        fi\n    done\n\n    error \"Failed to start application after $MAX_RETRIES attempts\"\n    exit 1\n}\n\n# Function to monitor the application\nmonitor_app() {\n    log \"Starting application monitor...\"\n\n    # Start the application first\n    if ! is_app_running; then\n        start_app\n    fi\n\n    # Monitor loop\n    while true; do\n        if ! is_app_running; then\n            warning \"Application is not running, attempting to restart...\"\n            start_app\n        fi\n        sleep 30  # Check every 30 seconds\n    done\n}\n\n# Main execution\nmain() {\n    log \"=== AlgoFactory Startup Script ===\"\n    log \"Project Directory: $PROJECT_DIR\"\n    log \"Shared Virtual Environment: $SHARED_VENV\"\n    log \"Application File: $APP_FILE\"\n\n    # Handle command line arguments\n    case \"${1:-start}\" in\n        \"start\")\n            if is_app_running; then\n                warning \"Application is already running\"\n                exit 0\n            fi\n            setup_environment\n            install_dependencies\n            start_app\n            ;;\n        \"stop\")\n            stop_app\n            ;;\n        \"restart\")\n            stop_app\n            setup_environment\n            install_dependencies\n            start_app\n            ;;\n        \"status\")\n            if is_app_running; then\n                success \"Application is running (PID: $(cat $PID_FILE))\"\n            else\n                warning \"Application is not running\"\n            fi\n            ;;\n        \"monitor\")\n            setup_environment\n            install_dependencies\n            monitor_app\n            ;;\n        \"logs\")\n            if [ -f \"$LOG_DIR/app.log\" ]; then\n                tail -f \"$LOG_DIR/app.log\"\n            else\n                error \"Log file not found: $LOG_DIR/app.log\"\n            fi\n            ;;\n        *)\n            echo \"Usage: $0 {start|stop|restart|status|monitor|logs}\"\n            echo \"  start   - Start the application\"\n            echo \"  stop    - Stop the application\"\n            echo \"  restart - Restart the application\"\n            echo \"  status  - Check application status\"\n            echo \"  monitor - Start with continuous monitoring\"\n            echo \"  logs    - Show application logs\"\n            exit 1\n            ;;\n    esac\n}\n\n# Trap signals for graceful shutdown\ntrap 'stop_app; exit 0' SIGTERM SIGINT\n\n# Run main function\nmain \"$@\"\n", "path": "ubuntu/myproject/algofactory/start.sh", "language": "shellscript", "prefixBegin": 0, "suffixEnd": 0}}], ["9c6dde0f-947c-4ddd-9830-3e1650fcbbad", {"value": {"selectedCode": "", "prefix": "\"\"\"\r\nWebSocket Proxy Dictionary Iteration Stress Test\r\n\r\nThis script tests the WebSocket proxy server's handling of concurrent dictionary modifications\r\nby creating multiple clients that rapidly connect, subscribe, and disconnect.\r\n\"\"\"\r\n\r\nimport asyncio\r\nimport websockets\r\nimport json\r\nimport random\r\nimport time\r\nimport argparse\r\nimport uuid\r\nimport logging\r\nimport os\r\nfrom datetime import datetime\r\n\r\n# Set up logging\r\nlog_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')\r\nos.makedirs(log_dir, exist_ok=True)\r\nlog_file = os.path.join(log_dir, f'websocket_test_{datetime.now().strftime(\"%Y%m%d_%H%M%S\")}.log')\r\n\r\n# Configure logging\r\nlogging.basicConfig(\r\n    level=logging.INFO,\r\n    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',\r\n    handlers=[\r\n        logging.FileHandler(log_file),\r\n        logging.StreamHandler()\r\n    ]\r\n)\r\n\r\nlogger = logging.getLogger('websocket_test')\r\n\r\n# Test configuration\r\nWEBSOCKET_URI = \"ws://127.0.0.1:20010\"\r\n", "suffix": "API_KEY = \"1a931f67eba14c15f8bfb24c7be25e8944338fab564a5a2a4b807ff1654b38f3\"\r\nCLIENT_COUNT = 10\r\nTEST_DURATION_SECONDS = 30\r\n\r\n# Symbols to use for testing\r\nSYMBOLS = [\r\n    {\"symbol\": \"CRUDEOIL18JUN25FUT\", \"exchange\": \"MCX\"},\r\n    {\"symbol\": \"NIFTY\", \"exchange\": \"NSE_INDEX\"},\r\n    {\"symbol\": \"SENSEX\", \"exchange\": \"BSE_INDEX\"},\r\n    {\"symbol\": \"RELIANCE26JUN25FUT\", \"exchange\": \"NFO\"}\r\n]\r\n\r\nclass WebSocketClient:\r\n    \"\"\"Represents a single WebSocket client for testing\"\"\"\r\n    \r\n    def __init__(self, client_id):\r\n        self.id = client_id\r\n        self.ws = None\r\n        self.connected = False\r\n        self.subscriptions = []\r\n    \r\n    async def connect(self):\r\n        \"\"\"Connect to the WebSocket server and authenticate\"\"\"\r\n        try:\r\n            self.ws = await websockets.connect(WEBSOCKET_URI)\r\n            self.connected = True\r\n            logger.info(f\"[{self.id}] Connected\")\r\n            \r\n            # Send authentication message\r\n            auth_msg = {\r\n                \"action\": \"authenticate\",\r\n                \"api_key\": API_KEY\r\n            }\r\n            await self.ws.send(json.dumps(auth_msg))\r\n            auth_response = json.loads(await self.ws.recv())\r\n            if auth_response.get(\"status\") == \"success\":\r\n                logger.info(f\"[{self.id}] Authentication successful\")\r\n                return True\r\n            else:\r\n                logger.warning(f\"[{self.id}] Authentication failed: {auth_response}\")\r\n                await self.disconnect()\r\n                return False\r\n        except Exception as e:\r\n            logger.error(f\"[{self.id}] Connection error: {e}\")\r\n            self.connected = False\r\n            return False\r\n    \r\n    async def subscribe(self, symbol, exchange, mode=1):\r\n        \"\"\"Subscribe to a market data feed\"\"\"\r\n        if not self.connected:\r\n            return False\r\n        \r\n        try:\r\n            sub_msg = {\r\n                \"action\": \"subscribe\",\r\n                \"symbol\": symbol,\r\n                \"exchange\": exchange,\r\n                \"mode\": mode  # LTP mode\r\n            }\r\n            await self.ws.send(json.dumps(sub_msg))\r\n            response = json.loads(await self.ws.recv())\r\n            if response.get(\"status\") == \"success\":\r\n                self.subscriptions.append(f\"{exchange}:{symbol}\")\r\n                logger.info(f\"[{self.id}] Subscribed to {exchange}:{symbol}\")\r\n                return True\r\n            else:\r\n                logger.warning(f\"[{self.id}] Subscription failed: {response}\")\r\n                return False\r\n        except Exception as e:\r\n            logger.error(f\"[{self.id}] Subscription error: {e}\")\r\n            return False\r\n    \r\n    async def unsubscribe(self, symbol, exchange):\r\n        \"\"\"Unsubscribe from a market data feed\"\"\"\r\n        if not self.connected or f\"{exchange}:{symbol}\" not in self.subscriptions:\r\n            return False\r\n        \r\n        try:\r\n            unsub_msg = {\r\n                \"action\": \"unsubscribe\",\r\n                \"symbol\": symbol,\r\n                \"exchange\": exchange\r\n            }\r\n            await self.ws.send(json.dumps(unsub_msg))\r\n            response = json.loads(await self.ws.recv())\r\n            if response.get(\"status\") in [\"success\", \"partial\"]:\r\n                self.subscriptions.remove(f\"{exchange}:{symbol}\")\r\n                logger.info(f\"[{self.id}] Unsubscribed from {exchange}:{symbol}\")\r\n                return True\r\n            else:\r\n                logger.warning(f\"[{self.id}] Unsubscription failed: {response}\")\r\n                return False\r\n        except Exception as e:\r\n            logger.error(f\"[{self.id}] Unsubscription error: {e}\")\r\n            return False\r\n    \r\n    async def disconnect(self):\r\n        \"\"\"Disconnect from the WebSocket server\"\"\"\r\n        if self.connected and self.ws:\r\n            try:\r\n                await self.ws.close()\r\n                logger.info(f\"[{self.id}] Disconnected\")\r\n            except Exception as e:\r\n                logger.error(f\"[{self.id}] Disconnect error: {e}\")\r\n            finally:\r\n                self.connected = False\r\n                self.subscriptions = []\r\n\r\nasync def listener(client):\r\n    \"\"\"Background task to continuously receive messages\"\"\"\r\n    try:\r\n        while client.connected:\r\n            try:\r\n                message = await asyncio.wait_for(client.ws.recv(), timeout=0.1)\r\n                # We're not interested in the content of the message, just receiving them\r\n                # logger.debug(f\"[{client.id}] Received: {message[:50]}...\")\r\n            except asyncio.TimeoutError:\r\n                # No message received within timeout, continue the loop\r\n                continue\r\n            except websockets.exceptions.ConnectionClosed:\r\n                logger.info(f\"[{client.id}] Connection closed\")\r\n                client.connected = False\r\n                break\r\n    except Exception as e:\r\n        logger.error(f\"[{client.id}] Listener error: {e}\")\r\n        client.connected = False\r\n\r\nasync def random_client_behavior(client, running_flag):\r\n    \"\"\"Exhibit random behavior for a client - connect, subscribe, unsubscribe, disconnect\"\"\"\r\n    # Start listener task\r\n    listener_task = asyncio.create_task(listener(client))\r\n    \r\n    try:\r\n        # Connect\r\n        if not await client.connect():\r\n            return\r\n        \r\n        # Random subscriptions\r\n        for _ in range(random.randint(1, len(SYMBOLS))):\r\n            symbol_info = random.choice(SYMBOLS)\r\n            await client.subscribe(symbol_info[\"symbol\"], symbol_info[\"exchange\"])\r\n            # Brief delay between subscriptions\r\n            await asyncio.sleep(random.uniform(0.05, 0.2))\r\n        \r\n        while running_flag.is_set():\r\n            # Perform random actions\r\n            action = random.choices(\r\n                [\"subscribe\", \"unsubscribe\", \"sleep\"],\r\n                weights=[0.3, 0.3, 0.4],\r\n                k=1\r\n            )[0]\r\n            \r\n            if action == \"subscribe\" and len(client.subscriptions) < len(SYMBOLS):\r\n                # Subscribe to a new symbol\r\n                for symbol_info in SYMBOLS:\r\n                    key = f\"{symbol_info['exchange']}:{symbol_info['symbol']}\"\r\n                    if key not in client.subscriptions:\r\n                        await client.subscribe(symbol_info[\"symbol\"], symbol_info[\"exchange\"])\r\n                        break\r\n                        \r\n            elif action == \"unsubscribe\" and client.subscriptions:\r\n                # Unsubscribe from a random symbol\r\n                sub_key = random.choice(client.subscriptions)\r\n                exchange, symbol = sub_key.split(\":\")\r\n                await client.unsubscribe(symbol, exchange)\r\n            \r\n            # Sleep briefly between actions\r\n            await asyncio.sleep(random.uniform(0.1, 0.5))\r\n        \r\n    except Exception as e:\r\n        print(f\"[{client.id}] Error during random behavior: {e}\")\r\n    finally:\r\n        # Clean up\r\n        await client.disconnect()\r\n        # Cancel listener task\r\n        listener_task.cancel()\r\n        try:\r\n            await listener_task\r\n        except asyncio.CancelledError:\r\n            pass\r\n\r\nasync def run_test(client_count, duration):\r\n    \"\"\"Run the stress test with multiple clients for the specified duration\"\"\"\r\n    header = f\"\\n{'='*60}\\nWEBSOCKET PROXY DICTIONARY ITERATION STRESS TEST\\n{'='*60}\\n\"\r\n    header += f\"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\"\r\n    header += f\"Number of clients: {client_count}\\n\"\r\n    header += f\"Test duration: {duration} seconds\\n\"\r\n    header += f\"Logging to: {log_file}\\n\"\r\n    header += f\"{'='*60}\\n\"\r\n    \r\n    print(header)\r\n    logger.info(header)\r\n    \r\n    # Create clients\r\n    clients = [WebSocketClient(f\"Client-{i+1}\") for i in range(client_count)]\r\n    \r\n    # Flag to signal tasks to stop\r\n    running = asyncio.Event()\r\n    running.set()\r\n    \r\n    # Create tasks for each client\r\n    tasks = [asyncio.create_task(random_client_behavior(client, running)) for client in clients]\r\n    \r\n    # Create staggered connections\r\n    clients_in_flight = []\r\n    for i, client in enumerate(clients):\r\n        # Add client to tracking list\r\n        clients_in_flight.append(client)\r\n        \r\n        # Every 3rd client, disconnect a previous one to create churn\r\n        if i > 5 and i % 3 == 0 and clients_in_flight:\r\n            disconnected = clients_in_flight.pop(0)\r\n            await disconnected.disconnect()\r\n        \r\n        # Spread out connections\r\n        await asyncio.sleep(random.uniform(0.3, 0.7))\r\n    \r\n    # Run for specified duration\r\n    try:\r\n        await asyncio.sleep(duration)\r\n    finally:\r\n        shutdown_msg = \"\\nTest duration complete. Shutting down...\"\r\n        print(shutdown_msg)\r\n        logger.info(shutdown_msg)\r\n        # Signal tasks to stop\r\n        running.clear()\r\n        \r\n        # Wait for all tasks to complete\r\n        await asyncio.gather(*tasks, return_exceptions=True)\r\n    \r\n    summary = f\"\\n{'='*60}\\n\"\r\n    summary += f\"TEST COMPLETED at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\"\r\n    summary += f\"No dictionary size errors detected during the test.\\n\"\r\n    summary += f\"Log file saved to: {log_file}\\n\"\r\n    summary += f\"{'='*60}\\n\"\r\n    \r\n    print(summary)\r\n    logger.info(summary)\r\n\r\ndef main():\r\n    \"\"\"Main entry point\"\"\"\r\n    parser = argparse.ArgumentParser(description=\"WebSocket Proxy Dictionary Iteration Stress Test\")\r\n    parser.add_argument(\"--clients\", type=int, default=CLIENT_COUNT, help=\"Number of clients to simulate\")\r\n    parser.add_argument(\"--duration\", type=int, default=TEST_DURATION_SECONDS, help=\"Test duration in seconds\")\r\n    args = parser.parse_args()\r\n    \r\n    try:\r\n        asyncio.run(run_test(args.clients, args.duration))\r\n    except KeyboardInterrupt:\r\n        print(\"\\nTest interrupted by user.\")\r\n    \r\nif __name__ == \"__main__\":\r\n    main()\r\n", "path": "ubuntu/algofactory-multi/algofactory-8010/test/iteration_test.py", "language": "python", "prefixBegin": 0, "suffixEnd": 0}}], ["c17a8373-e034-487a-a1a0-************", {"value": {"selectedCode": "API_KEY = \"1a931f67eba14c15f8bfb24c7be25e8944338fab564a5a2a4b807ff1654b38f3\"", "prefix": "\"\"\"\r\nWebSocket Proxy Dictionary Iteration Stress Test\r\n\r\nThis script tests the WebSocket proxy server's handling of concurrent dictionary modifications\r\nby creating multiple clients that rapidly connect, subscribe, and disconnect.\r\n\"\"\"\r\n\r\nimport asyncio\r\nimport websockets\r\nimport json\r\nimport random\r\nimport time\r\nimport argparse\r\nimport uuid\r\nimport logging\r\nimport os\r\nfrom datetime import datetime\r\n\r\n# Set up logging\r\nlog_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')\r\nos.makedirs(log_dir, exist_ok=True)\r\nlog_file = os.path.join(log_dir, f'websocket_test_{datetime.now().strftime(\"%Y%m%d_%H%M%S\")}.log')\r\n\r\n# Configure logging\r\nlogging.basicConfig(\r\n    level=logging.INFO,\r\n    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',\r\n    handlers=[\r\n        logging.FileHandler(log_file),\r\n        logging.StreamHandler()\r\n    ]\r\n)\r\n\r\nlogger = logging.getLogger('websocket_test')\r\n\r\n# Test configuration\r\nWEBSOCKET_URI = \"ws://127.0.0.1:20010\"\r\n", "suffix": "\r\nCLIENT_COUNT = 10\r\nTEST_DURATION_SECONDS = 30\r\n\r\n# Symbols to use for testing\r\nSYMBOLS = [\r\n    {\"symbol\": \"CRUDEOIL18JUN25FUT\", \"exchange\": \"MCX\"},\r\n    {\"symbol\": \"NIFTY\", \"exchange\": \"NSE_INDEX\"},\r\n    {\"symbol\": \"SENSEX\", \"exchange\": \"BSE_INDEX\"},\r\n    {\"symbol\": \"REL<PERSON>NCE26JUN25FUT\", \"exchange\": \"NFO\"}\r\n]\r\n\r\nclass WebSocketClient:\r\n    \"\"\"Represents a single WebSocket client for testing\"\"\"\r\n    \r\n    def __init__(self, client_id):\r\n        self.id = client_id\r\n        self.ws = None\r\n        self.connected = False\r\n        self.subscriptions = []\r\n    \r\n    async def connect(self):\r\n        \"\"\"Connect to the WebSocket server and authenticate\"\"\"\r\n        try:\r\n            self.ws = await websockets.connect(WEBSOCKET_URI)\r\n            self.connected = True\r\n            logger.info(f\"[{self.id}] Connected\")\r\n            \r\n            # Send authentication message\r\n            auth_msg = {\r\n                \"action\": \"authenticate\",\r\n                \"api_key\": API_KEY\r\n            }\r\n            await self.ws.send(json.dumps(auth_msg))\r\n            auth_response = json.loads(await self.ws.recv())\r\n            if auth_response.get(\"status\") == \"success\":\r\n                logger.info(f\"[{self.id}] Authentication successful\")\r\n                return True\r\n            else:\r\n                logger.warning(f\"[{self.id}] Authentication failed: {auth_response}\")\r\n                await self.disconnect()\r\n                return False\r\n        except Exception as e:\r\n            logger.error(f\"[{self.id}] Connection error: {e}\")\r\n            self.connected = False\r\n            return False\r\n    \r\n    async def subscribe(self, symbol, exchange, mode=1):\r\n        \"\"\"Subscribe to a market data feed\"\"\"\r\n        if not self.connected:\r\n            return False\r\n        \r\n        try:\r\n            sub_msg = {\r\n                \"action\": \"subscribe\",\r\n                \"symbol\": symbol,\r\n                \"exchange\": exchange,\r\n                \"mode\": mode  # LTP mode\r\n            }\r\n            await self.ws.send(json.dumps(sub_msg))\r\n            response = json.loads(await self.ws.recv())\r\n            if response.get(\"status\") == \"success\":\r\n                self.subscriptions.append(f\"{exchange}:{symbol}\")\r\n                logger.info(f\"[{self.id}] Subscribed to {exchange}:{symbol}\")\r\n                return True\r\n            else:\r\n                logger.warning(f\"[{self.id}] Subscription failed: {response}\")\r\n                return False\r\n        except Exception as e:\r\n            logger.error(f\"[{self.id}] Subscription error: {e}\")\r\n            return False\r\n    \r\n    async def unsubscribe(self, symbol, exchange):\r\n        \"\"\"Unsubscribe from a market data feed\"\"\"\r\n        if not self.connected or f\"{exchange}:{symbol}\" not in self.subscriptions:\r\n            return False\r\n        \r\n        try:\r\n            unsub_msg = {\r\n                \"action\": \"unsubscribe\",\r\n                \"symbol\": symbol,\r\n                \"exchange\": exchange\r\n            }\r\n            await self.ws.send(json.dumps(unsub_msg))\r\n            response = json.loads(await self.ws.recv())\r\n            if response.get(\"status\") in [\"success\", \"partial\"]:\r\n                self.subscriptions.remove(f\"{exchange}:{symbol}\")\r\n                logger.info(f\"[{self.id}] Unsubscribed from {exchange}:{symbol}\")\r\n                return True\r\n            else:\r\n                logger.warning(f\"[{self.id}] Unsubscription failed: {response}\")\r\n                return False\r\n        except Exception as e:\r\n            logger.error(f\"[{self.id}] Unsubscription error: {e}\")\r\n            return False\r\n    \r\n    async def disconnect(self):\r\n        \"\"\"Disconnect from the WebSocket server\"\"\"\r\n        if self.connected and self.ws:\r\n            try:\r\n                await self.ws.close()\r\n                logger.info(f\"[{self.id}] Disconnected\")\r\n            except Exception as e:\r\n                logger.error(f\"[{self.id}] Disconnect error: {e}\")\r\n            finally:\r\n                self.connected = False\r\n                self.subscriptions = []\r\n\r\nasync def listener(client):\r\n    \"\"\"Background task to continuously receive messages\"\"\"\r\n    try:\r\n        while client.connected:\r\n            try:\r\n                message = await asyncio.wait_for(client.ws.recv(), timeout=0.1)\r\n                # We're not interested in the content of the message, just receiving them\r\n                # logger.debug(f\"[{client.id}] Received: {message[:50]}...\")\r\n            except asyncio.TimeoutError:\r\n                # No message received within timeout, continue the loop\r\n                continue\r\n            except websockets.exceptions.ConnectionClosed:\r\n                logger.info(f\"[{client.id}] Connection closed\")\r\n                client.connected = False\r\n                break\r\n    except Exception as e:\r\n        logger.error(f\"[{client.id}] Listener error: {e}\")\r\n        client.connected = False\r\n\r\nasync def random_client_behavior(client, running_flag):\r\n    \"\"\"Exhibit random behavior for a client - connect, subscribe, unsubscribe, disconnect\"\"\"\r\n    # Start listener task\r\n    listener_task = asyncio.create_task(listener(client))\r\n    \r\n    try:\r\n        # Connect\r\n        if not await client.connect():\r\n            return\r\n        \r\n        # Random subscriptions\r\n        for _ in range(random.randint(1, len(SYMBOLS))):\r\n            symbol_info = random.choice(SYMBOLS)\r\n            await client.subscribe(symbol_info[\"symbol\"], symbol_info[\"exchange\"])\r\n            # Brief delay between subscriptions\r\n            await asyncio.sleep(random.uniform(0.05, 0.2))\r\n        \r\n        while running_flag.is_set():\r\n            # Perform random actions\r\n            action = random.choices(\r\n                [\"subscribe\", \"unsubscribe\", \"sleep\"],\r\n                weights=[0.3, 0.3, 0.4],\r\n                k=1\r\n            )[0]\r\n            \r\n            if action == \"subscribe\" and len(client.subscriptions) < len(SYMBOLS):\r\n                # Subscribe to a new symbol\r\n                for symbol_info in SYMBOLS:\r\n                    key = f\"{symbol_info['exchange']}:{symbol_info['symbol']}\"\r\n                    if key not in client.subscriptions:\r\n                        await client.subscribe(symbol_info[\"symbol\"], symbol_info[\"exchange\"])\r\n                        break\r\n                        \r\n            elif action == \"unsubscribe\" and client.subscriptions:\r\n                # Unsubscribe from a random symbol\r\n                sub_key = random.choice(client.subscriptions)\r\n                exchange, symbol = sub_key.split(\":\")\r\n                await client.unsubscribe(symbol, exchange)\r\n            \r\n            # Sleep briefly between actions\r\n            await asyncio.sleep(random.uniform(0.1, 0.5))\r\n        \r\n    except Exception as e:\r\n        print(f\"[{client.id}] Error during random behavior: {e}\")\r\n    finally:\r\n        # Clean up\r\n        await client.disconnect()\r\n        # Cancel listener task\r\n        listener_task.cancel()\r\n        try:\r\n            await listener_task\r\n        except asyncio.CancelledError:\r\n            pass\r\n\r\nasync def run_test(client_count, duration):\r\n    \"\"\"Run the stress test with multiple clients for the specified duration\"\"\"\r\n    header = f\"\\n{'='*60}\\nWEBSOCKET PROXY DICTIONARY ITERATION STRESS TEST\\n{'='*60}\\n\"\r\n    header += f\"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\"\r\n    header += f\"Number of clients: {client_count}\\n\"\r\n    header += f\"Test duration: {duration} seconds\\n\"\r\n    header += f\"Logging to: {log_file}\\n\"\r\n    header += f\"{'='*60}\\n\"\r\n    \r\n    print(header)\r\n    logger.info(header)\r\n    \r\n    # Create clients\r\n    clients = [WebSocketClient(f\"Client-{i+1}\") for i in range(client_count)]\r\n    \r\n    # Flag to signal tasks to stop\r\n    running = asyncio.Event()\r\n    running.set()\r\n    \r\n    # Create tasks for each client\r\n    tasks = [asyncio.create_task(random_client_behavior(client, running)) for client in clients]\r\n    \r\n    # Create staggered connections\r\n    clients_in_flight = []\r\n    for i, client in enumerate(clients):\r\n        # Add client to tracking list\r\n        clients_in_flight.append(client)\r\n        \r\n        # Every 3rd client, disconnect a previous one to create churn\r\n        if i > 5 and i % 3 == 0 and clients_in_flight:\r\n            disconnected = clients_in_flight.pop(0)\r\n            await disconnected.disconnect()\r\n        \r\n        # Spread out connections\r\n        await asyncio.sleep(random.uniform(0.3, 0.7))\r\n    \r\n    # Run for specified duration\r\n    try:\r\n        await asyncio.sleep(duration)\r\n    finally:\r\n        shutdown_msg = \"\\nTest duration complete. Shutting down...\"\r\n        print(shutdown_msg)\r\n        logger.info(shutdown_msg)\r\n        # Signal tasks to stop\r\n        running.clear()\r\n        \r\n        # Wait for all tasks to complete\r\n        await asyncio.gather(*tasks, return_exceptions=True)\r\n    \r\n    summary = f\"\\n{'='*60}\\n\"\r\n    summary += f\"TEST COMPLETED at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\"\r\n    summary += f\"No dictionary size errors detected during the test.\\n\"\r\n    summary += f\"Log file saved to: {log_file}\\n\"\r\n    summary += f\"{'='*60}\\n\"\r\n    \r\n    print(summary)\r\n    logger.info(summary)\r\n\r\ndef main():\r\n    \"\"\"Main entry point\"\"\"\r\n    parser = argparse.ArgumentParser(description=\"WebSocket Proxy Dictionary Iteration Stress Test\")\r\n    parser.add_argument(\"--clients\", type=int, default=CLIENT_COUNT, help=\"Number of clients to simulate\")\r\n    parser.add_argument(\"--duration\", type=int, default=TEST_DURATION_SECONDS, help=\"Test duration in seconds\")\r\n    args = parser.parse_args()\r\n    \r\n    try:\r\n        asyncio.run(run_test(args.clients, args.duration))\r\n    except KeyboardInterrupt:\r\n        print(\"\\nTest interrupted by user.\")\r\n    \r\nif __name__ == \"__main__\":\r\n    main()\r\n", "path": "ubuntu/algofactory-multi/algofactory-8010/test/iteration_test.py", "language": "python", "prefixBegin": 0, "suffixEnd": 0}}], ["daf592f1-7bd8-4584-b32a-e3d21d5f1645", {"value": {"selectedCode": "API_KEY = \"1a931f67eba14c15f8bfb24c7be25e8944338fab564a5a2a4b807ff1654b38f3\"", "prefix": "\"\"\"\r\nWebSocket Proxy Dictionary Iteration Stress Test\r\n\r\nThis script tests the WebSocket proxy server's handling of concurrent dictionary modifications\r\nby creating multiple clients that rapidly connect, subscribe, and disconnect.\r\n\"\"\"\r\n\r\nimport asyncio\r\nimport websockets\r\nimport json\r\nimport random\r\nimport time\r\nimport argparse\r\nimport uuid\r\nimport logging\r\nimport os\r\nfrom datetime import datetime\r\n\r\n# Set up logging\r\nlog_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')\r\nos.makedirs(log_dir, exist_ok=True)\r\nlog_file = os.path.join(log_dir, f'websocket_test_{datetime.now().strftime(\"%Y%m%d_%H%M%S\")}.log')\r\n\r\n# Configure logging\r\nlogging.basicConfig(\r\n    level=logging.INFO,\r\n    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',\r\n    handlers=[\r\n        logging.FileHandler(log_file),\r\n        logging.StreamHandler()\r\n    ]\r\n)\r\n\r\nlogger = logging.getLogger('websocket_test')\r\n\r\n# Test configuration\r\nWEBSOCKET_URI = \"ws://127.0.0.1:20010\"\r\n", "suffix": "\r\nCLIENT_COUNT = 10\r\nTEST_DURATION_SECONDS = 30\r\n\r\n# Symbols to use for testing\r\nSYMBOLS = [\r\n    {\"symbol\": \"CRUDEOIL18JUN25FUT\", \"exchange\": \"MCX\"},\r\n    {\"symbol\": \"NIFTY\", \"exchange\": \"NSE_INDEX\"},\r\n    {\"symbol\": \"SENSEX\", \"exchange\": \"BSE_INDEX\"},\r\n    {\"symbol\": \"REL<PERSON>NCE26JUN25FUT\", \"exchange\": \"NFO\"}\r\n]\r\n\r\nclass WebSocketClient:\r\n    \"\"\"Represents a single WebSocket client for testing\"\"\"\r\n    \r\n    def __init__(self, client_id):\r\n        self.id = client_id\r\n        self.ws = None\r\n        self.connected = False\r\n        self.subscriptions = []\r\n    \r\n    async def connect(self):\r\n        \"\"\"Connect to the WebSocket server and authenticate\"\"\"\r\n        try:\r\n            self.ws = await websockets.connect(WEBSOCKET_URI)\r\n            self.connected = True\r\n            logger.info(f\"[{self.id}] Connected\")\r\n            \r\n            # Send authentication message\r\n            auth_msg = {\r\n                \"action\": \"authenticate\",\r\n                \"api_key\": API_KEY\r\n            }\r\n            await self.ws.send(json.dumps(auth_msg))\r\n            auth_response = json.loads(await self.ws.recv())\r\n            if auth_response.get(\"status\") == \"success\":\r\n                logger.info(f\"[{self.id}] Authentication successful\")\r\n                return True\r\n            else:\r\n                logger.warning(f\"[{self.id}] Authentication failed: {auth_response}\")\r\n                await self.disconnect()\r\n                return False\r\n        except Exception as e:\r\n            logger.error(f\"[{self.id}] Connection error: {e}\")\r\n            self.connected = False\r\n            return False\r\n    \r\n    async def subscribe(self, symbol, exchange, mode=1):\r\n        \"\"\"Subscribe to a market data feed\"\"\"\r\n        if not self.connected:\r\n            return False\r\n        \r\n        try:\r\n            sub_msg = {\r\n                \"action\": \"subscribe\",\r\n                \"symbol\": symbol,\r\n                \"exchange\": exchange,\r\n                \"mode\": mode  # LTP mode\r\n            }\r\n            await self.ws.send(json.dumps(sub_msg))\r\n            response = json.loads(await self.ws.recv())\r\n            if response.get(\"status\") == \"success\":\r\n                self.subscriptions.append(f\"{exchange}:{symbol}\")\r\n                logger.info(f\"[{self.id}] Subscribed to {exchange}:{symbol}\")\r\n                return True\r\n            else:\r\n                logger.warning(f\"[{self.id}] Subscription failed: {response}\")\r\n                return False\r\n        except Exception as e:\r\n            logger.error(f\"[{self.id}] Subscription error: {e}\")\r\n            return False\r\n    \r\n    async def unsubscribe(self, symbol, exchange):\r\n        \"\"\"Unsubscribe from a market data feed\"\"\"\r\n        if not self.connected or f\"{exchange}:{symbol}\" not in self.subscriptions:\r\n            return False\r\n        \r\n        try:\r\n            unsub_msg = {\r\n                \"action\": \"unsubscribe\",\r\n                \"symbol\": symbol,\r\n                \"exchange\": exchange\r\n            }\r\n            await self.ws.send(json.dumps(unsub_msg))\r\n            response = json.loads(await self.ws.recv())\r\n            if response.get(\"status\") in [\"success\", \"partial\"]:\r\n                self.subscriptions.remove(f\"{exchange}:{symbol}\")\r\n                logger.info(f\"[{self.id}] Unsubscribed from {exchange}:{symbol}\")\r\n                return True\r\n            else:\r\n                logger.warning(f\"[{self.id}] Unsubscription failed: {response}\")\r\n                return False\r\n        except Exception as e:\r\n            logger.error(f\"[{self.id}] Unsubscription error: {e}\")\r\n            return False\r\n    \r\n    async def disconnect(self):\r\n        \"\"\"Disconnect from the WebSocket server\"\"\"\r\n        if self.connected and self.ws:\r\n            try:\r\n                await self.ws.close()\r\n                logger.info(f\"[{self.id}] Disconnected\")\r\n            except Exception as e:\r\n                logger.error(f\"[{self.id}] Disconnect error: {e}\")\r\n            finally:\r\n                self.connected = False\r\n                self.subscriptions = []\r\n\r\nasync def listener(client):\r\n    \"\"\"Background task to continuously receive messages\"\"\"\r\n    try:\r\n        while client.connected:\r\n            try:\r\n                message = await asyncio.wait_for(client.ws.recv(), timeout=0.1)\r\n                # We're not interested in the content of the message, just receiving them\r\n                # logger.debug(f\"[{client.id}] Received: {message[:50]}...\")\r\n            except asyncio.TimeoutError:\r\n                # No message received within timeout, continue the loop\r\n                continue\r\n            except websockets.exceptions.ConnectionClosed:\r\n                logger.info(f\"[{client.id}] Connection closed\")\r\n                client.connected = False\r\n                break\r\n    except Exception as e:\r\n        logger.error(f\"[{client.id}] Listener error: {e}\")\r\n        client.connected = False\r\n\r\nasync def random_client_behavior(client, running_flag):\r\n    \"\"\"Exhibit random behavior for a client - connect, subscribe, unsubscribe, disconnect\"\"\"\r\n    # Start listener task\r\n    listener_task = asyncio.create_task(listener(client))\r\n    \r\n    try:\r\n        # Connect\r\n        if not await client.connect():\r\n            return\r\n        \r\n        # Random subscriptions\r\n        for _ in range(random.randint(1, len(SYMBOLS))):\r\n            symbol_info = random.choice(SYMBOLS)\r\n            await client.subscribe(symbol_info[\"symbol\"], symbol_info[\"exchange\"])\r\n            # Brief delay between subscriptions\r\n            await asyncio.sleep(random.uniform(0.05, 0.2))\r\n        \r\n        while running_flag.is_set():\r\n            # Perform random actions\r\n            action = random.choices(\r\n                [\"subscribe\", \"unsubscribe\", \"sleep\"],\r\n                weights=[0.3, 0.3, 0.4],\r\n                k=1\r\n            )[0]\r\n            \r\n            if action == \"subscribe\" and len(client.subscriptions) < len(SYMBOLS):\r\n                # Subscribe to a new symbol\r\n                for symbol_info in SYMBOLS:\r\n                    key = f\"{symbol_info['exchange']}:{symbol_info['symbol']}\"\r\n                    if key not in client.subscriptions:\r\n                        await client.subscribe(symbol_info[\"symbol\"], symbol_info[\"exchange\"])\r\n                        break\r\n                        \r\n            elif action == \"unsubscribe\" and client.subscriptions:\r\n                # Unsubscribe from a random symbol\r\n                sub_key = random.choice(client.subscriptions)\r\n                exchange, symbol = sub_key.split(\":\")\r\n                await client.unsubscribe(symbol, exchange)\r\n            \r\n            # Sleep briefly between actions\r\n            await asyncio.sleep(random.uniform(0.1, 0.5))\r\n        \r\n    except Exception as e:\r\n        print(f\"[{client.id}] Error during random behavior: {e}\")\r\n    finally:\r\n        # Clean up\r\n        await client.disconnect()\r\n        # Cancel listener task\r\n        listener_task.cancel()\r\n        try:\r\n            await listener_task\r\n        except asyncio.CancelledError:\r\n            pass\r\n\r\nasync def run_test(client_count, duration):\r\n    \"\"\"Run the stress test with multiple clients for the specified duration\"\"\"\r\n    header = f\"\\n{'='*60}\\nWEBSOCKET PROXY DICTIONARY ITERATION STRESS TEST\\n{'='*60}\\n\"\r\n    header += f\"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\"\r\n    header += f\"Number of clients: {client_count}\\n\"\r\n    header += f\"Test duration: {duration} seconds\\n\"\r\n    header += f\"Logging to: {log_file}\\n\"\r\n    header += f\"{'='*60}\\n\"\r\n    \r\n    print(header)\r\n    logger.info(header)\r\n    \r\n    # Create clients\r\n    clients = [WebSocketClient(f\"Client-{i+1}\") for i in range(client_count)]\r\n    \r\n    # Flag to signal tasks to stop\r\n    running = asyncio.Event()\r\n    running.set()\r\n    \r\n    # Create tasks for each client\r\n    tasks = [asyncio.create_task(random_client_behavior(client, running)) for client in clients]\r\n    \r\n    # Create staggered connections\r\n    clients_in_flight = []\r\n    for i, client in enumerate(clients):\r\n        # Add client to tracking list\r\n        clients_in_flight.append(client)\r\n        \r\n        # Every 3rd client, disconnect a previous one to create churn\r\n        if i > 5 and i % 3 == 0 and clients_in_flight:\r\n            disconnected = clients_in_flight.pop(0)\r\n            await disconnected.disconnect()\r\n        \r\n        # Spread out connections\r\n        await asyncio.sleep(random.uniform(0.3, 0.7))\r\n    \r\n    # Run for specified duration\r\n    try:\r\n        await asyncio.sleep(duration)\r\n    finally:\r\n        shutdown_msg = \"\\nTest duration complete. Shutting down...\"\r\n        print(shutdown_msg)\r\n        logger.info(shutdown_msg)\r\n        # Signal tasks to stop\r\n        running.clear()\r\n        \r\n        # Wait for all tasks to complete\r\n        await asyncio.gather(*tasks, return_exceptions=True)\r\n    \r\n    summary = f\"\\n{'='*60}\\n\"\r\n    summary += f\"TEST COMPLETED at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\"\r\n    summary += f\"No dictionary size errors detected during the test.\\n\"\r\n    summary += f\"Log file saved to: {log_file}\\n\"\r\n    summary += f\"{'='*60}\\n\"\r\n    \r\n    print(summary)\r\n    logger.info(summary)\r\n\r\ndef main():\r\n    \"\"\"Main entry point\"\"\"\r\n    parser = argparse.ArgumentParser(description=\"WebSocket Proxy Dictionary Iteration Stress Test\")\r\n    parser.add_argument(\"--clients\", type=int, default=CLIENT_COUNT, help=\"Number of clients to simulate\")\r\n    parser.add_argument(\"--duration\", type=int, default=TEST_DURATION_SECONDS, help=\"Test duration in seconds\")\r\n    args = parser.parse_args()\r\n    \r\n    try:\r\n        asyncio.run(run_test(args.clients, args.duration))\r\n    except KeyboardInterrupt:\r\n        print(\"\\nTest interrupted by user.\")\r\n    \r\nif __name__ == \"__main__\":\r\n    main()\r\n", "path": "ubuntu/algofactory-multi/algofactory-8010/test/iteration_test.py", "language": "python", "prefixBegin": 0, "suffixEnd": 0}}], ["abeead36-2e63-41bf-be06-0e8f4d43ff66", {"value": {"selectedCode": "", "prefix": "\"\"\"\r\nWebSocket Proxy Dictionary Iteration Stress Test\r\n\r\nThis script tests the WebSocket proxy server's handling of concurrent dictionary modifications\r\nby creating multiple clients that rapidly connect, subscribe, and disconnect.\r\n\"\"\"\r\n\r\nimport asyncio\r\nimport websockets\r\nimport json\r\nimport random\r\nimport time\r\nimport argparse\r\nimport uuid\r\nimport logging\r\nimport os\r\nfrom datetime import datetime\r\n\r\n# Set up logging\r\nlog_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')\r\nos.makedirs(log_dir, exist_ok=True)\r\nlog_file = os.path.join(log_dir, f'websocket_test_{datetime.now().strftime(\"%Y%m%d_%H%M%S\")}.log')\r\n\r\n# Configure logging\r\nlogging.basicConfig(\r\n    level=logging.INFO,\r\n    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',\r\n    handlers=[\r\n        logging.FileHandler(log_file),\r\n        logging.StreamHandler()\r\n    ]\r\n)\r\n\r\nlogger = logging.getLogger('websocket_test')\r\n\r\n# Test configuration\r\nWEBSOCKET_URI = \"ws://127.0.0.1:20010\"\r\n", "suffix": "API_KEY = \"1a931f67eba14c15f8bfb24c7be25e8944338fab564a5a2a4b807ff1654b38f3\"\r\nCLIENT_COUNT = 10\r\nTEST_DURATION_SECONDS = 30\r\n\r\n# Symbols to use for testing\r\nSYMBOLS = [\r\n    {\"symbol\": \"CRUDEOIL18JUN25FUT\", \"exchange\": \"MCX\"},\r\n    {\"symbol\": \"NIFTY\", \"exchange\": \"NSE_INDEX\"},\r\n    {\"symbol\": \"SENSEX\", \"exchange\": \"BSE_INDEX\"},\r\n    {\"symbol\": \"RELIANCE26JUN25FUT\", \"exchange\": \"NFO\"}\r\n]\r\n\r\nclass WebSocketClient:\r\n    \"\"\"Represents a single WebSocket client for testing\"\"\"\r\n    \r\n    def __init__(self, client_id):\r\n        self.id = client_id\r\n        self.ws = None\r\n        self.connected = False\r\n        self.subscriptions = []\r\n    \r\n    async def connect(self):\r\n        \"\"\"Connect to the WebSocket server and authenticate\"\"\"\r\n        try:\r\n            self.ws = await websockets.connect(WEBSOCKET_URI)\r\n            self.connected = True\r\n            logger.info(f\"[{self.id}] Connected\")\r\n            \r\n            # Send authentication message\r\n            auth_msg = {\r\n                \"action\": \"authenticate\",\r\n                \"api_key\": API_KEY\r\n            }\r\n            await self.ws.send(json.dumps(auth_msg))\r\n            auth_response = json.loads(await self.ws.recv())\r\n            if auth_response.get(\"status\") == \"success\":\r\n                logger.info(f\"[{self.id}] Authentication successful\")\r\n                return True\r\n            else:\r\n                logger.warning(f\"[{self.id}] Authentication failed: {auth_response}\")\r\n                await self.disconnect()\r\n                return False\r\n        except Exception as e:\r\n            logger.error(f\"[{self.id}] Connection error: {e}\")\r\n            self.connected = False\r\n            return False\r\n    \r\n    async def subscribe(self, symbol, exchange, mode=1):\r\n        \"\"\"Subscribe to a market data feed\"\"\"\r\n        if not self.connected:\r\n            return False\r\n        \r\n        try:\r\n            sub_msg = {\r\n                \"action\": \"subscribe\",\r\n                \"symbol\": symbol,\r\n                \"exchange\": exchange,\r\n                \"mode\": mode  # LTP mode\r\n            }\r\n            await self.ws.send(json.dumps(sub_msg))\r\n            response = json.loads(await self.ws.recv())\r\n            if response.get(\"status\") == \"success\":\r\n                self.subscriptions.append(f\"{exchange}:{symbol}\")\r\n                logger.info(f\"[{self.id}] Subscribed to {exchange}:{symbol}\")\r\n                return True\r\n            else:\r\n                logger.warning(f\"[{self.id}] Subscription failed: {response}\")\r\n                return False\r\n        except Exception as e:\r\n            logger.error(f\"[{self.id}] Subscription error: {e}\")\r\n            return False\r\n    \r\n    async def unsubscribe(self, symbol, exchange):\r\n        \"\"\"Unsubscribe from a market data feed\"\"\"\r\n        if not self.connected or f\"{exchange}:{symbol}\" not in self.subscriptions:\r\n            return False\r\n        \r\n        try:\r\n            unsub_msg = {\r\n                \"action\": \"unsubscribe\",\r\n                \"symbol\": symbol,\r\n                \"exchange\": exchange\r\n            }\r\n            await self.ws.send(json.dumps(unsub_msg))\r\n            response = json.loads(await self.ws.recv())\r\n            if response.get(\"status\") in [\"success\", \"partial\"]:\r\n                self.subscriptions.remove(f\"{exchange}:{symbol}\")\r\n                logger.info(f\"[{self.id}] Unsubscribed from {exchange}:{symbol}\")\r\n                return True\r\n            else:\r\n                logger.warning(f\"[{self.id}] Unsubscription failed: {response}\")\r\n                return False\r\n        except Exception as e:\r\n            logger.error(f\"[{self.id}] Unsubscription error: {e}\")\r\n            return False\r\n    \r\n    async def disconnect(self):\r\n        \"\"\"Disconnect from the WebSocket server\"\"\"\r\n        if self.connected and self.ws:\r\n            try:\r\n                await self.ws.close()\r\n                logger.info(f\"[{self.id}] Disconnected\")\r\n            except Exception as e:\r\n                logger.error(f\"[{self.id}] Disconnect error: {e}\")\r\n            finally:\r\n                self.connected = False\r\n                self.subscriptions = []\r\n\r\nasync def listener(client):\r\n    \"\"\"Background task to continuously receive messages\"\"\"\r\n    try:\r\n        while client.connected:\r\n            try:\r\n                message = await asyncio.wait_for(client.ws.recv(), timeout=0.1)\r\n                # We're not interested in the content of the message, just receiving them\r\n                # logger.debug(f\"[{client.id}] Received: {message[:50]}...\")\r\n            except asyncio.TimeoutError:\r\n                # No message received within timeout, continue the loop\r\n                continue\r\n            except websockets.exceptions.ConnectionClosed:\r\n                logger.info(f\"[{client.id}] Connection closed\")\r\n                client.connected = False\r\n                break\r\n    except Exception as e:\r\n        logger.error(f\"[{client.id}] Listener error: {e}\")\r\n        client.connected = False\r\n\r\nasync def random_client_behavior(client, running_flag):\r\n    \"\"\"Exhibit random behavior for a client - connect, subscribe, unsubscribe, disconnect\"\"\"\r\n    # Start listener task\r\n    listener_task = asyncio.create_task(listener(client))\r\n    \r\n    try:\r\n        # Connect\r\n        if not await client.connect():\r\n            return\r\n        \r\n        # Random subscriptions\r\n        for _ in range(random.randint(1, len(SYMBOLS))):\r\n            symbol_info = random.choice(SYMBOLS)\r\n            await client.subscribe(symbol_info[\"symbol\"], symbol_info[\"exchange\"])\r\n            # Brief delay between subscriptions\r\n            await asyncio.sleep(random.uniform(0.05, 0.2))\r\n        \r\n        while running_flag.is_set():\r\n            # Perform random actions\r\n            action = random.choices(\r\n                [\"subscribe\", \"unsubscribe\", \"sleep\"],\r\n                weights=[0.3, 0.3, 0.4],\r\n                k=1\r\n            )[0]\r\n            \r\n            if action == \"subscribe\" and len(client.subscriptions) < len(SYMBOLS):\r\n                # Subscribe to a new symbol\r\n                for symbol_info in SYMBOLS:\r\n                    key = f\"{symbol_info['exchange']}:{symbol_info['symbol']}\"\r\n                    if key not in client.subscriptions:\r\n                        await client.subscribe(symbol_info[\"symbol\"], symbol_info[\"exchange\"])\r\n                        break\r\n                        \r\n            elif action == \"unsubscribe\" and client.subscriptions:\r\n                # Unsubscribe from a random symbol\r\n                sub_key = random.choice(client.subscriptions)\r\n                exchange, symbol = sub_key.split(\":\")\r\n                await client.unsubscribe(symbol, exchange)\r\n            \r\n            # Sleep briefly between actions\r\n            await asyncio.sleep(random.uniform(0.1, 0.5))\r\n        \r\n    except Exception as e:\r\n        print(f\"[{client.id}] Error during random behavior: {e}\")\r\n    finally:\r\n        # Clean up\r\n        await client.disconnect()\r\n        # Cancel listener task\r\n        listener_task.cancel()\r\n        try:\r\n            await listener_task\r\n        except asyncio.CancelledError:\r\n            pass\r\n\r\nasync def run_test(client_count, duration):\r\n    \"\"\"Run the stress test with multiple clients for the specified duration\"\"\"\r\n    header = f\"\\n{'='*60}\\nWEBSOCKET PROXY DICTIONARY ITERATION STRESS TEST\\n{'='*60}\\n\"\r\n    header += f\"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\"\r\n    header += f\"Number of clients: {client_count}\\n\"\r\n    header += f\"Test duration: {duration} seconds\\n\"\r\n    header += f\"Logging to: {log_file}\\n\"\r\n    header += f\"{'='*60}\\n\"\r\n    \r\n    print(header)\r\n    logger.info(header)\r\n    \r\n    # Create clients\r\n    clients = [WebSocketClient(f\"Client-{i+1}\") for i in range(client_count)]\r\n    \r\n    # Flag to signal tasks to stop\r\n    running = asyncio.Event()\r\n    running.set()\r\n    \r\n    # Create tasks for each client\r\n    tasks = [asyncio.create_task(random_client_behavior(client, running)) for client in clients]\r\n    \r\n    # Create staggered connections\r\n    clients_in_flight = []\r\n    for i, client in enumerate(clients):\r\n        # Add client to tracking list\r\n        clients_in_flight.append(client)\r\n        \r\n        # Every 3rd client, disconnect a previous one to create churn\r\n        if i > 5 and i % 3 == 0 and clients_in_flight:\r\n            disconnected = clients_in_flight.pop(0)\r\n            await disconnected.disconnect()\r\n        \r\n        # Spread out connections\r\n        await asyncio.sleep(random.uniform(0.3, 0.7))\r\n    \r\n    # Run for specified duration\r\n    try:\r\n        await asyncio.sleep(duration)\r\n    finally:\r\n        shutdown_msg = \"\\nTest duration complete. Shutting down...\"\r\n        print(shutdown_msg)\r\n        logger.info(shutdown_msg)\r\n        # Signal tasks to stop\r\n        running.clear()\r\n        \r\n        # Wait for all tasks to complete\r\n        await asyncio.gather(*tasks, return_exceptions=True)\r\n    \r\n    summary = f\"\\n{'='*60}\\n\"\r\n    summary += f\"TEST COMPLETED at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\"\r\n    summary += f\"No dictionary size errors detected during the test.\\n\"\r\n    summary += f\"Log file saved to: {log_file}\\n\"\r\n    summary += f\"{'='*60}\\n\"\r\n    \r\n    print(summary)\r\n    logger.info(summary)\r\n\r\ndef main():\r\n    \"\"\"Main entry point\"\"\"\r\n    parser = argparse.ArgumentParser(description=\"WebSocket Proxy Dictionary Iteration Stress Test\")\r\n    parser.add_argument(\"--clients\", type=int, default=CLIENT_COUNT, help=\"Number of clients to simulate\")\r\n    parser.add_argument(\"--duration\", type=int, default=TEST_DURATION_SECONDS, help=\"Test duration in seconds\")\r\n    args = parser.parse_args()\r\n    \r\n    try:\r\n        asyncio.run(run_test(args.clients, args.duration))\r\n    except KeyboardInterrupt:\r\n        print(\"\\nTest interrupted by user.\")\r\n    \r\nif __name__ == \"__main__\":\r\n    main()\r\n", "path": "ubuntu/algofactory-multi/algofactory-8010/test/iteration_test.py", "language": "python", "prefixBegin": 0, "suffixEnd": 0}}], ["f40057a0-099a-4c08-b6af-a434679560f0", {"value": {"selectedCode": "", "prefix": "# Rate limiting zones\n", "suffix": "limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;\nlimit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;\nlimit_req_zone $binary_remote_addr zone=general:10m rate=30r/m;\n\n# Upstream configuration for AlgoFactory\nupstream algofactory_backend {\n    server 127.0.0.1:5000 fail_timeout=30s max_fails=3;\n    keepalive 32;\n}\n\n# HTTP to HTTPS redirect\nserver {\n    listen 80;\n    listen [::]:80;\n    server_name algo.algofactory.in;\n    \n    # Security headers\n    add_header X-Frame-Options DENY always;\n    add_header X-Content-Type-Options nosniff always;\n    add_header X-XSS-Protection \"1; mode=block\" always;\n    \n    # Redirect all HTTP traffic to HTTPS\n    return 301 https://$server_name$request_uri;\n}\n\n# HTTPS server configuration\nserver {\n    listen 443 ssl http2;\n    listen [::]:443 ssl http2;\n    server_name algo.algofactory.in;\n    \n    # SSL Configuration (will be updated after SSL certificate setup)\n    ssl_certificate /etc/ssl/certs/algofactory.crt;\n    ssl_certificate_key /etc/ssl/private/algofactory.key;\n    ssl_protocols TLSv1.2 TLSv1.3;\n    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;\n    ssl_prefer_server_ciphers off;\n    ssl_session_cache shared:SSL:10m;\n    ssl_session_timeout 10m;\n    \n    # Security headers\n    add_header Strict-Transport-Security \"max-age=31536000; includeSubDomains; preload\" always;\n    add_header X-Frame-Options DENY always;\n    add_header X-Content-Type-Options nosniff always;\n    add_header X-XSS-Protection \"1; mode=block\" always;\n    add_header Referrer-Policy \"strict-origin-when-cross-origin\" always;\n    add_header Permissions-Policy \"camera=(), microphone=(), geolocation=(), payment=(), usb=()\" always;\n    \n    # Gzip compression\n    gzip on;\n    gzip_vary on;\n    gzip_min_length 1024;\n    gzip_proxied any;\n    gzip_comp_level 6;\n    gzip_types\n        text/plain\n        text/css\n        text/xml\n        text/javascript\n        application/json\n        application/javascript\n        application/xml+rss\n        application/atom+xml\n        image/svg+xml;\n    \n    # Client settings\n    client_max_body_size 10M;\n    client_body_timeout 60s;\n    client_header_timeout 60s;\n    \n    # Logging\n    access_log /var/log/nginx/algofactory_access.log;\n    error_log /var/log/nginx/algofactory_error.log;\n    \n    # Root location - main application\n    location / {\n        # Rate limiting\n        limit_req zone=general burst=10 nodelay;\n        \n        # Proxy settings\n        proxy_pass http://algofactory_backend;\n        proxy_http_version 1.1;\n        proxy_set_header Upgrade $http_upgrade;\n        proxy_set_header Connection 'upgrade';\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n        proxy_set_header X-Forwarded-Host $server_name;\n        proxy_cache_bypass $http_upgrade;\n        \n        # Timeouts\n        proxy_connect_timeout 30s;\n        proxy_send_timeout 60s;\n        proxy_read_timeout 60s;\n        \n        # Buffer settings\n        proxy_buffering on;\n        proxy_buffer_size 4k;\n        proxy_buffers 8 4k;\n        proxy_busy_buffers_size 8k;\n    }\n    \n    # API endpoints with stricter rate limiting\n    location /api/ {\n        limit_req zone=api burst=20 nodelay;\n        \n        proxy_pass http://algofactory_backend;\n        proxy_http_version 1.1;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n        \n        # API specific timeouts\n        proxy_connect_timeout 10s;\n        proxy_send_timeout 30s;\n        proxy_read_timeout 30s;\n    }\n    \n    # Login endpoints with very strict rate limiting\n    location /auth/login {\n        limit_req zone=login burst=3 nodelay;\n        \n        proxy_pass http://algofactory_backend;\n        proxy_http_version 1.1;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n    }\n    \n    # WebSocket support for real-time features\n    location /socket.io/ {\n        proxy_pass http://algofactory_backend;\n        proxy_http_version 1.1;\n        proxy_set_header Upgrade $http_upgrade;\n        proxy_set_header Connection \"upgrade\";\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n        \n        # WebSocket specific settings\n        proxy_connect_timeout 7d;\n        proxy_send_timeout 7d;\n        proxy_read_timeout 7d;\n    }\n    \n    # Static files caching\n    location /static/ {\n        proxy_pass http://algofactory_backend;\n        proxy_cache_valid 200 1h;\n        expires 1h;\n        add_header Cache-Control \"public, immutable\";\n    }\n    \n    # Health check endpoint for monitoring\n    location /health {\n        proxy_pass http://algofactory_backend;\n        access_log off;\n    }\n    \n    # Status endpoint for monitoring\n    location /status {\n        proxy_pass http://algofactory_backend;\n        access_log off;\n        allow 127.0.0.1;\n        allow ::1;\n        # Add your monitoring server IPs here\n        # allow YOUR_MONITORING_IP;\n        deny all;\n    }\n    \n    # Block access to sensitive files\n    location ~ /\\. {\n        deny all;\n        access_log off;\n        log_not_found off;\n    }\n    \n    location ~ \\.(env|log|ini|conf)$ {\n        deny all;\n        access_log off;\n        log_not_found off;\n    }\n}\n\n# Server block for monitoring from external services\nserver {\n    listen 8080;\n    server_name algo.algofactory.in;\n    \n    # Simple status page for external monitoring\n    location /monitor {\n        access_log off;\n        return 200 \"AlgoFactory Status: OK\\nTimestamp: $time_iso8601\\nServer: $hostname\\n\";\n        add_header Content-Type text/plain;\n    }\n    \n    # Detailed status (restricted access)\n    location /monitor/detailed {\n        access_log off;\n        allow 127.0.0.1;\n        # Add your monitoring service IPs here\n        deny all;\n        \n        proxy_pass http://algofactory_backend/health;\n    }\n}\n", "path": "ubuntu/algofactory-multi/algofactory-8012/nginx-algofactory.conf", "language": "properties", "prefixBegin": 0, "suffixEnd": 0}}]]