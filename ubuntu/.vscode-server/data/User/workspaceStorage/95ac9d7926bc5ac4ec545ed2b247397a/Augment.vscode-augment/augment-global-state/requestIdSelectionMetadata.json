[["f0419baa-40db-4065-ae2b-3d9e5bc9fd88", {"value": {"selectedCode": "", "prefix": "# Broker Configuration\r\nBROKER_API_KEY = 'MZA0cLWq'\r\nBROKER_API_SECRET = 'XIA6RJ3HPG4ZRKKYJLIZ6ROKAM'\r\n\r\n# Market Data Configuration (Optional and Required only for XTS API Supported Brokers)\r\n\r\nBROKER_API_KEY_MARKET = 'YOUR_BROKER_MARKET_API_KEY'\r\nBROKER_API_SECRET_MARKET = 'YOUR_BROKER_MARKET_API_SECRET'\r\n\r\nREDIRECT_URL = 'https://algo.algofactory.in/angel/callback'  # Change if different\r\n\r\n# Valid Brokers Configuration\r\nVALID_BROKERS = 'fivepaisa,fivepaisaxts,aliceblue,angel,compositedge,dhan,dhan_sandbox,firstock,flattrade,fyers,groww,iifl,kotak,jainam,jainampro,paytm,pocketful,shoonya,tradejini,upstox,wisdom,zebu,zerodha'\r\n\r\n# Security Configuration\r\n# IMPORTANT: Generate new random values for both keys during setup!\r\n\r\n# AlgoFactory Application Key\r\nAPP_KEY = '3daa0403ce2501ee7432b75bf100048e3cf510d63d2754f952e93d88bf07ea84'\r\n\r\n# Security Pepper - Used for hashing/encryption of sensitive data\r\n# This is used for:\r\n# 1. API key hashing\r\n# 2. User password hashing\r\n# 3. Broker auth token encryption\r\n# Generate a new random string during setup using: python -c \"import secrets; print(secrets.token_hex(32))\"\r\nAPI_KEY_PEPPER = 'a25d94718479b170c16278e321ea6c989358bf499a658fd20c90033cef8ce772'\r\n\r\n# AlgoFactory Database Configuration\r\nDATABASE_URL = 'sqlite:///db/openalgo.db' \r\n\r\n# AlgoFactory Ngrok Configuration\r\nNGROK_ALLOW = 'FALSE' \r\n\r\n# AlgoFactory Hosted Server (Custom Domain Name) or Ngrok Domain Configuration\r\n# Change to your custom domain or Ngrok domain\r\nHOST_SERVER = 'https://algo.algofactory.in'  \r\n\r\n# AlgoFactory Flask App Host and Port Configuration\r\n# For 0.0.0.0 (accessible from other devices on the network)\r\n# Flask Environment - development or production\r\nFLASK_HOST_IP='0.0.0.0'  \r\nFLASK_PORT='5000' \r\nFLASK_DEBUG='False' \r\nFLASK_ENV='development'\r\n\r\n# WebSocket Configuration\r\nWEBSOCKET_HOST='localhost'\r\n", "suffix": "WEBSOCKET_PORT='8765'\r\nWEBSOCKET_URL='ws://localhost:8765'\r\n\r\n# ZeroMQ Configuration\r\nZMQ_HOST='localhost'\r\nZMQ_PORT='5555'\r\n\r\n# AlgoFactory Rate Limit Settings\r\nLOGIN_RATE_LIMIT_MIN = \"5 per minute\" \r\nLOGIN_RATE_LIMIT_HOUR = \"25 per hour\"\r\nAPI_RATE_LIMIT=\"10 per second\"\r\n\r\n# AlgoFactory API Configuration\r\n\r\n# Required to give 0.5 second to 1 second delay between multi-legged option strategies\r\n# Single legged orders are not affected by this setting.\r\nSMART_ORDER_DELAY = '0.5'\r\n\r\n# Session Expiry Time (24-hour format, IST)\r\n# All user sessions will automatically expire at this time daily\r\nSESSION_EXPIRY_TIME = '03:00'\r\n\r\n# AlgoFactory CORS (Cross-Origin Resource Sharing) Configuration\r\n# Set to TRUE to enable CORS support, FALSE to disable\r\nCORS_ENABLED = 'TRUE'\r\n\r\n# Comma-separated list of allowed origins (domains)\r\n# Example: http://localhost:3000,https://example.com\r\n# Use '*' to allow all origins (not recommended for production)\r\nCORS_ALLOWED_ORIGINS = 'https://algo.algofactory.in'\r\n\r\n# Comma-separated list of allowed HTTP methods\r\n# Default: GET,POST\r\nCORS_ALLOWED_METHODS = 'GET,POST,DELETE,PUT,PATCH'\r\n\r\n# Comma-separated list of allowed headers\r\n# Default Flask-CORS values will be used if not specified\r\nCORS_ALLOWED_HEADERS = 'Content-Type,Authorization,X-Requested-With'\r\n\r\n# Comma-separated list of headers exposed to the browser\r\nCORS_EXPOSED_HEADERS = ''\r\n\r\n# Whether to allow credentials (cookies, authorization headers)\r\n# Set to TRUE only if you need to support credentials\r\nCORS_ALLOW_CREDENTIALS = 'FALSE'\r\n\r\n# Max age (in seconds) for browser to cache preflight requests\r\n# Default: 86400 (24 hours)\r\nCORS_MAX_AGE = '86400'\r\n\r\n# AlgoFactory Content Security Policy (CSP) Configuration\r\n# Set to TRUE to enable CSP, FALSE to disable\r\nCSP_ENABLED = 'TRUE'\r\n\r\n# Set to TRUE to use Content-Security-Policy-Report-Only mode (testing without blocking)\r\n# This will report violations but not block content\r\nCSP_REPORT_ONLY = 'FALSE'\r\n\r\n# Default source directive - restricts all resource types by default\r\nCSP_DEFAULT_SRC = \"'self'\"\r\n\r\n# Script source directive - controls where scripts can be loaded from\r\n# Includes Socket.IO CDN which is required by the application\r\n# 'unsafe-inline' is needed for Socket.IO to function properly\r\n# Cloudflare Insights is used for analytics\r\nCSP_SCRIPT_SRC = \"'self' 'unsafe-inline' https://cdn.socket.io https://static.cloudflareinsights.com\"\r\n\r\n# Style source directive - controls where styles can be loaded from\r\n# 'unsafe-inline' is needed for some inline styles in the application\r\nCSP_STYLE_SRC = \"'self' 'unsafe-inline'\"\r\n\r\n# Image source directive - controls where images can be loaded from\r\n# 'data:' allows base64 encoded images\r\nCSP_IMG_SRC = \"'self' data:\"\r\n\r\n# Connect source directive - controls what network connections are allowed\r\n# Includes WebSocket connections needed for real-time updates\r\nCSP_CONNECT_SRC = \"'self' wss: ws:\"\r\n\r\n# Font source directive - controls where fonts can be loaded from\r\nCSP_FONT_SRC = \"'self'\"\r\n\r\n# Object source directive - controls where plugins can be loaded from\r\n# 'none' disables all object, embed, and applet elements\r\nCSP_OBJECT_SRC = \"'none'\"\r\n\r\n# Media source directive - controls where audio and video can be loaded from\r\n# Allows audio alerts from your domain and potentially CDN sources in the future\r\nCSP_MEDIA_SRC = \"'self' data: https://*.amazonaws.com https://*.cloudfront.net\"\r\n\r\n# Frame source directive - controls where iframes can be loaded from\r\n# If you integrate with TradingView or other platforms, you may need to add their domains\r\nCSP_FRAME_SRC = \"'self'\"\r\n\r\n# Form action directive - restricts where forms can be submitted to\r\nCSP_FORM_ACTION = \"'self'\"\r\n\r\n# Frame ancestors directive - controls which sites can embed your site in frames\r\n# This helps prevent clickjacking attacks\r\nCSP_FRAME_ANCESTORS = \"'self'\"\r\n\r\n# Base URI directive - restricts what base URIs can be used\r\nCSP_BASE_URI = \"'self'\"\r\n\r\n# Set to TRUE to upgrade insecure (HTTP) requests to HTTPS\r\n# Recommended for production environments\r\nCSP_UPGRADE_INSECURE_REQUESTS = 'FALSE'\r\n\r\n# URI to report CSP violations to (optional)\r\n# Example: /csp-report\r\nCSP_REPORT_URI = ''\r\n\r\n# CSRF (Cross-Site Request Forgery) Protection Configuration\r\n# Set to TRUE to enable CSRF protection, FALSE to disable\r\nCSRF_ENABLED = 'TRUE'\r\n\r\n# CSRF Token Time Limit (in seconds)\r\n# Leave empty for no time limit (tokens valid for entire session)\r\n# Example: 3600 = 1 hour, 86400 = 24 hours\r\nCSRF_TIME_LIMIT = ''\r\n", "path": "ubuntu/myproject/algofactory/.env", "language": "properties", "prefixBegin": 0, "suffixEnd": 0}}], ["3126c46c-deb8-4198-ad1f-060383b177c9", {"value": {"selectedCode": "", "prefix": "# Rate limiting zones\n", "suffix": "limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;\nlimit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;\nlimit_req_zone $binary_remote_addr zone=general:10m rate=30r/m;\n\n# Upstream configuration for AlgoFactory\nupstream algofactory_backend {\n    server 127.0.0.1:5000 fail_timeout=30s max_fails=3;\n    keepalive 32;\n}\n\n# HTTP to HTTPS redirect\nserver {\n    listen 80;\n    listen [::]:80;\n    server_name algo.algofactory.in;\n    \n    # Security headers\n    add_header X-Frame-Options DENY always;\n    add_header X-Content-Type-Options nosniff always;\n    add_header X-XSS-Protection \"1; mode=block\" always;\n    \n    # Redirect all HTTP traffic to HTTPS\n    return 301 https://$server_name$request_uri;\n}\n\n# HTTPS server configuration\nserver {\n    listen 443 ssl http2;\n    listen [::]:443 ssl http2;\n    server_name algo.algofactory.in;\n    \n    # SSL Configuration (will be updated after SSL certificate setup)\n    ssl_certificate /etc/ssl/certs/algofactory.crt;\n    ssl_certificate_key /etc/ssl/private/algofactory.key;\n    ssl_protocols TLSv1.2 TLSv1.3;\n    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;\n    ssl_prefer_server_ciphers off;\n    ssl_session_cache shared:SSL:10m;\n    ssl_session_timeout 10m;\n    \n    # Security headers\n    add_header Strict-Transport-Security \"max-age=31536000; includeSubDomains; preload\" always;\n    add_header X-Frame-Options DENY always;\n    add_header X-Content-Type-Options nosniff always;\n    add_header X-XSS-Protection \"1; mode=block\" always;\n    add_header Referrer-Policy \"strict-origin-when-cross-origin\" always;\n    add_header Permissions-Policy \"camera=(), microphone=(), geolocation=(), payment=(), usb=()\" always;\n    \n    # Gzip compression\n    gzip on;\n    gzip_vary on;\n    gzip_min_length 1024;\n    gzip_proxied any;\n    gzip_comp_level 6;\n    gzip_types\n        text/plain\n        text/css\n        text/xml\n        text/javascript\n        application/json\n        application/javascript\n        application/xml+rss\n        application/atom+xml\n        image/svg+xml;\n    \n    # Client settings\n    client_max_body_size 10M;\n    client_body_timeout 60s;\n    client_header_timeout 60s;\n    \n    # Logging\n    access_log /var/log/nginx/algofactory_access.log;\n    error_log /var/log/nginx/algofactory_error.log;\n    \n    # Root location - main application\n    location / {\n        # Rate limiting\n        limit_req zone=general burst=10 nodelay;\n        \n        # Proxy settings\n        proxy_pass http://algofactory_backend;\n        proxy_http_version 1.1;\n        proxy_set_header Upgrade $http_upgrade;\n        proxy_set_header Connection 'upgrade';\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n        proxy_set_header X-Forwarded-Host $server_name;\n        proxy_cache_bypass $http_upgrade;\n        \n        # Timeouts\n        proxy_connect_timeout 30s;\n        proxy_send_timeout 60s;\n        proxy_read_timeout 60s;\n        \n        # Buffer settings\n        proxy_buffering on;\n        proxy_buffer_size 4k;\n        proxy_buffers 8 4k;\n        proxy_busy_buffers_size 8k;\n    }\n    \n    # API endpoints with stricter rate limiting\n    location /api/ {\n        limit_req zone=api burst=20 nodelay;\n        \n        proxy_pass http://algofactory_backend;\n        proxy_http_version 1.1;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n        \n        # API specific timeouts\n        proxy_connect_timeout 10s;\n        proxy_send_timeout 30s;\n        proxy_read_timeout 30s;\n    }\n    \n    # Login endpoints with very strict rate limiting\n    location /auth/login {\n        limit_req zone=login burst=3 nodelay;\n        \n        proxy_pass http://algofactory_backend;\n        proxy_http_version 1.1;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n    }\n    \n    # WebSocket support for real-time features\n    location /socket.io/ {\n        proxy_pass http://algofactory_backend;\n        proxy_http_version 1.1;\n        proxy_set_header Upgrade $http_upgrade;\n        proxy_set_header Connection \"upgrade\";\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n        \n        # WebSocket specific settings\n        proxy_connect_timeout 7d;\n        proxy_send_timeout 7d;\n        proxy_read_timeout 7d;\n    }\n    \n    # Static files caching\n    location /static/ {\n        proxy_pass http://algofactory_backend;\n        proxy_cache_valid 200 1h;\n        expires 1h;\n        add_header Cache-Control \"public, immutable\";\n    }\n    \n    # Health check endpoint for monitoring\n    location /health {\n        proxy_pass http://algofactory_backend;\n        access_log off;\n    }\n    \n    # Status endpoint for monitoring\n    location /status {\n        proxy_pass http://algofactory_backend;\n        access_log off;\n        allow 127.0.0.1;\n        allow ::1;\n        # Add your monitoring server IPs here\n        # allow YOUR_MONITORING_IP;\n        deny all;\n    }\n    \n    # Block access to sensitive files\n    location ~ /\\. {\n        deny all;\n        access_log off;\n        log_not_found off;\n    }\n    \n    location ~ \\.(env|log|ini|conf)$ {\n        deny all;\n        access_log off;\n        log_not_found off;\n    }\n}\n\n# Server block for monitoring from external services\nserver {\n    listen 8080;\n    server_name algo.algofactory.in;\n    \n    # Simple status page for external monitoring\n    location /monitor {\n        access_log off;\n        return 200 \"AlgoFactory Status: OK\\nTimestamp: $time_iso8601\\nServer: $hostname\\n\";\n        add_header Content-Type text/plain;\n    }\n    \n    # Detailed status (restricted access)\n    location /monitor/detailed {\n        access_log off;\n        allow 127.0.0.1;\n        # Add your monitoring service IPs here\n        deny all;\n        \n        proxy_pass http://algofactory_backend/health;\n    }\n}\n", "path": "ubuntu/myproject/algofactory/nginx-algofactory.conf", "language": "properties", "prefixBegin": 0, "suffixEnd": 0}}]]