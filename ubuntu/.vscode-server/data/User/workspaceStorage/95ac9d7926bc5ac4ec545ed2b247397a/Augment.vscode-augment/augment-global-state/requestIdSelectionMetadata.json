[["f0419baa-40db-4065-ae2b-3d9e5bc9fd88", {"value": {"selectedCode": "", "prefix": "# Broker Configuration\r\nBROKER_API_KEY = 'MZA0cLWq'\r\nBROKER_API_SECRET = 'XIA6RJ3HPG4ZRKKYJLIZ6ROKAM'\r\n\r\n# Market Data Configuration (Optional and Required only for XTS API Supported Brokers)\r\n\r\nBROKER_API_KEY_MARKET = 'YOUR_BROKER_MARKET_API_KEY'\r\nBROKER_API_SECRET_MARKET = 'YOUR_BROKER_MARKET_API_SECRET'\r\n\r\nREDIRECT_URL = 'https://algo.algofactory.in/angel/callback'  # Change if different\r\n\r\n# Valid Brokers Configuration\r\nVALID_BROKERS = 'fivepaisa,fivepaisaxts,aliceblue,angel,compositedge,dhan,dhan_sandbox,firstock,flattrade,fyers,groww,iifl,kotak,jainam,jainampro,paytm,pocketful,shoonya,tradejini,upstox,wisdom,zebu,zerodha'\r\n\r\n# Security Configuration\r\n# IMPORTANT: Generate new random values for both keys during setup!\r\n\r\n# AlgoFactory Application Key\r\nAPP_KEY = '3daa0403ce2501ee7432b75bf100048e3cf510d63d2754f952e93d88bf07ea84'\r\n\r\n# Security Pepper - Used for hashing/encryption of sensitive data\r\n# This is used for:\r\n# 1. API key hashing\r\n# 2. User password hashing\r\n# 3. Broker auth token encryption\r\n# Generate a new random string during setup using: python -c \"import secrets; print(secrets.token_hex(32))\"\r\nAPI_KEY_PEPPER = 'a25d94718479b170c16278e321ea6c989358bf499a658fd20c90033cef8ce772'\r\n\r\n# AlgoFactory Database Configuration\r\nDATABASE_URL = 'sqlite:///db/openalgo.db' \r\n\r\n# AlgoFactory Ngrok Configuration\r\nNGROK_ALLOW = 'FALSE' \r\n\r\n# AlgoFactory Hosted Server (Custom Domain Name) or Ngrok Domain Configuration\r\n# Change to your custom domain or Ngrok domain\r\nHOST_SERVER = 'https://algo.algofactory.in'  \r\n\r\n# AlgoFactory Flask App Host and Port Configuration\r\n# For 0.0.0.0 (accessible from other devices on the network)\r\n# Flask Environment - development or production\r\nFLASK_HOST_IP='0.0.0.0'  \r\nFLASK_PORT='5000' \r\nFLASK_DEBUG='False' \r\nFLASK_ENV='development'\r\n\r\n# WebSocket Configuration\r\nWEBSOCKET_HOST='localhost'\r\n", "suffix": "WEBSOCKET_PORT='8765'\r\nWEBSOCKET_URL='ws://localhost:8765'\r\n\r\n# ZeroMQ Configuration\r\nZMQ_HOST='localhost'\r\nZMQ_PORT='5555'\r\n\r\n# AlgoFactory Rate Limit Settings\r\nLOGIN_RATE_LIMIT_MIN = \"5 per minute\" \r\nLOGIN_RATE_LIMIT_HOUR = \"25 per hour\"\r\nAPI_RATE_LIMIT=\"10 per second\"\r\n\r\n# AlgoFactory API Configuration\r\n\r\n# Required to give 0.5 second to 1 second delay between multi-legged option strategies\r\n# Single legged orders are not affected by this setting.\r\nSMART_ORDER_DELAY = '0.5'\r\n\r\n# Session Expiry Time (24-hour format, IST)\r\n# All user sessions will automatically expire at this time daily\r\nSESSION_EXPIRY_TIME = '03:00'\r\n\r\n# AlgoFactory CORS (Cross-Origin Resource Sharing) Configuration\r\n# Set to TRUE to enable CORS support, FALSE to disable\r\nCORS_ENABLED = 'TRUE'\r\n\r\n# Comma-separated list of allowed origins (domains)\r\n# Example: http://localhost:3000,https://example.com\r\n# Use '*' to allow all origins (not recommended for production)\r\nCORS_ALLOWED_ORIGINS = 'https://algo.algofactory.in'\r\n\r\n# Comma-separated list of allowed HTTP methods\r\n# Default: GET,POST\r\nCORS_ALLOWED_METHODS = 'GET,POST,DELETE,PUT,PATCH'\r\n\r\n# Comma-separated list of allowed headers\r\n# Default Flask-CORS values will be used if not specified\r\nCORS_ALLOWED_HEADERS = 'Content-Type,Authorization,X-Requested-With'\r\n\r\n# Comma-separated list of headers exposed to the browser\r\nCORS_EXPOSED_HEADERS = ''\r\n\r\n# Whether to allow credentials (cookies, authorization headers)\r\n# Set to TRUE only if you need to support credentials\r\nCORS_ALLOW_CREDENTIALS = 'FALSE'\r\n\r\n# Max age (in seconds) for browser to cache preflight requests\r\n# Default: 86400 (24 hours)\r\nCORS_MAX_AGE = '86400'\r\n\r\n# AlgoFactory Content Security Policy (CSP) Configuration\r\n# Set to TRUE to enable CSP, FALSE to disable\r\nCSP_ENABLED = 'TRUE'\r\n\r\n# Set to TRUE to use Content-Security-Policy-Report-Only mode (testing without blocking)\r\n# This will report violations but not block content\r\nCSP_REPORT_ONLY = 'FALSE'\r\n\r\n# Default source directive - restricts all resource types by default\r\nCSP_DEFAULT_SRC = \"'self'\"\r\n\r\n# Script source directive - controls where scripts can be loaded from\r\n# Includes Socket.IO CDN which is required by the application\r\n# 'unsafe-inline' is needed for Socket.IO to function properly\r\n# Cloudflare Insights is used for analytics\r\nCSP_SCRIPT_SRC = \"'self' 'unsafe-inline' https://cdn.socket.io https://static.cloudflareinsights.com\"\r\n\r\n# Style source directive - controls where styles can be loaded from\r\n# 'unsafe-inline' is needed for some inline styles in the application\r\nCSP_STYLE_SRC = \"'self' 'unsafe-inline'\"\r\n\r\n# Image source directive - controls where images can be loaded from\r\n# 'data:' allows base64 encoded images\r\nCSP_IMG_SRC = \"'self' data:\"\r\n\r\n# Connect source directive - controls what network connections are allowed\r\n# Includes WebSocket connections needed for real-time updates\r\nCSP_CONNECT_SRC = \"'self' wss: ws:\"\r\n\r\n# Font source directive - controls where fonts can be loaded from\r\nCSP_FONT_SRC = \"'self'\"\r\n\r\n# Object source directive - controls where plugins can be loaded from\r\n# 'none' disables all object, embed, and applet elements\r\nCSP_OBJECT_SRC = \"'none'\"\r\n\r\n# Media source directive - controls where audio and video can be loaded from\r\n# Allows audio alerts from your domain and potentially CDN sources in the future\r\nCSP_MEDIA_SRC = \"'self' data: https://*.amazonaws.com https://*.cloudfront.net\"\r\n\r\n# Frame source directive - controls where iframes can be loaded from\r\n# If you integrate with TradingView or other platforms, you may need to add their domains\r\nCSP_FRAME_SRC = \"'self'\"\r\n\r\n# Form action directive - restricts where forms can be submitted to\r\nCSP_FORM_ACTION = \"'self'\"\r\n\r\n# Frame ancestors directive - controls which sites can embed your site in frames\r\n# This helps prevent clickjacking attacks\r\nCSP_FRAME_ANCESTORS = \"'self'\"\r\n\r\n# Base URI directive - restricts what base URIs can be used\r\nCSP_BASE_URI = \"'self'\"\r\n\r\n# Set to TRUE to upgrade insecure (HTTP) requests to HTTPS\r\n# Recommended for production environments\r\nCSP_UPGRADE_INSECURE_REQUESTS = 'FALSE'\r\n\r\n# URI to report CSP violations to (optional)\r\n# Example: /csp-report\r\nCSP_REPORT_URI = ''\r\n\r\n# CSRF (Cross-Site Request Forgery) Protection Configuration\r\n# Set to TRUE to enable CSRF protection, FALSE to disable\r\nCSRF_ENABLED = 'TRUE'\r\n\r\n# CSRF Token Time Limit (in seconds)\r\n# Leave empty for no time limit (tokens valid for entire session)\r\n# Example: 3600 = 1 hour, 86400 = 24 hours\r\nCSRF_TIME_LIMIT = ''\r\n", "path": "ubuntu/myproject/algofactory/.env", "language": "properties", "prefixBegin": 0, "suffixEnd": 0}}], ["3126c46c-deb8-4198-ad1f-060383b177c9", {"value": {"selectedCode": "", "prefix": "# Rate limiting zones\n", "suffix": "limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;\nlimit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;\nlimit_req_zone $binary_remote_addr zone=general:10m rate=30r/m;\n\n# Upstream configuration for AlgoFactory\nupstream algofactory_backend {\n    server 127.0.0.1:5000 fail_timeout=30s max_fails=3;\n    keepalive 32;\n}\n\n# HTTP to HTTPS redirect\nserver {\n    listen 80;\n    listen [::]:80;\n    server_name algo.algofactory.in;\n    \n    # Security headers\n    add_header X-Frame-Options DENY always;\n    add_header X-Content-Type-Options nosniff always;\n    add_header X-XSS-Protection \"1; mode=block\" always;\n    \n    # Redirect all HTTP traffic to HTTPS\n    return 301 https://$server_name$request_uri;\n}\n\n# HTTPS server configuration\nserver {\n    listen 443 ssl http2;\n    listen [::]:443 ssl http2;\n    server_name algo.algofactory.in;\n    \n    # SSL Configuration (will be updated after SSL certificate setup)\n    ssl_certificate /etc/ssl/certs/algofactory.crt;\n    ssl_certificate_key /etc/ssl/private/algofactory.key;\n    ssl_protocols TLSv1.2 TLSv1.3;\n    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;\n    ssl_prefer_server_ciphers off;\n    ssl_session_cache shared:SSL:10m;\n    ssl_session_timeout 10m;\n    \n    # Security headers\n    add_header Strict-Transport-Security \"max-age=31536000; includeSubDomains; preload\" always;\n    add_header X-Frame-Options DENY always;\n    add_header X-Content-Type-Options nosniff always;\n    add_header X-XSS-Protection \"1; mode=block\" always;\n    add_header Referrer-Policy \"strict-origin-when-cross-origin\" always;\n    add_header Permissions-Policy \"camera=(), microphone=(), geolocation=(), payment=(), usb=()\" always;\n    \n    # Gzip compression\n    gzip on;\n    gzip_vary on;\n    gzip_min_length 1024;\n    gzip_proxied any;\n    gzip_comp_level 6;\n    gzip_types\n        text/plain\n        text/css\n        text/xml\n        text/javascript\n        application/json\n        application/javascript\n        application/xml+rss\n        application/atom+xml\n        image/svg+xml;\n    \n    # Client settings\n    client_max_body_size 10M;\n    client_body_timeout 60s;\n    client_header_timeout 60s;\n    \n    # Logging\n    access_log /var/log/nginx/algofactory_access.log;\n    error_log /var/log/nginx/algofactory_error.log;\n    \n    # Root location - main application\n    location / {\n        # Rate limiting\n        limit_req zone=general burst=10 nodelay;\n        \n        # Proxy settings\n        proxy_pass http://algofactory_backend;\n        proxy_http_version 1.1;\n        proxy_set_header Upgrade $http_upgrade;\n        proxy_set_header Connection 'upgrade';\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n        proxy_set_header X-Forwarded-Host $server_name;\n        proxy_cache_bypass $http_upgrade;\n        \n        # Timeouts\n        proxy_connect_timeout 30s;\n        proxy_send_timeout 60s;\n        proxy_read_timeout 60s;\n        \n        # Buffer settings\n        proxy_buffering on;\n        proxy_buffer_size 4k;\n        proxy_buffers 8 4k;\n        proxy_busy_buffers_size 8k;\n    }\n    \n    # API endpoints with stricter rate limiting\n    location /api/ {\n        limit_req zone=api burst=20 nodelay;\n        \n        proxy_pass http://algofactory_backend;\n        proxy_http_version 1.1;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n        \n        # API specific timeouts\n        proxy_connect_timeout 10s;\n        proxy_send_timeout 30s;\n        proxy_read_timeout 30s;\n    }\n    \n    # Login endpoints with very strict rate limiting\n    location /auth/login {\n        limit_req zone=login burst=3 nodelay;\n        \n        proxy_pass http://algofactory_backend;\n        proxy_http_version 1.1;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n    }\n    \n    # WebSocket support for real-time features\n    location /socket.io/ {\n        proxy_pass http://algofactory_backend;\n        proxy_http_version 1.1;\n        proxy_set_header Upgrade $http_upgrade;\n        proxy_set_header Connection \"upgrade\";\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n        \n        # WebSocket specific settings\n        proxy_connect_timeout 7d;\n        proxy_send_timeout 7d;\n        proxy_read_timeout 7d;\n    }\n    \n    # Static files caching\n    location /static/ {\n        proxy_pass http://algofactory_backend;\n        proxy_cache_valid 200 1h;\n        expires 1h;\n        add_header Cache-Control \"public, immutable\";\n    }\n    \n    # Health check endpoint for monitoring\n    location /health {\n        proxy_pass http://algofactory_backend;\n        access_log off;\n    }\n    \n    # Status endpoint for monitoring\n    location /status {\n        proxy_pass http://algofactory_backend;\n        access_log off;\n        allow 127.0.0.1;\n        allow ::1;\n        # Add your monitoring server IPs here\n        # allow YOUR_MONITORING_IP;\n        deny all;\n    }\n    \n    # Block access to sensitive files\n    location ~ /\\. {\n        deny all;\n        access_log off;\n        log_not_found off;\n    }\n    \n    location ~ \\.(env|log|ini|conf)$ {\n        deny all;\n        access_log off;\n        log_not_found off;\n    }\n}\n\n# Server block for monitoring from external services\nserver {\n    listen 8080;\n    server_name algo.algofactory.in;\n    \n    # Simple status page for external monitoring\n    location /monitor {\n        access_log off;\n        return 200 \"AlgoFactory Status: OK\\nTimestamp: $time_iso8601\\nServer: $hostname\\n\";\n        add_header Content-Type text/plain;\n    }\n    \n    # Detailed status (restricted access)\n    location /monitor/detailed {\n        access_log off;\n        allow 127.0.0.1;\n        # Add your monitoring service IPs here\n        deny all;\n        \n        proxy_pass http://algofactory_backend/health;\n    }\n}\n", "path": "ubuntu/myproject/algofactory/nginx-algofactory.conf", "language": "properties", "prefixBegin": 0, "suffixEnd": 0}}], ["3ceb723e-ce23-4852-a7e7-1cde29c2fbea", {"value": {"selectedCode": "", "prefix": "# AlgoFactory Multi-Instance Architecture Roadmap\n", "suffix": "\n## 🎯 **Objective**\nCreate a scalable multi-tenant system where each user gets their own isolated AlgoFactory instance with unique ports and subdomains.\n\n## 🏗️ **Architecture Overview**\n\n### **Port Allocation Strategy**\n```\nInstance ID: 1010, 1011, 1012, etc.\n- Flask Port: 1000 + instance_id (1010, 1011, 1012...)\n- WebSocket Port: 12000 + instance_id (12010, 12011, 12012...)\n- ZMQ Port: 15000 + instance_id (15010, 15011, 15012...)\n```\n\n### **Domain Strategy**\n```\n- 1010.algofactory.in → Instance 1010 (Port 1010)\n- 1011.algofactory.in → Instance 1011 (Port 1011)\n- 1012.algofactory.in → Instance 1012 (Port 1012)\n```\n\n### **Folder Structure**\n```\n/home/<USER>/algofactory-multi/\n├── template/                    # Master template (copy of current algofactory)\n│   ├── app.py\n│   ├── requirements.txt\n│   ├── .env.template\n│   └── ... (all current files)\n├── instances/\n│   ├── algofactory-1010/       # Instance 1010\n│   │   ├── app.py\n│   │   ├── .env                # Port 1010, WebSocket 12010, ZMQ 15010\n│   │   ├── db/                 # Isolated database\n│   │   └── logs/               # Instance-specific logs\n│   ├── algofactory-1011/       # Instance 1011\n│   └── algofactory-1012/       # Instance 1012\n├── manager/                     # Management scripts\n│   ├── instance_manager.py     # Main management script\n│   ├── create_instance.sh      # Create new instance\n│   ├── start_all.sh           # Start all instances\n│   ├── stop_all.sh            # Stop all instances\n│   └── monitor_all.py         # Monitor all instances\n├── nginx/                       # Nginx configurations\n│   ├── template.conf           # Nginx template\n│   └── sites/                  # Generated configs\n│       ├── 1010.algofactory.in.conf\n│       └── 1011.algofactory.in.conf\n└── monitoring/                  # Centralized monitoring\n    ├── dashboard.html          # Multi-instance dashboard\n    └── status_api.py           # Status API for all instances\n```\n\n## 🚀 **Implementation Phases**\n\n### **Phase 1: Setup Multi-Instance Foundation**\n1. Create folder structure\n2. Copy current algofactory as template\n3. Create instance manager script\n4. Test with 2 instances (1010, 1011)\n\n### **Phase 2: Nginx & SSL Automation**\n1. Create dynamic Nginx configuration generator\n2. Setup automatic SSL certificate generation\n3. Configure wildcard DNS (*.algofactory.in)\n4. Test subdomain routing\n\n### **Phase 3: Instance Management**\n1. Build web-based instance manager\n2. Create user assignment system\n3. Implement instance lifecycle management\n4. Add resource monitoring\n\n### **Phase 4: Monitoring & Scaling**\n1. Centralized monitoring dashboard\n2. Auto-scaling based on demand\n3. Health checks and auto-restart\n4. Performance optimization\n\n## 🔧 **Key Components to Build**\n\n### **1. Instance Manager Script**\n```bash\n./instance_manager.py create 1010    # Create instance 1010\n./instance_manager.py start 1010     # Start instance 1010\n./instance_manager.py stop 1010      # Stop instance 1010\n./instance_manager.py delete 1010    # Delete instance 1010\n./instance_manager.py list           # List all instances\n./instance_manager.py status         # Status of all instances\n```\n\n### **2. Environment Template**\n```env\n# Instance-specific configuration\nINSTANCE_ID=1010\nFLASK_PORT=1010\nWEBSOCKET_PORT=12010\nZMQ_PORT=15010\nDATABASE_URL=sqlite:///db/algofactory-1010.db\nHOST_SERVER=https://1010.algofactory.in\n```\n\n### **3. Nginx Template**\n```nginx\nserver {\n    listen 443 ssl http2;\n    server_name {INSTANCE_ID}.algofactory.in;\n    \n    location / {\n        proxy_pass http://127.0.0.1:{FLASK_PORT};\n        # ... other proxy settings\n    }\n}\n```\n\n### **4. SystemD Service Template**\n```ini\n[Unit]\nDescription=AlgoFactory Instance {INSTANCE_ID}\n\n[Service]\nExecStart=/home/<USER>/algofactory-multi/instances/algofactory-{INSTANCE_ID}/start.sh monitor\nWorkingDirectory=/home/<USER>/algofactory-multi/instances/algofactory-{INSTANCE_ID}\n```\n\n## 📊 **Resource Planning**\n\n### **Port Ranges**\n- Flask: 1010-1999 (990 instances max)\n- WebSocket: 12010-12999 (990 instances max)\n- ZMQ: 15010-15999 (990 instances max)\n\n### **System Resources**\n- Each instance: ~200MB RAM\n- 10 instances: ~2GB RAM\n- 50 instances: ~10GB RAM\n\n### **Storage**\n- Each instance: ~100MB disk\n- Logs: ~10MB per day per instance\n- Database: Variable based on usage\n\n## 🔐 **Security Considerations**\n\n1. **Isolation**: Each instance has separate database and files\n2. **Firewall**: Only necessary ports exposed\n3. **SSL**: Automatic certificate generation for each subdomain\n4. **Access Control**: User-to-instance mapping\n5. **Resource Limits**: CPU and memory limits per instance\n\n## 🎯 **Success Metrics**\n\n1. **Scalability**: Ability to create 100+ instances\n2. **Performance**: <2 second instance creation\n3. **Reliability**: 99.9% uptime per instance\n4. **Management**: Web-based instance management\n5. **Monitoring**: Real-time status of all instances\n\n## 🚀 **Next Steps**\n\n1. **Start with Phase 1**: Create basic multi-instance setup\n2. **Test with 2-3 instances**: Validate the concept\n3. **Automate Nginx**: Dynamic configuration generation\n4. **Build management interface**: Web-based control panel\n5. **Scale gradually**: Add monitoring and optimization\n\n## 💡 **Benefits**\n\n1. **User Isolation**: Each user has dedicated environment\n2. **Scalability**: Easy to add new instances\n3. **Reliability**: One instance failure doesn't affect others\n4. **Customization**: Each instance can have different configurations\n5. **Resource Management**: Better resource allocation and monitoring\n", "path": "ubuntu/myproject/algofactory/multi-instance-roadmap.md", "language": "markdown", "prefixBegin": 0, "suffixEnd": 0}}], ["75cbc269-821c-493b-bb18-1294c7f568aa", {"value": {"selectedCode": "", "prefix": "4053\n", "suffix": "", "path": "ubuntu/myproject/algofactory/app.pid", "language": "plaintext", "prefixBegin": 0, "suffixEnd": 0}}], ["de7457f0-82ef-4a9e-b420-b022cfe7e49f", {"value": {"selectedCode": "", "prefix": "# AlgoFactory Instance Configuration\n", "suffix": "# This file is auto-generated for instance 1010\n\n# Instance Configuration\nINSTANCE_ID=1010\n\n# Broker Configuration\nBROKER_API_KEY = 'MZA0cLWq'\nBROKER_API_SECRET = 'XIA6RJ3HPG4ZRKKYJLIZ6ROKAM'\n\n# Market Data Configuration (Optional and Required only for XTS API Supported Brokers)\nBROKER_API_KEY_MARKET = 'YOUR_BROKER_MARKET_API_KEY'\nBROKER_API_SECRET_MARKET = 'YOUR_BROKER_MARKET_API_SECRET'\n\nREDIRECT_URL = 'https://1010.algofactory.in/angel/callback'\n\n# Valid Brokers Configuration\nVALID_BROKERS = 'fivepaisa,fivepaisaxts,aliceblue,angel,compositedge,dhan,dhan_sandbox,firstock,flattrade,fyers,groww,iifl,kotak,jainam,jainampro,paytm,pocketful,shoonya,tradejini,upstox,wisdom,zebu,zerodha'\n\n# Security Configuration\nAPP_KEY = '3daa0403ce2501ee7432b75bf100048e3cf510d63d2754f952e93d88bf07ea84'\nAPI_KEY_PEPPER = 'a25d94718479b170c16278e321ea6c989358bf499a658fd20c90033cef8ce772'\n\n# Database Configuration\nDATABASE_URL = 'sqlite:///db/algofactory-1010.db' \n\n# Ngrok Configuration\nNGROK_ALLOW = 'FALSE' \n\n# Host Server Configuration\nHOST_SERVER = 'https://1010.algofactory.in'  \n\n# Flask App Configuration\nFLASK_HOST_IP='0.0.0.0'  \nFLASK_PORT='1010' \nFLASK_DEBUG='False' \nFLASK_ENV='production'\n\n# WebSocket Configuration\nWEBSOCKET_HOST='localhost'\nWEBSOCKET_PORT='13010'\nWEBSOCKET_URL='ws://localhost:13010'\n\n# ZeroMQ Configuration\nZMQ_HOST='localhost'\nZMQ_PORT='16010'\n\n# Rate Limit Settings\nLOGIN_RATE_LIMIT_MIN = \"5 per minute\" \nLOGIN_RATE_LIMIT_HOUR = \"25 per hour\"\nAPI_RATE_LIMIT=\"10 per second\"\n\n# API Configuration\nSMART_ORDER_DELAY = '0.5'\nSESSION_EXPIRY_TIME = '03:00'\n\n# CORS Configuration\nCORS_ENABLED = 'TRUE'\nCORS_ALLOWED_ORIGINS = 'https://1010.algofactory.in'\nCORS_ALLOWED_METHODS = 'GET,POST,DELETE,PUT,PATCH'\nCORS_ALLOWED_HEADERS = 'Content-Type,Authorization,X-Requested-With'\nCORS_EXPOSED_HEADERS = ''\nCORS_ALLOW_CREDENTIALS = 'FALSE'\nCORS_MAX_AGE = '86400'\n\n# CSP Configuration\nCSP_ENABLED = 'TRUE'\nCSP_REPORT_ONLY = 'FALSE'\nCSP_DEFAULT_SRC = \"'self'\"\nCSP_SCRIPT_SRC = \"'self' 'unsafe-inline' https://cdn.socket.io https://static.cloudflareinsights.com\"\nCSP_STYLE_SRC = \"'self' 'unsafe-inline'\"\nCSP_IMG_SRC = \"'self' data:\"\nCSP_CONNECT_SRC = \"'self' wss: ws:\"\nCSP_FONT_SRC = \"'self'\"\nCSP_OBJECT_SRC = \"'none'\"\nCSP_MEDIA_SRC = \"'self' data: https://*.amazonaws.com https://*.cloudfront.net\"\nCSP_FRAME_SRC = \"'self'\"\nCSP_FORM_ACTION = \"'self'\"\nCSP_FRAME_ANCESTORS = \"'self'\"\nCSP_BASE_URI = \"'self'\"\nCSP_UPGRADE_INSECURE_REQUESTS = 'FALSE'\nCSP_REPORT_URI = ''\n\n# CSRF Configuration\nCSRF_ENABLED = 'TRUE'\nCSRF_TIME_LIMIT = ''\n", "path": "ubuntu/algofactory-multi/instances/algofactory-1010/.env", "language": "properties", "prefixBegin": 0, "suffixEnd": 0}}], ["253865cc-f19e-46f7-87a8-a615035d4dd1", {"value": {"selectedCode": "", "prefix": "# Instance 1010 Configuration\nINSTANCE_ID=1010\nBROKER_API_KEY=MZA0cLWq\nBROKER_API_SECRET=XIA6RJ3HPG4ZRKKYJLIZ6ROKAM\nREDIRECT_URL=https://1010.algofactory.in/angel/callback\nVALID_BROKERS=fivepaisa,fivepaisaxts,aliceblue,angel,compositedge,dhan,dhan_sandbox,firstock,flattrade,fyers,groww,iifl,kotak,jainam,jainampro,paytm,pocketful,shoonya,tradejini,upstox,wisdom,zebu,zerodha\nAPP_KEY=3daa0403ce2501ee7432b75bf100048e3cf510d63d2754f952e93d88bf07ea84\nAPI_KEY_PEPPER=a25d94718479b170c16278e321ea6c989358bf499a658fd20c90033cef8ce772\nDATABASE_URL=sqlite:///db/algofactory-1010.db\nNGROK_ALLOW=FALSE\nHOST_SERVER=https://1010.algofactory.in\nFLASK_HOST_IP=0.0.0.0\nFLASK_PORT=1010\nFLASK_DEBUG=False\nFLASK_ENV=production\nWEBSOCKET_HOST=localhost\nWEBSOCKET_PORT=13010\nWEBSOCKET_URL=ws://localhost:13010\nZMQ_HOST=localhost\nZMQ_PORT=16010\nLOGIN_RATE_LIMIT_MIN=5 per minute\nLOGIN_RATE_LIMIT_HOUR=25 per hour\nAPI_RATE_LIMIT=10 per second\nSMART_ORDER_DELAY=0.5\nSESSION_EXPIRY_TIME=03:00\nCORS_ENABLED=TRUE\nCORS_ALLOWED_ORIGINS=https://1010.algofactory.in\nCORS_ALLOWED_METHODS=GET,POST,DELETE,PUT,PATCH\nCORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Requested-With\nCORS_EXPOSED_HEADERS=\nCORS_ALLOW_CREDENTIALS=FALSE\nCORS_MAX_AGE=86400\nCSP_ENABLED=TRUE\nCSP_REPORT_ONLY=FALSE\nCSP_DEFAULT_SRC='self'\nCSP_SCRIPT_SRC='self' 'unsafe-inline' https://cdn.socket.io https://static.cloudflareinsights.com\nCSP_STYLE_SRC='self' 'unsafe-inline'\nCSP_IMG_SRC='self' data:\nCSP_CONNECT_SRC='self' wss: ws:\nCSP_FONT_SRC='self'\nCSP_OBJECT_SRC='none'\nCSP_MEDIA_SRC='self' data: https://*.amazonaws.com https://*.cloudfront.net\nCSP_FRAME_SRC='self'\nCSP_FORM_ACTION='self'\nCSP_FRAME_ANCESTORS='self'\nCSP_BASE_URI='self'\nCSP_UPGRADE_INSECURE_REQUESTS=FALSE\nCSP_REPORT_URI=\nCSRF_ENABLED=TRUE\nCSRF_TIME_LIMIT=\n", "suffix": "", "path": "ubuntu/algofactory-multi/instances/algofactory-1010/.env", "language": "properties", "prefixBegin": 0, "suffixEnd": 0}}], ["6319aa3e-e4a3-4805-aa21-5d73ee2777f4", {"value": {"selectedCode": "", "prefix": "# Broker Configuration\nBROKER_API_KEY = 'MZA0cLWq'\nBROKER_API_SECRET = 'XIA6RJ3HPG4ZRKKYJLIZ6ROKAM'\n\n# Market Data Configuration (Optional and Required only for XTS API Supported Brokers)\n\nBROKER_API_KEY_MARKET = 'YOUR_BROKER_MARKET_API_KEY'\nBROKER_API_SECRET_MARKET = 'YOUR_BROKER_MARKET_API_SECRET'\n\nREDIRECT_URL = 'https://1010.algofactory.in/angel/callback'  # Change if different\n\n# Valid Brokers Configuration\nVALID_BROKERS = 'fivepaisa,fivepaisaxts,aliceblue,angel,compositedge,dhan,dhan_sandbox,firstock,flattrade,fyers,groww,iifl,kotak,jainam,jainampro,paytm,pocketful,shoonya,tradejini,upstox,wisdom,zebu,zerodha'\n\n# Security Configuration\n# IMPORTANT: Generate new random values for both keys during setup!\n\n# AlgoFactory Application Key\nAPP_KEY = '3daa0403ce2501ee7432b75bf100048e3cf510d63d2754f952e93d88bf07ea84'\n\n# Security Pepper - Used for hashing/encryption of sensitive data\n# This is used for:\n# 1. API key hashing\n# 2. User password hashing\n# 3. Broker auth token encryption\n# Generate a new random string during setup using: python -c \"import secrets; print(secrets.token_hex(32))\"\nAPI_KEY_PEPPER = 'a25d94718479b170c16278e321ea6c989358bf499a658fd20c90033cef8ce772'\n\n# AlgoFactory Database Configuration\nDATABASE_URL = 'sqlite:///db/algofactory-1010.db'\n\n# AlgoFactory Ngrok Configuration\nNGROK_ALLOW = 'FALSE'\n\n# AlgoFactory Hosted Server (Custom Domain Name) or Ngrok Domain Configuration\n# Change to your custom domain or Ngrok domain\nHOST_SERVER = 'https://1010.algofactory.in'\n\n# AlgoFactory Flask App Host and Port Configuration\n# For 0.0.0.0 (accessible from other devices on the network)\n# Flask Environment - development or production\nFLASK_HOST_IP='0.0.0.0'\nFLASK_PORT='1010'\nFLASK_DEBUG='False'\nFLASK_ENV='production'\n\n# WebSocket Configuration\nWEBSOCKET_HOST='localhost'\nWEBSOCKET_PORT='13010'\nWEBSOCKET_URL='ws://localhost:13010'\n\n", "suffix": "# ZeroMQ Configuration\nZMQ_HOST='localhost'\nZMQ_PORT='16010'\n\n# AlgoFactory Rate Limit Settings\nLOGIN_RATE_LIMIT_MIN = \"5 per minute\"\nLOGIN_RATE_LIMIT_HOUR = \"25 per hour\"\nAPI_RATE_LIMIT=\"10 per second\"\n\n# AlgoFactory API Configuration\n\n# Required to give 0.5 second to 1 second delay between multi-legged option strategies\n# Single legged orders are not affected by this setting.\nSMART_ORDER_DELAY = '0.5'\n\n# Session Expiry Time (24-hour format, IST)\n# All user sessions will automatically expire at this time daily\nSESSION_EXPIRY_TIME = '03:00'\n\n# AlgoFactory CORS (Cross-Origin Resource Sharing) Configuration\n# Set to TRUE to enable CORS support, <PERSON><PERSON><PERSON> to disable\nCORS_ENABLED = 'TRUE'\n\n# Comma-separated list of allowed origins (domains)\n# Example: http://localhost:3000,https://example.com\n# Use '*' to allow all origins (not recommended for production)\nCORS_ALLOWED_ORIGINS = 'https://1010.algofactory.in'\n\n# Comma-separated list of allowed HTTP methods\n# Default: GET,POST\nCORS_ALLOWED_METHODS = 'GET,POST,DELETE,PUT,PATCH'\n\n# Comma-separated list of allowed headers\n# Default Flask-CORS values will be used if not specified\nCORS_ALLOWED_HEADERS = 'Content-Type,Authorization,X-Requested-With'\n\n# Comma-separated list of headers exposed to the browser\nCORS_EXPOSED_HEADERS = ''\n\n# Whether to allow credentials (cookies, authorization headers)\n# Set to TRUE only if you need to support credentials\nCORS_ALLOW_CREDENTIALS = 'FALSE'\n\n# Max age (in seconds) for browser to cache preflight requests\n# Default: 86400 (24 hours)\nCORS_MAX_AGE = '86400'\n\n# AlgoFactory Content Security Policy (CSP) Configuration\n# Set to TRUE to enable CSP, FALSE to disable\nCSP_ENABLED = 'TRUE'\n\n# Set to TRUE to use Content-Security-Policy-Report-Only mode (testing without blocking)\n# This will report violations but not block content\nCSP_REPORT_ONLY = 'FALSE'\n\n# Default source directive - restricts all resource types by default\nCSP_DEFAULT_SRC = \"'self'\"\n\n# Script source directive - controls where scripts can be loaded from\n# Includes Socket.IO CDN which is required by the application\n# 'unsafe-inline' is needed for Socket.IO to function properly\n# Cloudflare Insights is used for analytics\nCSP_SCRIPT_SRC = \"'self' 'unsafe-inline' https://cdn.socket.io https://static.cloudflareinsights.com\"\n\n# Style source directive - controls where styles can be loaded from\n# 'unsafe-inline' is needed for some inline styles in the application\nCSP_STYLE_SRC = \"'self' 'unsafe-inline'\"\n\n# Image source directive - controls where images can be loaded from\n# 'data:' allows base64 encoded images\nCSP_IMG_SRC = \"'self' data:\"\n\n# Connect source directive - controls what network connections are allowed\n# Includes WebSocket connections needed for real-time updates\nCSP_CONNECT_SRC = \"'self' wss: ws:\"\n\n# Font source directive - controls where fonts can be loaded from\nCSP_FONT_SRC = \"'self'\"\n\n# Object source directive - controls where plugins can be loaded from\n# 'none' disables all object, embed, and applet elements\nCSP_OBJECT_SRC = \"'none'\"\n\n# Media source directive - controls where audio and video can be loaded from\n# Allows audio alerts from your domain and potentially CDN sources in the future\nCSP_MEDIA_SRC = \"'self' data: https://*.amazonaws.com https://*.cloudfront.net\"\n\n# Frame source directive - controls where iframes can be loaded from\n# If you integrate with TradingView or other platforms, you may need to add their domains\nCSP_FRAME_SRC = \"'self'\"\n\n# Form action directive - restricts where forms can be submitted to\nCSP_FORM_ACTION = \"'self'\"\n\n# Frame ancestors directive - controls which sites can embed your site in frames\n# This helps prevent clickjacking attacks\nCSP_FRAME_ANCESTORS = \"'self'\"\n\n# Base URI directive - restricts what base URIs can be used\nCSP_BASE_URI = \"'self'\"\n\n# Set to TRUE to upgrade insecure (HTTP) requests to HTTPS\n# Recommended for production environments\nCSP_UPGRADE_INSECURE_REQUESTS = 'FALSE'\n\n# URI to report CSP violations to (optional)\n# Example: /csp-report\nCSP_REPORT_URI = ''\n\n# CSRF (Cross-Site Request Forgery) Protection Configuration\n# Set to TRUE to enable CSRF protection, FALSE to disable\nCSRF_ENABLED = 'TRUE'\n\n# CSRF Token Time Limit (in seconds)\n# Leave empty for no time limit (tokens valid for entire session)\n# Example: 3600 = 1 hour, 86400 = 24 hours\nCSRF_TIME_LIMIT = ''\n", "path": "ubuntu/algofactory-multi/instances/algofactory-1010/.env", "language": "properties", "prefixBegin": 0, "suffixEnd": 0}}], ["f52fcd17-a56e-4080-97c0-786ba187fc3f", {"value": {"selectedCode": "", "prefix": "# Instance Configuration\n", "suffix": "INSTANCE_ID = '8010'\n\n# Broker Configuration\nBROKER_API_KEY = 'MZA0cLWq'\nBROKER_API_SECRET = 'XIA6RJ3HPG4ZRKKYJLIZ6ROKAM'\n\n# Market Data Configuration (Optional and Required only for XTS API Supported Brokers)\n\nBROKER_API_KEY_MARKET = 'YOUR_BROKER_MARKET_API_KEY'\nBROKER_API_SECRET_MARKET = 'YOUR_BROKER_MARKET_API_SECRET'\n\nREDIRECT_URL = 'https://8010.algofactory.in/angel/callback'  # Change if different\n\n# Valid Brokers Configuration\nVALID_BROKERS = 'fivepaisa,fivepaisaxts,aliceblue,angel,compositedge,dhan,dhan_sandbox,firstock,flattrade,fyers,groww,iifl,kotak,jainam,jainampro,paytm,pocketful,shoonya,tradejini,upstox,wisdom,zebu,zerodha'\n\n# Security Configuration\n# IMPORTANT: Generate new random values for both keys during setup!\n\n# AlgoFactory Application Key\nAPP_KEY = '3daa0403ce2501ee7432b75bf100048e3cf510d63d2754f952e93d88bf07ea84'\n\n# Security Pepper - Used for hashing/encryption of sensitive data\n# This is used for:\n# 1. API key hashing\n# 2. User password hashing\n# 3. Broker auth token encryption\n# Generate a new random string during setup using: python -c \"import secrets; print(secrets.token_hex(32))\"\nAPI_KEY_PEPPER = 'a25d94718479b170c16278e321ea6c989358bf499a658fd20c90033cef8ce772'\n\n# AlgoFactory Database Configuration\nDATABASE_URL = 'sqlite:///db/algofactory-8010.db'\n\n# AlgoFactory Ngrok Configuration\nNGROK_ALLOW = 'FALSE'\n\n# AlgoFactory Hosted Server (Custom Domain Name) or Ngrok Domain Configuration\n# Change to your custom domain or Ngrok domain\nHOST_SERVER = 'https://8010.algofactory.in'\n\n# AlgoFactory Flask App Host and Port Configuration\n# For 0.0.0.0 (accessible from other devices on the network)\n# Flask Environment - development or production\nFLASK_HOST_IP='0.0.0.0'\nFLASK_PORT='8010'\nFLASK_DEBUG='False'\nFLASK_ENV='production'\n\n# WebSocket Configuration\nWEBSOCKET_HOST='localhost'\nWEBSOCKET_PORT='20010'\nWEBSOCKET_URL='ws://localhost:20010'\n\n# ZeroMQ Configuration\nZMQ_HOST='localhost'\nZMQ_PORT='23010'\n\n# AlgoFactory Rate Limit Settings\nLOGIN_RATE_LIMIT_MIN = \"5 per minute\"\nLOGIN_RATE_LIMIT_HOUR = \"25 per hour\"\nAPI_RATE_LIMIT=\"10 per second\"\n\n# AlgoFactory API Configuration\n\n# Required to give 0.5 second to 1 second delay between multi-legged option strategies\n# Single legged orders are not affected by this setting.\nSMART_ORDER_DELAY = '0.5'\n\n# Session Expiry Time (24-hour format, IST)\n# All user sessions will automatically expire at this time daily\nSESSION_EXPIRY_TIME = '03:00'\n\n# AlgoFactory CORS (Cross-Origin Resource Sharing) Configuration\n# Set to TRUE to enable CORS support, FALSE to disable\nCORS_ENABLED = 'TRUE'\n\n# Comma-separated list of allowed origins (domains)\n# Example: http://localhost:3000,https://example.com\n# Use '*' to allow all origins (not recommended for production)\nCORS_ALLOWED_ORIGINS = 'https://8010.algofactory.in'\n\n# Comma-separated list of allowed HTTP methods\n# Default: GET,POST\nCORS_ALLOWED_METHODS = 'GET,POST,DELETE,PUT,PATCH'\n\n# Comma-separated list of allowed headers\n# Default Flask-CORS values will be used if not specified\nCORS_ALLOWED_HEADERS = 'Content-Type,Authorization,X-Requested-With'\n\n# Comma-separated list of headers exposed to the browser\nCORS_EXPOSED_HEADERS = ''\n\n# Whether to allow credentials (cookies, authorization headers)\n# Set to TRUE only if you need to support credentials\nCORS_ALLOW_CREDENTIALS = 'FALSE'\n\n# Max age (in seconds) for browser to cache preflight requests\n# Default: 86400 (24 hours)\nCORS_MAX_AGE = '86400'\n\n# AlgoFactory Content Security Policy (CSP) Configuration\n# Set to TRUE to enable CSP, FALSE to disable\nCSP_ENABLED = 'TRUE'\n\n# Set to TRUE to use Content-Security-Policy-Report-Only mode (testing without blocking)\n# This will report violations but not block content\nCSP_REPORT_ONLY = 'FALSE'\n\n# Default source directive - restricts all resource types by default\nCSP_DEFAULT_SRC = \"'self'\"\n\n# Script source directive - controls where scripts can be loaded from\n# Includes Socket.IO CDN which is required by the application\n# 'unsafe-inline' is needed for Socket.IO to function properly\n# Cloudflare Insights is used for analytics\nCSP_SCRIPT_SRC = \"'self' 'unsafe-inline' https://cdn.socket.io https://static.cloudflareinsights.com\"\n\n# Style source directive - controls where styles can be loaded from\n# 'unsafe-inline' is needed for some inline styles in the application\nCSP_STYLE_SRC = \"'self' 'unsafe-inline'\"\n\n# Image source directive - controls where images can be loaded from\n# 'data:' allows base64 encoded images\nCSP_IMG_SRC = \"'self' data:\"\n\n# Connect source directive - controls what network connections are allowed\n# Includes WebSocket connections needed for real-time updates\nCSP_CONNECT_SRC = \"'self' wss: ws:\"\n\n# Font source directive - controls where fonts can be loaded from\nCSP_FONT_SRC = \"'self'\"\n\n# Object source directive - controls where plugins can be loaded from\n# 'none' disables all object, embed, and applet elements\nCSP_OBJECT_SRC = \"'none'\"\n\n# Media source directive - controls where audio and video can be loaded from\n# Allows audio alerts from your domain and potentially CDN sources in the future\nCSP_MEDIA_SRC = \"'self' data: https://*.amazonaws.com https://*.cloudfront.net\"\n\n# Frame source directive - controls where iframes can be loaded from\n# If you integrate with TradingView or other platforms, you may need to add their domains\nCSP_FRAME_SRC = \"'self'\"\n\n# Form action directive - restricts where forms can be submitted to\nCSP_FORM_ACTION = \"'self'\"\n\n# Frame ancestors directive - controls which sites can embed your site in frames\n# This helps prevent clickjacking attacks\nCSP_FRAME_ANCESTORS = \"'self'\"\n\n# Base URI directive - restricts what base URIs can be used\nCSP_BASE_URI = \"'self'\"\n\n# Set to TRUE to upgrade insecure (HTTP) requests to HTTPS\n# Recommended for production environments\nCSP_UPGRADE_INSECURE_REQUESTS = 'FALSE'\n\n# URI to report CSP violations to (optional)\n# Example: /csp-report\nCSP_REPORT_URI = ''\n\n# CSRF (Cross-Site Request Forgery) Protection Configuration\n# Set to TRUE to enable CSRF protection, FALSE to disable\nCSRF_ENABLED = 'TRUE'\n\n# CSRF Token Time Limit (in seconds)\n# Leave empty for no time limit (tokens valid for entire session)\n# Example: 3600 = 1 hour, 86400 = 24 hours\nCSRF_TIME_LIMIT = ''\n", "path": "ubuntu/algofactory-multi/instances/algofactory-8010/.env", "language": "properties", "prefixBegin": 0, "suffixEnd": 0}}], ["07d29db4-0fc0-4f29-a39c-658f73620b6c", {"value": {"selectedCode": "", "prefix": "# Broker Configuration\n", "suffix": "BROKER_API_KEY = 'MZA0cLWq'\nBROKER_API_SECRET = 'XIA6RJ3HPG4ZRKKYJLIZ6ROKAM'\nBROKER_API_KEY_MARKET = 'YOUR_BROKER_MARKET_API_KEY'\nBROKER_API_SECRET_MARKET = 'YOUR_BROKER_MARKET_API_SECRET'\n\n# Application Configuration\nAPP_KEY = '3daa0403ce2501ee7432b75bf100048e3cf510d63d2754f952e93d88bf07ea84'\nAPI_KEY_PEPPER = 'a25d94718479b170c16278e321ea6c989358bf499a658fd20c90033cef8ce772'\n\n# Flask Configuration\nFLASK_ENV = 'production'\nFLASK_DEBUG = 'False'\nFLASK_HOST_IP = '0.0.0.0'\nFLASK_PORT = '1010'\n\n# Database Configuration\nDATABASE_URL = 'sqlite:///db/algofactory-1010.db'\n\n# WebSocket Configuration\nWEBSOCKET_HOST = 'localhost'\nWEBSOCKET_PORT = '13010'\nWEBSOCKET_URL = 'ws://localhost:13010'\n\n# ZMQ Configuration\nZMQ_HOST = 'localhost'\nZMQ_PORT = '16010'\n\n# Server Configuration\nHOST_SERVER = 'https://1010.algofactory.in'\nREDIRECT_URL = 'https://1010.algofactory.in/angel/callback'\n\n# Security Configuration\nCORS_ENABLED = 'TRUE'\nCORS_ALLOWED_ORIGINS = 'https://1010.algofactory.in'\nCORS_ALLOWED_METHODS = 'GET,POST,DELETE,PUT,PATCH'\nCORS_ALLOWED_HEADERS = 'Content-Type,Authorization,X-Requested-With'\nCORS_ALLOW_CREDENTIALS = 'FALSE'\nCORS_EXPOSED_HEADERS = ''\nCORS_MAX_AGE = '86400'\n\n# CSP Configuration\nCSP_ENABLED = 'TRUE'\nCSP_DEFAULT_SRC = \"'self'\"\nCSP_SCRIPT_SRC = \"'self' 'unsafe-inline' https://cdn.socket.io https://static.cloudflareinsights.com\"\nCSP_STYLE_SRC = \"'self' 'unsafe-inline'\"\nCSP_IMG_SRC = \"'self' data:\"\nCSP_FONT_SRC = \"'self'\"\nCSP_CONNECT_SRC = \"'self' wss: ws:\"\nCSP_MEDIA_SRC = \"'self' data: https://*.amazonaws.com https://*.cloudfront.net\"\nCSP_OBJECT_SRC = \"'none'\"\nCSP_BASE_URI = \"'self'\"\nCSP_FORM_ACTION = \"'self'\"\nCSP_FRAME_ANCESTORS = \"'self'\"\nCSP_FRAME_SRC = \"'self'\"\nCSP_UPGRADE_INSECURE_REQUESTS = 'FALSE'\nCSP_REPORT_ONLY = 'FALSE'\nCSP_REPORT_URI = ''\n\n# CSRF Configuration\nCSRF_ENABLED = 'TRUE'\n\n# Rate Limiting\nAPI_RATE_LIMIT = '10 per second'\nLOGIN_RATE_LIMIT_MIN = '5 per minute'\nLOGIN_RATE_LIMIT_HOUR = '25 per hour'\n\n# Session Configuration\nSESSION_EXPIRY_TIME = '03:00'\n\n# Trading Configuration\nSMART_ORDER_DELAY = '0.5'\n\n# Broker List\nVALID_BROKERS = 'fivepaisa,fivepaisaxts,aliceblue,angel,compositedge,dhan,dhan_sandbox,firstock,flattrade,fyers,groww,iifl,kotak,jainam,jainampro,paytm,pocketful,shoonya,tradejini,upstox,wisdom,zebu,zerodha'\n\n# Development Configuration\nNGROK_ALLOW = 'FALSE'\n", "path": "ubuntu/algofactory-multi/algofactory-1010/.env", "language": "properties", "prefixBegin": 0, "suffixEnd": 0}}]]