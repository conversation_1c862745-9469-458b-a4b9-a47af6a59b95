{"/home/<USER>/": {"rootPath": "/home", "relPath": "ubuntu/"}, "/home/<USER>/shared-venv/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/"}, "/home/<USER>/shared-venv/share/man/man1/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/share/man/man1/"}, "/home/<USER>/shared-venv/share/jupyter/kernels/python3/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/share/jupyter/kernels/python3/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/zmq-0.0.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/zmq-0.0.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/zmq/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/zmq/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/zmq/utils/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/zmq/utils/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/zmq/tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/zmq/tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/zmq/sugar/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/zmq/sugar/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/zmq/ssh/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/zmq/ssh/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/zmq/log/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/zmq/log/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/zmq/green/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/zmq/green/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/zmq/green/eventloop/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/zmq/green/eventloop/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/zmq/eventloop/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/zmq/eventloop/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/zmq/devices/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/zmq/devices/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/zmq/backend/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/zmq/backend/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/zmq/backend/cython/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/zmq/backend/cython/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/zmq/backend/cffi/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/zmq/backend/cffi/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/zmq/auth/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/zmq/auth/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/zipp-3.19.2.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/zipp-3.19.2.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/zipp/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/zipp/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/zipp/compat/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/zipp/compat/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/yaml/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/yaml/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms-3.1.2.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms-3.1.2.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms-3.1.2.dist-info/licenses/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms-3.1.2.dist-info/licenses/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/widgets/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/widgets/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/locale/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/locale/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/locale/zh_TW/LC_MESSAGES/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/locale/zh_TW/LC_MESSAGES/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/locale/zh/LC_MESSAGES/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/locale/zh/LC_MESSAGES/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/locale/uk/LC_MESSAGES/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/locale/uk/LC_MESSAGES/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/locale/tr/LC_MESSAGES/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/locale/tr/LC_MESSAGES/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/locale/sv/LC_MESSAGES/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/locale/sv/LC_MESSAGES/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/locale/sk/LC_MESSAGES/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/locale/sk/LC_MESSAGES/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/locale/ru/LC_MESSAGES/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/locale/ru/LC_MESSAGES/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/locale/ro/LC_MESSAGES/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/locale/ro/LC_MESSAGES/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/locale/pt/LC_MESSAGES/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/locale/pt/LC_MESSAGES/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/locale/pl/LC_MESSAGES/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/locale/pl/LC_MESSAGES/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/locale/nl/LC_MESSAGES/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/locale/nl/LC_MESSAGES/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/locale/nb/LC_MESSAGES/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/locale/nb/LC_MESSAGES/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/locale/ko/LC_MESSAGES/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/locale/ko/LC_MESSAGES/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/locale/ja/LC_MESSAGES/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/locale/ja/LC_MESSAGES/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/locale/it/LC_MESSAGES/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/locale/it/LC_MESSAGES/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/locale/hu/LC_MESSAGES/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/locale/hu/LC_MESSAGES/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/locale/he/LC_MESSAGES/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/locale/he/LC_MESSAGES/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/locale/fr/LC_MESSAGES/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/locale/fr/LC_MESSAGES/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/locale/fi/LC_MESSAGES/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/locale/fi/LC_MESSAGES/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/locale/fa/LC_MESSAGES/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/locale/fa/LC_MESSAGES/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/locale/et/LC_MESSAGES/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/locale/et/LC_MESSAGES/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/locale/es/LC_MESSAGES/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/locale/es/LC_MESSAGES/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/locale/en/LC_MESSAGES/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/locale/en/LC_MESSAGES/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/locale/el/LC_MESSAGES/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/locale/el/LC_MESSAGES/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/locale/de_CH/LC_MESSAGES/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/locale/de_CH/LC_MESSAGES/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/locale/de/LC_MESSAGES/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/locale/de/LC_MESSAGES/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/locale/cy/LC_MESSAGES/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/locale/cy/LC_MESSAGES/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/locale/cs_CZ/LC_MESSAGES/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/locale/cs_CZ/LC_MESSAGES/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/locale/ca/LC_MESSAGES/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/locale/ca/LC_MESSAGES/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/locale/bg/LC_MESSAGES/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/locale/bg/LC_MESSAGES/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/locale/ar/LC_MESSAGES/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/locale/ar/LC_MESSAGES/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/fields/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/fields/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wtforms/csrf/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wtforms/csrf/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wsproto-1.2.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wsproto-1.2.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wsproto/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wsproto/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wrapt-1.16.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wrapt-1.16.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wrapt/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wrapt/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wheel-0.43.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wheel-0.43.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wheel/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wheel/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wheel/vendored/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wheel/vendored/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wheel/vendored/packaging/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wheel/vendored/packaging/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wheel/cli/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wheel/cli/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug-3.1.2.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/werkzeug-3.1.2.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/werkzeug/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/wrappers/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/werkzeug/wrappers/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/sansio/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/werkzeug/sansio/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/routing/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/werkzeug/routing/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/middleware/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/werkzeug/middleware/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/debug/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/werkzeug/debug/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/debug/shared/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/werkzeug/debug/shared/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/datastructures/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/werkzeug/datastructures/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/websockets-15.0.1.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/websockets-15.0.1.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/websockets/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/websockets/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/websockets/sync/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/websockets/sync/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/websockets/legacy/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/websockets/legacy/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/websockets/extensions/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/websockets/extensions/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/websockets/asyncio/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/websockets/asyncio/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/websocket_client-1.8.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/websocket_client-1.8.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/websocket/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/websocket/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/websocket/tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/websocket/tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/websocket/tests/data/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/websocket/tests/data/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wcwidth-0.2.13.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wcwidth-0.2.13.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/wcwidth/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/wcwidth/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/urllib3-2.2.2.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/urllib3-2.2.2.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/urllib3-2.2.2.dist-info/licenses/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/urllib3-2.2.2.dist-info/licenses/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/urllib3/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/urllib3/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/urllib3/util/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/urllib3/util/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/urllib3/contrib/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/urllib3/contrib/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/urllib3/contrib/emscripten/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/urllib3/contrib/emscripten/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tzlocal-5.2.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tzlocal-5.2.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tzlocal/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tzlocal/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tzdata-2024.1.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tzdata-2024.1.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tzdata/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tzdata/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/US/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/US/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/Pacific/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/Pacific/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/Mexico/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/Mexico/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/orm/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/sqlalchemy/orm/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/America/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/America/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tornado/test/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tornado/test/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/sql/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/sqlalchemy/sql/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/traitlets/utils/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/traitlets/utils/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/cyextension/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/sqlalchemy/cyextension/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/socketio/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/socketio/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/connectors/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/sqlalchemy/connectors/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/stack_data/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/stack_data/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/util/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/sqlalchemy/util/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/dialects/mysql/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/sqlalchemy/dialects/mysql/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tornado/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tornado/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/testing/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/sqlalchemy/testing/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/event/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/sqlalchemy/event/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/traitlets/config/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/traitlets/config/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/engine/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/sqlalchemy/engine/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/typing_extensions-4.12.2.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/typing_extensions-4.12.2.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/sqlalchemy/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/testing/suite/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/sqlalchemy/testing/suite/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/future/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/sqlalchemy/future/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/testing/fixtures/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/sqlalchemy/testing/fixtures/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/ext/mypy/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/sqlalchemy/ext/mypy/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/sniffio-1.3.1.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/sniffio-1.3.1.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/dialects/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/sqlalchemy/dialects/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/ext/asyncio/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/sqlalchemy/ext/asyncio/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/stack_data-0.6.3.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/stack_data-0.6.3.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/dialects/sqlite/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/sqlalchemy/dialects/sqlite/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/Etc/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/Etc/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/traitlets/tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/traitlets/tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/sniffio/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/sniffio/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/Asia/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/Asia/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/sniffio/_tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/sniffio/_tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/dialects/mssql/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/sqlalchemy/dialects/mssql/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/America/North_Dakota/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/America/North_Dakota/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/Africa/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/Africa/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/ext/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/sqlalchemy/ext/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/testing/plugin/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/sqlalchemy/testing/plugin/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tornado/test/static/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tornado/test/static/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/dialects/oracle/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/sqlalchemy/dialects/oracle/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/traitlets/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/traitlets/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/dialects/postgresql/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/sqlalchemy/dialects/postgresql/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/traitlets-5.14.3.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/traitlets-5.14.3.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/Australia/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/Australia/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/six-1.16.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/six-1.16.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tornado/test/gettext_translations/fr_FR/LC_MESSAGES/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tornado/test/gettext_translations/fr_FR/LC_MESSAGES/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tornado/platform/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tornado/platform/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/ext/declarative/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/sqlalchemy/ext/declarative/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tornado/test/csv_translations/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tornado/test/csv_translations/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/pool/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/sqlalchemy/pool/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/Europe/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/Europe/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/Brazil/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/Brazil/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/Indian/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/Indian/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/America/Indiana/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/America/Indiana/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/traitlets-5.14.3.dist-info/licenses/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/traitlets-5.14.3.dist-info/licenses/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/America/Kentucky/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/America/Kentucky/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/Chile/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/Chile/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/Arctic/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/Arctic/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/Antarctica/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/Antarctica/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tornado/test/static/dir/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tornado/test/static/dir/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/Atlantic/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/Atlantic/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tornado/test/templates/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tornado/test/templates/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/Canada/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/Canada/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/America/Argentina/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tzdata/zoneinfo/America/Argentina/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/jaraco/text/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/jaraco/text/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/more_itertools/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/more_itertools/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/rich/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/rich/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/rpds_py-0.20.0.dist-info/license_files/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/rpds_py-0.20.0.dist-info/license_files/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/simple_websocket-1.0.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/simple_websocket-1.0.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pyotp-2.9.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pyotp-2.9.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pygments/lexers/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pygments/lexers/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_distutils/tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_distutils/tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pygments/styles/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pygments/styles/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/python_dateutil-2.9.0.post0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/python_dateutil-2.9.0.post0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/command/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/command/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/rich-13.7.1.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/rich-13.7.1.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/zipp/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/zipp/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/tests/config/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/tests/config/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_distutils/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_distutils/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/qrcode/compat/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/qrcode/compat/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/qrcode/image/styles/moduledrawers/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/qrcode/image/styles/moduledrawers/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/requests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/requests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/jaraco/functools/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/jaraco/functools/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pyzmq-26.3.0.dist-info/licenses/licenses/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pyzmq-26.3.0.dist-info/licenses/licenses/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pyotp/contrib/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pyotp/contrib/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/python_dotenv-1.0.1.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/python_dotenv-1.0.1.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pygments/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pygments/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pytz/zoneinfo/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pytz/zoneinfo/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pyotp/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pyotp/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/referencing-0.35.1.dist-info/licenses/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/referencing-0.35.1.dist-info/licenses/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/compat/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/compat/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/zipp/compat/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/zipp/compat/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/autocommand/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/autocommand/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_distutils/command/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_distutils/command/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pyngrok/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pyngrok/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pyzmq-26.3.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pyzmq-26.3.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pygments-2.18.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pygments-2.18.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/requests-2.32.3.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/requests-2.32.3.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/compat/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/compat/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/referencing/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/referencing/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/tests/integration/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/tests/integration/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/python_socketio-5.11.3.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/python_socketio-5.11.3.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pytz-2024.1.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pytz-2024.1.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/referencing-0.35.1.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/referencing-0.35.1.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/packaging/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/packaging/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/rpds_py-0.20.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/rpds_py-0.20.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/simple_websocket/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/simple_websocket/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pytz/zoneinfo/Etc/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pytz/zoneinfo/Etc/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/tests/config/downloads/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/tests/config/downloads/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/tests/compat/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/tests/compat/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pyngrok-7.1.6.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pyngrok-7.1.6.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/qrcode/tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/qrcode/tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/qrcode-8.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/qrcode-8.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/licenses/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/licenses/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/packaging-24.2.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/packaging-24.2.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/typeguard/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/typeguard/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools-80.3.1.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools-80.3.1.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/rust/cryptography-x509/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/rust/cryptography-x509/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/jaraco/collections/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/jaraco/collections/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/config/_validate_pyproject/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/config/_validate_pyproject/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/config/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/config/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/referencing/tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/referencing/tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/rust/cryptography-cffi/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/rust/cryptography-cffi/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/python_engineio-4.9.1.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/python_engineio-4.9.1.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/rust/cryptography-x509-verification/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/rust/cryptography-x509-verification/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/qrcode/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/qrcode/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/qrcode/image/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/qrcode/image/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/tomli/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/tomli/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/rust/cryptography-openssl/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/rust/cryptography-openssl/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_distutils/compat/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_distutils/compat/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_distutils/compilers/C/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_distutils/compilers/C/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/platformdirs/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/platformdirs/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/backports/tarfile/compat/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/backports/tarfile/compat/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pygments-2.18.0.dist-info/licenses/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pygments-2.18.0.dist-info/licenses/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/packaging/licenses/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/packaging/licenses/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/backports/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/backports/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/inflect/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/inflect/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/rpds/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/rpds/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pytz/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pytz/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_distutils/compilers/C/tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_distutils/compilers/C/tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pyzmq-26.3.0.dist-info/licenses/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pyzmq-26.3.0.dist-info/licenses/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/qrcode/image/styles/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/qrcode/image/styles/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_distutils/tests/compat/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_distutils/tests/compat/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/tests/indexes/test_links_priority/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/tests/indexes/test_links_priority/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/backports/tarfile/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/backports/tarfile/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/rust/cryptography-key-parsing/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/rust/cryptography-key-parsing/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/rust/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/rust/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/rust/cryptography-keepalive/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/rust/cryptography-keepalive/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/inflect/compat/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/inflect/compat/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/jaraco/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/jaraco/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/tests/indexes/test_links_priority/simple/foobar/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/tests/indexes/test_links_priority/simple/foobar/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas_ta-0.3.14b0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas_ta-0.3.14b0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/prompt_toolkit/output/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/prompt_toolkit/output/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_vendor/pygments/formatters/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_vendor/pygments/formatters/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_vendor/rich/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_vendor/rich/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_internal/commands/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_internal/commands/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pure_eval/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pure_eval/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_internal/utils/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_internal/utils/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/prompt_toolkit-3.0.50.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/prompt_toolkit-3.0.50.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/psutil/tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/psutil/tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_internal/cli/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_internal/cli/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/prompt_toolkit/key_binding/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/prompt_toolkit/key_binding/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tornado-6.5.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tornado-6.5.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/prompt_toolkit/layout/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/prompt_toolkit/layout/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_vendor/resolvelib/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_vendor/resolvelib/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_internal/operations/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_internal/operations/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_vendor/certifi/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_vendor/certifi/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/prompt_toolkit/clipboard/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/prompt_toolkit/clipboard/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_internal/distributions/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_internal/distributions/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_internal/operations/install/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_internal/operations/install/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/prompt_toolkit/widgets/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/prompt_toolkit/widgets/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_internal/models/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_internal/models/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/platformdirs-4.3.7.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/platformdirs-4.3.7.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/prompt_toolkit/completion/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/prompt_toolkit/completion/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/prompt_toolkit/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/prompt_toolkit/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_internal/req/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_internal/req/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pycparser-2.22.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pycparser-2.22.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/parso/python/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/parso/python/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/ptyprocess-0.7.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/ptyprocess-0.7.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_vendor/distlib/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_vendor/distlib/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pure_eval-0.2.3.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pure_eval-0.2.3.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/parso-0.8.4.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/parso-0.8.4.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_vendor/urllib3/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_vendor/urllib3/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/ptyprocess/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/ptyprocess/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pillow-11.0.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pillow-11.0.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_vendor/platformdirs/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_vendor/platformdirs/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/prompt_toolkit/key_binding/bindings/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/prompt_toolkit/key_binding/bindings/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/prompt_toolkit/input/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/prompt_toolkit/input/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_internal/operations/build/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_internal/operations/build/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_vendor/urllib3/util/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_vendor/urllib3/util/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_vendor/requests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_vendor/requests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_internal/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_internal/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_internal/network/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_internal/network/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/prompt_toolkit/contrib/telnet/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/prompt_toolkit/contrib/telnet/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_internal/metadata/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_internal/metadata/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pexpect/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pexpect/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_vendor/tomli/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_vendor/tomli/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pygments/formatters/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pygments/formatters/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/prompt_toolkit/filters/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/prompt_toolkit/filters/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pycparser/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pycparser/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pycparser/ply/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pycparser/ply/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_internal/resolution/resolvelib/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_internal/resolution/resolvelib/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/prompt_toolkit/application/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/prompt_toolkit/application/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_vendor/pygments/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_vendor/pygments/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pkg_resources/tests/data/my-test-package-source/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pkg_resources/tests/data/my-test-package-source/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_vendor/cachecontrol/caches/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_vendor/cachecontrol/caches/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_vendor/cachecontrol/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_vendor/cachecontrol/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_internal/metadata/importlib/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_internal/metadata/importlib/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/prompt_toolkit/formatted_text/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/prompt_toolkit/formatted_text/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_vendor/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_vendor/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/tornado-6.5.dist-info/licenses/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/tornado-6.5.dist-info/licenses/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/psutil/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/psutil/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/platformdirs/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/platformdirs/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/prompt_toolkit/contrib/regular_languages/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/prompt_toolkit/contrib/regular_languages/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/prompt_toolkit/lexers/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/prompt_toolkit/lexers/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_vendor/urllib3/packages/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_vendor/urllib3/packages/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_vendor/idna/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_vendor/idna/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/psutil-7.0.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/psutil-7.0.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pexpect-4.9.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pexpect-4.9.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_vendor/pyproject_hooks/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_vendor/pyproject_hooks/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pkg_resources/tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pkg_resources/tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_vendor/packaging/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_vendor/packaging/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_internal/resolution/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_internal/resolution/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_vendor/truststore/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_vendor/truststore/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_vendor/urllib3/contrib/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_vendor/urllib3/contrib/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_internal/vcs/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_internal/vcs/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/prompt_toolkit/styles/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/prompt_toolkit/styles/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/parso/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/parso/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_vendor/urllib3/contrib/_securetransport/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_vendor/urllib3/contrib/_securetransport/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_internal/locations/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_internal/locations/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_vendor/msgpack/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_vendor/msgpack/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_vendor/distro/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_vendor/distro/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_internal/index/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_internal/index/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pkg_resources/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pkg_resources/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_vendor/pyproject_hooks/_in_process/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_vendor/pyproject_hooks/_in_process/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_vendor/pygments/lexers/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_vendor/pygments/lexers/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas_ta/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas_ta/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_internal/resolution/legacy/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_internal/resolution/legacy/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/prompt_toolkit/contrib/completers/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/prompt_toolkit/contrib/completers/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_vendor/urllib3/packages/backports/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_vendor/urllib3/packages/backports/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/parso/pgen2/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/parso/pgen2/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/prompt_toolkit/eventloop/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/prompt_toolkit/eventloop/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/prompt_toolkit/shortcuts/progress_bar/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/prompt_toolkit/shortcuts/progress_bar/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/prompt_toolkit/shortcuts/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/prompt_toolkit/shortcuts/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/prompt_toolkit/contrib/ssh/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/prompt_toolkit/contrib/ssh/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_vendor/pygments/filters/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_vendor/pygments/filters/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/prompt_toolkit/contrib/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/prompt_toolkit/contrib/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas_ta/volume/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas_ta/volume/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_vendor/pygments/styles/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_vendor/pygments/styles/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pygments/filters/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pygments/filters/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_vendor/pkg_resources/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_vendor/pkg_resources/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/platformdirs-4.3.7.dist-info/licenses/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/platformdirs-4.3.7.dist-info/licenses/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/io/formats/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/io/formats/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/io/formats/style/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/io/formats/style/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/scalar/timedelta/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/scalar/timedelta/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/window/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/window/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/indexes/datetimes/methods/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/indexes/datetimes/methods/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/io/pytables/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/io/pytables/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/groupby/transform/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/groupby/transform/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/indexes/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/indexes/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/series/methods/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/series/methods/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/resample/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/resample/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas_ta/momentum/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas_ta/momentum/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas_ta/overlap/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas_ta/overlap/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/series/accessors/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/series/accessors/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/groupby/methods/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/groupby/methods/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/scalar/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/scalar/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/indexes/period/methods/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/indexes/period/methods/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/indexes/ranges/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/indexes/ranges/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/tseries/holiday/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/tseries/holiday/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/indexes/timedeltas/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/indexes/timedeltas/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/indexes/datetimes/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/indexes/datetimes/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/indexes/period/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/indexes/period/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/frame/methods/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/frame/methods/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/libs/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/libs/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/indexes/multi/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/indexes/multi/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/generic/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/generic/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/indexing/interval/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/indexing/interval/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/io/parser/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/io/parser/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas-2.2.3.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas-2.2.3.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/indexes/interval/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/indexes/interval/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/plotting/frame/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/plotting/frame/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas_ta/volatility/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas_ta/volatility/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/frame/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/frame/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/indexing/multiindex/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/indexing/multiindex/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/util/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/util/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/reshape/merge/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/reshape/merge/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/indexing/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/indexing/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/plotting/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/plotting/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas_ta/trend/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas_ta/trend/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/tseries/offsets/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/tseries/offsets/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/groupby/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/groupby/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/reshape/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/reshape/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/tslibs/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/tslibs/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/tools/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/tools/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/interchange/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/interchange/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/io/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/io/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/scalar/period/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/scalar/period/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/indexes/numeric/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/indexes/numeric/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/scalar/interval/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/scalar/interval/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/indexes/categorical/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/indexes/categorical/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/io/parser/common/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/io/parser/common/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/tseries/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/tseries/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/series/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/series/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/io/excel/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/io/excel/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas_ta/utils/data/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas_ta/utils/data/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/scalar/timestamp/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/scalar/timestamp/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/util/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/util/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas_ta/candles/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas_ta/candles/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/tseries/frequencies/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/tseries/frequencies/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/scalar/timedelta/methods/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/scalar/timedelta/methods/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/strings/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/strings/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/scalar/timestamp/methods/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/scalar/timestamp/methods/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/window/moments/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/window/moments/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/indexes/base_class/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/indexes/base_class/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas_ta/utils/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas_ta/utils/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/reshape/concat/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/reshape/concat/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/series/indexing/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/series/indexing/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/internals/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/internals/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas_ta/statistics/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas_ta/statistics/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/indexes/datetimelike_/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/indexes/datetimelike_/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/io/parser/usecols/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/io/parser/usecols/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/groupby/aggregate/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/groupby/aggregate/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/io/json/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/io/json/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/io/xml/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/io/xml/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/io/sas/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/io/sas/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/io/parser/dtypes/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/io/parser/dtypes/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/util/version/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/util/version/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tseries/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tseries/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/indexes/timedeltas/methods/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/indexes/timedeltas/methods/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas_ta/performance/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas_ta/performance/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas_ta/cycles/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas_ta/cycles/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/reductions/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/reductions/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/indexes/object/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/indexes/object/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/packaging-24.1.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/packaging-24.1.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/typing/tests/data/fail/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/typing/tests/data/fail/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/core/tools/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/core/tools/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/random/_examples/cython/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/random/_examples/cython/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/api/typing/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/api/typing/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/arrays/floating/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/arrays/floating/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/typing/tests/data/pass/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/typing/tests/data/pass/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy-2.2.4.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy-2.2.4.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/core/ops/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/core/ops/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/ma/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/ma/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/typing/tests/data/reveal/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/typing/tests/data/reveal/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/apply/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/apply/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/core/indexes/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/core/indexes/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/openalgo-1.0.13.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/openalgo-1.0.13.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/copy_view/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/copy_view/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/core/dtypes/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/core/dtypes/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/core/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/core/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/arrays/masked/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/arrays/masked/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/core/groupby/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/core/groupby/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/base/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/base/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/extension/base/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/extension/base/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/core/arrays/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/core/arrays/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/extension/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/extension/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/_config/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/_config/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/matrixlib/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/matrixlib/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/random/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/random/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/arrays/sparse/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/arrays/sparse/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/random/tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/random/tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/_libs/tslibs/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/_libs/tslibs/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/dtypes/cast/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/dtypes/cast/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/core/reshape/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/core/reshape/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/io/formats/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/io/formats/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/compat/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/compat/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/core/computation/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/core/computation/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/io/excel/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/io/excel/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/matrixlib/tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/matrixlib/tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/core/indexers/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/core/indexers/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/core/strings/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/core/strings/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/arithmetic/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/arithmetic/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/io/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/io/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/core/window/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/core/window/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/frame/indexing/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/frame/indexing/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/copy_view/index/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/copy_view/index/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/plotting/_matplotlib/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/plotting/_matplotlib/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/arrays/string_/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/arrays/string_/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/io/json/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/io/json/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/api/indexers/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/api/indexers/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/construction/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/construction/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/io/formats/templates/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/io/formats/templates/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/core/_numba/kernels/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/core/_numba/kernels/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/polynomial/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/polynomial/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/strings/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/strings/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/arrays/period/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/arrays/period/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/arrays/categorical/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/arrays/categorical/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/packaging/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/packaging/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/polynomial/tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/polynomial/tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/random/_examples/cffi/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/random/_examples/cffi/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/extension/json/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/extension/json/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/core/internals/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/core/internals/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/core/arrays/arrow/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/core/arrays/arrow/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/core/methods/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/core/methods/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/core/array_algos/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/core/array_algos/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/ma/tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/ma/tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/random/tests/data/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/random/tests/data/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/typing/tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/typing/tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/_libs/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/_libs/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/ordered_set-4.1.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/ordered_set-4.1.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/arrays/timedeltas/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/arrays/timedeltas/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/random/_examples/numba/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/random/_examples/numba/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/_libs/window/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/_libs/window/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/testing/tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/testing/tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/plotting/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/plotting/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/io/parsers/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/io/parsers/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/arrays/boolean/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/arrays/boolean/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/io/sas/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/io/sas/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/testing/_private/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/testing/_private/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/core/util/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/core/util/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/_testing/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/_testing/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/arrays/integer/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/arrays/integer/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/dtypes/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/dtypes/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/api/extensions/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/api/extensions/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/ordered_set/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/ordered_set/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/arrays/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/arrays/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/arrays/numpy_/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/arrays/numpy_/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/core/sparse/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/core/sparse/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/openalgo/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/openalgo/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/arrays/interval/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/arrays/interval/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/rec/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/rec/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/arrays/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/arrays/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/extension/list/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/extension/list/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/compat/numpy/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/compat/numpy/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/extension/date/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/extension/date/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/extension/decimal/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/extension/decimal/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/frame/constructors/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/frame/constructors/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/typing/tests/data/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/typing/tests/data/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/testing/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/testing/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/typing/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/typing/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/core/interchange/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/core/interchange/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/config/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/config/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/core/arrays/sparse/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/core/arrays/sparse/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/computation/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/computation/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/api/interchange/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/api/interchange/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/api/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/api/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/typing/tests/data/misc/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/typing/tests/data/misc/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/api/types/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/api/types/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/arrays/datetimes/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/arrays/datetimes/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/tests/extension/array_with_attr/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/tests/extension/array_with_attr/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/core/_numba/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/core/_numba/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/io/clipboard/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/io/clipboard/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/errors/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/errors/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/linalg/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/linalg/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/openalgo-1.0.13.dist-info/licenses/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/openalgo-1.0.13.dist-info/licenses/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pandas/api/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pandas/api/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/linalg/tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/linalg/tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/lib/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/lib/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/lib/tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/lib/tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/fft/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/fft/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/fft/tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/fft/tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/f2py/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/f2py/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/value_attrspec/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/value_attrspec/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/string/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/string/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/size/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/size/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/routines/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/routines/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/return_real/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/return_real/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/return_logical/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/return_logical/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/return_integer/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/return_integer/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/return_complex/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/return_complex/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/return_character/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/return_character/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/regression/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/regression/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/quoted_character/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/quoted_character/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/parameter/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/parameter/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/negative_bounds/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/negative_bounds/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/modules/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/modules/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/modules/gh26920/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/modules/gh26920/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/modules/gh25337/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/modules/gh25337/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/mixed/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/mixed/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/kind/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/kind/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/isocintrin/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/isocintrin/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/f2cmap/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/f2cmap/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/crackfortran/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/crackfortran/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/common/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/common/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/cli/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/cli/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/callback/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/callback/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/block_docstring/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/block_docstring/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/assumed_shape/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/assumed_shape/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/array_from_pyobj/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/array_from_pyobj/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/abstract_interface/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/f2py/tests/src/abstract_interface/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/f2py/src/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/f2py/src/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/f2py/_backends/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/f2py/_backends/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/doc/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/doc/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/core/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/core/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/compat/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/compat/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/compat/tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/compat/tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/char/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/char/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/_utils/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/_utils/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/_typing/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/_typing/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/_pyinstaller/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/_pyinstaller/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/_pyinstaller/tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/_pyinstaller/tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/_core/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/_core/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/_core/tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/_core/tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/_core/tests/examples/limited_api/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/_core/tests/examples/limited_api/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/_core/tests/examples/cython/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/_core/tests/examples/cython/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/_core/tests/data/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/_core/tests/data/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/_core/lib/pkgconfig/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/_core/lib/pkgconfig/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/_core/lib/npy-pkg-config/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/_core/lib/npy-pkg-config/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/_core/include/numpy/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/_core/include/numpy/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/numpy/_core/include/numpy/random/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/numpy/_core/include/numpy/random/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/nest_asyncio-1.6.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/nest_asyncio-1.6.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/mdurl-0.1.2.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/mdurl-0.1.2.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/mdurl/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/mdurl/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/matplotlib_inline-0.1.7.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/matplotlib_inline-0.1.7.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/matplotlib_inline/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/matplotlib_inline/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/marshmallow-3.22.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/marshmallow-3.22.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/marshmallow/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/marshmallow/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/markupsafe/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/markupsafe/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/markdown_it_py-3.0.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/markdown_it_py-3.0.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/markdown_it/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/markdown_it/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/markdown_it/rules_inline/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/markdown_it/rules_inline/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/markdown_it/rules_core/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/markdown_it/rules_core/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/markdown_it/rules_block/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/markdown_it/rules_block/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/markdown_it/presets/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/markdown_it/presets/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/markdown_it/helpers/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/markdown_it/helpers/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/markdown_it/common/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/markdown_it/common/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/markdown_it/cli/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/markdown_it/cli/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/logzero-1.7.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/logzero-1.7.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/logzero/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/logzero/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/limits-3.13.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/limits-3.13.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/limits/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/limits/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/limits/storage/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/limits/storage/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/limits/resources/redis/lua_scripts/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/limits/resources/redis/lua_scripts/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/limits/aio/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/limits/aio/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/limits/aio/storage/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/limits/aio/storage/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jwt/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jwt/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jupyter_core-5.7.2.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jupyter_core-5.7.2.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jupyter_core-5.7.2.dist-info/licenses/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jupyter_core-5.7.2.dist-info/licenses/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jupyter_core/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jupyter_core/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jupyter_core/utils/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jupyter_core/utils/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jupyter_client-8.6.3.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jupyter_client-8.6.3.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jupyter_client-8.6.3.dist-info/licenses/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jupyter_client-8.6.3.dist-info/licenses/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jupyter_client/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jupyter_client/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jupyter_client/ssh/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jupyter_client/ssh/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jupyter_client/provisioning/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jupyter_client/provisioning/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jupyter_client/ioloop/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jupyter_client/ioloop/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jupyter_client/blocking/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jupyter_client/blocking/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jupyter_client/asynchronous/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jupyter_client/asynchronous/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jsonschema_specifications-2023.12.1.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jsonschema_specifications-2023.12.1.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jsonschema_specifications-2023.12.1.dist-info/licenses/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jsonschema_specifications-2023.12.1.dist-info/licenses/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jsonschema_specifications/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jsonschema_specifications/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jsonschema_specifications/tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jsonschema_specifications/tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jsonschema_specifications/schemas/draft7/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jsonschema_specifications/schemas/draft7/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jsonschema_specifications/schemas/draft6/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jsonschema_specifications/schemas/draft6/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jsonschema_specifications/schemas/draft4/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jsonschema_specifications/schemas/draft4/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jsonschema_specifications/schemas/draft3/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jsonschema_specifications/schemas/draft3/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jsonschema_specifications/schemas/draft202012/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jsonschema_specifications/schemas/draft202012/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jsonschema_specifications/schemas/draft201909/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jsonschema_specifications/schemas/draft201909/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jsonschema_specifications/schemas/draft201909/vocabularies/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jsonschema_specifications/schemas/draft201909/vocabularies/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jsonschema-4.23.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jsonschema-4.23.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jsonschema-4.23.0.dist-info/licenses/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jsonschema-4.23.0.dist-info/licenses/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jsonschema/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jsonschema/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jsonschema/tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jsonschema/tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jsonschema/benchmarks/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jsonschema/benchmarks/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jsonschema/benchmarks/issue232/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jsonschema/benchmarks/issue232/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jinja2-3.1.6.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jinja2-3.1.6.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jinja2-3.1.6.dist-info/licenses/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jinja2-3.1.6.dist-info/licenses/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jinja2/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jinja2/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi-0.19.2.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi-0.19.2.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/waitress/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/waitress/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/typed_ast/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/typed_ast/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/six/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/six/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/six/moves/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/six/moves/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/six/moves/urllib/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/six/moves/urllib/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/pyrfc3339/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/pyrfc3339/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/pkg_resources/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/pkg_resources/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/jwt/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/jwt/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/jwt/contrib/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/jwt/contrib/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/jwt/contrib/algorithms/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/jwt/contrib/algorithms/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/freezegun/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/freezegun/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/filelock/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/filelock/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/docutils/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/docutils/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/docutils/parsers/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/docutils/parsers/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/docutils/parsers/rst/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/docutils/parsers/rst/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/aiofiles/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/aiofiles/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/aiofiles/threadpool/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/3/aiofiles/threadpool/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/yaml/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/yaml/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/werkzeug/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/werkzeug/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/werkzeug/middleware/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/werkzeug/middleware/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/werkzeug/debug/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/werkzeug/debug/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/werkzeug/contrib/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/werkzeug/contrib/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/tzlocal/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/tzlocal/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/slugify/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/slugify/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/simplejson/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/simplejson/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/retry/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/retry/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/requests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/requests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/requests/packages/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/requests/packages/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/requests/packages/urllib3/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/requests/packages/urllib3/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/requests/packages/urllib3/util/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/requests/packages/urllib3/util/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/requests/packages/urllib3/packages/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/requests/packages/urllib3/packages/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/requests/packages/urllib3/packages/ssl_match_hostname/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/requests/packages/urllib3/packages/ssl_match_hostname/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/requests/packages/urllib3/contrib/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/requests/packages/urllib3/contrib/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/redis/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/redis/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/pytz/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/pytz/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/pynamodb/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/pynamodb/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/pynamodb/connection/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/pynamodb/connection/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/pymysql/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/pymysql/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/pymysql/constants/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/pymysql/constants/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/pyVmomi/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/pyVmomi/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/pyVmomi/vmodl/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/pyVmomi/vmodl/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/pyVmomi/vim/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/pyVmomi/vim/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/paramiko/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/paramiko/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/nmap/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/nmap/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/maxminddb/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/maxminddb/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/markupsafe/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/markupsafe/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/markdown/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/markdown/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/markdown/extensions/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/markdown/extensions/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/jinja2/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/jinja2/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/google/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/google/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/google/protobuf/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/google/protobuf/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/emoji/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/emoji/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/dbm/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/dbm/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/click/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/click/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/utils/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/utils/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2/tornado/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2/tornado/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/views/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/views/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/ctypes/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/ctypes/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2/scribe/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2/scribe/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/boto/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/boto/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/asyncio/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/asyncio/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/distutils/command/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/distutils/command/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/primitives/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/primitives/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/email/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/email/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/lib2to3/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/lib2to3/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/xml/parsers/expat/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/xml/parsers/expat/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2/distutils/command/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2/distutils/command/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/venv/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/venv/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/unittest/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/unittest/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3.7/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3.7/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2/six/moves/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2/six/moves/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/primitives/asymmetric/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/primitives/asymmetric/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2/kazoo/recipe/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2/kazoo/recipe/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/flask/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/flask/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/backends/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/backends/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/multiprocessing/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/multiprocessing/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/email/mime/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/email/mime/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/boto/s3/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/boto/s3/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2/email/mime/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2/email/mime/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2/six/moves/urllib/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2/six/moves/urllib/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/wsgiref/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/wsgiref/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/urllib/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/urllib/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/primitives/ciphers/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/primitives/ciphers/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2/distutils/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2/distutils/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/attr/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/attr/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/distutils/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/distutils/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2/email/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2/email/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/bleach/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/bleach/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/collections/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/collections/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/urls/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/urls/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/geoip2/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/geoip2/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/cryptography/x509/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/cryptography/x509/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2/fb303/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2/fb303/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/msilib/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/msilib/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/characteristic/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/characteristic/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/xml/parsers/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/xml/parsers/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/boto/elb/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/boto/elb/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/curses/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/curses/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/lib2to3/pgen2/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/lib2to3/pgen2/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2/six/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2/six/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/chardet/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/chardet/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/cachetools/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/cachetools/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2/encodings/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2/encodings/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/xml/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/xml/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/google/protobuf/internal/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/google/protobuf/internal/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/primitives/kdf/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/primitives/kdf/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2/multiprocessing/dummy/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2/multiprocessing/dummy/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/xml/etree/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/xml/etree/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/concurrent/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/concurrent/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/views/decorators/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/views/decorators/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/concurrent/futures/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/concurrent/futures/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/html/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/html/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/tkinter/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/tkinter/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/logging/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/logging/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/xml/dom/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/xml/dom/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/pyexpat/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/pyexpat/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/importlib/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/importlib/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/pydoc_data/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/pydoc_data/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/xmlrpc/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/xmlrpc/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2/OpenSSL/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2/OpenSSL/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/bindings/openssl/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/bindings/openssl/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/multiprocessing/dummy/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/multiprocessing/dummy/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2/kazoo/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2/kazoo/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/ensurepip/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/ensurepip/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/boto/kms/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/boto/kms/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/_typeshed/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/_typeshed/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/dateutil/tz/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/dateutil/tz/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/backports/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/backports/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/primitives/serialization/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/primitives/serialization/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/datetimerange/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/datetimerange/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/xml/sax/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/xml/sax/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2/multiprocessing/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2/multiprocessing/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/json/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/json/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2/os/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2/os/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/sqlite3/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/2and3/sqlite3/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/google/protobuf/compiler/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/google/protobuf/compiler/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/google/protobuf/util/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/google/protobuf/util/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/views/generic/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/views/generic/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/utils/translation/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/utils/translation/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/dateutil/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/dateutil/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/http/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/http/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2/routes/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2/routes/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/cryptography/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/cryptography/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/primitives/twofactor/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/primitives/twofactor/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2/concurrent/futures/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2/concurrent/futures/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/deprecated/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/deprecated/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/encodings/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/encodings/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3.9/zoneinfo/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3.9/zoneinfo/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2/concurrent/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2/concurrent/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/flask/json/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/flask/json/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/bindings/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/bindings/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/os/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3/os/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/boto/ec2/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/boto/ec2/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3.9/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/stdlib/3.9/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/atomicwrites/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/typeshed/third_party/2and3/atomicwrites/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/ipython_pygments_lexers-1.1.1.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/ipython_pygments_lexers-1.1.1.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/idna-3.7.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/idna-3.7.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/db/backends/sqlite3/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/db/backends/sqlite3/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/contenttypes/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/contenttypes/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/ipykernel/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/ipykernel/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/inference/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/inference/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/importlib_resources/tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/importlib_resources/tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/greenlet/platform/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/greenlet/platform/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/hpack/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/hpack/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/auth/management/commands/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/auth/management/commands/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/h11/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/h11/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/greenlet/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/greenlet/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/idna/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/idna/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/ipykernel/inprocess/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/ipykernel/inprocess/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/flask_login/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/flask_login/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/core/serializers/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/core/serializers/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/importlib_resources/tests/data02/one/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/importlib_resources/tests/data02/one/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/ipython-9.0.2.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/ipython-9.0.2.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/inference/compiled/subprocess/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/inference/compiled/subprocess/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/db/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/db/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/inference/value/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/inference/value/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/flask_restx-1.3.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/flask_restx-1.3.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/core/checks/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/core/checks/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/flask_restx/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/flask_restx/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/template/loaders/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/template/loaders/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/apps/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/apps/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/auth/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/auth/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/sessions/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/sessions/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/sessions/management/commands/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/sessions/management/commands/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/core/files/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/core/files/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/hyperframe/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/hyperframe/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/template/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/template/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/hyperframe-6.1.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/hyperframe-6.1.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/postgres/fields/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/postgres/fields/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/auth/handlers/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/auth/handlers/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/itsdangerous-2.2.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/itsdangerous-2.2.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/flask_sqlalchemy-3.1.1.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/flask_sqlalchemy-3.1.1.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/gunicorn-23.0.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/gunicorn-23.0.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/core/management/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/core/management/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/importlib_resources/tests/data01/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/importlib_resources/tests/data01/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/db/models/fields/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/db/models/fields/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/itsdangerous/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/itsdangerous/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/middleware/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/middleware/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/db/migrations/operations/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/db/migrations/operations/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/api/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/api/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/httpcore/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/httpcore/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/flask_wtf/recaptcha/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/flask_wtf/recaptcha/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/forms/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/forms/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/core/mail/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/core/mail/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/httpcore/_sync/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/httpcore/_sync/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/staticfiles/management/commands/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/staticfiles/management/commands/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/db/models/functions/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/db/models/functions/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/flask_sqlalchemy/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/flask_sqlalchemy/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/httpx/_transports/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/httpx/_transports/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/messages/storage/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/messages/storage/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/flask_wtf-1.2.1.dist-info/licenses/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/flask_wtf-1.2.1.dist-info/licenses/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/db/models/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/db/models/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/ipykernel/comm/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/ipykernel/comm/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/httpx/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/httpx/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/core/management/commands/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/core/management/commands/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/test/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/test/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/db/models/sql/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/db/models/sql/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/ipykernel/resources/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/ipykernel/resources/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/sites/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/sites/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/core/checks/security/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/core/checks/security/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/flask_limiter/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/flask_limiter/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/inference/gradual/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/inference/gradual/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/gunicorn/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/gunicorn/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/flask_restx/templates/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/flask_restx/templates/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/sitemaps/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/sitemaps/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/greenlet-3.1.1.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/greenlet-3.1.1.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/flask_restx/static/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/flask_restx/static/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/gunicorn/http/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/gunicorn/http/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/core/handlers/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/core/handlers/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/httpx-0.28.1.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/httpx-0.28.1.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/httpcore/_async/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/httpcore/_async/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/gunicorn/instrument/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/gunicorn/instrument/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/greenlet/tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/greenlet/tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/h2/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/h2/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/messages/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/messages/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/db/backends/mysql/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/db/backends/mysql/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/sessions/backends/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/sessions/backends/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/db/backends/base/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/db/backends/base/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/importlib_resources/tests/compat/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/importlib_resources/tests/compat/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/core/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/core/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/ipykernel-6.29.5.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/ipykernel-6.29.5.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/h2-4.2.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/h2-4.2.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/db/migrations/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/db/migrations/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/importlib_resources/compat/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/importlib_resources/compat/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/postgres/aggregates/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/postgres/aggregates/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/ipykernel/gui/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/ipykernel/gui/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/gunicorn/app/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/gunicorn/app/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/httpcore-1.0.9.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/httpcore-1.0.9.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/postgres/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/postgres/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/flatpages/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/flatpages/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/core/mail/backends/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/core/mail/backends/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/template/backends/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/template/backends/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/db/backends/postgresql/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/db/backends/postgresql/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/importlib_resources-6.4.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/importlib_resources-6.4.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/inference/compiled/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/inference/compiled/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/flask_wtf/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/flask_wtf/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/httpcore/_backends/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/httpcore/_backends/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/importlib_resources/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/importlib_resources/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/templatetags/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/templatetags/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/staticfiles/templatetags/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/staticfiles/templatetags/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/admin/views/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/admin/views/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/conf/locale/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/conf/locale/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/flask_cors-6.0.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/flask_cors-6.0.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/auth/management/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/auth/management/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/flask_wtf-1.2.1.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/flask_wtf-1.2.1.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/h11-0.16.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/h11-0.16.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/flatpages/templatetags/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/flatpages/templatetags/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/contenttypes/management/commands/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/contenttypes/management/commands/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/db/backends/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/db/backends/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/flask_cors/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/flask_cors/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/admin/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/admin/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/core/cache/backends/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/core/cache/backends/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/conf/urls/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/conf/urls/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/ipykernel/pylab/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/ipykernel/pylab/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/staticfiles/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/staticfiles/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/plugins/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/plugins/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/hpack-4.1.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/hpack-4.1.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/conf/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/conf/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/flask_socketio/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/flask_socketio/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/gis/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/gis/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/http/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/http/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/admin/templatetags/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/admin/templatetags/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/admindocs/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/admindocs/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/importlib_resources/tests/data01/subdirectory/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/importlib_resources/tests/data01/subdirectory/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/flask_restx/schemas/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/flask_restx/schemas/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/redirects/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/redirects/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/gunicorn/workers/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/gunicorn/workers/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/flask_limiter/contrib/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/flask_limiter/contrib/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/api/refactoring/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/api/refactoring/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/importlib_resources/tests/namespacedata01/subdirectory/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/importlib_resources/tests/namespacedata01/subdirectory/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/importlib_resources/future/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/importlib_resources/future/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/flask_restx/static/files/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/flask_restx/static/files/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/importlib_resources/tests/data02/subdirectory/subsubdir/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/importlib_resources/tests/data02/subdirectory/subsubdir/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/core/servers/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/core/servers/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/importlib_resources/tests/data02/two/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/importlib_resources/tests/data02/two/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/syndication/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/syndication/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/sessions/management/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/sessions/management/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/humanize/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/humanize/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/gis/db/models/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/gis/db/models/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/httpcore-1.0.9.dist-info/licenses/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/httpcore-1.0.9.dist-info/licenses/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/importlib_resources/tests/namespacedata01/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/importlib_resources/tests/namespacedata01/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/h11-0.16.0.dist-info/licenses/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/h11-0.16.0.dist-info/licenses/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/core/cache/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/core/cache/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/db/backends/dummy/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/db/backends/dummy/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/importlib_resources/tests/data02/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/importlib_resources/tests/data02/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/humanize/templatetags/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/humanize/templatetags/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/sitemaps/management/commands/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/sitemaps/management/commands/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/dispatch/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/dispatch/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/staticfiles/management/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/staticfiles/management/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/ipykernel-6.29.5.dist-info/licenses/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/ipykernel-6.29.5.dist-info/licenses/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/contenttypes/management/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/contenttypes/management/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/sitemaps/management/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/sitemaps/management/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/gis/db/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/jedi/third_party/django-stubs/django-stubs/contrib/gis/db/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/httpx-0.28.1.dist-info/licenses/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/httpx-0.28.1.dist-info/licenses/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/executing/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/executing/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/darkdetect/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/darkdetect/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/email_validator-2.1.1.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/email_validator-2.1.1.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/cryptography/hazmat/bindings/openssl/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/cryptography/hazmat/bindings/openssl/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/green/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/eventlet/green/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/dns/rdtypes/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/dns/rdtypes/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/duckdb-1.2.2.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/duckdb-1.2.2.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/dns/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/dns/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/dns/rdtypes/IN/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/dns/rdtypes/IN/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/dns/quic/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/dns/quic/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/cryptography/hazmat/primitives/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/cryptography/hazmat/primitives/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/flask/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/flask/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/cryptography/hazmat/bindings/_rust/openssl/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/cryptography/hazmat/bindings/_rust/openssl/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/support/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/eventlet/support/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/windows/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/windows/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/cffi/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/cffi/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/debugpy/server/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/debugpy/server/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/debugpy/common/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/debugpy/common/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/decorator-5.2.1.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/decorator-5.2.1.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/engineio/async_drivers/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/engineio/async_drivers/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/flask-3.0.3.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/flask-3.0.3.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/comm-0.2.2.dist-info/licenses/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/comm-0.2.2.dist-info/licenses/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/pydev_ipython/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/pydev_ipython/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/flask/json/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/flask/json/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/dns/dnssecalgs/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/dns/dnssecalgs/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/charset_normalizer-3.3.2.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/charset_normalizer-3.3.2.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/cryptography/hazmat/primitives/kdf/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/cryptography/hazmat/primitives/kdf/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/engineio/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/engineio/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/dns/rdtypes/ANY/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/dns/rdtypes/ANY/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/cryptography/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/cryptography/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/extensions/types/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/extensions/types/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/duckdb-stubs/functional/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/duckdb-stubs/functional/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/certifi-2024.7.4.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/certifi-2024.7.4.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/_pydevd_sys_monitoring/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/_pydevd_sys_monitoring/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/dotenv/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/dotenv/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/pydev_sitecustomize/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/pydev_sitecustomize/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/blinker/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/blinker/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/linux_and_mac/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/linux_and_mac/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/eventlet/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_concurrency_analyser/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_concurrency_analyser/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/bidict/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/bidict/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/debugpy-1.8.13.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/debugpy-1.8.13.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/cryptography-44.0.1.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/cryptography-44.0.1.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/blinker-1.8.2.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/blinker-1.8.2.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/cryptography/hazmat/primitives/serialization/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/cryptography/hazmat/primitives/serialization/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/cryptography-44.0.1.dist-info/licenses/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/cryptography-44.0.1.dist-info/licenses/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/bcrypt-4.1.3.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/bcrypt-4.1.3.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/email_validator/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/email_validator/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/green/OpenSSL/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/eventlet/green/OpenSSL/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/hubs/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/eventlet/hubs/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/cryptography/hazmat/decrepit/ciphers/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/cryptography/hazmat/decrepit/ciphers/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/zipkin/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/eventlet/zipkin/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/executing-2.2.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/executing-2.2.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/duckdb/experimental/spark/sql/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/duckdb/experimental/spark/sql/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/fsnotify/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/fsnotify/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/green/urllib/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/eventlet/green/urllib/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/debugpy/adapter/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/debugpy/adapter/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/charset_normalizer/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/charset_normalizer/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/duckdb-stubs/typing/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/duckdb-stubs/typing/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/cryptography/hazmat/primitives/twofactor/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/cryptography/hazmat/primitives/twofactor/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet-0.40.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/eventlet-0.40.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/green/http/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/eventlet/green/http/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/darkdetect-0.8.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/darkdetect-0.8.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/cryptography/x509/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/cryptography/x509/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/debugpy/launcher/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/debugpy/launcher/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/extensions/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/extensions/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/flask/sansio/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/flask/sansio/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/comm/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/comm/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/zipkin/_thrift/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/eventlet/zipkin/_thrift/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/dateutil/parser/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/dateutil/parser/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/dateutil/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/dateutil/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/cryptography/hazmat/decrepit/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/cryptography/hazmat/decrepit/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/duckdb/experimental/spark/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/duckdb/experimental/spark/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/click-8.1.7.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/click-8.1.7.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/_debug_adapter/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/_debug_adapter/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/colorama-0.4.6.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/colorama-0.4.6.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/colorama/tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/colorama/tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/click/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/click/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/common/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/common/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/dateutil/tz/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/dateutil/tz/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/deprecated/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/deprecated/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/duckdb-stubs/value/constant/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/duckdb-stubs/value/constant/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/cryptography/hazmat/primitives/asymmetric/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/cryptography/hazmat/primitives/asymmetric/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/cryptography/hazmat/bindings/_rust/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/cryptography/hazmat/bindings/_rust/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/zipkin/_thrift/zipkinCore/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/eventlet/zipkin/_thrift/zipkinCore/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/colorama/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/colorama/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/certifi/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/certifi/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/bidict-0.23.1.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/bidict-0.23.1.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/bcrypt/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/bcrypt/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/cffi-1.17.1.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/cffi-1.17.1.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet-0.40.0.dist-info/licenses/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/eventlet-0.40.0.dist-info/licenses/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/debugpy/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/debugpy/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/greenio/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/eventlet/greenio/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/cryptography/hazmat/backends/openssl/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/cryptography/hazmat/backends/openssl/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/cryptography/hazmat/primitives/ciphers/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/cryptography/hazmat/primitives/ciphers/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/duckdb/query_graph/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/duckdb/query_graph/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/attrs-24.2.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/attrs-24.2.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/comm-0.2.2.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/comm-0.2.2.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/colorama-0.4.6.dist-info/licenses/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/colorama-0.4.6.dist-info/licenses/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/cachetools-5.3.3.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/cachetools-5.3.3.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/debugpy/_vendored/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/dnspython-2.6.1.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/dnspython-2.6.1.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/duckdb/experimental/spark/errors/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/duckdb/experimental/spark/errors/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/dns/rdtypes/CH/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/dns/rdtypes/CH/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/duckdb/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/duckdb/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/duckdb/experimental/spark/errors/exceptions/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/duckdb/experimental/spark/errors/exceptions/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/charset_normalizer/cli/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/charset_normalizer/cli/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/duckdb/experimental/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/duckdb/experimental/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/cryptography/hazmat/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/cryptography/hazmat/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/dateutil/zoneinfo/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/dateutil/zoneinfo/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/dnspython-2.6.1.dist-info/licenses/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/dnspython-2.6.1.dist-info/licenses/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/duckdb-stubs/value/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/duckdb-stubs/value/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/cachetools/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/cachetools/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/duckdb/functional/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/duckdb/functional/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/cryptography/hazmat/backends/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/cryptography/hazmat/backends/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/duckdb-stubs/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/duckdb-stubs/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/cryptography/hazmat/bindings/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/cryptography/hazmat/bindings/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/duckdb/typing/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/duckdb/typing/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/duckdb/value/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/duckdb/value/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/IPython/core/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/IPython/core/"}, "/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/"}, "/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/IPython/sphinxext/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/IPython/sphinxext/"}, "/home/<USER>/shared-venv/bin/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/bin/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/_argon2_cffi_bindings/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/_argon2_cffi_bindings/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/IPython/testing/plugin/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/IPython/testing/plugin/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/PIL/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/PIL/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/IPython/core/magics/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/IPython/core/magics/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/Deprecated-1.2.14.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/Deprecated-1.2.14.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/asttokens/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/asttokens/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/anyio/_core/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/anyio/_core/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/Flask_Bcrypt-1.0.1.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/Flask_Bcrypt-1.0.1.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/IPython/terminal/pt_inputhooks/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/IPython/terminal/pt_inputhooks/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/IPython/extensions/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/IPython/extensions/"}, "/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/IPython/lib/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/IPython/lib/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/MarkupSafe-2.1.5.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/MarkupSafe-2.1.5.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/aniso8601/builders/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/aniso8601/builders/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/apscheduler/executors/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/apscheduler/executors/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/IPython/utils/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/IPython/utils/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/anyio/abc/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/anyio/abc/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/APScheduler-3.11.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/APScheduler-3.11.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/anyio/_backends/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/anyio/_backends/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/anyio/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/anyio/"}, "/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/IPython/terminal/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/IPython/terminal/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/Flask_Login-0.6.3.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/Flask_Login-0.6.3.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/aniso8601/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/aniso8601/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/anyio/streams/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/anyio/streams/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/attrs/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/attrs/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/apscheduler/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/apscheduler/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/Flask_Limiter-3.7.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/Flask_Limiter-3.7.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/IPython/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/IPython/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/aniso8601/tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/aniso8601/tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/IPython/testing/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/IPython/testing/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/attr/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/attr/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/aniso8601-9.0.1.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/aniso8601-9.0.1.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/adbc_driver_duckdb/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/adbc_driver_duckdb/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/apscheduler/triggers/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/apscheduler/triggers/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/PyYAML-6.0.1.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/PyYAML-6.0.1.dist-info/"}, "/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/PyJWT-2.8.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/PyJWT-2.8.0.dist-info/"}, "/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/apscheduler/jobstores/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/apscheduler/jobstores/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/argon2_cffi_bindings-21.2.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/argon2_cffi_bindings-21.2.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/asttokens-3.0.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/asttokens-3.0.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/argon2/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/argon2/"}, "/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.482.1/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/extensions/augment.vscode-augment-0.482.1/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/anyio-4.8.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/anyio-4.8.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/apscheduler/schedulers/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/apscheduler/schedulers/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/Flask_SocketIO-5.3.6.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/Flask_SocketIO-5.3.6.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/attrs-24.2.0.dist-info/licenses/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/attrs-24.2.0.dist-info/licenses/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/SQLAlchemy-2.0.31.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/SQLAlchemy-2.0.31.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/argon2_cffi-23.1.0.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/argon2_cffi-23.1.0.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/IPython/terminal/shortcuts/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/IPython/terminal/shortcuts/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/_distutils_hack/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/_distutils_hack/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/IPython/extensions/deduperreload/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/IPython/extensions/deduperreload/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/apscheduler/triggers/cron/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/apscheduler/triggers/cron/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/IPython/external/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/IPython/external/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/_yaml/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/_yaml/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/aniso8601/builders/tests/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/aniso8601/builders/tests/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/IPython/core/profile/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/IPython/core/profile/"}, "/home/<USER>/shared-venv/include/site/python3.12/greenlet/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/include/site/python3.12/greenlet/"}, "/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/argon2_cffi-23.1.0.dist-info/licenses/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/argon2_cffi-23.1.0.dist-info/licenses/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/lib/web/cookies/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/lib/web/cookies/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/uuid/dist/commonjs-browser/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/uuid/dist/commonjs-browser/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/vscode-regexpp/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/vscode-regexpp/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/pump/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/pump/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/lib/web/websocket/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/lib/web/websocket/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/graceful-fs/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/graceful-fs/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/contrib/terminal/common/scripts/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/contrib/terminal/common/scripts/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/mkdirp/lib/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/mkdirp/lib/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/semver/ranges/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/semver/ranges/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/uuid/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/uuid/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/tar-stream/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/tar-stream/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/proxy-from-env/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/proxy-from-env/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/yauzl/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/yauzl/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/opentype.js/src/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/opentype.js/src/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/font-finder/dist/tables/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/font-finder/dist/tables/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/opentype.js/src/tables/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/opentype.js/src/tables/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/node-pty/lib/worker/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/node-pty/lib/worker/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/is-number/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/is-number/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/lib/util/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/lib/util/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/vsce-sign/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/vsce-sign/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/ip-address/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/ip-address/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/addon-webgl/lib/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/addon-webgl/lib/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/jschardet/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/jschardet/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/applicationinsights-shims/dist/esm/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/applicationinsights-shims/dist/esm/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/font-ligatures/dist/processors/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/font-ligatures/dist/processors/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/jsbn/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/jsbn/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/addon-clipboard/lib/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/addon-clipboard/lib/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/inherits/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/inherits/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/applicationinsights-shims/browser/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/applicationinsights-shims/browser/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/native-watchdog/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/native-watchdog/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/string_decoder/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/string_decoder/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/addon-image/out/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/addon-image/out/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/vsda/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/vsda/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/js-base64/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/js-base64/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/tree-sitter-wasm/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/tree-sitter-wasm/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/semver/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/semver/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/vscode-oniguruma/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/vscode-oniguruma/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/uuid/dist/esm-node/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/uuid/dist/esm-node/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/yallist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/yallist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/iconv-lite-umd/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/iconv-lite-umd/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/vscode-languagedetection/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/vscode-languagedetection/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/once/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/once/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/deviceid/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/deviceid/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/strip-json-comments/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/strip-json-comments/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/lib/interceptor/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/lib/interceptor/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/get-system-fonts/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/get-system-fonts/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/mkdirp-classic/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/mkdirp-classic/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/addon-search/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/addon-search/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/fs-extra/lib/move/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/fs-extra/lib/move/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/node-pty/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/node-pty/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/addon-progress/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/addon-progress/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/vscode-regexp-languagedetection/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/vscode-regexp-languagedetection/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/spdlog/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/spdlog/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/dynamicproto-js/lib/dist/system/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/dynamicproto-js/lib/dist/system/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/mimic-response/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/mimic-response/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/fs-extra/lib/ensure/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/fs-extra/lib/ensure/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/fs-constants/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/fs-constants/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/addon-progress/lib/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/addon-progress/lib/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/lru-cache/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/lru-cache/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/node-pty/lib/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/node-pty/lib/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/agent-base/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/agent-base/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/opentype.js/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/opentype.js/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/fill-range/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/fill-range/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/semver/functions/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/semver/functions/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@parcel/watcher/scripts/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@parcel/watcher/scripts/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/font-ligatures/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/font-ligatures/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/fs-extra/lib/util/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/fs-extra/lib/util/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/sprintf-js/src/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/sprintf-js/src/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/vscode-oniguruma/release/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/vscode-oniguruma/release/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/opentype.js/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/opentype.js/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/applicationinsights-core-js/dist-esm/JavaScriptSDK.Interfaces/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/applicationinsights-core-js/dist-esm/JavaScriptSDK.Interfaces/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/decompress-response/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/decompress-response/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@parcel/watcher/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@parcel/watcher/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/lib/api/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/lib/api/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/fs-extra/lib/path-exists/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/fs-extra/lib/path-exists/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/lib/dispatcher/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/lib/dispatcher/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/addon-image/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/addon-image/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/kerberos/lib/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/kerberos/lib/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/applicationinsights-shims/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/applicationinsights-shims/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/dynamicproto-js/lib/dist/node/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/dynamicproto-js/lib/dist/node/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/agent-base/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/agent-base/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/fs-extra/lib/copy/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/fs-extra/lib/copy/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/lib/web/cache/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/lib/web/cache/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/lib/mock/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/lib/mock/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/readable-stream/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/readable-stream/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/opentype.js/bin/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/opentype.js/bin/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/headless/lib-headless/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/headless/lib-headless/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/fs-extra/lib/json/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/fs-extra/lib/json/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/buffer/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/buffer/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/font-finder/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/font-finder/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/ip-address/dist/v4/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/ip-address/dist/v4/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/uuid/dist/esm-browser/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/uuid/dist/esm-browser/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/ieee754/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/ieee754/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/node-abi/scripts/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/node-abi/scripts/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/deviceid/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/deviceid/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/readable-stream/lib/internal/streams/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/readable-stream/lib/internal/streams/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/socks/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/socks/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/lib/core/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/lib/core/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/dynamicproto-js/lib/dist/esm/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/dynamicproto-js/lib/dist/esm/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/fs-extra/lib/remove/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/fs-extra/lib/remove/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/minimist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/minimist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/readable-stream/lib/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/readable-stream/lib/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/micromatch/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/micromatch/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/simple-concat/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/simple-concat/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/tar-fs/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/tar-fs/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/proxy-agent/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/proxy-agent/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/xterm/lib/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/xterm/lib/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/jschardet/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/jschardet/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/mkdirp/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/mkdirp/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/applicationinsights-shims/dist/umd/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/applicationinsights-shims/dist/umd/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/kerberos/lib/auth_processes/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/kerberos/lib/auth_processes/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/applicationinsights-core-js/dist-esm/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/applicationinsights-core-js/dist-esm/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/lib/web/fetch/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/lib/web/fetch/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/font-ligatures/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/font-ligatures/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/uuid/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/uuid/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@tootallnate/once/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@tootallnate/once/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/rc/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/rc/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/tas-client-umd/lib/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/tas-client-umd/lib/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/deep-extend/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/deep-extend/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/jschardet/scripts/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/jschardet/scripts/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/ripgrep/node_modules/yauzl/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/ripgrep/node_modules/yauzl/"}, "/home/<USER>/.vscode-server/cli/servers/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/lib/llhttp/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/lib/llhttp/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/end-of-stream/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/end-of-stream/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/yazl/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/yazl/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/napi-build-utils/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/napi-build-utils/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/applicationinsights-shims/dist-esm/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/applicationinsights-shims/dist-esm/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/dynamicproto-js/lib/dist/cjs/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/dynamicproto-js/lib/dist/cjs/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/sprintf-js/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/sprintf-js/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/ini/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/ini/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/universalify/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/universalify/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/tas-client-umd/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/tas-client-umd/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/https-proxy-agent/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/https-proxy-agent/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/ripgrep/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/ripgrep/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/fs-extra/lib/mkdirs/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/fs-extra/lib/mkdirs/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/xterm/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/xterm/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/dynamicproto-js/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/dynamicproto-js/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/vsce-sign/src/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/vsce-sign/src/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/promise-stream-reader/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/promise-stream-reader/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/ip-address/dist/v6/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/ip-address/dist/v6/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/font-finder/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/font-finder/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/ripgrep/build/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/ripgrep/build/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/braces/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/braces/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/picomatch/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/picomatch/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/wrappy/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/wrappy/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/bl/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/bl/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/cookie/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/cookie/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/vscode-languagedetection/dist/lib/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/vscode-languagedetection/dist/lib/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/jsonfile/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/jsonfile/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/lib/web/eventsource/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/lib/web/eventsource/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/applicationinsights-core-js/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/applicationinsights-core-js/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/ms/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/ms/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/addon-ligatures/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/addon-ligatures/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@parcel/watcher/node_modules/detect-libc/bin/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@parcel/watcher/node_modules/detect-libc/bin/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/github-from-package/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/github-from-package/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/fs-extra/lib/empty/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/fs-extra/lib/empty/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@parcel/watcher/node_modules/detect-libc/lib/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@parcel/watcher/node_modules/detect-libc/lib/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/semver/classes/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/semver/classes/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/picomatch/lib/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/picomatch/lib/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/tree-sitter-wasm/wasm/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/tree-sitter-wasm/wasm/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/fs-extra/lib/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/fs-extra/lib/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/semver/internal/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/semver/internal/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/string_decoder/lib/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/string_decoder/lib/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/file-uri-to-path/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/file-uri-to-path/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/tunnel-agent/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/tunnel-agent/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/files/node/watcher/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/files/node/watcher/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/http-proxy-agent/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/http-proxy-agent/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/util-deprecate/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/util-deprecate/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/lib/handler/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/lib/handler/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/addon-search/lib/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/addon-search/lib/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/is-glob/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/is-glob/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/socks-proxy-agent/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/socks-proxy-agent/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/addon-ligatures/lib/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/addon-ligatures/lib/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/deep-extend/lib/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/deep-extend/lib/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/spdlog/azure-pipelines/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/spdlog/azure-pipelines/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/buffer-crc32/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/buffer-crc32/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/smart-buffer/build/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/smart-buffer/build/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/node-pty/node-addon-api/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/node-pty/node-addon-api/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/simple-get/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/simple-get/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/vscode-textmate/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/vscode-textmate/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/fd-slicer/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/fd-slicer/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/to-regex-range/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/to-regex-range/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/socks/build/common/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/socks/build/common/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/ripgrep/lib/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/ripgrep/lib/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/mkdirp/bin/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/mkdirp/bin/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/addon-serialize/lib/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/addon-serialize/lib/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/tiny-inflate/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/tiny-inflate/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/lib/cache/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/lib/cache/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/addon-webgl/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/addon-webgl/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/iconv-lite-umd/lib/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/iconv-lite-umd/lib/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/bindings/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/bindings/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/dynamicproto-js/tools/rollup/node/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/dynamicproto-js/tools/rollup/node/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/node-pty/lib/shared/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/node-pty/lib/shared/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/smart-buffer/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/smart-buffer/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/pend/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/pend/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/ip-address/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/ip-address/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/rc/lib/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/rc/lib/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/http-proxy-agent/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/http-proxy-agent/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/safe-buffer/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/safe-buffer/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/base64-js/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/base64-js/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/braces/lib/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/braces/lib/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/headless/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/headless/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@parcel/watcher/node_modules/detect-libc/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@parcel/watcher/node_modules/detect-libc/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/promise-stream-reader/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/promise-stream-reader/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/socks-proxy-agent/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/socks-proxy-agent/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/dynamicproto-js/lib/dist/umd/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/dynamicproto-js/lib/dist/umd/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/is-extglob/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/is-extglob/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/expand-template/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/expand-template/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/addon-image/lib/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/addon-image/lib/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/socks/build/client/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/socks/build/client/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/dynamicproto-js/lib/dist/iife/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/dynamicproto-js/lib/dist/iife/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/semver/bin/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/semver/bin/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/dynamicproto-js/lib/dist/amd/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/dynamicproto-js/lib/dist/amd/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/vsda/rust/web/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/vsda/rust/web/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/chownr/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/chownr/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/fs-extra/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/fs-extra/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/addon-clipboard/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/addon-clipboard/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/addon-unicode11/lib/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/addon-unicode11/lib/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/dynamicproto-js/tools/rollup/esm/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/dynamicproto-js/tools/rollup/esm/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@tootallnate/once/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@tootallnate/once/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/debug/src/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/debug/src/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/fs-extra/lib/fs/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/fs-extra/lib/fs/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/lib/web/websocket/stream/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/lib/web/websocket/stream/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/addon-unicode11/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/addon-unicode11/"}, "/home/<USER>/.vscode-server/data/User/globalStorage/augment.vscode-augment/augment-global-state/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/User/globalStorage/augment.vscode-augment/augment-global-state/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/fs-extra/lib/output-file/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/fs-extra/lib/output-file/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/detect-libc/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/detect-libc/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/vscode-languagedetection/model/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/vscode-languagedetection/model/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/proxy-agent/out/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/proxy-agent/out/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/detect-libc/lib/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/detect-libc/lib/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/socks/build/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/socks/build/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/addon-serialize/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/addon-serialize/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/opentype.js/externs/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/opentype.js/externs/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/kerberos/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/kerberos/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/get-system-fonts/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/get-system-fonts/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/node-abi/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/node-abi/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/vscode-textmate/release/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/vscode-textmate/release/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/base/node/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/base/node/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/debug/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/debug/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/xterm/css/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@xterm/xterm/css/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/https-proxy-agent/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/https-proxy-agent/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/lib/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/lib/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/scripts/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/undici/scripts/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/sprintf-js/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/sprintf-js/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/vscode-languagedetection/cli/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/vscode-languagedetection/cli/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/applicationinsights-core-js/types/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/applicationinsights-core-js/types/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/vscode-regexp-languagedetection/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/vscode-regexp-languagedetection/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/uuid/dist/bin/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/uuid/dist/bin/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/deviceid/azure-pipelines/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@vscode/deviceid/azure-pipelines/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/applicationinsights-core-js/dist-esm/JavaScriptSDK.Enums/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/applicationinsights-core-js/dist-esm/JavaScriptSDK.Enums/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/applicationinsights-core-js/dist-esm/JavaScriptSDK/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/applicationinsights-core-js/dist-esm/JavaScriptSDK/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/applicationinsights-core-js/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/applicationinsights-core-js/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/applicationinsights-core-js/browser/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/applicationinsights-core-js/browser/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/1ds-post-js/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/1ds-post-js/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/1ds-post-js/dist-esm/src/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/1ds-post-js/dist-esm/src/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/1ds-post-js/dist-esm/src/typings/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/1ds-post-js/dist-esm/src/typings/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/1ds-post-js/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/1ds-post-js/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/1ds-post-js/bundle/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/1ds-post-js/bundle/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/1ds-core-js/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/1ds-core-js/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/1ds-core-js/dist-esm/src/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/1ds-core-js/dist-esm/src/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/1ds-core-js/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/1ds-core-js/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/1ds-core-js/bundle/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/node_modules/@microsoft/1ds-core-js/bundle/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/typescript-language-features/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/typescript-language-features/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/typescript-language-features/schemas/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/typescript-language-features/schemas/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/typescript-language-features/resources/walkthroughs/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/typescript-language-features/resources/walkthroughs/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/typescript-language-features/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/typescript-language-features/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/tunnel-forwarding/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/tunnel-forwarding/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/tunnel-forwarding/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/tunnel-forwarding/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/tunnel-forwarding/.vscode/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/tunnel-forwarding/.vscode/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/terminal-suggest/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/terminal-suggest/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/terminal-suggest/dist/fig/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/terminal-suggest/dist/fig/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/simple-browser/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/simple-browser/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/simple-browser/media/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/simple-browser/media/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/simple-browser/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/simple-browser/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/search-result/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/search-result/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/search-result/syntaxes/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/search-result/syntaxes/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/search-result/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/search-result/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/search-result/dist/media/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/search-result/dist/media/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/references-view/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/references-view/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/references-view/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/references-view/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/php-language-features/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/php-language-features/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/php-language-features/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/php-language-features/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/npm/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/npm/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/npm/images/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/npm/images/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/npm/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/npm/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/node_modules/typescript/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/node_modules/typescript/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/node_modules/typescript/lib/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/node_modules/typescript/lib/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/node_modules/typescript/lib/zh-tw/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/node_modules/typescript/lib/zh-tw/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/node_modules/typescript/lib/zh-cn/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/node_modules/typescript/lib/zh-cn/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/node_modules/typescript/lib/tr/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/node_modules/typescript/lib/tr/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/node_modules/typescript/lib/ru/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/node_modules/typescript/lib/ru/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/node_modules/typescript/lib/pt-br/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/node_modules/typescript/lib/pt-br/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/node_modules/typescript/lib/pl/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/node_modules/typescript/lib/pl/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/node_modules/typescript/lib/ko/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/node_modules/typescript/lib/ko/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/node_modules/typescript/lib/ja/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/node_modules/typescript/lib/ja/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/node_modules/typescript/lib/it/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/node_modules/typescript/lib/it/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/node_modules/typescript/lib/fr/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/node_modules/typescript/lib/fr/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/node_modules/typescript/lib/es/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/node_modules/typescript/lib/es/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/node_modules/typescript/lib/de/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/node_modules/typescript/lib/de/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/node_modules/typescript/lib/cs/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/node_modules/typescript/lib/cs/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.vscode-js-profile-table/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.vscode-js-profile-table/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.vscode-js-profile-table/out/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.vscode-js-profile-table/out/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug-companion/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug-companion/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug-companion/out/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug-companion/out/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/src/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/src/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/src/vendor/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/src/vendor/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/src/ui/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/src/ui/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/src/targets/node/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/src/targets/node/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/light/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/light/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/dark/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/dark/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/microsoft-authentication/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/microsoft-authentication/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/microsoft-authentication/media/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/microsoft-authentication/media/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/microsoft-authentication/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/microsoft-authentication/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/merge-conflict/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/merge-conflict/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/merge-conflict/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/merge-conflict/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/media-preview/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/media-preview/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/media-preview/media/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/media-preview/media/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/media-preview/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/media-preview/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-math/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-math/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-math/syntaxes/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-math/syntaxes/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-math/preview-styles/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-math/preview-styles/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-math/notebook-out/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-math/notebook-out/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-math/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-math/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/schemas/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/schemas/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/notebook-out/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/notebook-out/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/media/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/media/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/json-language-features/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/json-language-features/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/json-language-features/server/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/json-language-features/server/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/json-language-features/server/dist/node/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/json-language-features/server/dist/node/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/json-language-features/client/dist/node/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/json-language-features/client/dist/node/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/jake/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/jake/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/jake/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/jake/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ipynb/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ipynb/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ipynb/notebook-out/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ipynb/notebook-out/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ipynb/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ipynb/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/html-language-features/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/html-language-features/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/html-language-features/server/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/html-language-features/server/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/html-language-features/server/lib/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/html-language-features/server/lib/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/html-language-features/server/dist/node/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/html-language-features/server/dist/node/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/html-language-features/schemas/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/html-language-features/schemas/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/html-language-features/client/dist/node/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/html-language-features/client/dist/node/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/gulp/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/gulp/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/gulp/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/gulp/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/grunt/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/grunt/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/grunt/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/grunt/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github-authentication/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github-authentication/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github-authentication/media/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github-authentication/media/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github-authentication/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github-authentication/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/docs/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/docs/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/.github/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/.github/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git-base/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git-base/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git-base/syntaxes/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git-base/syntaxes/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git-base/languages/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git-base/languages/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git-base/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git-base/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/light/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/light/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/dark/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/dark/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/extension-editing/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/extension-editing/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/extension-editing/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/extension-editing/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/emmet/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/emmet/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/emmet/dist/node/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/emmet/dist/node/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/debug-server-ready/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/debug-server-ready/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/debug-server-ready/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/debug-server-ready/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/debug-auto-launch/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/debug-auto-launch/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/debug-auto-launch/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/debug-auto-launch/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/debug-auto-launch/.vscode/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/debug-auto-launch/.vscode/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/css-language-features/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/css-language-features/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/css-language-features/server/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/css-language-features/server/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/css-language-features/server/dist/node/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/css-language-features/server/dist/node/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/css-language-features/schemas/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/css-language-features/schemas/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/css-language-features/client/dist/node/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/css-language-features/client/dist/node/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/configuration-editing/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/configuration-editing/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/configuration-editing/schemas/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/configuration-editing/schemas/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/configuration-editing/dist/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/configuration-editing/dist/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/bin/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/bin/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/bin/remote-cli/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/bin/remote-cli/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/bin/helpers/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/bin/helpers/"}, "/home/<USER>/.ssh/": {"rootPath": "/home", "relPath": "ubuntu/.ssh/"}, "/home/<USER>/.dotnet/corefx/cryptography/crls/": {"rootPath": "/home", "relPath": "ubuntu/.dotnet/corefx/cryptography/crls/"}, "/home/<USER>/.cache/": {"rootPath": "/home", "relPath": "ubuntu/.cache/"}, "/home/<USER>/.cache/pip/wheels/fd/ed/18/2a12fd1b7906c63efca6accb351929f2c7f6bbc674e1c0ba5d/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/wheels/fd/ed/18/2a12fd1b7906c63efca6accb351929f2c7f6bbc674e1c0ba5d/"}, "/home/<USER>/.cache/pip/wheels/68/8e/d4/3ed4272b059c74f0b9ae3930e45f075f295805c87d69b850f0/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/wheels/68/8e/d4/3ed4272b059c74f0b9ae3930e45f075f295805c87d69b850f0/"}, "/home/<USER>/.cache/pip/http-v2/f/f/0/e/0/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/f/f/0/e/0/"}, "/home/<USER>/.cache/pip/http-v2/f/c/a/c/4/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/f/c/a/c/4/"}, "/home/<USER>/.cache/pip/http-v2/f/b/9/a/e/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/f/b/9/a/e/"}, "/home/<USER>/.cache/pip/http-v2/e/f/9/9/6/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/e/f/9/9/6/"}, "/home/<USER>/.cache/pip/http-v2/e/d/9/4/7/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/e/d/9/4/7/"}, "/home/<USER>/.cache/pip/http-v2/e/c/3/4/5/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/e/c/3/4/5/"}, "/home/<USER>/.cache/pip/http-v2/e/a/2/c/b/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/e/a/2/c/b/"}, "/home/<USER>/.cache/pip/http-v2/e/8/9/1/c/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/e/8/9/1/c/"}, "/home/<USER>/.cache/pip/http-v2/e/1/3/8/8/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/e/1/3/8/8/"}, "/home/<USER>/.cache/pip/http-v2/e/0/d/3/2/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/e/0/d/3/2/"}, "/home/<USER>/.cache/pip/http-v2/d/e/f/a/5/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/d/e/f/a/5/"}, "/home/<USER>/.cache/pip/http-v2/d/d/b/4/f/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/d/d/b/4/f/"}, "/home/<USER>/.cache/pip/http-v2/d/7/d/e/c/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/d/7/d/e/c/"}, "/home/<USER>/.cache/pip/http-v2/d/6/a/5/d/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/d/6/a/5/d/"}, "/home/<USER>/.cache/pip/http-v2/d/5/d/1/8/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/d/5/d/1/8/"}, "/home/<USER>/.cache/pip/http-v2/d/0/6/1/4/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/d/0/6/1/4/"}, "/home/<USER>/.cache/pip/http-v2/c/d/7/9/3/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/c/d/7/9/3/"}, "/home/<USER>/.cache/pip/http-v2/c/9/6/b/6/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/c/9/6/b/6/"}, "/home/<USER>/.cache/pip/http-v2/c/5/1/5/a/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/c/5/1/5/a/"}, "/home/<USER>/.cache/pip/http-v2/c/3/b/5/4/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/c/3/b/5/4/"}, "/home/<USER>/.cache/pip/http-v2/c/3/6/d/a/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/c/3/6/d/a/"}, "/home/<USER>/.cache/pip/http-v2/c/2/3/d/2/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/c/2/3/d/2/"}, "/home/<USER>/.cache/pip/http-v2/c/1/d/c/b/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/c/1/d/c/b/"}, "/home/<USER>/.cache/pip/http-v2/c/0/9/7/7/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/c/0/9/7/7/"}, "/home/<USER>/.cache/pip/http-v2/b/b/d/6/3/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/b/b/d/6/3/"}, "/home/<USER>/.cache/pip/http-v2/b/b/0/5/6/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/b/b/0/5/6/"}, "/home/<USER>/.cache/pip/http-v2/b/9/d/f/2/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/b/9/d/f/2/"}, "/home/<USER>/.cache/pip/http-v2/b/6/3/f/1/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/b/6/3/f/1/"}, "/home/<USER>/.cache/pip/http-v2/b/4/9/b/a/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/b/4/9/b/a/"}, "/home/<USER>/.cache/pip/http-v2/b/4/8/3/0/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/b/4/8/3/0/"}, "/home/<USER>/.cache/pip/http-v2/b/4/1/2/9/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/b/4/1/2/9/"}, "/home/<USER>/.cache/pip/http-v2/b/0/c/3/0/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/b/0/c/3/0/"}, "/home/<USER>/.cache/pip/http-v2/a/9/a/9/d/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/a/9/a/9/d/"}, "/home/<USER>/.cache/pip/http-v2/a/7/0/0/d/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/a/7/0/0/d/"}, "/home/<USER>/.cache/pip/http-v2/a/6/6/2/d/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/a/6/6/2/d/"}, "/home/<USER>/.cache/pip/http-v2/a/5/c/a/a/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/a/5/c/a/a/"}, "/home/<USER>/.cache/pip/http-v2/a/5/2/e/d/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/a/5/2/e/d/"}, "/home/<USER>/.cache/pip/http-v2/a/4/0/6/2/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/a/4/0/6/2/"}, "/home/<USER>/.cache/pip/http-v2/a/2/0/9/3/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/a/2/0/9/3/"}, "/home/<USER>/.cache/pip/http-v2/a/0/5/c/e/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/a/0/5/c/e/"}, "/home/<USER>/.cache/pip/http-v2/a/0/3/e/7/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/a/0/3/e/7/"}, "/home/<USER>/.cache/pip/http-v2/9/9/0/6/3/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/9/9/0/6/3/"}, "/home/<USER>/.cache/pip/http-v2/9/8/5/e/8/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/9/8/5/e/8/"}, "/home/<USER>/.cache/pip/http-v2/9/4/c/3/6/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/9/4/c/3/6/"}, "/home/<USER>/.cache/pip/http-v2/9/3/6/e/f/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/9/3/6/e/f/"}, "/home/<USER>/.cache/pip/http-v2/9/2/f/b/d/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/9/2/f/b/d/"}, "/home/<USER>/.cache/pip/http-v2/9/2/e/f/7/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/9/2/e/f/7/"}, "/home/<USER>/.cache/pip/http-v2/9/1/6/c/1/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/9/1/6/c/1/"}, "/home/<USER>/.cache/pip/http-v2/9/0/b/e/f/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/9/0/b/e/f/"}, "/home/<USER>/.cache/pip/http-v2/8/f/f/6/f/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/8/f/f/6/f/"}, "/home/<USER>/.cache/pip/http-v2/8/e/c/f/f/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/8/e/c/f/f/"}, "/home/<USER>/.cache/pip/http-v2/8/d/6/d/f/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/8/d/6/d/f/"}, "/home/<USER>/.cache/pip/http-v2/8/d/0/f/b/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/8/d/0/f/b/"}, "/home/<USER>/.cache/pip/http-v2/8/c/3/e/c/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/8/c/3/e/c/"}, "/home/<USER>/.cache/pip/http-v2/8/c/1/f/f/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/8/c/1/f/f/"}, "/home/<USER>/.cache/pip/http-v2/8/8/b/8/b/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/8/8/b/8/b/"}, "/home/<USER>/.cache/pip/http-v2/8/6/a/0/9/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/8/6/a/0/9/"}, "/home/<USER>/.cache/pip/http-v2/8/4/e/e/7/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/8/4/e/e/7/"}, "/home/<USER>/.cache/pip/http-v2/8/2/5/e/6/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/8/2/5/e/6/"}, "/home/<USER>/.cache/pip/http-v2/8/1/3/a/2/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/8/1/3/a/2/"}, "/home/<USER>/.cache/pip/http-v2/8/1/1/e/5/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/8/1/1/e/5/"}, "/home/<USER>/.cache/pip/http-v2/7/a/9/a/3/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/7/a/9/a/3/"}, "/home/<USER>/.cache/pip/http-v2/7/a/6/6/b/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/7/a/6/6/b/"}, "/home/<USER>/.cache/pip/http-v2/7/7/1/a/1/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/7/7/1/a/1/"}, "/home/<USER>/.cache/pip/http-v2/7/6/7/5/f/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/7/6/7/5/f/"}, "/home/<USER>/.cache/pip/http-v2/7/1/0/b/4/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/7/1/0/b/4/"}, "/home/<USER>/.cache/pip/http-v2/7/0/8/f/f/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/7/0/8/f/f/"}, "/home/<USER>/.cache/pip/http-v2/7/0/3/7/d/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/7/0/3/7/d/"}, "/home/<USER>/.cache/pip/http-v2/6/f/4/e/2/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/6/f/4/e/2/"}, "/home/<USER>/.cache/pip/http-v2/6/d/d/6/4/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/6/d/d/6/4/"}, "/home/<USER>/.cache/pip/http-v2/6/8/2/9/4/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/6/8/2/9/4/"}, "/home/<USER>/.cache/pip/http-v2/6/5/a/f/8/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/6/5/a/f/8/"}, "/home/<USER>/.cache/pip/http-v2/6/5/0/a/e/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/6/5/0/a/e/"}, "/home/<USER>/.cache/pip/http-v2/5/f/c/9/2/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/5/f/c/9/2/"}, "/home/<USER>/.cache/pip/http-v2/5/d/8/6/2/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/5/d/8/6/2/"}, "/home/<USER>/.cache/pip/http-v2/5/b/9/a/9/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/5/b/9/a/9/"}, "/home/<USER>/.cache/pip/http-v2/5/9/6/f/2/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/5/9/6/f/2/"}, "/home/<USER>/.cache/pip/http-v2/5/8/8/6/7/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/5/8/8/6/7/"}, "/home/<USER>/.cache/pip/http-v2/5/7/0/8/5/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/5/7/0/8/5/"}, "/home/<USER>/.cache/pip/http-v2/5/6/f/4/4/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/5/6/f/4/4/"}, "/home/<USER>/.cache/pip/http-v2/5/0/d/a/4/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/5/0/d/a/4/"}, "/home/<USER>/.cache/pip/http-v2/4/e/f/5/6/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/4/e/f/5/6/"}, "/home/<USER>/.cache/pip/http-v2/4/b/5/b/7/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/4/b/5/b/7/"}, "/home/<USER>/.cache/pip/http-v2/4/8/5/b/b/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/4/8/5/b/b/"}, "/home/<USER>/.cache/pip/http-v2/4/3/8/6/e/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/4/3/8/6/e/"}, "/home/<USER>/myproject/openalgo/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/"}, "/home/<USER>/myproject/openalgo/websocket_proxy/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/websocket_proxy/"}, "/home/<USER>/myproject/openalgo/utils/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/utils/"}, "/home/<USER>/myproject/openalgo/upgrade/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/upgrade/"}, "/home/<USER>/myproject/openalgo/tmp/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/tmp/"}, "/home/<USER>/myproject/openalgo/test/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/test/"}, "/home/<USER>/myproject/openalgo/templates/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/templates/"}, "/home/<USER>/myproject/openalgo/templates/traffic/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/templates/traffic/"}, "/home/<USER>/myproject/openalgo/templates/strategy/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/templates/strategy/"}, "/home/<USER>/myproject/openalgo/templates/latency/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/templates/latency/"}, "/home/<USER>/myproject/openalgo/templates/components/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/templates/components/"}, "/home/<USER>/myproject/openalgo/templates/chartink/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/templates/chartink/"}, "/home/<USER>/.cache/pip/http-v2/3/9/0/f/c/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/3/9/0/f/c/"}, "/home/<USER>/.cache/pip/http-v2/3/8/e/7/5/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/3/8/e/7/5/"}, "/home/<USER>/.cache/pip/http-v2/3/8/3/c/6/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/3/8/3/c/6/"}, "/home/<USER>/.cache/pip/http-v2/3/5/0/6/c/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/3/5/0/6/c/"}, "/home/<USER>/.cache/pip/http-v2/3/4/b/e/5/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/3/4/b/e/5/"}, "/home/<USER>/.cache/pip/http-v2/3/3/f/8/2/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/3/3/f/8/2/"}, "/home/<USER>/.cache/pip/http-v2/3/2/1/6/9/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/3/2/1/6/9/"}, "/home/<USER>/.cache/pip/http-v2/2/f/e/8/0/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/2/f/e/8/0/"}, "/home/<USER>/.cache/pip/http-v2/2/6/7/d/2/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/2/6/7/d/2/"}, "/home/<USER>/.cache/pip/http-v2/2/1/5/3/0/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/2/1/5/3/0/"}, "/home/<USER>/.cache/pip/http-v2/2/0/9/a/2/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/2/0/9/a/2/"}, "/home/<USER>/.cache/pip/http-v2/2/0/6/b/6/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/2/0/6/b/6/"}, "/home/<USER>/.cache/pip/http-v2/2/0/4/4/d/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/2/0/4/4/d/"}, "/home/<USER>/.cache/pip/http-v2/1/e/d/0/7/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/1/e/d/0/7/"}, "/home/<USER>/.cache/pip/http-v2/1/e/6/2/0/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/1/e/6/2/0/"}, "/home/<USER>/.cache/pip/http-v2/1/c/b/8/6/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/1/c/b/8/6/"}, "/home/<USER>/.cache/pip/http-v2/1/a/9/0/8/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/1/a/9/0/8/"}, "/home/<USER>/.cache/pip/http-v2/1/a/4/e/0/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/1/a/4/e/0/"}, "/home/<USER>/.cache/pip/http-v2/1/9/2/8/2/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/1/9/2/8/2/"}, "/home/<USER>/.cache/pip/http-v2/1/5/e/c/9/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/1/5/e/c/9/"}, "/home/<USER>/.cache/pip/http-v2/1/0/e/1/4/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/1/0/e/1/4/"}, "/home/<USER>/.cache/pip/http-v2/0/b/b/b/e/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/0/b/b/b/e/"}, "/home/<USER>/.cache/pip/http-v2/0/a/5/b/5/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/0/a/5/b/5/"}, "/home/<USER>/.cache/pip/http-v2/0/a/5/4/a/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/0/a/5/4/a/"}, "/home/<USER>/.cache/pip/http-v2/0/8/e/7/a/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/0/8/e/7/a/"}, "/home/<USER>/.cache/pip/http-v2/0/8/9/9/1/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/0/8/9/9/1/"}, "/home/<USER>/.cache/pip/http-v2/0/8/5/6/0/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/0/8/5/6/0/"}, "/home/<USER>/.cache/pip/http-v2/0/5/e/7/e/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/0/5/e/7/e/"}, "/home/<USER>/.cache/pip/http-v2/0/5/d/2/b/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/0/5/d/2/b/"}, "/home/<USER>/.cache/pip/http-v2/0/3/a/5/f/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/0/3/a/5/f/"}, "/home/<USER>/.cache/pip/http-v2/0/1/0/4/7/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/0/1/0/4/7/"}, "/home/<USER>/myproject/openalgo/strategies/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/strategies/"}, "/home/<USER>/myproject/openalgo/static/js/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/static/js/"}, "/home/<USER>/myproject/openalgo/static/favicon/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/static/favicon/"}, "/home/<USER>/myproject/openalgo/static/css/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/static/css/"}, "/home/<USER>/myproject/openalgo/src/css/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/src/css/"}, "/home/<USER>/myproject/openalgo/services/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/services/"}, "/home/<USER>/myproject/openalgo/restx_api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/restx_api/"}, "/home/<USER>/myproject/openalgo/install/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/install/"}, "/home/<USER>/myproject/openalgo/download/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/download/"}, "/home/<USER>/myproject/openalgo/docs/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/docs/"}, "/home/<USER>/myproject/openalgo/design/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/design/"}, "/home/<USER>/myproject/openalgo/db/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/db/"}, "/home/<USER>/myproject/openalgo/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/database/"}, "/home/<USER>/myproject/openalgo/collections/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/collections/"}, "/home/<USER>/myproject/openalgo/broker/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/"}, "/home/<USER>/myproject/openalgo/broker/zerodha/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/zerodha/"}, "/home/<USER>/myproject/openalgo/broker/zerodha/streaming/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/zerodha/streaming/"}, "/home/<USER>/myproject/openalgo/broker/zerodha/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/zerodha/mapping/"}, "/home/<USER>/myproject/openalgo/broker/zerodha/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/zerodha/database/"}, "/home/<USER>/myproject/openalgo/broker/zerodha/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/zerodha/api/"}, "/home/<USER>/myproject/openalgo/broker/zebu/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/zebu/"}, "/home/<USER>/myproject/openalgo/broker/zebu/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/zebu/mapping/"}, "/home/<USER>/myproject/openalgo/broker/zebu/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/zebu/database/"}, "/home/<USER>/myproject/openalgo/broker/zebu/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/zebu/api/"}, "/home/<USER>/myproject/openalgo/broker/wisdom/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/wisdom/"}, "/home/<USER>/myproject/openalgo/broker/wisdom/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/wisdom/mapping/"}, "/home/<USER>/myproject/openalgo/broker/wisdom/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/wisdom/database/"}, "/home/<USER>/myproject/openalgo/broker/wisdom/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/wisdom/api/"}, "/home/<USER>/myproject/openalgo/broker/upstox/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/upstox/"}, "/home/<USER>/myproject/openalgo/broker/upstox/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/upstox/mapping/"}, "/home/<USER>/myproject/openalgo/broker/upstox/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/upstox/database/"}, "/home/<USER>/myproject/openalgo/broker/upstox/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/upstox/api/"}, "/home/<USER>/myproject/openalgo/broker/tradejini/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/tradejini/"}, "/home/<USER>/myproject/openalgo/broker/tradejini/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/tradejini/mapping/"}, "/home/<USER>/myproject/openalgo/broker/tradejini/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/tradejini/api/"}, "/home/<USER>/myproject/openalgo/broker/shoonya/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/shoonya/"}, "/home/<USER>/myproject/openalgo/broker/shoonya/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/shoonya/mapping/"}, "/home/<USER>/myproject/openalgo/broker/shoonya/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/shoonya/api/"}, "/home/<USER>/myproject/openalgo/broker/pocketful/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/pocketful/mapping/"}, "/home/<USER>/myproject/openalgo/broker/pocketful/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/pocketful/database/"}, "/home/<USER>/myproject/openalgo/broker/pocketful/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/pocketful/api/"}, "/home/<USER>/myproject/openalgo/broker/paytm/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/paytm/api/"}, "/home/<USER>/myproject/openalgo/broker/kotak/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/kotak/"}, "/home/<USER>/myproject/openalgo/broker/kotak/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/kotak/mapping/"}, "/home/<USER>/myproject/openalgo/broker/kotak/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/kotak/database/"}, "/home/<USER>/myproject/openalgo/broker/kotak/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/kotak/api/"}, "/home/<USER>/myproject/openalgo/broker/jainampro/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/jainampro/api/"}, "/home/<USER>/myproject/openalgo/broker/iifl/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/iifl/api/"}, "/home/<USER>/myproject/openalgo/broker/jainam/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/jainam/api/"}, "/home/<USER>/myproject/openalgo/broker/jainam/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/jainam/mapping/"}, "/home/<USER>/myproject/openalgo/broker/iifl/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/iifl/mapping/"}, "/home/<USER>/myproject/openalgo/broker/jainampro/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/jainampro/mapping/"}, "/home/<USER>/myproject/openalgo/broker/iifl/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/iifl/database/"}, "/home/<USER>/myproject/openalgo/broker/paytm/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/paytm/"}, "/home/<USER>/myproject/openalgo/broker/pocketful/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/pocketful/"}, "/home/<USER>/myproject/openalgo/broker/paytm/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/paytm/mapping/"}, "/home/<USER>/myproject/openalgo/broker/iifl/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/iifl/"}, "/home/<USER>/myproject/openalgo/broker/tradejini/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/tradejini/database/"}, "/home/<USER>/myproject/openalgo/broker/jainam/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/jainam/"}, "/home/<USER>/myproject/openalgo/broker/jainampro/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/jainampro/"}, "/home/<USER>/myproject/openalgo/broker/jainam/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/jainam/database/"}, "/home/<USER>/myproject/openalgo/broker/jainampro/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/jainampro/database/"}, "/home/<USER>/myproject/openalgo/broker/shoonya/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/shoonya/database/"}, "/home/<USER>/myproject/openalgo/broker/paytm/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/paytm/database/"}, "/home/<USER>/myproject/openalgo/broker/groww/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/groww/"}, "/home/<USER>/myproject/openalgo/broker/groww/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/groww/mapping/"}, "/home/<USER>/myproject/openalgo/broker/groww/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/groww/database/"}, "/home/<USER>/myproject/openalgo/broker/groww/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/groww/api/"}, "/home/<USER>/myproject/openalgo/broker/fyers/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/fyers/"}, "/home/<USER>/myproject/openalgo/broker/fyers/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/fyers/mapping/"}, "/home/<USER>/myproject/openalgo/broker/fyers/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/fyers/api/"}, "/home/<USER>/myproject/openalgo/broker/flattrade/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/flattrade/"}, "/home/<USER>/myproject/openalgo/broker/flattrade/streaming/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/flattrade/streaming/"}, "/home/<USER>/myproject/openalgo/broker/flattrade/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/flattrade/mapping/"}, "/home/<USER>/myproject/openalgo/broker/flattrade/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/flattrade/database/"}, "/home/<USER>/myproject/openalgo/broker/flattrade/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/flattrade/api/"}, "/home/<USER>/myproject/openalgo/broker/fivepaisaxts/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/fivepaisaxts/"}, "/home/<USER>/myproject/openalgo/broker/fivepaisaxts/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/fivepaisaxts/mapping/"}, "/home/<USER>/myproject/openalgo/broker/fivepaisaxts/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/fivepaisaxts/database/"}, "/home/<USER>/myproject/openalgo/broker/fivepaisaxts/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/fivepaisaxts/api/"}, "/home/<USER>/myproject/openalgo/broker/fivepaisa/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/fivepaisa/"}, "/home/<USER>/myproject/openalgo/broker/fivepaisa/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/fivepaisa/mapping/"}, "/home/<USER>/myproject/openalgo/broker/fivepaisa/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/fivepaisa/database/"}, "/home/<USER>/myproject/openalgo/broker/fivepaisa/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/fivepaisa/api/"}, "/home/<USER>/myproject/openalgo/broker/firstock/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/firstock/"}, "/home/<USER>/myproject/openalgo/broker/firstock/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/firstock/mapping/"}, "/home/<USER>/myproject/openalgo/broker/firstock/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/firstock/database/"}, "/home/<USER>/myproject/openalgo/broker/firstock/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/firstock/api/"}, "/home/<USER>/myproject/openalgo/broker/dhan_sandbox/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/dhan_sandbox/"}, "/home/<USER>/myproject/openalgo/broker/dhan_sandbox/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/dhan_sandbox/mapping/"}, "/home/<USER>/myproject/openalgo/broker/dhan_sandbox/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/dhan_sandbox/database/"}, "/home/<USER>/myproject/openalgo/broker/dhan_sandbox/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/dhan_sandbox/api/"}, "/home/<USER>/myproject/openalgo/broker/dhan/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/dhan/"}, "/home/<USER>/myproject/openalgo/broker/dhan/streaming/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/dhan/streaming/"}, "/home/<USER>/myproject/openalgo/broker/dhan/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/dhan/mapping/"}, "/home/<USER>/myproject/openalgo/broker/dhan/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/dhan/database/"}, "/home/<USER>/myproject/openalgo/broker/dhan/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/dhan/api/"}, "/home/<USER>/myproject/openalgo/broker/compositedge/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/compositedge/"}, "/home/<USER>/myproject/openalgo/broker/compositedge/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/compositedge/mapping/"}, "/home/<USER>/myproject/openalgo/broker/compositedge/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/compositedge/database/"}, "/home/<USER>/myproject/openalgo/broker/compositedge/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/compositedge/api/"}, "/home/<USER>/myproject/openalgo/broker/angel/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/angel/"}, "/home/<USER>/myproject/openalgo/broker/angel/streaming/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/angel/streaming/"}, "/home/<USER>/myproject/openalgo/broker/angel/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/angel/mapping/"}, "/home/<USER>/myproject/openalgo/broker/angel/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/angel/database/"}, "/home/<USER>/myproject/openalgo/broker/angel/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/angel/api/"}, "/home/<USER>/myproject/openalgo/broker/aliceblue/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/aliceblue/"}, "/home/<USER>/myproject/openalgo/broker/aliceblue/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/aliceblue/mapping/"}, "/home/<USER>/myproject/openalgo/broker/aliceblue/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/aliceblue/database/"}, "/home/<USER>/myproject/openalgo/broker/aliceblue/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/aliceblue/api/"}, "/home/<USER>/myproject/openalgo/blueprints/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/blueprints/"}, "/home/<USER>/myproject/openalgo/.ebextensions/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/.ebextensions/"}, "/home/<USER>/myproject/algofactory/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/"}, "/home/<USER>/myproject/openalgo/broker/fyers/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/openalgo/broker/fyers/database/"}, "/home/<USER>/myproject/algofactory/websocket_proxy/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/websocket_proxy/"}, "/home/<USER>/myproject/algofactory/utils/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/utils/"}, "/home/<USER>/myproject/algofactory/upgrade/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/upgrade/"}, "/home/<USER>/myproject/algofactory/tmp/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/tmp/"}, "/home/<USER>/myproject/algofactory/test/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/test/"}, "/home/<USER>/myproject/algofactory/templates/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/templates/"}, "/home/<USER>/myproject/algofactory/templates/traffic/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/templates/traffic/"}, "/home/<USER>/myproject/algofactory/templates/strategy/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/templates/strategy/"}, "/home/<USER>/myproject/algofactory/templates/latency/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/templates/latency/"}, "/home/<USER>/myproject/algofactory/templates/components/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/templates/components/"}, "/home/<USER>/myproject/algofactory/templates/chartink/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/templates/chartink/"}, "/home/<USER>/myproject/algofactory/strategies/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/strategies/"}, "/home/<USER>/myproject/algofactory/static/js/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/static/js/"}, "/home/<USER>/myproject/algofactory/static/css/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/static/css/"}, "/home/<USER>/myproject/algofactory/src/css/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/src/css/"}, "/home/<USER>/myproject/algofactory/services/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/services/"}, "/home/<USER>/myproject/algofactory/restx_api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/restx_api/"}, "/home/<USER>/myproject/algofactory/install/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/install/"}, "/home/<USER>/myproject/algofactory/download/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/download/"}, "/home/<USER>/myproject/algofactory/docs/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/docs/"}, "/home/<USER>/myproject/algofactory/design/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/design/"}, "/home/<USER>/myproject/algofactory/db/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/db/"}, "/home/<USER>/myproject/algofactory/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/database/"}, "/home/<USER>/myproject/algofactory/collections/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/collections/"}, "/home/<USER>/myproject/algofactory/broker/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/"}, "/home/<USER>/myproject/algofactory/broker/zerodha/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/zerodha/"}, "/home/<USER>/myproject/algofactory/broker/zerodha/streaming/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/zerodha/streaming/"}, "/home/<USER>/myproject/algofactory/broker/zerodha/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/zerodha/mapping/"}, "/home/<USER>/myproject/algofactory/broker/zerodha/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/zerodha/database/"}, "/home/<USER>/myproject/algofactory/broker/zerodha/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/zerodha/api/"}, "/home/<USER>/myproject/algofactory/broker/zebu/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/zebu/"}, "/home/<USER>/myproject/algofactory/broker/zebu/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/zebu/mapping/"}, "/home/<USER>/myproject/algofactory/broker/zebu/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/zebu/database/"}, "/home/<USER>/myproject/algofactory/broker/zebu/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/zebu/api/"}, "/home/<USER>/myproject/algofactory/static/favicon/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/static/favicon/"}, "/home/<USER>/myproject/algofactory/broker/wisdom/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/wisdom/"}, "/home/<USER>/myproject/algofactory/broker/wisdom/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/wisdom/mapping/"}, "/home/<USER>/myproject/algofactory/broker/wisdom/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/wisdom/database/"}, "/home/<USER>/myproject/algofactory/broker/wisdom/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/wisdom/api/"}, "/home/<USER>/myproject/algofactory/broker/upstox/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/upstox/"}, "/home/<USER>/myproject/algofactory/broker/upstox/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/upstox/mapping/"}, "/home/<USER>/myproject/algofactory/broker/upstox/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/upstox/database/"}, "/home/<USER>/myproject/algofactory/broker/upstox/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/upstox/api/"}, "/home/<USER>/myproject/algofactory/broker/tradejini/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/tradejini/"}, "/home/<USER>/myproject/algofactory/broker/tradejini/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/tradejini/mapping/"}, "/home/<USER>/myproject/algofactory/broker/tradejini/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/tradejini/database/"}, "/home/<USER>/myproject/algofactory/broker/tradejini/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/tradejini/api/"}, "/home/<USER>/myproject/algofactory/broker/shoonya/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/shoonya/"}, "/home/<USER>/myproject/algofactory/broker/shoonya/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/shoonya/mapping/"}, "/home/<USER>/myproject/algofactory/broker/shoonya/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/shoonya/database/"}, "/home/<USER>/myproject/algofactory/broker/shoonya/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/shoonya/api/"}, "/home/<USER>/myproject/algofactory/broker/pocketful/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/pocketful/"}, "/home/<USER>/myproject/algofactory/broker/pocketful/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/pocketful/mapping/"}, "/home/<USER>/myproject/algofactory/broker/pocketful/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/pocketful/database/"}, "/home/<USER>/myproject/algofactory/broker/pocketful/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/pocketful/api/"}, "/home/<USER>/myproject/algofactory/broker/paytm/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/paytm/"}, "/home/<USER>/myproject/algofactory/broker/paytm/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/paytm/mapping/"}, "/home/<USER>/myproject/algofactory/broker/paytm/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/paytm/database/"}, "/home/<USER>/myproject/algofactory/broker/paytm/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/paytm/api/"}, "/home/<USER>/myproject/algofactory/broker/kotak/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/kotak/"}, "/home/<USER>/myproject/algofactory/broker/kotak/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/kotak/mapping/"}, "/home/<USER>/myproject/algofactory/broker/kotak/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/kotak/database/"}, "/home/<USER>/myproject/algofactory/broker/kotak/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/kotak/api/"}, "/home/<USER>/myproject/algofactory/broker/jainampro/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/jainampro/"}, "/home/<USER>/myproject/algofactory/broker/jainampro/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/jainampro/mapping/"}, "/home/<USER>/myproject/algofactory/broker/jainampro/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/jainampro/database/"}, "/home/<USER>/myproject/algofactory/broker/jainampro/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/jainampro/api/"}, "/home/<USER>/myproject/algofactory/broker/jainam/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/jainam/"}, "/home/<USER>/myproject/algofactory/broker/jainam/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/jainam/mapping/"}, "/home/<USER>/myproject/algofactory/broker/jainam/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/jainam/database/"}, "/home/<USER>/myproject/algofactory/broker/jainam/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/jainam/api/"}, "/home/<USER>/myproject/algofactory/broker/iifl/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/iifl/"}, "/home/<USER>/myproject/algofactory/broker/iifl/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/iifl/mapping/"}, "/home/<USER>/myproject/algofactory/broker/iifl/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/iifl/database/"}, "/home/<USER>/myproject/algofactory/broker/iifl/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/iifl/api/"}, "/home/<USER>/myproject/algofactory/broker/groww/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/groww/"}, "/home/<USER>/myproject/algofactory/broker/groww/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/groww/mapping/"}, "/home/<USER>/myproject/algofactory/broker/groww/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/groww/database/"}, "/home/<USER>/myproject/algofactory/broker/groww/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/groww/api/"}, "/home/<USER>/myproject/algofactory/broker/fyers/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/fyers/"}, "/home/<USER>/myproject/algofactory/broker/fyers/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/fyers/mapping/"}, "/home/<USER>/myproject/algofactory/broker/fyers/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/fyers/database/"}, "/home/<USER>/myproject/algofactory/broker/fyers/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/fyers/api/"}, "/home/<USER>/myproject/algofactory/broker/flattrade/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/flattrade/"}, "/home/<USER>/myproject/algofactory/broker/flattrade/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/flattrade/mapping/"}, "/home/<USER>/myproject/algofactory/broker/flattrade/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/flattrade/database/"}, "/home/<USER>/myproject/algofactory/broker/flattrade/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/flattrade/api/"}, "/home/<USER>/myproject/algofactory/broker/fivepaisaxts/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/fivepaisaxts/"}, "/home/<USER>/myproject/algofactory/broker/fivepaisaxts/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/fivepaisaxts/mapping/"}, "/home/<USER>/myproject/algofactory/broker/fivepaisaxts/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/fivepaisaxts/database/"}, "/home/<USER>/myproject/algofactory/broker/fivepaisaxts/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/fivepaisaxts/api/"}, "/home/<USER>/myproject/algofactory/broker/fivepaisa/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/fivepaisa/"}, "/home/<USER>/myproject/algofactory/broker/fivepaisa/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/fivepaisa/mapping/"}, "/home/<USER>/myproject/algofactory/broker/fivepaisa/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/fivepaisa/database/"}, "/home/<USER>/myproject/algofactory/broker/fivepaisa/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/fivepaisa/api/"}, "/home/<USER>/myproject/algofactory/broker/firstock/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/firstock/"}, "/home/<USER>/myproject/algofactory/broker/firstock/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/firstock/mapping/"}, "/home/<USER>/myproject/algofactory/broker/firstock/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/firstock/database/"}, "/home/<USER>/myproject/algofactory/broker/firstock/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/firstock/api/"}, "/home/<USER>/myproject/algofactory/broker/dhan_sandbox/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/dhan_sandbox/"}, "/home/<USER>/myproject/algofactory/broker/dhan_sandbox/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/dhan_sandbox/mapping/"}, "/home/<USER>/myproject/algofactory/broker/dhan_sandbox/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/dhan_sandbox/database/"}, "/home/<USER>/myproject/algofactory/broker/dhan_sandbox/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/dhan_sandbox/api/"}, "/home/<USER>/myproject/algofactory/broker/dhan/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/dhan/"}, "/home/<USER>/myproject/algofactory/broker/dhan/streaming/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/dhan/streaming/"}, "/home/<USER>/myproject/algofactory/broker/dhan/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/dhan/mapping/"}, "/home/<USER>/myproject/algofactory/broker/dhan/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/dhan/database/"}, "/home/<USER>/myproject/algofactory/broker/dhan/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/dhan/api/"}, "/home/<USER>/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/"}, "/home/<USER>/myproject/algofactory/broker/compositedge/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/compositedge/"}, "/home/<USER>/myproject/algofactory/broker/compositedge/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/compositedge/mapping/"}, "/home/<USER>/myproject/algofactory/broker/compositedge/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/compositedge/database/"}, "/home/<USER>/myproject/algofactory/broker/compositedge/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/compositedge/api/"}, "/home/<USER>/myproject/algofactory/broker/angel/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/angel/"}, "/home/<USER>/myproject/algofactory/broker/angel/streaming/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/angel/streaming/"}, "/home/<USER>/myproject/algofactory/broker/angel/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/angel/mapping/"}, "/home/<USER>/myproject/algofactory/broker/angel/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/angel/database/"}, "/home/<USER>/myproject/algofactory/broker/angel/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/angel/api/"}, "/home/<USER>/myproject/algofactory/broker/aliceblue/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/aliceblue/"}, "/home/<USER>/myproject/algofactory/broker/aliceblue/mapping/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/aliceblue/mapping/"}, "/home/<USER>/myproject/algofactory/broker/aliceblue/database/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/aliceblue/database/"}, "/home/<USER>/myproject/algofactory/broker/aliceblue/api/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/broker/aliceblue/api/"}, "/home/<USER>/myproject/algofactory/blueprints/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/blueprints/"}, "/home/<USER>/myproject/algofactory/.ebextensions/": {"rootPath": "/home", "relPath": "ubuntu/myproject/algofactory/.ebextensions/"}, "/home/<USER>/.vscode-server/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/"}, "/home/<USER>/.vscode-server/extensions/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/extensions/"}, "/home/<USER>/.vscode-server/data/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/"}, "/home/<USER>/.vscode-server/data/logs/20250622T184336/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T184336/"}, "/home/<USER>/.vscode-server/data/logs/20250622T184336/exthost6/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T184336/exthost6/"}, "/home/<USER>/.vscode-server/data/logs/20250622T184336/exthost6/vscode.github/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T184336/exthost6/vscode.github/"}, "/home/<USER>/.vscode-server/data/logs/20250622T184336/exthost6/vscode.git/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T184336/exthost6/vscode.git/"}, "/home/<USER>/.vscode-server/data/logs/20250622T184336/exthost6/Augment.vscode-augment/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T184336/exthost6/Augment.vscode-augment/"}, "/home/<USER>/.vscode-server/data/logs/20250622T184336/exthost5/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T184336/exthost5/"}, "/home/<USER>/.vscode-server/data/logs/20250622T184336/exthost5/vscode.github/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T184336/exthost5/vscode.github/"}, "/home/<USER>/.vscode-server/data/logs/20250622T184336/exthost5/vscode.git/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T184336/exthost5/vscode.git/"}, "/home/<USER>/.vscode-server/data/logs/20250622T184336/exthost5/Augment.vscode-augment/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T184336/exthost5/Augment.vscode-augment/"}, "/home/<USER>/.vscode-server/data/logs/20250622T184336/exthost4/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T184336/exthost4/"}, "/home/<USER>/.vscode-server/data/logs/20250622T184336/exthost4/vscode.github/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T184336/exthost4/vscode.github/"}, "/home/<USER>/.vscode-server/data/logs/20250622T184336/exthost4/vscode.git/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T184336/exthost4/vscode.git/"}, "/home/<USER>/.vscode-server/data/logs/20250622T184336/exthost3/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T184336/exthost3/"}, "/home/<USER>/.vscode-server/data/logs/20250622T184336/exthost3/vscode.github/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T184336/exthost3/vscode.github/"}, "/home/<USER>/.vscode-server/data/logs/20250622T184336/exthost3/vscode.git/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T184336/exthost3/vscode.git/"}, "/home/<USER>/.vscode-server/data/logs/20250622T184336/exthost2/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T184336/exthost2/"}, "/home/<USER>/.vscode-server/data/logs/20250622T184336/exthost2/vscode.github/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T184336/exthost2/vscode.github/"}, "/home/<USER>/.vscode-server/data/logs/20250622T184336/exthost2/vscode.git/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T184336/exthost2/vscode.git/"}, "/home/<USER>/.vscode-server/data/logs/20250622T184336/exthost1/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T184336/exthost1/"}, "/home/<USER>/.vscode-server/data/logs/20250622T184336/exthost1/vscode.github/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T184336/exthost1/vscode.github/"}, "/home/<USER>/.vscode-server/data/logs/20250622T184336/exthost1/vscode.git/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T184336/exthost1/vscode.git/"}, "/home/<USER>/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/Augment.vscode-augment/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/Augment.vscode-augment/"}, "/home/<USER>/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/Augment.vscode-augment/augment-user-assets/task-storage/tasks/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/Augment.vscode-augment/augment-user-assets/task-storage/tasks/"}, "/home/<USER>/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/Augment.vscode-augment/augment-user-assets/task-storage/manifest/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/Augment.vscode-augment/augment-user-assets/task-storage/manifest/"}, "/home/<USER>/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/"}, "/home/<USER>/.vscode-server/data/User/workspaceStorage/8fb0da0c66a291b7cbfae207d20e731c/Augment.vscode-augment/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/User/workspaceStorage/8fb0da0c66a291b7cbfae207d20e731c/Augment.vscode-augment/"}, "/home/<USER>/.vscode-server/data/User/History/-571e7b31/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/User/History/-571e7b31/"}, "/home/<USER>/.vscode-server/data/CachedProfilesData/__default__profile__/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/CachedProfilesData/__default__profile__/"}, "/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/"}, "/home/<USER>/.cache/Microsoft/DeveloperTools/": {"rootPath": "/home", "relPath": "ubuntu/.cache/Microsoft/DeveloperTools/"}, "/home/<USER>/.vscode-server/data/User/History/984d552/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/User/History/984d552/"}, "/home/<USER>/.vscode-server/data/logs/20250622T184336/exthost6/vscode.json-language-features/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T184336/exthost6/vscode.json-language-features/"}, "/home/<USER>/.vscode-server/data/User/History/68928534/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/User/History/68928534/"}, "/home/<USER>/.vscode-server/data/User/History/4c9eaf87/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/User/History/4c9eaf87/"}, "/home/<USER>/.cache/pip/http-v2/d/d/c/0/4/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/http-v2/d/d/c/0/4/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip-25.1.1.dist-info/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip-25.1.1.dist-info/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip-25.1.1.dist-info/licenses/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip-25.1.1.dist-info/licenses/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_vendor/tomli_w/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_vendor/tomli_w/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_vendor/dependency_groups/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_vendor/dependency_groups/"}, "/home/<USER>/.cache/pip/selfcheck/": {"rootPath": "/home", "relPath": "ubuntu/.cache/pip/selfcheck/"}, "/home/<USER>/.vscode-server/data/User/History/7e2c7f6b/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/User/History/7e2c7f6b/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_vendor/resolvelib/resolvers/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_vendor/resolvelib/resolvers/"}, "/home/<USER>/shared-venv/lib/python3.12/site-packages/pip/_vendor/packaging/licenses/": {"rootPath": "/home", "relPath": "ubuntu/shared-venv/lib/python3.12/site-packages/pip/_vendor/packaging/licenses/"}, "/home/<USER>/.vscode-server/data/logs/20250622T200504/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T200504/"}, "/home/<USER>/.vscode-server/data/logs/20250622T200504/exthost1/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T200504/exthost1/"}, "/home/<USER>/.vscode-server/data/logs/20250622T200504/exthost1/vscode.github/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T200504/exthost1/vscode.github/"}, "/home/<USER>/.vscode-server/data/logs/20250622T200504/exthost1/vscode.git/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T200504/exthost1/vscode.git/"}, "/home/<USER>/.vscode-server/data/logs/20250622T200504/exthost1/Augment.vscode-augment/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T200504/exthost1/Augment.vscode-augment/"}, "/home/<USER>/.vscode-server/data/logs/20250622T184336/exthost6/vscode.html-language-features/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T184336/exthost6/vscode.html-language-features/"}, "/home/<USER>/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/Augment.vscode-augment/augment-user-assets/checkpoint-documents/1c862745-9469-458b-a4b9-a47af6a59b95/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/Augment.vscode-augment/augment-user-assets/checkpoint-documents/1c862745-9469-458b-a4b9-a47af6a59b95/"}, "/home/<USER>/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/Augment.vscode-augment/augment-user-assets/agent-edits/shards/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/Augment.vscode-augment/augment-user-assets/agent-edits/shards/"}, "/home/<USER>/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/Augment.vscode-augment/augment-global-state/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/Augment.vscode-augment/augment-global-state/"}, "/home/<USER>/.vscode-server/data/logs/20250622T200504/exthost1/vscode.json-language-features/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T200504/exthost1/vscode.json-language-features/"}, "/home/<USER>/.vscode-server/data/logs/20250622T205501/exthost1/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T205501/exthost1/"}, "/home/<USER>/.vscode-server/data/logs/20250622T205501/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T205501/"}, "/home/<USER>/.vscode-server/data/logs/20250622T205501/exthost1/vscode.github/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T205501/exthost1/vscode.github/"}, "/home/<USER>/.vscode-server/data/logs/20250622T205501/exthost1/vscode.git/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T205501/exthost1/vscode.git/"}, "/home/<USER>/.vscode-server/data/logs/20250622T205501/exthost1/Augment.vscode-augment/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T205501/exthost1/Augment.vscode-augment/"}, "/home/<USER>/.vscode-server/data/logs/20250622T205501/exthost1/vscode.json-language-features/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T205501/exthost1/vscode.json-language-features/"}, "/home/<USER>/.vscode-server/data/logs/20250622T205501/exthost1/vscode.markdown-language-features/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T205501/exthost1/vscode.markdown-language-features/"}, "/home/<USER>/algofactory-multi/template/test/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/test/"}, "/home/<USER>/algofactory-multi/template/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/"}, "/home/<USER>/algofactory-multi/template/templates/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/templates/"}, "/home/<USER>/algofactory-multi/template/templates/traffic/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/templates/traffic/"}, "/home/<USER>/algofactory-multi/template/templates/strategy/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/templates/strategy/"}, "/home/<USER>/algofactory-multi/template/templates/latency/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/templates/latency/"}, "/home/<USER>/algofactory-multi/template/templates/components/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/templates/components/"}, "/home/<USER>/algofactory-multi/template/templates/chartink/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/templates/chartink/"}, "/home/<USER>/algofactory-multi/template/download/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/download/"}, "/home/<USER>/algofactory-multi/template/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/database/"}, "/home/<USER>/algofactory-multi/template/src/css/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/src/css/"}, "/home/<USER>/algofactory-multi/template/broker/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/"}, "/home/<USER>/algofactory-multi/template/broker/aliceblue/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/aliceblue/api/"}, "/home/<USER>/algofactory-multi/template/broker/aliceblue/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/aliceblue/mapping/"}, "/home/<USER>/algofactory-multi/template/broker/aliceblue/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/aliceblue/"}, "/home/<USER>/algofactory-multi/template/broker/angel/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/angel/"}, "/home/<USER>/algofactory-multi/template/broker/angel/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/angel/api/"}, "/home/<USER>/algofactory-multi/template/broker/angel/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/angel/mapping/"}, "/home/<USER>/algofactory-multi/template/broker/angel/streaming/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/angel/streaming/"}, "/home/<USER>/algofactory-multi/template/broker/compositedge/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/compositedge/api/"}, "/home/<USER>/algofactory-multi/template/broker/compositedge/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/compositedge/"}, "/home/<USER>/algofactory-multi/template/broker/dhan/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/dhan/"}, "/home/<USER>/algofactory-multi/template/broker/dhan/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/dhan/api/"}, "/home/<USER>/algofactory-multi/template/broker/dhan/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/dhan/mapping/"}, "/home/<USER>/algofactory-multi/template/broker/dhan/streaming/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/dhan/streaming/"}, "/home/<USER>/algofactory-multi/template/broker/dhan_sandbox/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/dhan_sandbox/"}, "/home/<USER>/algofactory-multi/template/broker/dhan_sandbox/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/dhan_sandbox/api/"}, "/home/<USER>/algofactory-multi/template/broker/dhan_sandbox/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/dhan_sandbox/mapping/"}, "/home/<USER>/algofactory-multi/template/broker/firstock/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/firstock/"}, "/home/<USER>/algofactory-multi/template/broker/firstock/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/firstock/api/"}, "/home/<USER>/algofactory-multi/template/broker/firstock/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/firstock/mapping/"}, "/home/<USER>/algofactory-multi/template/broker/fivepaisa/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/fivepaisa/"}, "/home/<USER>/algofactory-multi/template/broker/fivepaisa/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/fivepaisa/api/"}, "/home/<USER>/algofactory-multi/template/broker/fivepaisa/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/fivepaisa/database/"}, "/home/<USER>/algofactory-multi/template/broker/fivepaisa/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/fivepaisa/mapping/"}, "/home/<USER>/algofactory-multi/template/broker/fivepaisaxts/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/fivepaisaxts/api/"}, "/home/<USER>/algofactory-multi/template/broker/fivepaisaxts/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/fivepaisaxts/"}, "/home/<USER>/algofactory-multi/template/broker/fivepaisaxts/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/fivepaisaxts/mapping/"}, "/home/<USER>/algofactory-multi/template/broker/flattrade/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/flattrade/"}, "/home/<USER>/algofactory-multi/template/broker/flattrade/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/flattrade/api/"}, "/home/<USER>/algofactory-multi/template/broker/fyers/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/fyers/mapping/"}, "/home/<USER>/algofactory-multi/template/broker/fyers/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/fyers/"}, "/home/<USER>/algofactory-multi/template/broker/groww/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/groww/"}, "/home/<USER>/algofactory-multi/template/broker/groww/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/groww/api/"}, "/home/<USER>/algofactory-multi/template/broker/groww/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/groww/mapping/"}, "/home/<USER>/algofactory-multi/template/broker/iifl/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/iifl/api/"}, "/home/<USER>/algofactory-multi/template/broker/iifl/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/iifl/"}, "/home/<USER>/algofactory-multi/template/broker/iifl/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/iifl/mapping/"}, "/home/<USER>/algofactory-multi/template/broker/jainam/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/jainam/api/"}, "/home/<USER>/algofactory-multi/template/broker/jainam/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/jainam/"}, "/home/<USER>/algofactory-multi/template/broker/jainam/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/jainam/mapping/"}, "/home/<USER>/algofactory-multi/template/broker/jainampro/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/jainampro/api/"}, "/home/<USER>/algofactory-multi/template/broker/jainampro/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/jainampro/"}, "/home/<USER>/algofactory-multi/template/broker/jainampro/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/jainampro/mapping/"}, "/home/<USER>/algofactory-multi/template/broker/kotak/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/kotak/"}, "/home/<USER>/algofactory-multi/template/broker/kotak/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/kotak/api/"}, "/home/<USER>/algofactory-multi/template/broker/kotak/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/kotak/mapping/"}, "/home/<USER>/algofactory-multi/template/broker/paytm/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/paytm/api/"}, "/home/<USER>/algofactory-multi/template/broker/paytm/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/paytm/mapping/"}, "/home/<USER>/algofactory-multi/template/broker/paytm/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/paytm/"}, "/home/<USER>/algofactory-multi/template/broker/pocketful/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/pocketful/api/"}, "/home/<USER>/algofactory-multi/template/broker/pocketful/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/pocketful/mapping/"}, "/home/<USER>/algofactory-multi/template/broker/pocketful/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/pocketful/"}, "/home/<USER>/algofactory-multi/template/broker/shoonya/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/shoonya/"}, "/home/<USER>/algofactory-multi/template/broker/shoonya/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/shoonya/api/"}, "/home/<USER>/algofactory-multi/template/broker/shoonya/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/shoonya/database/"}, "/home/<USER>/algofactory-multi/template/broker/shoonya/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/shoonya/mapping/"}, "/home/<USER>/algofactory-multi/template/broker/tradejini/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/tradejini/"}, "/home/<USER>/algofactory-multi/template/broker/tradejini/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/tradejini/api/"}, "/home/<USER>/algofactory-multi/template/broker/tradejini/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/tradejini/mapping/"}, "/home/<USER>/algofactory-multi/template/broker/upstox/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/upstox/"}, "/home/<USER>/algofactory-multi/template/broker/upstox/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/upstox/api/"}, "/home/<USER>/algofactory-multi/template/broker/upstox/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/upstox/mapping/"}, "/home/<USER>/algofactory-multi/template/broker/wisdom/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/wisdom/api/"}, "/home/<USER>/algofactory-multi/template/broker/wisdom/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/wisdom/"}, "/home/<USER>/algofactory-multi/template/broker/wisdom/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/wisdom/mapping/"}, "/home/<USER>/algofactory-multi/template/broker/zebu/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/zebu/"}, "/home/<USER>/algofactory-multi/template/broker/zebu/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/zebu/api/"}, "/home/<USER>/algofactory-multi/template/broker/zebu/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/zebu/mapping/"}, "/home/<USER>/algofactory-multi/template/broker/zerodha/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/zerodha/api/"}, "/home/<USER>/algofactory-multi/template/broker/zerodha/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/zerodha/mapping/"}, "/home/<USER>/algofactory-multi/template/broker/zerodha/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/zerodha/"}, "/home/<USER>/algofactory-multi/template/broker/zerodha/streaming/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/zerodha/streaming/"}, "/home/<USER>/algofactory-multi/template/collections/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/collections/"}, "/home/<USER>/algofactory-multi/template/design/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/design/"}, "/home/<USER>/algofactory-multi/template/docs/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/docs/"}, "/home/<USER>/algofactory-multi/template/install/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/install/"}, "/home/<USER>/algofactory-multi/template/restx_api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/restx_api/"}, "/home/<USER>/algofactory-multi/template/static/css/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/static/css/"}, "/home/<USER>/algofactory-multi/template/broker/dhan/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/dhan/database/"}, "/home/<USER>/algofactory-multi/template/broker/flattrade/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/flattrade/mapping/"}, "/home/<USER>/algofactory-multi/template/broker/fyers/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/fyers/api/"}, "/home/<USER>/algofactory-multi/template/tmp/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/tmp/"}, "/home/<USER>/algofactory-multi/template/upgrade/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/upgrade/"}, "/home/<USER>/algofactory-multi/template/websocket_proxy/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/websocket_proxy/"}, "/home/<USER>/algofactory-multi/template/.ebextensions/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/.ebextensions/"}, "/home/<USER>/algofactory-multi/template/broker/aliceblue/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/aliceblue/database/"}, "/home/<USER>/algofactory-multi/template/broker/compositedge/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/compositedge/database/"}, "/home/<USER>/algofactory-multi/template/broker/fivepaisaxts/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/fivepaisaxts/database/"}, "/home/<USER>/algofactory-multi/template/broker/flattrade/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/flattrade/database/"}, "/home/<USER>/algofactory-multi/template/broker/fyers/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/fyers/database/"}, "/home/<USER>/algofactory-multi/template/broker/groww/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/groww/database/"}, "/home/<USER>/algofactory-multi/template/broker/iifl/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/iifl/database/"}, "/home/<USER>/algofactory-multi/template/broker/jainam/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/jainam/database/"}, "/home/<USER>/algofactory-multi/template/broker/jainampro/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/jainampro/database/"}, "/home/<USER>/algofactory-multi/template/broker/kotak/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/kotak/database/"}, "/home/<USER>/algofactory-multi/template/broker/paytm/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/paytm/database/"}, "/home/<USER>/algofactory-multi/template/broker/pocketful/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/pocketful/database/"}, "/home/<USER>/algofactory-multi/template/broker/tradejini/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/tradejini/database/"}, "/home/<USER>/algofactory-multi/template/broker/upstox/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/upstox/database/"}, "/home/<USER>/algofactory-multi/template/broker/wisdom/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/wisdom/database/"}, "/home/<USER>/algofactory-multi/template/broker/zerodha/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/zerodha/database/"}, "/home/<USER>/algofactory-multi/template/broker/zebu/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/zebu/database/"}, "/home/<USER>/algofactory-multi/template/broker/compositedge/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/compositedge/mapping/"}, "/home/<USER>/algofactory-multi/template/broker/angel/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/angel/database/"}, "/home/<USER>/algofactory-multi/template/broker/dhan_sandbox/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/dhan_sandbox/database/"}, "/home/<USER>/algofactory-multi/template/broker/firstock/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/broker/firstock/database/"}, "/home/<USER>/algofactory-multi/template/static/js/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/static/js/"}, "/home/<USER>/algofactory-multi/template/static/favicon/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/static/favicon/"}, "/home/<USER>/algofactory-multi/template/blueprints/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/blueprints/"}, "/home/<USER>/algofactory-multi/template/services/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/services/"}, "/home/<USER>/algofactory-multi/template/strategies/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/strategies/"}, "/home/<USER>/algofactory-multi/template/utils/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/template/utils/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/database/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/download/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/download/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/templates/components/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/templates/components/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/templates/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/templates/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/test/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/test/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/templates/traffic/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/templates/traffic/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/templates/strategy/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/templates/strategy/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/templates/latency/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/templates/latency/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/templates/chartink/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/templates/chartink/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/src/css/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/src/css/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/aliceblue/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/aliceblue/api/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/aliceblue/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/aliceblue/mapping/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/aliceblue/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/aliceblue/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/angel/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/angel/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/angel/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/angel/api/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/angel/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/angel/mapping/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/angel/streaming/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/angel/streaming/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/compositedge/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/compositedge/api/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/compositedge/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/compositedge/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/compositedge/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/compositedge/mapping/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/dhan/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/dhan/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/dhan/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/dhan/api/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/dhan/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/dhan/mapping/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/dhan/streaming/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/dhan/streaming/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/dhan_sandbox/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/dhan_sandbox/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/dhan_sandbox/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/dhan_sandbox/api/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/dhan_sandbox/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/dhan_sandbox/mapping/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/firstock/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/firstock/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/firstock/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/firstock/api/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/firstock/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/firstock/mapping/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/fivepaisa/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/fivepaisa/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/fivepaisa/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/fivepaisa/api/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/fivepaisa/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/fivepaisa/mapping/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/fivepaisaxts/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/fivepaisaxts/api/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/fivepaisaxts/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/fivepaisaxts/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/fivepaisaxts/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/fivepaisaxts/mapping/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/flattrade/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/flattrade/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/flattrade/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/flattrade/api/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/flattrade/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/flattrade/mapping/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/fyers/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/fyers/api/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/fyers/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/fyers/mapping/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/fyers/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/fyers/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/groww/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/groww/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/groww/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/groww/api/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/groww/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/groww/mapping/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/iifl/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/iifl/api/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/iifl/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/iifl/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/iifl/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/iifl/mapping/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/jainam/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/jainam/api/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/jainam/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/jainam/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/jainam/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/jainam/mapping/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/jainampro/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/jainampro/api/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/jainampro/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/jainampro/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/jainampro/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/jainampro/mapping/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/kotak/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/kotak/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/kotak/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/kotak/api/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/kotak/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/kotak/mapping/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/paytm/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/paytm/api/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/paytm/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/paytm/mapping/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/paytm/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/paytm/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/pocketful/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/pocketful/api/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/pocketful/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/pocketful/mapping/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/pocketful/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/pocketful/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/shoonya/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/shoonya/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/shoonya/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/shoonya/api/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/shoonya/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/shoonya/mapping/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/tradejini/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/tradejini/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/tradejini/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/tradejini/api/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/tradejini/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/tradejini/mapping/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/upstox/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/upstox/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/upstox/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/upstox/api/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/upstox/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/upstox/mapping/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/zebu/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/zebu/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/zebu/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/zebu/api/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/zebu/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/zebu/mapping/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/zerodha/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/zerodha/api/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/zerodha/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/zerodha/mapping/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/zerodha/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/zerodha/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/zerodha/streaming/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/zerodha/streaming/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/collections/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/collections/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/design/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/design/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/docs/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/docs/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/restx_api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/restx_api/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/static/favicon/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/static/favicon/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/static/js/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/static/js/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/tmp/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/tmp/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/.ebextensions/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/.ebextensions/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/install/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/install/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/zebu/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/zebu/database/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/zerodha/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/zerodha/database/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/wisdom/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/wisdom/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/wisdom/mapping/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/wisdom/mapping/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/wisdom/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/wisdom/database/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/wisdom/api/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/wisdom/api/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/upstox/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/upstox/database/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/tradejini/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/tradejini/database/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/shoonya/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/shoonya/database/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/pocketful/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/pocketful/database/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/paytm/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/paytm/database/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/kotak/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/kotak/database/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/jainampro/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/jainampro/database/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/jainam/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/jainam/database/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/iifl/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/iifl/database/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/groww/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/groww/database/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/fyers/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/fyers/database/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/flattrade/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/flattrade/database/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/fivepaisaxts/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/fivepaisaxts/database/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/fivepaisa/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/fivepaisa/database/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/firstock/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/firstock/database/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/dhan_sandbox/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/dhan_sandbox/database/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/dhan/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/dhan/database/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/compositedge/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/compositedge/database/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/angel/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/angel/database/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/broker/aliceblue/database/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/broker/aliceblue/database/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/static/css/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/static/css/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/blueprints/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/blueprints/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/upgrade/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/upgrade/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/websocket_proxy/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/websocket_proxy/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/services/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/services/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/strategies/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/strategies/"}, "/home/<USER>/algofactory-multi/instances/algofactory-1010/utils/": {"rootPath": "/home", "relPath": "ubuntu/algofactory-multi/instances/algofactory-1010/utils/"}, "/home/<USER>/.vscode-server/data/User/History/30aa9e21/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/User/History/30aa9e21/"}, "/home/<USER>/.vscode-server/data/logs/20250622T212258/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T212258/"}, "/home/<USER>/.vscode-server/data/logs/20250622T212258/exthost1/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T212258/exthost1/"}, "/home/<USER>/.vscode-server/data/logs/20250622T212258/exthost1/vscode.github/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T212258/exthost1/vscode.github/"}, "/home/<USER>/.vscode-server/data/logs/20250622T212258/exthost1/vscode.git/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T212258/exthost1/vscode.git/"}, "/home/<USER>/.vscode-server/data/logs/20250622T212258/exthost1/Augment.vscode-augment/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T212258/exthost1/Augment.vscode-augment/"}, "/home/<USER>/.vscode-server/data/logs/20250622T212258/exthost1/vscode.json-language-features/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/logs/20250622T212258/exthost1/vscode.json-language-features/"}, "/home/<USER>/.vscode-server/data/User/History/2e014689/": {"rootPath": "/home", "relPath": "ubuntu/.vscode-server/data/User/History/2e014689/"}}