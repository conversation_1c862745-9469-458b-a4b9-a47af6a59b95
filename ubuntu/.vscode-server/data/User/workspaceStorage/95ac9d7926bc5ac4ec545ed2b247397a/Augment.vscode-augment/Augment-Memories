# Preferences
- User prefers Python applications to run in shared Python environments with full permissions and robust error handling via shell scripts.
- User prefers to hardcode instance IDs (like 1010) as usernames in HTML forms to prevent user modification in multi-instance applications.
- User prefers to keep original templates unchanged in multi-instance applications and only modify ports/configuration.
- User prefers multi-instance setup with directory naming pattern algofactory-1010, algofactory-1011, etc.
- User prefers automated 24/7 server monitoring with auto-restart scripts to ensure services stay running continuously.

# Constraints
- User has 1GB RAM server limitation causing crashes and needs VS Code-compatible optimization for development environment.
`