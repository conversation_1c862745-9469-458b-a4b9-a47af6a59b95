#!/usr/bin/env python3

"""
Production runner for AlgoFactory Admin Dashboard
Uses Gunicorn for production deployment
"""

import os
import sys
import signal
import time
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

def signal_handler(signum, frame):
    print(f"\n🛑 Received signal {signum}, shutting down gracefully...")
    sys.exit(0)

def main():
    # Set up signal handlers
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)
    
    print("🚀 Starting AlgoFactory Admin Dashboard (Production)")
    print("=" * 55)
    print(f"Time: {time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    print(f"Directory: {os.getcwd()}")
    print(f"Python: {sys.executable}")
    print("")
    
    try:
        # Import and run the app
        from app import app, socketio

        print("✅ System monitoring will start with app")
        print("🌐 Starting web server on http://0.0.0.0:9001")
        print("📊 Admin Dashboard: https://admin.algofactory.in")
        print("🔐 Login: admin / admin123")
        print("")

        # Run with SocketIO
        socketio.run(app,
                    host='0.0.0.0',
                    port=9001,
                    debug=False,
                    use_reloader=False,
                    log_output=True,
                    allow_unsafe_werkzeug=True)

    except KeyboardInterrupt:
        print("\n🛑 Shutdown requested by user")
    except Exception as e:
        print(f"❌ Error starting admin dashboard: {e}")
        import traceback
        traceback.print_exc()
        return 1
    finally:
        print("✅ Admin dashboard stopped")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
