<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <!-- Theme script to prevent flash -->
    <script>
        (function() {
            const savedTheme = localStorage.getItem('admin-theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);
        })();
    </script>
    
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="AlgoFactory Admin Dashboard - Advanced server management system">
    <title>{% block title %}AlgoFactory Admin{% endblock %}</title>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="{{ url_for('static', filename='favicon.ico') }}">
    
    <!-- DaisyUI + Tailwind CSS -->
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.4.24/dist/full.min.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    
    <!-- Chart.js for metrics visualization -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Socket.IO for real-time updates -->
    <script src="https://cdn.socket.io/4.7.4/socket.io.min.js"></script>
    
    <!-- Custom styles -->
    <style>
        .sidebar-transition {
            transition: transform 0.3s ease-in-out;
        }
        
        .metric-card {
            transition: all 0.3s ease;
        }
        
        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .status-indicator {
            animation: pulse 2s infinite;
        }
        
        .status-online {
            background-color: #10b981;
        }
        
        .status-warning {
            background-color: #f59e0b;
        }
        
        .status-error {
            background-color: #ef4444;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
        }
        
        .log-entry {
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
        }
        
        .progress-ring {
            transform: rotate(-90deg);
        }
        
        .progress-ring-circle {
            transition: stroke-dasharray 0.35s;
            transform-origin: 50% 50%;
        }
        
        /* Custom scrollbar */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-track {
            background: hsl(var(--b2));
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: hsl(var(--bc) / 0.3);
            border-radius: 3px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: hsl(var(--bc) / 0.5);
        }
    </style>
    
    {% block head %}{% endblock %}
</head>
<body class="min-h-screen bg-base-100">
    <!-- Main Layout -->
    <div class="drawer lg:drawer-open">
        <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
        
        <!-- Main Content -->
        <div class="drawer-content flex flex-col">
            <!-- Top Navigation -->
            <div class="navbar bg-base-100 shadow-sm border-b border-base-200">
                <div class="flex-none lg:hidden">
                    <label for="drawer-toggle" class="btn btn-square btn-ghost">
                        <i class="fas fa-bars text-lg"></i>
                    </label>
                </div>
                
                <div class="flex-1">
                    <h1 class="text-xl font-semibold text-base-content">
                        {% block page_title %}AlgoFactory Admin Dashboard{% endblock %}
                    </h1>
                </div>
                
                <div class="flex-none gap-2">
                    <!-- Connection Status -->
                    <div class="indicator">
                        <span id="connection-status" class="indicator-item badge badge-error badge-xs"></span>
                        <div class="tooltip tooltip-bottom" data-tip="Connection Status">
                            <i class="fas fa-wifi text-lg"></i>
                        </div>
                    </div>
                    
                    <!-- Theme Toggle -->
                    <div class="dropdown dropdown-end">
                        <div tabindex="0" role="button" class="btn btn-ghost btn-circle">
                            <i class="fas fa-palette text-lg"></i>
                        </div>
                        <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                            <li><a onclick="setTheme('light')"><i class="fas fa-sun"></i> Light</a></li>
                            <li><a onclick="setTheme('dark')"><i class="fas fa-moon"></i> Dark</a></li>
                            <li><a onclick="setTheme('cupcake')"><i class="fas fa-heart"></i> Cupcake</a></li>
                            <li><a onclick="setTheme('cyberpunk')"><i class="fas fa-robot"></i> Cyberpunk</a></li>
                            <li><a onclick="setTheme('synthwave')"><i class="fas fa-wave-square"></i> Synthwave</a></li>
                        </ul>
                    </div>
                    
                    <!-- User Menu -->
                    <div class="dropdown dropdown-end">
                        <div tabindex="0" role="button" class="btn btn-ghost btn-circle avatar">
                            <div class="w-8 rounded-full bg-primary text-primary-content flex items-center justify-center">
                                <i class="fas fa-user"></i>
                            </div>
                        </div>
                        <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                            <li class="menu-title">
                                <span>{{ session.username or 'Admin' }}</span>
                            </li>
                            <li><a href="{{ url_for('dashboard') }}"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                            <li><a href="{{ url_for('change_password') }}"><i class="fas fa-key"></i> Change Password</a></li>
                            <li><a href="#"><i class="fas fa-cog"></i> Settings</a></li>
                            <div class="divider my-1"></div>
                            <li><a href="{{ url_for('logout') }}" class="text-error"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Page Content -->
            <main class="flex-1 p-4 lg:p-6">
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        <div class="mb-4 space-y-2">
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'success' if category == 'success' else 'error' }} shadow-lg">
                                    <div class="flex items-center">
                                        {% if category == 'success' %}
                                            <i class="fas fa-check-circle text-lg"></i>
                                        {% else %}
                                            <i class="fas fa-exclamation-circle text-lg"></i>
                                        {% endif %}
                                        <span>{{ message }}</span>
                                        <button onclick="this.parentElement.parentElement.remove()" class="btn btn-ghost btn-sm btn-circle ml-auto">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                {% endwith %}
                
                <!-- Toast Container -->
                <div id="toast-container" class="toast toast-top toast-end z-50"></div>
                
                {% block content %}{% endblock %}
            </main>
        </div>
        
        <!-- Sidebar -->
        <div class="drawer-side">
            <label for="drawer-toggle" aria-label="close sidebar" class="drawer-overlay"></label>
            <aside class="min-h-full w-64 bg-base-200 text-base-content">
                <!-- Logo -->
                <div class="p-4 border-b border-base-300">
                    <div class="flex items-center gap-3">
                        <div class="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                            <i class="fas fa-cogs text-primary-content text-lg"></i>
                        </div>
                        <div>
                            <h2 class="font-bold text-lg">AlgoFactory</h2>
                            <p class="text-xs text-base-content/70">Admin Dashboard</p>
                        </div>
                    </div>
                </div>
                
                <!-- Navigation Menu -->
                <ul class="menu p-4 space-y-2">
                    <!-- Dashboard -->
                    <li>
                        <a href="{{ url_for('dashboard') }}" class="{% if request.endpoint == 'dashboard' %}active{% endif %}">
                            <i class="fas fa-tachometer-alt"></i>
                            Dashboard
                        </a>
                    </li>
                    
                    <!-- System Management -->
                    <li class="menu-title">
                        <span>System Management</span>
                    </li>
                    <li>
                        <a href="{{ url_for('system_monitor') }}" class="{% if request.endpoint == 'system_monitor' %}active{% endif %}">
                            <i class="fas fa-server"></i>
                            System Monitor
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('instances') }}" class="{% if request.endpoint == 'instances' %}active{% endif %}">
                            <i class="fas fa-cubes"></i>
                            AlgoFactory Instances
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('nginx_management') }}" class="{% if request.endpoint == 'nginx_management' %}active{% endif %}">
                            <i class="fas fa-globe"></i>
                            Nginx Management
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('ssl_certificates') }}" class="{% if request.endpoint == 'ssl_certificates' %}active{% endif %}">
                            <i class="fas fa-lock"></i>
                            SSL Certificates
                        </a>
                    </li>
                    
                    <!-- Monitoring & Logs -->
                    <li class="menu-title">
                        <span>Monitoring & Logs</span>
                    </li>
                    <li>
                        <a href="{{ url_for('system_logs') }}" class="{% if request.endpoint == 'system_logs' %}active{% endif %}">
                            <i class="fas fa-file-alt"></i>
                            System Logs
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('performance_metrics') }}" class="{% if request.endpoint == 'performance_metrics' %}active{% endif %}">
                            <i class="fas fa-chart-line"></i>
                            Performance Metrics
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('alerts') }}" class="{% if request.endpoint == 'alerts' %}active{% endif %}">
                            <i class="fas fa-bell"></i>
                            Alerts & Notifications
                        </a>
                    </li>
                    
                    <!-- Tools & Utilities -->
                    <li class="menu-title">
                        <span>Tools & Utilities</span>
                    </li>
                    <li>
                        <a href="{{ url_for('backup') }}" class="{% if request.endpoint == 'backup' %}active{% endif %}">
                            <i class="fas fa-database"></i>
                            Backup & Restore
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('tasks') }}" class="{% if request.endpoint == 'tasks' %}active{% endif %}">
                            <i class="fas fa-tasks"></i>
                            Task Manager
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('web_terminal') }}" class="{% if request.endpoint == 'web_terminal' %}active{% endif %}">
                            <i class="fas fa-terminal"></i>
                            Web Terminal
                        </a>
                    </li>
                    
                    <!-- Settings -->
                    <li class="menu-title">
                        <span>Configuration</span>
                    </li>
                    <li>
                        <a href="{{ url_for('settings') }}" class="{% if request.endpoint == 'settings' %}active{% endif %}">
                            <i class="fas fa-cog"></i>
                            System Settings
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('users') }}" class="{% if request.endpoint == 'users' %}active{% endif %}">
                            <i class="fas fa-users"></i>
                            User Management
                        </a>
                    </li>
                </ul>
                
                <!-- System Status Footer -->
                <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-base-300">
                    <div class="text-xs space-y-1">
                        <div class="flex justify-between">
                            <span>System Status:</span>
                            <span id="system-status" class="badge badge-success badge-xs">Online</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Uptime:</span>
                            <span id="system-uptime" class="text-base-content/70">--</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Memory:</span>
                            <span id="memory-usage" class="text-base-content/70">--</span>
                        </div>
                    </div>
                </div>
            </aside>
        </div>
    </div>
    
    <!-- Global JavaScript -->
    <script>
        // Theme management
        function setTheme(theme) {
            document.documentElement.setAttribute('data-theme', theme);
            localStorage.setItem('admin-theme', theme);
            showToast(`Theme changed to ${theme}`, 'success');
        }
        
        // Toast notifications
        function showToast(message, type = 'info', duration = 3000) {
            const container = document.getElementById('toast-container');
            const toast = document.createElement('div');
            
            const alertClass = type === 'success' ? 'alert-success' : 
                              type === 'error' ? 'alert-error' : 
                              type === 'warning' ? 'alert-warning' : 'alert-info';
            
            const icon = type === 'success' ? 'fa-check-circle' : 
                        type === 'error' ? 'fa-exclamation-circle' : 
                        type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle';
            
            toast.className = `alert ${alertClass} shadow-lg mb-2`;
            toast.innerHTML = `
                <div class="flex items-center">
                    <i class="fas ${icon}"></i>
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="btn btn-ghost btn-sm btn-circle ml-auto">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            
            container.appendChild(toast);
            
            // Auto remove after duration
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, duration);
        }
        
        // Socket.IO connection
        let socket = null;
        
        function initializeSocket() {
            socket = io('/admin');
            
            socket.on('connect', function() {
                console.log('Connected to admin dashboard');
                updateConnectionStatus(true);
            });
            
            socket.on('disconnect', function() {
                console.log('Disconnected from admin dashboard');
                updateConnectionStatus(false);
            });
            
            socket.on('system_update', function(data) {
                handleSystemUpdate(data);
            });
            
            socket.on('system_alert', function(alert) {
                showToast(alert.message, alert.severity === 'critical' ? 'error' : 'warning');
            });
        }
        
        function updateConnectionStatus(connected) {
            const statusElement = document.getElementById('connection-status');
            if (connected) {
                statusElement.className = 'indicator-item badge badge-success badge-xs';
            } else {
                statusElement.className = 'indicator-item badge badge-error badge-xs';
            }
        }
        
        function handleSystemUpdate(data) {
            // Update sidebar status
            if (data.data && data.data.system) {
                const system = data.data.system;
                
                // Update memory usage
                const memoryElement = document.getElementById('memory-usage');
                if (memoryElement && system.memory) {
                    memoryElement.textContent = `${system.memory.percent}%`;
                }
                
                // Update system status
                const statusElement = document.getElementById('system-status');
                if (statusElement) {
                    const memoryPercent = system.memory ? system.memory.percent : 0;
                    const cpuPercent = system.cpu ? system.cpu.percent : 0;
                    
                    if (memoryPercent > 90 || cpuPercent > 90) {
                        statusElement.className = 'badge badge-error badge-xs';
                        statusElement.textContent = 'Critical';
                    } else if (memoryPercent > 80 || cpuPercent > 80) {
                        statusElement.className = 'badge badge-warning badge-xs';
                        statusElement.textContent = 'Warning';
                    } else {
                        statusElement.className = 'badge badge-success badge-xs';
                        statusElement.textContent = 'Online';
                    }
                }
            }
            
            // Trigger custom event for page-specific handlers
            window.dispatchEvent(new CustomEvent('systemUpdate', { detail: data }));
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initializeSocket();
        });
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (socket) {
                socket.disconnect();
            }
        });
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
