#!/bin/bash

# AlgoFactory Robust Startup Script
# This script automatically starts app.py with full error handling and recovery

set -e  # Exit on any error

# Configuration
PROJECT_DIR="/home/<USER>/myproject/algofactory"
SHARED_VENV="/home/<USER>/shared-venv"
APP_FILE="app.py"
LOG_DIR="$PROJECT_DIR/logs"
PID_FILE="$PROJECT_DIR/app.pid"
MAX_RETRIES=5
RETRY_DELAY=10

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1"
}

warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

# Function to check if app is running
is_app_running() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0
        else
            rm -f "$PID_FILE"
            return 1
        fi
    fi
    return 1
}

# Function to stop the app
stop_app() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        log "Stopping application (PID: $pid)..."
        kill -TERM "$pid" 2>/dev/null || true
        sleep 5
        if ps -p "$pid" > /dev/null 2>&1; then
            warning "Application didn't stop gracefully, forcing kill..."
            kill -KILL "$pid" 2>/dev/null || true
        fi
        rm -f "$PID_FILE"
        success "Application stopped"
    fi
}

# Function to setup environment
setup_environment() {
    log "Setting up environment..."

    # Change to project directory
    cd "$PROJECT_DIR" || {
        error "Failed to change to project directory: $PROJECT_DIR"
        exit 1
    }

    # Create necessary directories with full permissions
    log "Creating necessary directories..."
    mkdir -p db logs tmp static/uploads

    # Set full permissions (777) for all necessary directories
    chmod -R 777 db logs tmp static 2>/dev/null || {
        warning "Could not set permissions on some directories (may be mounted volumes)"
    }

    # Check if shared virtual environment exists
    if [ ! -d "$SHARED_VENV" ]; then
        error "Shared virtual environment not found at: $SHARED_VENV"
        log "Creating shared virtual environment..."
        python3 -m venv "$SHARED_VENV" || {
            error "Failed to create virtual environment"
            exit 1
        }
    fi

    # Check if virtual environment has Python
    if [ ! -f "$SHARED_VENV/bin/python" ]; then
        error "Python not found in virtual environment: $SHARED_VENV/bin/python"
        exit 1
    fi

    success "Environment setup completed"
}

# Function to install/update dependencies
install_dependencies() {
    log "Installing/updating dependencies..."

    # Activate virtual environment
    source "$SHARED_VENV/bin/activate" || {
        error "Failed to activate virtual environment"
        exit 1
    }

    # Upgrade pip
    pip install --upgrade pip > /dev/null 2>&1 || {
        warning "Failed to upgrade pip"
    }

    # Install requirements if file exists
    if [ -f "requirements.txt" ]; then
        log "Installing requirements from requirements.txt..."
        pip install -r requirements.txt || {
            error "Failed to install requirements"
            exit 1
        }
    fi

    # Install additional packages that might be needed
    pip install gunicorn eventlet python-dotenv > /dev/null 2>&1 || {
        warning "Failed to install some additional packages"
    }

    success "Dependencies installed successfully"
}

# Function to start the application
start_app() {
    local retry_count=0

    while [ $retry_count -lt $MAX_RETRIES ]; do
        log "Starting AlgoFactory application (attempt $((retry_count + 1))/$MAX_RETRIES)..."

        # Activate virtual environment
        source "$SHARED_VENV/bin/activate" || {
            error "Failed to activate virtual environment"
            exit 1
        }

        # Check if app.py exists
        if [ ! -f "$APP_FILE" ]; then
            error "Application file not found: $APP_FILE"
            exit 1
        fi

        # Start the application with gunicorn
        nohup "$SHARED_VENV/bin/gunicorn" \
            --bind=0.0.0.0:5000 \
            --worker-class=eventlet \
            --workers=1 \
            --timeout=120 \
            --keep-alive=2 \
            --max-requests=1000 \
            --max-requests-jitter=100 \
            --preload \
            --log-level=info \
            --access-logfile="$LOG_DIR/access.log" \
            --error-logfile="$LOG_DIR/error.log" \
            --capture-output \
            --enable-stdio-inheritance \
            app:app > "$LOG_DIR/app.log" 2>&1 &

        local app_pid=$!
        echo $app_pid > "$PID_FILE"

        # Wait a moment and check if the app started successfully
        sleep 5

        if ps -p "$app_pid" > /dev/null 2>&1; then
            success "AlgoFactory application started successfully (PID: $app_pid)"
            success "Application is running on http://0.0.0.0:5000"
            success "Logs are available in: $LOG_DIR/"
            return 0
        else
            error "Application failed to start (attempt $((retry_count + 1)))"
            rm -f "$PID_FILE"

            # Show last few lines of error log
            if [ -f "$LOG_DIR/error.log" ]; then
                error "Last few lines from error log:"
                tail -10 "$LOG_DIR/error.log" | while read line; do
                    error "  $line"
                done
            fi

            retry_count=$((retry_count + 1))
            if [ $retry_count -lt $MAX_RETRIES ]; then
                warning "Retrying in $RETRY_DELAY seconds..."
                sleep $RETRY_DELAY
            fi
        fi
    done

    error "Failed to start application after $MAX_RETRIES attempts"
    exit 1
}

# Function to monitor the application
monitor_app() {
    log "Starting application monitor..."

    while true; do
        if ! is_app_running; then
            warning "Application is not running, attempting to restart..."
            start_app
        fi
        sleep 30  # Check every 30 seconds
    done
}

# Main execution
main() {
    log "=== AlgoFactory Startup Script ==="
    log "Project Directory: $PROJECT_DIR"
    log "Shared Virtual Environment: $SHARED_VENV"
    log "Application File: $APP_FILE"

    # Handle command line arguments
    case "${1:-start}" in
        "start")
            if is_app_running; then
                warning "Application is already running"
                exit 0
            fi
            setup_environment
            install_dependencies
            start_app
            ;;
        "stop")
            stop_app
            ;;
        "restart")
            stop_app
            setup_environment
            install_dependencies
            start_app
            ;;
        "status")
            if is_app_running; then
                success "Application is running (PID: $(cat $PID_FILE))"
            else
                warning "Application is not running"
            fi
            ;;
        "monitor")
            setup_environment
            install_dependencies
            monitor_app
            ;;
        "logs")
            if [ -f "$LOG_DIR/app.log" ]; then
                tail -f "$LOG_DIR/app.log"
            else
                error "Log file not found: $LOG_DIR/app.log"
            fi
            ;;
        *)
            echo "Usage: $0 {start|stop|restart|status|monitor|logs}"
            echo "  start   - Start the application"
            echo "  stop    - Stop the application"
            echo "  restart - Restart the application"
            echo "  status  - Check application status"
            echo "  monitor - Start with continuous monitoring"
            echo "  logs    - Show application logs"
            exit 1
            ;;
    esac
}

# Trap signals for graceful shutdown
trap 'stop_app; exit 0' SIGTERM SIGINT

# Run main function
main "$@"
