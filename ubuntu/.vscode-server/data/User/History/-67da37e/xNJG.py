#!/usr/bin/env python3

"""
Centralized Nginx Configuration Manager for AlgoFactory
Automates Nginx setup, SSL certificates, and reverse proxy configuration
"""

import os
import sys
import subprocess
import argparse
import json
from pathlib import Path
import time

class NginxManager:
    def __init__(self):
        self.nginx_sites_available = "/etc/nginx/sites-available"
        self.nginx_sites_enabled = "/etc/nginx/sites-enabled"
        self.base_domain = "algofactory.in"
        self.email = "<EMAIL>"
        
    def create_nginx_config(self, subdomain, port):
        """Create Nginx configuration for a subdomain"""
        domain = f"{subdomain}.{self.base_domain}"
        config_file = f"{self.nginx_sites_available}/{domain}.conf"
        
        config_content = f"""# AlgoFactory Nginx Configuration for {domain}
# Auto-generated by nginx_manager.py

server {{
    listen 80;
    server_name {domain};

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy strict-origin-when-cross-origin;

    # Rate limiting (applied from global configuration)
    limit_req zone=algofactory_general burst=20 nodelay;

    # Main location block
    location / {{
        # Proxy settings
        proxy_pass http://127.0.0.1:{port};
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        proxy_buffering off;

        # Buffer settings
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;

        # File upload size
        client_max_body_size 50M;
    }}

    # Static files optimization
    location /static/ {{
        proxy_pass http://127.0.0.1:{port};
        expires 1d;
        add_header Cache-Control "public, immutable";
    }}

    # API endpoints with rate limiting
    location /api/ {{
        limit_req zone=algofactory_api burst=10 nodelay;

        proxy_pass http://127.0.0.1:{port};
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # API specific timeouts
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }}

    # Health check endpoint
    location /health {{
        proxy_pass http://127.0.0.1:{port};
        access_log off;
    }}

    # Security - block common attack patterns
    location ~ /\\. {{
        deny all;
        access_log off;
        log_not_found off;
    }}

    location ~ /(config|logs|tmp)/ {{
        deny all;
        access_log off;
        log_not_found off;
    }}
}}

# HTTPS redirect will be added by Certbot
"""
        
        try:
            with open(config_file, 'w') as f:
                f.write(config_content)
            print(f"✅ Created Nginx config: {config_file}")
            return True
        except Exception as e:
            print(f"❌ Error creating Nginx config: {e}")
            return False
    
    def enable_site(self, subdomain):
        """Enable Nginx site"""
        domain = f"{subdomain}.{self.base_domain}"
        available_file = f"{self.nginx_sites_available}/{domain}.conf"
        enabled_file = f"{self.nginx_sites_enabled}/{domain}.conf"
        
        try:
            # Create symlink
            if os.path.exists(enabled_file):
                os.remove(enabled_file)
            os.symlink(available_file, enabled_file)
            print(f"✅ Enabled site: {domain}")
            return True
        except Exception as e:
            print(f"❌ Error enabling site: {e}")
            return False
    
    def test_nginx_config(self):
        """Test Nginx configuration"""
        try:
            result = subprocess.run(['nginx', '-t'], capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ Nginx configuration test passed")
                return True
            else:
                print(f"❌ Nginx configuration test failed: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ Error testing Nginx config: {e}")
            return False
    
    def reload_nginx(self):
        """Reload Nginx configuration"""
        try:
            subprocess.run(['systemctl', 'reload', 'nginx'], check=True)
            print("✅ Nginx reloaded successfully")
            return True
        except Exception as e:
            print(f"❌ Error reloading Nginx: {e}")
            return False
    
    def install_ssl(self, subdomain):
        """Install SSL certificate using Certbot"""
        domain = f"{subdomain}.{self.base_domain}"
        
        try:
            print(f"🔒 Installing SSL certificate for {domain}...")
            
            # Run certbot
            cmd = [
                'certbot', '--nginx',
                '-d', domain,
                '--non-interactive',
                '--agree-tos',
                '--email', self.email,
                '--redirect'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ SSL certificate installed for {domain}")
                return True
            else:
                print(f"❌ SSL installation failed: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Error installing SSL: {e}")
            return False
    
    def setup_subdomain(self, subdomain, port, skip_ssl=False):
        """Complete setup for a subdomain"""
        domain = f"{subdomain}.{self.base_domain}"
        
        print(f"🚀 Setting up {domain} -> port {port}")
        print("=" * 50)
        
        # Step 1: Check if port is available
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            
            if result != 0:
                print(f"⚠️  Warning: Port {port} is not responding")
                response = input(f"Continue anyway? (y/N): ")
                if response.lower() != 'y':
                    return False
        except Exception as e:
            print(f"⚠️  Could not check port {port}: {e}")
        
        # Step 2: Create Nginx config
        if not self.create_nginx_config(subdomain, port):
            return False
        
        # Step 3: Enable site
        if not self.enable_site(subdomain):
            return False
        
        # Step 4: Test Nginx config
        if not self.test_nginx_config():
            return False
        
        # Step 5: Reload Nginx
        if not self.reload_nginx():
            return False
        
        # Step 6: Install SSL
        if not skip_ssl:
            print("\n🔒 Installing SSL certificate...")
            if self.install_ssl(subdomain):
                print(f"🎉 Complete setup finished for https://{domain}")
            else:
                print(f"⚠️  Setup completed but SSL failed. You can retry SSL later with:")
                print(f"   sudo certbot --nginx -d {domain}")
        else:
            print(f"✅ Basic setup completed for http://{domain}")
            print(f"   To add SSL later: sudo certbot --nginx -d {domain}")
        
        return True
    
    def list_configured_sites(self):
        """List all configured AlgoFactory sites"""
        print("📋 Configured AlgoFactory Sites:")
        print("=" * 40)
        
        try:
            sites = []
            for file in os.listdir(self.nginx_sites_available):
                if file.endswith('.algofactory.in.conf'):
                    subdomain = file.replace('.algofactory.in.conf', '')
                    enabled = os.path.exists(f"{self.nginx_sites_enabled}/{file}")
                    
                    # Check SSL
                    ssl_status = "❌"
                    cert_path = f"/etc/letsencrypt/live/{subdomain}.algofactory.in"
                    if os.path.exists(cert_path):
                        ssl_status = "✅"
                    
                    # Check if enabled
                    status = "✅ Enabled" if enabled else "❌ Disabled"
                    
                    sites.append({
                        'subdomain': subdomain,
                        'domain': f"{subdomain}.algofactory.in",
                        'status': status,
                        'ssl': ssl_status
                    })
            
            if sites:
                for site in sorted(sites, key=lambda x: x['subdomain']):
                    print(f"🔹 {site['domain']}")
                    print(f"   Status: {site['status']}")
                    print(f"   SSL: {site['ssl']}")
                    print()
            else:
                print("   No AlgoFactory sites configured")
                
        except Exception as e:
            print(f"❌ Error listing sites: {e}")
    
    def bulk_setup(self, start_subdomain, end_subdomain):
        """Setup multiple subdomains in bulk"""
        start_num = int(start_subdomain)
        end_num = int(end_subdomain)
        
        print(f"🚀 Bulk setup: {start_subdomain}.algofactory.in to {end_subdomain}.algofactory.in")
        print("=" * 60)
        
        success_count = 0
        failed_domains = []
        
        for num in range(start_num, end_num + 1):
            subdomain = str(num)
            port = num  # Use subdomain number as port
            
            print(f"\n📍 Setting up {subdomain}.algofactory.in...")
            
            if self.setup_subdomain(subdomain, port, skip_ssl=True):
                success_count += 1
                print(f"✅ {subdomain}.algofactory.in setup completed")
            else:
                failed_domains.append(f"{subdomain}.algofactory.in")
                print(f"❌ {subdomain}.algofactory.in setup failed")
        
        # Summary
        print(f"\n📊 Bulk Setup Summary:")
        print("=" * 30)
        print(f"✅ Successful: {success_count}")
        print(f"❌ Failed: {len(failed_domains)}")
        
        if failed_domains:
            print(f"\nFailed domains:")
            for domain in failed_domains:
                print(f"   - {domain}")
        
        print(f"\n🔒 To install SSL for all domains:")
        print(f"   sudo python3 nginx_manager.py --bulk-ssl {start_subdomain} {end_subdomain}")
    
    def bulk_ssl_install(self, start_subdomain, end_subdomain):
        """Install SSL for multiple subdomains"""
        start_num = int(start_subdomain)
        end_num = int(end_subdomain)
        
        print(f"🔒 Bulk SSL installation: {start_subdomain} to {end_subdomain}")
        print("=" * 50)
        
        success_count = 0
        failed_domains = []
        
        for num in range(start_num, end_num + 1):
            subdomain = str(num)
            domain = f"{subdomain}.algofactory.in"
            
            print(f"\n🔒 Installing SSL for {domain}...")
            
            if self.install_ssl(subdomain):
                success_count += 1
            else:
                failed_domains.append(domain)
            
            # Small delay between SSL installations
            time.sleep(2)
        
        # Summary
        print(f"\n📊 SSL Installation Summary:")
        print("=" * 35)
        print(f"✅ Successful: {success_count}")
        print(f"❌ Failed: {len(failed_domains)}")
        
        if failed_domains:
            print(f"\nFailed SSL installations:")
            for domain in failed_domains:
                print(f"   - {domain}")

def main():
    parser = argparse.ArgumentParser(description="AlgoFactory Nginx Configuration Manager")
    parser.add_argument('--setup', nargs=2, metavar=('SUBDOMAIN', 'PORT'), 
                       help='Setup single subdomain (e.g., --setup 8013 8013)')
    parser.add_argument('--bulk', nargs=2, metavar=('START', 'END'),
                       help='Bulk setup subdomains (e.g., --bulk 1010 1020)')
    parser.add_argument('--bulk-ssl', nargs=2, metavar=('START', 'END'),
                       help='Bulk SSL installation (e.g., --bulk-ssl 1010 1020)')
    parser.add_argument('--list', action='store_true', help='List configured sites')
    parser.add_argument('--ssl', metavar='SUBDOMAIN', help='Install SSL for subdomain')
    
    args = parser.parse_args()
    
    # Check if running as root
    if os.geteuid() != 0:
        print("❌ This script must be run as root (use sudo)")
        sys.exit(1)
    
    manager = NginxManager()
    
    if args.setup:
        subdomain, port = args.setup
        manager.setup_subdomain(subdomain, int(port))
    elif args.bulk:
        start, end = args.bulk
        manager.bulk_setup(start, end)
    elif args.bulk_ssl:
        start, end = args.bulk_ssl
        manager.bulk_ssl_install(start, end)
    elif args.list:
        manager.list_configured_sites()
    elif args.ssl:
        manager.install_ssl(args.ssl)
    else:
        print("AlgoFactory Nginx Configuration Manager")
        print("=" * 40)
        print()
        print("Usage examples:")
        print("  sudo python3 nginx_manager.py --setup 8013 8013")
        print("  sudo python3 nginx_manager.py --bulk 1010 1020")
        print("  sudo python3 nginx_manager.py --bulk-ssl 1010 1020")
        print("  sudo python3 nginx_manager.py --list")
        print("  sudo python3 nginx_manager.py --ssl 8013")

if __name__ == "__main__":
    main()
