# AlgoFactory Nginx Optimization Configuration
# Place this in /etc/nginx/conf.d/algofactory-optimization.conf

# Rate Limiting Zones for AlgoFactory
limit_req_zone $binary_remote_addr zone=algofactory_api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=algofactory_login:10m rate=5r/m;
limit_req_zone $binary_remote_addr zone=algofactory_general:10m rate=20r/s;
limit_req_zone $binary_remote_addr zone=algofactory_websocket:10m rate=50r/s;

# Connection Limiting
limit_conn_zone $binary_remote_addr zone=algofactory_conn:10m;

# Upstream definitions for load balancing (if needed)
upstream algofactory_backend {
    least_conn;
    server 127.0.0.1:8010 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:8011 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:8012 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

# Cache zones
proxy_cache_path /var/cache/nginx/algofactory levels=1:2 keys_zone=algofactory_cache:10m max_size=100m inactive=60m use_temp_path=off;

# Map for WebSocket upgrade
map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
}

# Security headers map
map $sent_http_content_type $security_headers {
    ~*text/html "nosniff";
    default "";
}

# Geo-blocking (optional - uncomment and configure as needed)
# geo $blocked_country {
#     default 0;
#     # Block specific countries if needed
#     # CN 1;  # China
#     # RU 1;  # Russia
# }

# Log format for AlgoFactory
log_format algofactory_access '$remote_addr - $remote_user [$time_local] '
                              '"$request" $status $body_bytes_sent '
                              '"$http_referer" "$http_user_agent" '
                              '"$http_x_forwarded_for" '
                              'rt=$request_time uct="$upstream_connect_time" '
                              'uht="$upstream_header_time" urt="$upstream_response_time" '
                              'instance="$upstream_addr"';

# Server block template for AlgoFactory instances
# This is included by individual site configurations

# Common security headers
add_header X-Frame-Options DENY always;
add_header X-Content-Type-Options nosniff always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy strict-origin-when-cross-origin always;
add_header Permissions-Policy "camera=(), microphone=(), geolocation=(), payment=(), usb=(), screen-wake-lock=(), web-share=()" always;

# HSTS (only for HTTPS)
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;

# Hide server information
server_tokens off;

# Common location blocks for AlgoFactory
location /health {
    access_log off;
    return 200 "healthy\n";
    add_header Content-Type text/plain;
}

location /nginx-status {
    stub_status on;
    access_log off;
    allow 127.0.0.1;
    deny all;
}

# Block common attack patterns
location ~* \.(php|asp|aspx|jsp)$ {
    deny all;
    access_log off;
    log_not_found off;
}

location ~ /\. {
    deny all;
    access_log off;
    log_not_found off;
}

location ~ ~$ {
    deny all;
    access_log off;
    log_not_found off;
}

# Block SQL injection attempts
location ~* (union.*select|select.*union|select.*from|insert.*into|delete.*from|drop.*table) {
    deny all;
    access_log off;
    log_not_found off;
}

# Block common bot patterns
location ~* (bot|crawler|spider|scraper) {
    limit_req zone=algofactory_general burst=5 nodelay;
}

# Favicon handling
location = /favicon.ico {
    log_not_found off;
    access_log off;
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# Robots.txt
location = /robots.txt {
    log_not_found off;
    access_log off;
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# Static files optimization
location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    add_header Vary Accept-Encoding;
    
    # Enable compression
    gzip_static on;
    
    # Security headers for static files
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
}

# API endpoints rate limiting
location /api/ {
    limit_req zone=algofactory_api burst=20 nodelay;
    limit_conn algofactory_conn 10;
    
    # API specific headers
    add_header X-API-Version "1.0";
    add_header X-RateLimit-Limit "10";
    add_header X-RateLimit-Remaining $limit_req_status;
}

# Authentication endpoints
location ~ ^/(auth|login|logout) {
    limit_req zone=algofactory_login burst=5 nodelay;
    limit_conn algofactory_conn 5;
}

# WebSocket endpoints
location /socket.io/ {
    limit_req zone=algofactory_websocket burst=100 nodelay;
    
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection $connection_upgrade;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # WebSocket specific timeouts
    proxy_read_timeout 86400;
    proxy_send_timeout 86400;
    proxy_connect_timeout 60;
}

# Error pages
error_page 404 /404.html;
error_page 500 502 503 504 /50x.html;

location = /404.html {
    root /var/www/html;
    internal;
}

location = /50x.html {
    root /var/www/html;
    internal;
}

# Monitoring and metrics
location /metrics {
    access_log off;
    allow 127.0.0.1;
    deny all;
    return 200 "# AlgoFactory Nginx Metrics\n";
    add_header Content-Type text/plain;
}
