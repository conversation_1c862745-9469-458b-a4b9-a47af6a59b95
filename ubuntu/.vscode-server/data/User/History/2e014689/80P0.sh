#!/bin/bash

# Quick Instance Manager for Low Memory Environment
# Simplified version for 1GB RAM server

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

BASE_DIR="/home/<USER>/algofactory-multi"
TEMPLATE_DIR="$BASE_DIR/template"
INSTANCES_DIR="$BASE_DIR/instances"
SHARED_VENV="/home/<USER>/shared-venv"

log() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅${NC} $1"
}

error() {
    echo -e "${RED}❌${NC} $1" >&2
}

# Create template if not exists
create_template() {
    if [ ! -d "$TEMPLATE_DIR" ]; then
        log "Creating template..."
        mkdir -p "$BASE_DIR"
        cp -r /home/<USER>/myproject/algofactory "$TEMPLATE_DIR"
        rm -rf "$TEMPLATE_DIR"/{logs,db,app.pid,__pycache__}
        success "Template created"
    fi
}

# Create instance
create_instance() {
    local instance_id=$1
    local instance_dir="$INSTANCES_DIR/algofactory-$instance_id"
    
    if [ -d "$instance_dir" ]; then
        error "Instance $instance_id already exists"
        return 1
    fi
    
    create_template
    
    log "Creating instance $instance_id..."
    mkdir -p "$instance_dir"
    cp -r "$TEMPLATE_DIR"/* "$instance_dir/"
    
    # Create directories
    mkdir -p "$instance_dir"/{db,logs,tmp}
    
    # Create .env file with exact same format as original
    cat > "$instance_dir/.env" << EOF
# Instance Configuration
INSTANCE_ID = '$instance_id'

# Broker Configuration
BROKER_API_KEY = 'MZA0cLWq'
BROKER_API_SECRET = 'XIA6RJ3HPG4ZRKKYJLIZ6ROKAM'

# Market Data Configuration (Optional and Required only for XTS API Supported Brokers)

BROKER_API_KEY_MARKET = 'YOUR_BROKER_MARKET_API_KEY'
BROKER_API_SECRET_MARKET = 'YOUR_BROKER_MARKET_API_SECRET'

REDIRECT_URL = 'https://$instance_id.algofactory.in/angel/callback'  # Change if different

# Valid Brokers Configuration
VALID_BROKERS = 'fivepaisa,fivepaisaxts,aliceblue,angel,compositedge,dhan,dhan_sandbox,firstock,flattrade,fyers,groww,iifl,kotak,jainam,jainampro,paytm,pocketful,shoonya,tradejini,upstox,wisdom,zebu,zerodha'

# Security Configuration
# IMPORTANT: Generate new random values for both keys during setup!

# AlgoFactory Application Key
APP_KEY = '3daa0403ce2501ee7432b75bf100048e3cf510d63d2754f952e93d88bf07ea84'

# Security Pepper - Used for hashing/encryption of sensitive data
# This is used for:
# 1. API key hashing
# 2. User password hashing
# 3. Broker auth token encryption
# Generate a new random string during setup using: python -c "import secrets; print(secrets.token_hex(32))"
API_KEY_PEPPER = 'a25d94718479b170c16278e321ea6c989358bf499a658fd20c90033cef8ce772'

# AlgoFactory Database Configuration
DATABASE_URL = 'sqlite:///db/algofactory-$instance_id.db'

# AlgoFactory Ngrok Configuration
NGROK_ALLOW = 'FALSE'

# AlgoFactory Hosted Server (Custom Domain Name) or Ngrok Domain Configuration
# Change to your custom domain or Ngrok domain
HOST_SERVER = 'https://$instance_id.algofactory.in'

# AlgoFactory Flask App Host and Port Configuration
# For 0.0.0.0 (accessible from other devices on the network)
# Flask Environment - development or production
FLASK_HOST_IP='0.0.0.0'
FLASK_PORT='$instance_id'
FLASK_DEBUG='False'
FLASK_ENV='production'

# WebSocket Configuration
WEBSOCKET_HOST='localhost'
WEBSOCKET_PORT='$((12000 + instance_id))'
WEBSOCKET_URL='ws://localhost:$((12000 + instance_id))'

# ZeroMQ Configuration
ZMQ_HOST='localhost'
ZMQ_PORT='$((15000 + instance_id))'

# AlgoFactory Rate Limit Settings
LOGIN_RATE_LIMIT_MIN = "5 per minute"
LOGIN_RATE_LIMIT_HOUR = "25 per hour"
API_RATE_LIMIT="10 per second"

# AlgoFactory API Configuration

# Required to give 0.5 second to 1 second delay between multi-legged option strategies
# Single legged orders are not affected by this setting.
SMART_ORDER_DELAY = '0.5'

# Session Expiry Time (24-hour format, IST)
# All user sessions will automatically expire at this time daily
SESSION_EXPIRY_TIME = '03:00'

# AlgoFactory CORS (Cross-Origin Resource Sharing) Configuration
# Set to TRUE to enable CORS support, FALSE to disable
CORS_ENABLED = 'TRUE'

# Comma-separated list of allowed origins (domains)
# Example: http://localhost:3000,https://example.com
# Use '*' to allow all origins (not recommended for production)
CORS_ALLOWED_ORIGINS = 'https://$instance_id.algofactory.in'

# Comma-separated list of allowed HTTP methods
# Default: GET,POST
CORS_ALLOWED_METHODS = 'GET,POST,DELETE,PUT,PATCH'

# Comma-separated list of allowed headers
# Default Flask-CORS values will be used if not specified
CORS_ALLOWED_HEADERS = 'Content-Type,Authorization,X-Requested-With'

# Comma-separated list of headers exposed to the browser
CORS_EXPOSED_HEADERS = ''

# Whether to allow credentials (cookies, authorization headers)
# Set to TRUE only if you need to support credentials
CORS_ALLOW_CREDENTIALS = 'FALSE'

# Max age (in seconds) for browser to cache preflight requests
# Default: 86400 (24 hours)
CORS_MAX_AGE = '86400'

# AlgoFactory Content Security Policy (CSP) Configuration
# Set to TRUE to enable CSP, FALSE to disable
CSP_ENABLED = 'TRUE'

# Set to TRUE to use Content-Security-Policy-Report-Only mode (testing without blocking)
# This will report violations but not block content
CSP_REPORT_ONLY = 'FALSE'

# Default source directive - restricts all resource types by default
CSP_DEFAULT_SRC = "'self'"

# Script source directive - controls where scripts can be loaded from
# Includes Socket.IO CDN which is required by the application
# 'unsafe-inline' is needed for Socket.IO to function properly
# Cloudflare Insights is used for analytics
CSP_SCRIPT_SRC = "'self' 'unsafe-inline' https://cdn.socket.io https://static.cloudflareinsights.com"

# Style source directive - controls where styles can be loaded from
# 'unsafe-inline' is needed for some inline styles in the application
CSP_STYLE_SRC = "'self' 'unsafe-inline'"

# Image source directive - controls where images can be loaded from
# 'data:' allows base64 encoded images
CSP_IMG_SRC = "'self' data:"

# Connect source directive - controls what network connections are allowed
# Includes WebSocket connections needed for real-time updates
CSP_CONNECT_SRC = "'self' wss: ws:"

# Font source directive - controls where fonts can be loaded from
CSP_FONT_SRC = "'self'"

# Object source directive - controls where plugins can be loaded from
# 'none' disables all object, embed, and applet elements
CSP_OBJECT_SRC = "'none'"

# Media source directive - controls where audio and video can be loaded from
# Allows audio alerts from your domain and potentially CDN sources in the future
CSP_MEDIA_SRC = "'self' data: https://*.amazonaws.com https://*.cloudfront.net"

# Frame source directive - controls where iframes can be loaded from
# If you integrate with TradingView or other platforms, you may need to add their domains
CSP_FRAME_SRC = "'self'"

# Form action directive - restricts where forms can be submitted to
CSP_FORM_ACTION = "'self'"

# Frame ancestors directive - controls which sites can embed your site in frames
# This helps prevent clickjacking attacks
CSP_FRAME_ANCESTORS = "'self'"

# Base URI directive - restricts what base URIs can be used
CSP_BASE_URI = "'self'"

# Set to TRUE to upgrade insecure (HTTP) requests to HTTPS
# Recommended for production environments
CSP_UPGRADE_INSECURE_REQUESTS = 'FALSE'

# URI to report CSP violations to (optional)
# Example: /csp-report
CSP_REPORT_URI = ''

# CSRF (Cross-Site Request Forgery) Protection Configuration
# Set to TRUE to enable CSRF protection, FALSE to disable
CSRF_ENABLED = 'TRUE'

# CSRF Token Time Limit (in seconds)
# Leave empty for no time limit (tokens valid for entire session)
# Example: 3600 = 1 hour, 86400 = 24 hours
CSRF_TIME_LIMIT = ''
EOF
    
    # Create lightweight start script
    cat > "$instance_dir/start_light.sh" << 'EOF'
#!/bin/bash
set -e

INSTANCE_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INSTANCE_ID=$(basename "$INSTANCE_DIR" | cut -d'-' -f2)
SHARED_VENV="/home/<USER>/shared-venv"
PID_FILE="$INSTANCE_DIR/app.pid"

# Load environment variables (handle spaces around =)
set -a
eval "$(grep -v '^#' "$INSTANCE_DIR/.env" | sed 's/ *= */=/' | sed 's/^/export /')"
set +a

source "$SHARED_VENV/bin/activate"

case "${1:-start}" in
    "start")
        if [ -f "$PID_FILE" ] && ps -p "$(cat "$PID_FILE")" > /dev/null 2>&1; then
            echo "Instance $INSTANCE_ID already running"
            exit 0
        fi
        
        cd "$INSTANCE_DIR"
        # Use Flask development server for memory-constrained environment
        nohup "$SHARED_VENV/bin/python" app.py > "logs/app.log" 2>&1 &
        
        echo $! > "$PID_FILE"
        echo "Instance $INSTANCE_ID started on port $FLASK_PORT"
        ;;
    "stop")
        if [ -f "$PID_FILE" ]; then
            kill "$(cat "$PID_FILE")" 2>/dev/null || true
            rm -f "$PID_FILE"
            echo "Instance $INSTANCE_ID stopped"
        fi
        ;;
    "status")
        if [ -f "$PID_FILE" ] && ps -p "$(cat "$PID_FILE")" > /dev/null 2>&1; then
            echo "Instance $INSTANCE_ID: RUNNING (PID: $(cat "$PID_FILE"))"
        else
            echo "Instance $INSTANCE_ID: STOPPED"
        fi
        ;;
esac
EOF
    
    chmod +x "$instance_dir/start_light.sh"
    success "Instance $instance_id created at port $instance_id"
}

# Start instance
start_instance() {
    local instance_id=$1
    local instance_dir="$INSTANCES_DIR/algofactory-$instance_id"
    
    if [ ! -d "$instance_dir" ]; then
        error "Instance $instance_id does not exist"
        return 1
    fi
    
    cd "$instance_dir"
    ./start_light.sh start
}

# Stop instance
stop_instance() {
    local instance_id=$1
    local instance_dir="$INSTANCES_DIR/algofactory-$instance_id"
    
    if [ ! -d "$instance_dir" ]; then
        error "Instance $instance_id does not exist"
        return 1
    fi
    
    cd "$instance_dir"
    ./start_light.sh stop
}

# List instances
list_instances() {
    echo "AlgoFactory Instances:"
    echo "====================="
    
    if [ ! -d "$INSTANCES_DIR" ]; then
        echo "No instances found"
        return 0
    fi
    
    for dir in "$INSTANCES_DIR"/algofactory-*; do
        if [ -d "$dir" ]; then
            instance_id=$(basename "$dir" | cut -d'-' -f2)
            cd "$dir"
            status=$(./start_light.sh status | cut -d':' -f2 | xargs)
            echo "Instance $instance_id: $status | URL: https://$instance_id.algofactory.in"
        fi
    done
}

# Create Nginx config for instance
create_nginx_config() {
    local instance_id=$1

    # First create HTTP-only config for SSL certificate generation
    cat > "/tmp/$instance_id.algofactory.in.conf" << EOF
server {
    listen 80;
    server_name $instance_id.algofactory.in;

    location / {
        proxy_pass http://127.0.0.1:$instance_id;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;

        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
EOF

    sudo mv "/tmp/$instance_id.algofactory.in.conf" "/etc/nginx/sites-available/"
    sudo ln -sf "/etc/nginx/sites-available/$instance_id.algofactory.in.conf" "/etc/nginx/sites-enabled/"

    # Test and reload nginx
    if sudo nginx -t; then
        sudo systemctl reload nginx
        success "Nginx config created for $instance_id.algofactory.in"
        log "Now run: sudo certbot --nginx -d $instance_id.algofactory.in"
    else
        error "Nginx configuration test failed"
        return 1
    fi
}

# Main function
main() {
    case "${1:-help}" in
        "create")
            if [ -z "$2" ]; then
                error "Usage: $0 create INSTANCE_ID"
                exit 1
            fi
            create_instance "$2"
            ;;
        "start")
            if [ -z "$2" ]; then
                error "Usage: $0 start INSTANCE_ID"
                exit 1
            fi
            start_instance "$2"
            ;;
        "stop")
            if [ -z "$2" ]; then
                error "Usage: $0 stop INSTANCE_ID"
                exit 1
            fi
            stop_instance "$2"
            ;;
        "list")
            list_instances
            ;;
        "nginx")
            if [ -z "$2" ]; then
                error "Usage: $0 nginx INSTANCE_ID"
                exit 1
            fi
            create_nginx_config "$2"
            ;;
        "help"|*)
            echo "Quick Instance Manager for AlgoFactory"
            echo ""
            echo "Usage: $0 COMMAND [INSTANCE_ID]"
            echo ""
            echo "Commands:"
            echo "  create ID  - Create new instance"
            echo "  start ID   - Start instance"
            echo "  stop ID    - Stop instance"
            echo "  list       - List all instances"
            echo "  nginx ID   - Create Nginx config for instance"
            echo ""
            echo "Examples:"
            echo "  $0 create 1010"
            echo "  $0 start 1010"
            echo "  $0 nginx 1010"
            ;;
    esac
}

main "$@"
