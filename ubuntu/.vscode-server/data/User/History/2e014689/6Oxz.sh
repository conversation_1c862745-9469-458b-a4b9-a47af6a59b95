#!/bin/bash

# Quick Instance Manager for Low Memory Environment
# Simplified version for 1GB RAM server

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

BASE_DIR="/home/<USER>/algofactory-multi"
TEMPLATE_DIR="$BASE_DIR/template"
INSTANCES_DIR="$BASE_DIR/instances"
SHARED_VENV="/home/<USER>/shared-venv"

log() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅${NC} $1"
}

error() {
    echo -e "${RED}❌${NC} $1" >&2
}

# Create template if not exists
create_template() {
    if [ ! -d "$TEMPLATE_DIR" ]; then
        log "Creating template..."
        mkdir -p "$BASE_DIR"
        cp -r /home/<USER>/myproject/algofactory "$TEMPLATE_DIR"
        rm -rf "$TEMPLATE_DIR"/{logs,db,app.pid,__pycache__}
        success "Template created"
    fi
}

# Create instance
create_instance() {
    local instance_id=$1
    local instance_dir="$INSTANCES_DIR/algofactory-$instance_id"
    
    if [ -d "$instance_dir" ]; then
        error "Instance $instance_id already exists"
        return 1
    fi
    
    create_template
    
    log "Creating instance $instance_id..."
    mkdir -p "$instance_dir"
    cp -r "$TEMPLATE_DIR"/* "$instance_dir/"
    
    # Create directories
    mkdir -p "$instance_dir"/{db,logs,tmp}
    
    # Create .env file
    cat > "$instance_dir/.env" << EOF
# Instance $instance_id Configuration
INSTANCE_ID=$instance_id
BROKER_API_KEY=MZA0cLWq
BROKER_API_SECRET=XIA6RJ3HPG4ZRKKYJLIZ6ROKAM
REDIRECT_URL=https://$instance_id.algofactory.in/angel/callback
VALID_BROKERS=fivepaisa,fivepaisaxts,aliceblue,angel,compositedge,dhan,dhan_sandbox,firstock,flattrade,fyers,groww,iifl,kotak,jainam,jainampro,paytm,pocketful,shoonya,tradejini,upstox,wisdom,zebu,zerodha
APP_KEY=3daa0403ce2501ee7432b75bf100048e3cf510d63d2754f952e93d88bf07ea84
API_KEY_PEPPER=a25d94718479b170c16278e321ea6c989358bf499a658fd20c90033cef8ce772
DATABASE_URL=sqlite:///db/algofactory-$instance_id.db
NGROK_ALLOW=FALSE
HOST_SERVER=https://$instance_id.algofactory.in
FLASK_HOST_IP=0.0.0.0
FLASK_PORT=$instance_id
FLASK_DEBUG=False
FLASK_ENV=production
WEBSOCKET_HOST=localhost
WEBSOCKET_PORT=$((12000 + instance_id))
WEBSOCKET_URL=ws://localhost:$((12000 + instance_id))
ZMQ_HOST=localhost
ZMQ_PORT=$((15000 + instance_id))
LOGIN_RATE_LIMIT_MIN="5 per minute"
LOGIN_RATE_LIMIT_HOUR="25 per hour"
API_RATE_LIMIT="10 per second"
SMART_ORDER_DELAY=0.5
SESSION_EXPIRY_TIME=03:00
CORS_ENABLED=TRUE
CORS_ALLOWED_ORIGINS=https://$instance_id.algofactory.in
CORS_ALLOWED_METHODS=GET,POST,DELETE,PUT,PATCH
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Requested-With
CORS_EXPOSED_HEADERS=
CORS_ALLOW_CREDENTIALS=FALSE
CORS_MAX_AGE=86400
CSP_ENABLED=TRUE
CSP_REPORT_ONLY=FALSE
CSP_DEFAULT_SRC="'self'"
CSP_SCRIPT_SRC="'self' 'unsafe-inline' https://cdn.socket.io https://static.cloudflareinsights.com"
CSP_STYLE_SRC="'self' 'unsafe-inline'"
CSP_IMG_SRC="'self' data:"
CSP_CONNECT_SRC="'self' wss: ws:"
CSP_FONT_SRC="'self'"
CSP_OBJECT_SRC="'none'"
CSP_MEDIA_SRC="'self' data: https://*.amazonaws.com https://*.cloudfront.net"
CSP_FRAME_SRC="'self'"
CSP_FORM_ACTION="'self'"
CSP_FRAME_ANCESTORS="'self'"
CSP_BASE_URI="'self'"
CSP_UPGRADE_INSECURE_REQUESTS=FALSE
CSP_REPORT_URI=""
CSRF_ENABLED=TRUE
CSRF_TIME_LIMIT=""
EOF
    
    # Create lightweight start script
    cat > "$instance_dir/start_light.sh" << 'EOF'
#!/bin/bash
set -e

INSTANCE_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INSTANCE_ID=$(basename "$INSTANCE_DIR" | cut -d'-' -f2)
SHARED_VENV="/home/<USER>/shared-venv"
PID_FILE="$INSTANCE_DIR/app.pid"

source "$INSTANCE_DIR/.env"
source "$SHARED_VENV/bin/activate"

case "${1:-start}" in
    "start")
        if [ -f "$PID_FILE" ] && ps -p "$(cat "$PID_FILE")" > /dev/null 2>&1; then
            echo "Instance $INSTANCE_ID already running"
            exit 0
        fi
        
        cd "$INSTANCE_DIR"
        nohup "$SHARED_VENV/bin/gunicorn" \
            --bind="0.0.0.0:$FLASK_PORT" \
            --workers=1 \
            --worker-class=eventlet \
            --worker-connections=50 \
            --max-requests=200 \
            --timeout=30 \
            --preload \
            --log-level=warning \
            --access-logfile="logs/access.log" \
            --error-logfile="logs/error.log" \
            app:app > "logs/app.log" 2>&1 &
        
        echo $! > "$PID_FILE"
        echo "Instance $INSTANCE_ID started on port $FLASK_PORT"
        ;;
    "stop")
        if [ -f "$PID_FILE" ]; then
            kill "$(cat "$PID_FILE")" 2>/dev/null || true
            rm -f "$PID_FILE"
            echo "Instance $INSTANCE_ID stopped"
        fi
        ;;
    "status")
        if [ -f "$PID_FILE" ] && ps -p "$(cat "$PID_FILE")" > /dev/null 2>&1; then
            echo "Instance $INSTANCE_ID: RUNNING (PID: $(cat "$PID_FILE"))"
        else
            echo "Instance $INSTANCE_ID: STOPPED"
        fi
        ;;
esac
EOF
    
    chmod +x "$instance_dir/start_light.sh"
    success "Instance $instance_id created at port $instance_id"
}

# Start instance
start_instance() {
    local instance_id=$1
    local instance_dir="$INSTANCES_DIR/algofactory-$instance_id"
    
    if [ ! -d "$instance_dir" ]; then
        error "Instance $instance_id does not exist"
        return 1
    fi
    
    cd "$instance_dir"
    ./start_light.sh start
}

# Stop instance
stop_instance() {
    local instance_id=$1
    local instance_dir="$INSTANCES_DIR/algofactory-$instance_id"
    
    if [ ! -d "$instance_dir" ]; then
        error "Instance $instance_id does not exist"
        return 1
    fi
    
    cd "$instance_dir"
    ./start_light.sh stop
}

# List instances
list_instances() {
    echo "AlgoFactory Instances:"
    echo "====================="
    
    if [ ! -d "$INSTANCES_DIR" ]; then
        echo "No instances found"
        return 0
    fi
    
    for dir in "$INSTANCES_DIR"/algofactory-*; do
        if [ -d "$dir" ]; then
            instance_id=$(basename "$dir" | cut -d'-' -f2)
            cd "$dir"
            status=$(./start_light.sh status | cut -d':' -f2 | xargs)
            echo "Instance $instance_id: $status | URL: https://$instance_id.algofactory.in"
        fi
    done
}

# Create Nginx config for instance
create_nginx_config() {
    local instance_id=$1
    
    cat > "/tmp/$instance_id.algofactory.in.conf" << EOF
server {
    listen 80;
    server_name $instance_id.algofactory.in;
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name $instance_id.algofactory.in;
    
    ssl_certificate /etc/letsencrypt/live/$instance_id.algofactory.in/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$instance_id.algofactory.in/privkey.pem;
    
    location / {
        proxy_pass http://127.0.0.1:$instance_id;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF
    
    sudo mv "/tmp/$instance_id.algofactory.in.conf" "/etc/nginx/sites-available/"
    sudo ln -sf "/etc/nginx/sites-available/$instance_id.algofactory.in.conf" "/etc/nginx/sites-enabled/"
    sudo nginx -t && sudo systemctl reload nginx
    
    success "Nginx config created for $instance_id.algofactory.in"
}

# Main function
main() {
    case "${1:-help}" in
        "create")
            if [ -z "$2" ]; then
                error "Usage: $0 create INSTANCE_ID"
                exit 1
            fi
            create_instance "$2"
            ;;
        "start")
            if [ -z "$2" ]; then
                error "Usage: $0 start INSTANCE_ID"
                exit 1
            fi
            start_instance "$2"
            ;;
        "stop")
            if [ -z "$2" ]; then
                error "Usage: $0 stop INSTANCE_ID"
                exit 1
            fi
            stop_instance "$2"
            ;;
        "list")
            list_instances
            ;;
        "nginx")
            if [ -z "$2" ]; then
                error "Usage: $0 nginx INSTANCE_ID"
                exit 1
            fi
            create_nginx_config "$2"
            ;;
        "help"|*)
            echo "Quick Instance Manager for AlgoFactory"
            echo ""
            echo "Usage: $0 COMMAND [INSTANCE_ID]"
            echo ""
            echo "Commands:"
            echo "  create ID  - Create new instance"
            echo "  start ID   - Start instance"
            echo "  stop ID    - Stop instance"
            echo "  list       - List all instances"
            echo "  nginx ID   - Create Nginx config for instance"
            echo ""
            echo "Examples:"
            echo "  $0 create 1010"
            echo "  $0 start 1010"
            echo "  $0 nginx 1010"
            ;;
    esac
}

main "$@"
