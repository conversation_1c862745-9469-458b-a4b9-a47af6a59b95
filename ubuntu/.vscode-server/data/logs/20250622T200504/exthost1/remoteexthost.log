2025-06-22 20:05:06.747 [info] Extension host with pid 1610 started
2025-06-22 20:05:06.747 [error] Error: EEXIST: file already exists, open '/home/<USER>/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/vscode.lock'
2025-06-22 20:05:06.747 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/vscode.lock': Could not acquire lock, checking if the file is stale.
2025-06-22 20:05:06.754 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/vscode.lock': The pid 5018 appears to be gone.
2025-06-22 20:05:06.754 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/vscode.lock': Deleting a stale lock.
2025-06-22 20:05:06.768 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/vscode.lock': Lock acquired.
2025-06-22 20:05:07.103 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-06-22 20:05:07.104 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: false, activationEvent: '*', root cause: vscode.git
2025-06-22 20:05:07.105 [info] ExtensionService#_doActivateExtension vscode.tunnel-forwarding, startup: false, activationEvent: 'onTunnel'
2025-06-22 20:05:07.291 [info] ExtensionService#_doActivateExtension vscode.git, startup: false, activationEvent: '*'
2025-06-22 20:05:07.292 [info] ExtensionService#_doActivateExtension vscode.github, startup: false, activationEvent: '*'
2025-06-22 20:05:07.782 [info] Eager extensions activated
2025-06-22 20:05:07.783 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-06-22 20:05:07.784 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-06-22 20:05:07.784 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onStartupFinished'
2025-06-22 20:05:09.446 [warning] [Decorations] CAPPING events from decorations provider vscode.git 462
2025-06-22 20:05:10.748 [warning] [Decorations] CAPPING events from decorations provider vscode.git 462
2025-06-22 20:05:15.854 [error] CodeExpectedError: cannot open vscode-userdata:c%3A%5CUsers%5Canita%5CAppData%5CRoaming%5CCode%5CUser%5Ckeybindings.json. Detail: Unable to resolve filesystem provider with relative file path 'vscode-userdata:c:\Users\<USER>\AppData\Roaming\Code\User\keybindings.json'
    at Pze.$tryOpenDocument (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/workbench.desktop.main.js:1283:8666)
2025-06-22 20:05:42.535 [info] ExtensionService#_doActivateExtension vscode.npm, startup: false, activationEvent: 'onTerminalQuickFixRequest:ms-vscode.npm-command'
2025-06-22 20:05:48.588 [info] ExtensionService#_doActivateExtension vscode.configuration-editing, startup: false, activationEvent: 'onLanguage:json'
2025-06-22 20:05:48.588 [info] ExtensionService#_doActivateExtension vscode.extension-editing, startup: false, activationEvent: 'onLanguage:json'
2025-06-22 20:05:48.588 [info] ExtensionService#_doActivateExtension vscode.json-language-features, startup: false, activationEvent: 'onLanguage:json'
2025-06-22 20:08:13.976 [warning] [Decorations] CAPPING events from decorations provider vscode.git 462
