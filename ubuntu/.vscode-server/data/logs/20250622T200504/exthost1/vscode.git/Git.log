2025-06-22 20:05:08.814 [info] [main] Log level: Info
2025-06-22 20:05:08.814 [info] [main] Validating found git in: "git"
2025-06-22 20:05:08.814 [info] [main] Using git "2.43.0" from "git"
2025-06-22 20:05:08.814 [info] [Model][doInitialScan] Initial repository scan started
2025-06-22 20:05:08.814 [info] > git rev-parse --show-toplevel [922ms]
2025-06-22 20:05:08.814 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-22 20:05:08.814 [info] > git rev-parse --show-toplevel [95ms]
2025-06-22 20:05:08.825 [info] > git rev-parse --git-dir --git-common-dir [6ms]
2025-06-22 20:05:08.850 [info] [Model][openRepository] Opened repository (path): /home/<USER>/myproject/algofactory
2025-06-22 20:05:08.850 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/myproject/algofactory
2025-06-22 20:05:08.874 [info] > git rev-parse --show-toplevel [10ms]
2025-06-22 20:05:08.874 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-22 20:05:08.879 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-22 20:05:08.879 [info] > git config --get commit.template [17ms]
2025-06-22 20:05:08.903 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [19ms]
2025-06-22 20:05:08.907 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 20:05:08.911 [info] > git rev-parse refs/remotes/origin/main [4ms]
2025-06-22 20:05:08.941 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [18ms]
2025-06-22 20:05:09.404 [info] > git show --textconv :nginx-algofactory.conf [10ms]
2025-06-22 20:05:09.412 [info] > git ls-files --stage -- nginx-algofactory.conf [12ms]
2025-06-22 20:05:09.420 [info] > git status -z -uall [502ms]
2025-06-22 20:05:09.420 [info] > git hash-object -t tree /dev/null [9ms]
2025-06-22 20:05:09.420 [warning] [GitFileSystemProvider][readFile] File not found - git:/home/<USER>/myproject/algofactory/nginx-algofactory.conf.git?%7B%22path%22%3A%22%2Fhome%2Fubuntu%2Fmyproject%2Falgofactory%2Fnginx-algofactory.conf%22%2C%22ref%22%3A%22%22%7D
2025-06-22 20:05:09.422 [info] > git hash-object -t tree /dev/null [5ms]
2025-06-22 20:05:09.422 [warning] [GitFileSystemProvider][stat] File not found - git:/home/<USER>/myproject/algofactory/nginx-algofactory.conf.git?%7B%22path%22%3A%22%2Fhome%2Fubuntu%2Fmyproject%2Falgofactory%2Fnginx-algofactory.conf%22%2C%22ref%22%3A%22%22%7D
2025-06-22 20:05:09.528 [info] > git check-ignore -v -z --stdin [9ms]
2025-06-22 20:05:09.529 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [20ms]
2025-06-22 20:05:09.967 [info] > git config --get --local branch.main.vscode-merge-base [435ms]
2025-06-22 20:05:09.977 [info] > git config --get commit.template [449ms]
2025-06-22 20:05:09.994 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [18ms]
2025-06-22 20:05:10.088 [info] > git merge-base refs/heads/main refs/remotes/origin/main [85ms]
2025-06-22 20:05:10.148 [info] > git diff --name-status -z --diff-filter=ADMR 89ac1fedef89963fb80f591849bbcbe1dc91b13e...refs/remotes/origin/main [52ms]
2025-06-22 20:05:10.157 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [83ms]
2025-06-22 20:05:10.468 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 20:05:10.531 [info] > git rev-parse refs/remotes/origin/main [63ms]
2025-06-22 20:05:10.726 [info] > git status -z -uall [190ms]
2025-06-22 20:05:10.732 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [189ms]
2025-06-22 20:05:31.527 [info] > git check-ignore -v -z --stdin [9ms]
2025-06-22 20:05:32.123 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-22 20:08:13.552 [info] > git config --get commit.template [5ms]
2025-06-22 20:08:13.562 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [12ms]
2025-06-22 20:08:13.565 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 20:08:13.567 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-22 20:08:13.580 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-22 20:08:13.969 [info] > git status -z -uall [398ms]
2025-06-22 20:08:31.784 [info] > git config --get commit.template [8ms]
2025-06-22 20:08:31.785 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-22 20:08:31.792 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 20:08:31.794 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-22 20:08:31.815 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-22 20:08:31.864 [info] > git status -z -uall [64ms]
2025-06-22 20:08:36.913 [info] > git config --get commit.template [8ms]
2025-06-22 20:08:36.929 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [17ms]
2025-06-22 20:08:36.934 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 20:08:36.934 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-22 20:08:36.948 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-22 20:08:37.421 [info] > git status -z -uall [483ms]
2025-06-22 20:08:42.449 [info] > git config --get commit.template [7ms]
2025-06-22 20:08:42.463 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [14ms]
2025-06-22 20:08:42.467 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 20:08:42.469 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-22 20:08:42.488 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-22 20:08:43.009 [info] > git status -z -uall [536ms]
2025-06-22 20:08:49.322 [info] > git config --get commit.template [6ms]
2025-06-22 20:08:49.323 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-22 20:08:49.328 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 20:08:49.329 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 20:08:49.343 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-22 20:08:49.809 [info] > git status -z -uall [476ms]
2025-06-22 20:09:01.520 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-22 20:09:01.520 [info] > git config --get commit.template [13ms]
2025-06-22 20:09:01.524 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 20:09:01.526 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-22 20:09:01.539 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-22 20:09:01.979 [info] > git status -z -uall [448ms]
2025-06-22 20:09:52.441 [info] > git config --get commit.template [14ms]
2025-06-22 20:09:52.441 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-22 20:09:52.444 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 20:09:52.446 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 20:09:52.460 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-22 20:09:52.554 [info] > git status -z -uall [104ms]
2025-06-22 20:10:00.448 [info] > git config --get commit.template [2ms]
2025-06-22 20:10:00.456 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-22 20:10:00.459 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 20:10:00.460 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 20:10:00.473 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-22 20:10:00.498 [info] > git status -z -uall [36ms]
2025-06-22 20:11:29.606 [info] > git ls-files --stage -- start.sh [25ms]
2025-06-22 20:11:29.621 [info] > git cat-file -s 6fec8e8c92ff7dfbd30745f7218074badabc077b [10ms]
2025-06-22 20:11:29.622 [info] > git show --textconv :start.sh [46ms]
2025-06-22 20:11:30.051 [info] > git check-ignore -v -z --stdin [4ms]
