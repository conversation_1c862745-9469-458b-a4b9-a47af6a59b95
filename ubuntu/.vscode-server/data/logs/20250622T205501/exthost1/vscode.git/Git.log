2025-06-22 20:55:05.174 [info] [main] Log level: Info
2025-06-22 20:55:05.174 [info] [main] Validating found git in: "git"
2025-06-22 20:55:05.174 [info] [main] Using git "2.43.0" from "git"
2025-06-22 20:55:05.174 [info] [Model][doInitialScan] Initial repository scan started
2025-06-22 20:55:05.174 [info] > git rev-parse --show-toplevel [760ms]
2025-06-22 20:55:05.174 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-22 20:55:05.174 [info] > git rev-parse --show-toplevel [8ms]
2025-06-22 20:55:05.174 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-22 20:55:05.174 [info] [Model][doInitialScan] Initial repository scan completed - repositories (0), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-22 21:10:01.923 [info] > git rev-parse --show-toplevel [113ms]
2025-06-22 21:10:01.989 [info] > git rev-parse --git-dir --git-common-dir [23ms]
2025-06-22 21:10:02.055 [info] [Model][openRepository] Opened repository (path): /home/<USER>/myproject/algofactory
2025-06-22 21:10:02.059 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/myproject/algofactory
2025-06-22 21:10:02.125 [info] > git config --get commit.template [32ms]
2025-06-22 21:10:02.200 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [79ms]
2025-06-22 21:10:02.212 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 21:10:02.212 [info] > git rev-parse refs/remotes/origin/main [4ms]
2025-06-22 21:10:02.231 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-22 21:10:02.747 [info] > git status -z -uall [527ms]
2025-06-22 21:10:02.821 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [13ms]
2025-06-22 21:10:02.826 [info] > git config --get commit.template [9ms]
2025-06-22 21:10:02.831 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-22 21:10:02.831 [info] > git config --get --local branch.main.vscode-merge-base [6ms]
2025-06-22 21:10:02.839 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 21:10:02.840 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [5ms]
2025-06-22 21:10:02.847 [info] > git rev-parse refs/remotes/origin/main [8ms]
2025-06-22 21:10:02.977 [info] > git status -z -uall [126ms]
2025-06-22 21:10:02.977 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [118ms]
2025-06-22 21:10:03.004 [info] > git merge-base refs/heads/main refs/remotes/origin/main [159ms]
2025-06-22 21:10:03.063 [info] > git merge-base refs/heads/main refs/remotes/origin/main [63ms]
2025-06-22 21:10:03.086 [info] > git diff --name-status -z --diff-filter=ADMR 89ac1fedef89963fb80f591849bbcbe1dc91b13e...refs/remotes/origin/main [12ms]
2025-06-22 21:10:03.087 [info] > git diff --name-status -z --diff-filter=ADMR 89ac1fedef89963fb80f591849bbcbe1dc91b13e...refs/remotes/origin/main [72ms]
2025-06-22 21:10:03.778 [info] > git check-ignore -v -z --stdin [3ms]
2025-06-22 21:10:07.108 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-22 21:10:11.567 [info] > git config --get commit.template [0ms]
2025-06-22 21:10:11.572 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-22 21:10:11.575 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 21:10:11.576 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 21:10:11.589 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-22 21:10:11.615 [info] > git status -z -uall [35ms]
2025-06-22 21:10:15.555 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-22 21:10:53.409 [info] > git config --get commit.template [4ms]
2025-06-22 21:10:53.417 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-06-22 21:10:53.420 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 21:10:53.421 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 21:10:53.436 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-22 21:10:53.944 [info] > git status -z -uall [519ms]
2025-06-22 21:11:13.358 [info] > git rev-parse --show-toplevel [207ms]
2025-06-22 21:11:13.368 [info] > git rev-parse --git-dir --git-common-dir [2ms]
2025-06-22 21:11:13.375 [info] [Model][openRepository] Opened repository (path): /home/<USER>/algofactory-multi/template
2025-06-22 21:11:13.376 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/algofactory-multi/template
2025-06-22 21:11:13.464 [info] > git config --get commit.template [84ms]
2025-06-22 21:11:13.481 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [12ms]
2025-06-22 21:11:13.486 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/template/.git/refs/remotes/origin/main'
2025-06-22 21:11:13.486 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 21:11:13.507 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [12ms]
2025-06-22 21:11:13.560 [info] > git status -z -uall [70ms]
2025-06-22 21:11:13.595 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [11ms]
2025-06-22 21:11:13.600 [info] > git config --get --local branch.main.vscode-merge-base [1ms]
2025-06-22 21:11:13.605 [info] > git config --get commit.template [14ms]
2025-06-22 21:11:13.669 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [64ms]
2025-06-22 21:11:13.744 [info] > git merge-base refs/heads/main refs/remotes/origin/main [69ms]
2025-06-22 21:11:13.751 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [12ms]
2025-06-22 21:11:13.756 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/template/.git/refs/remotes/origin/main'
2025-06-22 21:11:13.756 [info] > git diff --name-status -z --diff-filter=ADMR 89ac1fedef89963fb80f591849bbcbe1dc91b13e...refs/remotes/origin/main [9ms]
2025-06-22 21:11:13.759 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-06-22 21:11:13.776 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-22 21:11:13.888 [info] > git status -z -uall [126ms]
2025-06-22 21:11:13.978 [info] > git check-ignore -v -z --stdin [9ms]
2025-06-22 21:11:15.654 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-22 21:11:20.247 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-22 21:11:28.068 [info] > git check-ignore -v -z --stdin [9ms]
2025-06-22 21:11:42.279 [info] > git rev-parse --show-toplevel [26ms]
2025-06-22 21:11:42.287 [info] > git rev-parse --git-dir --git-common-dir [1ms]
2025-06-22 21:11:42.293 [info] [Model][openRepository] Opened repository (path): /home/<USER>/algofactory-multi/instances/algofactory-1010
2025-06-22 21:11:42.293 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/algofactory-multi/instances/algofactory-1010
2025-06-22 21:11:42.312 [info] > git config --get commit.template [11ms]
2025-06-22 21:11:42.318 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-22 21:11:42.326 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/instances/algofactory-1010/.git/refs/remotes/origin/main'
2025-06-22 21:11:42.331 [info] > git rev-parse refs/remotes/origin/main [5ms]
2025-06-22 21:11:42.351 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-22 21:11:42.383 [info] > git status -z -uall [48ms]
2025-06-22 21:11:42.528 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [102ms]
2025-06-22 21:11:42.536 [info] > git config --get commit.template [8ms]
2025-06-22 21:11:42.537 [info] > git config --get --local branch.main.vscode-merge-base [4ms]
2025-06-22 21:11:42.549 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-22 21:11:42.549 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [7ms]
2025-06-22 21:11:42.558 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/instances/algofactory-1010/.git/refs/remotes/origin/main'
2025-06-22 21:11:42.558 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-22 21:11:42.567 [info] > git merge-base refs/heads/main refs/remotes/origin/main [14ms]
2025-06-22 21:11:42.575 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-22 21:11:42.580 [info] > git diff --name-status -z --diff-filter=ADMR 89ac1fedef89963fb80f591849bbcbe1dc91b13e...refs/remotes/origin/main [6ms]
2025-06-22 21:11:42.604 [info] > git status -z -uall [42ms]
2025-06-22 21:11:42.840 [info] > git check-ignore -v -z --stdin [7ms]
2025-06-22 21:11:42.840 [info] > git check-ignore -v -z --stdin [3ms]
2025-06-22 21:12:07.597 [info] > git check-ignore -v -z --stdin [6ms]
2025-06-22 21:12:15.509 [info] > git rev-parse --show-toplevel [5ms]
2025-06-22 21:12:15.522 [info] > git rev-parse --git-dir --git-common-dir [1ms]
2025-06-22 21:12:15.529 [info] [Model][openRepository] Opened repository (path): /home/<USER>/algofactory-multi/instances/algofactory-1011
2025-06-22 21:12:15.529 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/algofactory-multi/instances/algofactory-1011
2025-06-22 21:12:15.544 [info] > git config --get commit.template [1ms]
2025-06-22 21:12:15.559 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [11ms]
2025-06-22 21:12:15.561 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/instances/algofactory-1011/.git/refs/remotes/origin/main'
2025-06-22 21:12:15.564 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-06-22 21:12:15.589 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [15ms]
2025-06-22 21:12:15.649 [info] > git status -z -uall [80ms]
2025-06-22 21:12:15.684 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [13ms]
2025-06-22 21:12:15.691 [info] > git config --get commit.template [7ms]
2025-06-22 21:12:15.692 [info] > git config --get --local branch.main.vscode-merge-base [4ms]
2025-06-22 21:12:15.700 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [5ms]
2025-06-22 21:12:15.711 [info] > git merge-base refs/heads/main refs/remotes/origin/main [7ms]
2025-06-22 21:12:15.718 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [19ms]
2025-06-22 21:12:15.726 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/instances/algofactory-1011/.git/refs/remotes/origin/main'
2025-06-22 21:12:15.728 [info] > git diff --name-status -z --diff-filter=ADMR 89ac1fedef89963fb80f591849bbcbe1dc91b13e...refs/remotes/origin/main [10ms]
2025-06-22 21:12:15.731 [info] > git rev-parse refs/remotes/origin/main [4ms]
2025-06-22 21:12:15.820 [info] > git status -z -uall [85ms]
2025-06-22 21:12:15.821 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [79ms]
2025-06-22 21:12:16.089 [info] > git check-ignore -v -z --stdin [4ms]
2025-06-22 21:12:16.091 [info] > git check-ignore -v -z --stdin [10ms]
2025-06-22 21:12:47.128 [info] > git check-ignore -v -z --stdin [45ms]
2025-06-22 21:12:47.129 [info] > git check-ignore -v -z --stdin [43ms]
2025-06-22 21:12:48.152 [info] > git check-ignore -v -z --stdin [3ms]
2025-06-22 21:12:55.248 [info] > git check-ignore -v -z --stdin [67ms]
2025-06-22 21:12:56.722 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-22 21:13:13.228 [info] > git config --get commit.template [6ms]
2025-06-22 21:13:13.243 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [22ms]
2025-06-22 21:13:13.247 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 21:13:13.249 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-22 21:13:13.264 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-22 21:13:13.788 [info] > git status -z -uall [536ms]
2025-06-22 21:13:25.374 [info] > git config --get commit.template [42ms]
2025-06-22 21:13:25.388 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [51ms]
2025-06-22 21:13:25.392 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 21:13:25.396 [info] > git rev-parse refs/remotes/origin/main [4ms]
2025-06-22 21:13:25.470 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [64ms]
2025-06-22 21:13:25.882 [info] > git status -z -uall [481ms]
2025-06-22 21:13:39.506 [info] > git config --get commit.template [30ms]
2025-06-22 21:13:39.519 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [26ms]
2025-06-22 21:13:39.522 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 21:13:39.526 [info] > git rev-parse refs/remotes/origin/main [4ms]
2025-06-22 21:13:39.543 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-06-22 21:13:40.071 [info] > git status -z -uall [542ms]
2025-06-22 21:13:44.135 [warning] [Git][getHEAD] Failed to parse HEAD file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/instances/algofactory-1010/.git/HEAD'
2025-06-22 21:13:44.145 [warning] [Git][getRemotes] Error: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/instances/algofactory-1010/.git/config'
2025-06-22 21:13:44.156 [warning] [Git][getHEAD] Failed to parse HEAD file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/instances/algofactory-1010/.git/HEAD'
2025-06-22 21:13:44.161 [warning] [Git][getRemotes] Error: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/instances/algofactory-1010/.git/config'
2025-06-22 21:13:44.791 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-22 21:13:45.092 [info] > git config --get commit.template [1ms]
2025-06-22 21:13:45.097 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-22 21:13:45.101 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 21:13:45.102 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 21:13:45.114 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-22 21:13:45.141 [info] > git status -z -uall [36ms]
2025-06-22 21:13:47.960 [info] > git check-ignore -v -z --stdin [6ms]
2025-06-22 21:13:50.468 [info] > git check-ignore -v -z --stdin [6ms]
2025-06-22 21:13:55.239 [warning] [Git][getHEAD] Failed to parse HEAD file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/instances/algofactory-1011/.git/HEAD'
2025-06-22 21:13:55.245 [warning] [Git][getRemotes] Error: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/instances/algofactory-1011/.git/config'
2025-06-22 21:13:55.252 [warning] [Git][getHEAD] Failed to parse HEAD file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/instances/algofactory-1011/.git/HEAD'
2025-06-22 21:13:55.258 [warning] [Git][getRemotes] Error: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/instances/algofactory-1011/.git/config'
2025-06-22 21:13:55.899 [info] > git check-ignore -v -z --stdin [6ms]
2025-06-22 21:13:55.899 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-22 21:14:02.295 [info] > git rev-parse --show-toplevel [2ms]
2025-06-22 21:14:02.300 [info] > git rev-parse --git-dir --git-common-dir [1ms]
2025-06-22 21:14:02.304 [info] [Model][openRepository] Opened repository (path): /home/<USER>/algofactory-multi/instances/algofactory-1010
2025-06-22 21:14:02.304 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/algofactory-multi/instances/algofactory-1010
2025-06-22 21:14:02.309 [info] > git config --get commit.template [1ms]
2025-06-22 21:14:02.316 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-22 21:14:02.319 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/instances/algofactory-1010/.git/refs/remotes/origin/main'
2025-06-22 21:14:02.320 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 21:14:02.332 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-22 21:14:02.437 [info] > git status -z -uall [114ms]
2025-06-22 21:14:02.469 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-22 21:14:02.480 [info] > git config --get --local branch.main.vscode-merge-base [8ms]
2025-06-22 21:14:02.484 [info] > git config --get commit.template [17ms]
2025-06-22 21:14:02.495 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-22 21:14:02.495 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [11ms]
2025-06-22 21:14:02.503 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/instances/algofactory-1010/.git/refs/remotes/origin/main'
2025-06-22 21:14:02.503 [info] > git merge-base refs/heads/main refs/remotes/origin/main [4ms]
2025-06-22 21:14:02.511 [info] > git rev-parse refs/remotes/origin/main [7ms]
2025-06-22 21:14:02.524 [info] > git diff --name-status -z --diff-filter=ADMR 89ac1fedef89963fb80f591849bbcbe1dc91b13e...refs/remotes/origin/main [16ms]
2025-06-22 21:14:02.530 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-22 21:14:02.601 [info] > git status -z -uall [87ms]
2025-06-22 21:14:02.858 [info] > git check-ignore -v -z --stdin [6ms]
2025-06-22 21:14:02.858 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-22 21:14:13.973 [info] > git config --get commit.template [5ms]
2025-06-22 21:14:13.976 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-22 21:14:13.982 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 21:14:13.982 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-22 21:14:14.006 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [12ms]
2025-06-22 21:14:14.036 [info] > git status -z -uall [50ms]
2025-06-22 21:14:16.616 [info] > git show --textconv :app.pid [7ms]
2025-06-22 21:14:16.624 [info] > git ls-files --stage -- app.pid [9ms]
2025-06-22 21:14:16.631 [info] > git hash-object -t tree /dev/null [8ms]
2025-06-22 21:14:16.632 [warning] [GitFileSystemProvider][readFile] File not found - git:/home/<USER>/myproject/algofactory/app.pid.git?%7B%22path%22%3A%22%2Fhome%2Fubuntu%2Fmyproject%2Falgofactory%2Fapp.pid%22%2C%22ref%22%3A%22%22%7D
2025-06-22 21:14:16.632 [info] > git hash-object -t tree /dev/null [2ms]
2025-06-22 21:14:16.632 [warning] [GitFileSystemProvider][stat] File not found - git:/home/<USER>/myproject/algofactory/app.pid.git?%7B%22path%22%3A%22%2Fhome%2Fubuntu%2Fmyproject%2Falgofactory%2Fapp.pid%22%2C%22ref%22%3A%22%22%7D
2025-06-22 21:14:16.684 [info] > git blame --root --incremental 89ac1fedef89963fb80f591849bbcbe1dc91b13e -- app.pid [2ms]
2025-06-22 21:14:16.684 [info] fatal: no such path app.pid in 89ac1fedef89963fb80f591849bbcbe1dc91b13e
2025-06-22 21:14:19.074 [info] > git config --get commit.template [7ms]
2025-06-22 21:14:19.076 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-22 21:14:19.082 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 21:14:19.083 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 21:14:19.100 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-22 21:14:19.134 [info] > git status -z -uall [45ms]
2025-06-22 21:14:24.191 [info] > git config --get commit.template [29ms]
2025-06-22 21:14:24.204 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [41ms]
2025-06-22 21:14:24.213 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 21:14:24.219 [info] > git rev-parse refs/remotes/origin/main [6ms]
2025-06-22 21:14:24.238 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-06-22 21:14:24.745 [info] > git status -z -uall [522ms]
2025-06-22 21:15:07.203 [info] > git check-ignore -v -z --stdin [53ms]
2025-06-22 21:15:10.474 [info] > git check-ignore -v -z --stdin [49ms]
2025-06-22 21:15:12.234 [info] > git check-ignore -v -z --stdin [225ms]
2025-06-22 21:15:15.044 [info] > git show --textconv :.env [846ms]
2025-06-22 21:15:15.046 [info] > git ls-files --stage -- .env [847ms]
2025-06-22 21:15:15.464 [info] > git hash-object -t tree /dev/null [389ms]
2025-06-22 21:15:15.464 [warning] [GitFileSystemProvider][stat] File not found - git:/home/<USER>/algofactory-multi/instances/algofactory-1010/.env.git?%7B%22path%22%3A%22%2Fhome%2Fubuntu%2Falgofactory-multi%2Finstances%2Falgofactory-1010%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-22 21:15:15.465 [info] > git hash-object -t tree /dev/null [390ms]
2025-06-22 21:15:15.465 [warning] [GitFileSystemProvider][readFile] File not found - git:/home/<USER>/algofactory-multi/instances/algofactory-1010/.env.git?%7B%22path%22%3A%22%2Fhome%2Fubuntu%2Falgofactory-multi%2Finstances%2Falgofactory-1010%2F.env%22%2C%22ref%22%3A%22%22%7D
