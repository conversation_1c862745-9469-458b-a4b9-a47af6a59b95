2025-06-22 20:55:03.193 [info] Extension host with pid 1479 started
2025-06-22 20:55:03.193 [error] Error: EEXIST: file already exists, open '/home/<USER>/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/vscode.lock'
2025-06-22 20:55:03.199 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/vscode.lock': Could not acquire lock, checking if the file is stale.
2025-06-22 20:55:03.200 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/vscode.lock': The pid 1610 appears to be gone.
2025-06-22 20:55:03.200 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/vscode.lock': Deleting a stale lock.
2025-06-22 20:55:03.207 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/vscode.lock': Lock acquired.
2025-06-22 20:55:03.515 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-06-22 20:55:03.516 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: false, activationEvent: '*', root cause: vscode.git
2025-06-22 20:55:03.517 [info] ExtensionService#_doActivateExtension vscode.tunnel-forwarding, startup: false, activationEvent: 'onTunnel'
2025-06-22 20:55:03.649 [info] ExtensionService#_doActivateExtension vscode.git, startup: false, activationEvent: '*'
2025-06-22 20:55:03.649 [info] ExtensionService#_doActivateExtension vscode.github, startup: false, activationEvent: '*'
2025-06-22 20:55:04.284 [info] Eager extensions activated
2025-06-22 20:55:04.285 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-06-22 20:55:04.286 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-06-22 20:55:04.288 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onStartupFinished'
2025-06-22 20:55:12.101 [error] CodeExpectedError: cannot open vscode-userdata:c%3A%5CUsers%5Canita%5CAppData%5CRoaming%5CCode%5CUser%5Ckeybindings.json. Detail: Unable to resolve filesystem provider with relative file path 'vscode-userdata:c:\Users\<USER>\AppData\Roaming\Code\User\keybindings.json'
    at Pze.$tryOpenDocument (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/workbench.desktop.main.js:1283:8666)
