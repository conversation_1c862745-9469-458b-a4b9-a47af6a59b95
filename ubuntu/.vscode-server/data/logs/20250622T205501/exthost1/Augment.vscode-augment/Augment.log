2025-06-22 20:55:05.181 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-22 20:55:05.181 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{"enableCommitIndexing":false,"maxCommitsToIndex":100},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-06-22 20:55:05.181 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","enableNewThreadsList":false,"enableUntruncatedContentStorage":false,"maxLinesTerminalProcessOutput":0,"vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","vscodeSupportToolUseStartMinVersion":"","enableAgentAutoMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5,"historySummaryMinVersion":"","historySummaryMaxChars":0,"historySummaryLowerChars":0,"historySummaryPrompt":"","enableSpawnSubAgentTool":false}
2025-06-22 20:55:05.181 [info] 'AugmentExtension' Retrieving model config
2025-06-22 20:55:05.758 [info] 'AugmentExtension' Retrieved model config
2025-06-22 20:55:05.758 [info] 'AugmentExtension' Returning model config
2025-06-22 20:55:05.896 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - vscodeShareMinVersion: "" to "0.314.0"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - enableNewThreadsList: false to true
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 49512
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.472.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - openFileManagerV2Enabled: false to true
  - vscodeTaskListMinVersion: "" to "0.482.0"
  - vscodeSupportToolUseStartMinVersion: "" to "0.485.0"
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
  - historySummaryMaxChars: 0 to 200000
  - historySummaryLowerChars: 0 to 80000
  - historySummaryPrompt: "" to "Your task is to create a detailed summary of the conversation so far, paying close attention to the user's explicit requests and your previous actions.\nThis summary should be thorough in capturing technical details, code patterns, and architectural decisions that would be essential for continuing with the conversation and supporting any continuing tasks.\n\nYour summary should be structured as follows:\nContext: The context to continue the conversation with. If applicable based on the current task, this should include:\n1. Previous Conversation: High level details about what was discussed throughout the entire conversation with the user. This should be written to allow someone to be able to follow the general overarching conversation flow.\n2. Current Work: Describe in detail what was being worked on prior to this request to summarize the conversation. Pay special attention to the more recent messages in the conversation.\n3. Key Technical Concepts: List all important technical concepts, technologies, coding conventions, and frameworks discussed, which might be relevant for continuing with this work.\n4. Relevant Files and Code: If applicable, enumerate specific files and code sections examined, modified, or created for the task continuation. Pay special attention to the most recent messages and changes.\n5. Problem Solving: Document problems solved thus far and any ongoing troubleshooting efforts.\n6. Pending Tasks and Next Steps: Outline all pending tasks that you have explicitly been asked to work on, as well as list the next steps you will take for all outstanding work, if applicable. Include code snippets where they add clarity. For any next steps, include direct quotes from the most recent conversation showing exactly what task you were working on and where you left off. This should be verbatim to ensure there's no information loss in context between tasks.\n\nExample summary structure:\n1. Previous Conversation:\n[Detailed description]\n2. Current Work:\n[Detailed description]\n3. Key Technical Concepts:\n- [Concept 1]\n- [Concept 2]\n- [...]\n4. Relevant Files and Code:\n- [File Name 1]\n    - [Summary of why this file is important]\n    - [Summary of the changes made to this file, if any]\n    - [Important Code Snippet]\n- [File Name 2]\n    - [Important Code Snippet]\n- [...]\n5. Problem Solving:\n[Detailed description]\n6. Pending Tasks and Next Steps:\n- [Task 1 details & next steps]\n- [Task 2 details & next steps]\n- [...]\n\nOutput only the summary of the conversation so far, without any additional commentary or explanation.\n"
2025-06-22 20:55:05.896 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    /home (explicit) at 6/22/2025, 7:25:55 PM
2025-06-22 20:55:05.896 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-06-22 20:55:05.896 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-06-22 20:55:05.896 [info] 'SyncingPermissionTracker' Permission to sync folder /home granted at 6/22/2025, 7:25:55 PM; type = explicit
2025-06-22 20:55:05.896 [info] 'WorkspaceManager' Adding workspace folder home; folderRoot = /home; syncingPermission = granted
2025-06-22 20:55:05.896 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home (explicit) at 6/22/2025, 7:25:55 PM
2025-06-22 20:55:05.946 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-06-22 20:55:05.946 [info] 'HotKeyHints' HotKeyHints initialized
2025-06-22 20:55:05.952 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-06-22 20:55:05.987 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-22 20:55:05.987 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-22 20:55:06.735 [info] 'WorkspaceManager[home]' Start tracking
2025-06-22 20:55:06.784 [info] 'PathMap' Opened source folder /home with id 100
2025-06-22 20:55:06.784 [info] 'OpenFileManager' Opened source folder 100
2025-06-22 20:55:06.800 [info] 'MtimeCache[home]' reading blob name cache from /home/<USER>/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/Augment.vscode-augment/2cc974af6afc822c42a4e914df05c697348b80420941f6b27561b2bf688587b7/mtime-cache.json
2025-06-22 20:55:07.039 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-22 20:55:07.039 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-06-22 20:55:07.387 [info] 'MtimeCache[home]' read 12950 entries from /home/<USER>/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/Augment.vscode-augment/2cc974af6afc822c42a4e914df05c697348b80420941f6b27561b2bf688587b7/mtime-cache.json
2025-06-22 20:55:09.244 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-22 20:55:09.244 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-22 20:55:09.244 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-22 20:55:09.244 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-06-22 20:55:27.849 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-06-22 20:55:29.234 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-06-22 20:55:29.234 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-06-22 20:55:29.243 [info] 'TaskManager' Setting current root task UUID to abd4627c-60c1-4f48-88a1-5f41783481ba
2025-06-22 20:55:29.635 [error] 'GitReferenceMessenger' Failed to locally get remote url: Failed to get remote url, no remote found
2025-06-22 20:55:40.301 [info] 'WorkspaceManager[home]' Tracking enabled
2025-06-22 20:55:40.301 [info] 'WorkspaceManager[home]' Path metrics:
  - directories emitted: 4223
  - files emitted: 21175
  - other paths emitted: 4
  - total paths emitted: 25402
  - timing stats:
    - readDir: 76 ms
    - filter: 625 ms
    - yield: 179 ms
    - total: 1111 ms
2025-06-22 20:55:40.301 [info] 'WorkspaceManager[home]' File metrics:
  - paths accepted: 12994
  - paths not accessible: 0
  - not plain files: 0
  - large files: 112
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 12942
  - mtime cache misses: 54
  - probe batches: 19
  - blob names probed: 13011
  - files read: 8154
  - blobs uploaded: 19
  - timing stats:
    - ingestPath: 43 ms
    - probe: 14467 ms
    - stat: 173 ms
    - read: 12791 ms
    - upload: 2587 ms
2025-06-22 20:55:40.301 [info] 'WorkspaceManager[home]' Startup metrics:
  - create SourceFolder: 63 ms
  - read MtimeCache: 589 ms
  - pre-populate PathMap: 671 ms
  - create PathFilter: 1543 ms
  - create PathNotifier: 0 ms
  - enumerate paths: 1116 ms
  - purge stale PathMap entries: 10 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 29544 ms
  - enable persist: 29 ms
  - total: 33565 ms
2025-06-22 20:55:40.301 [info] 'WorkspaceManager' Workspace startup complete in 34428 ms
2025-06-22 20:56:46.195 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.vscode-server/data/logs/20250622T205501/exthost1/vscode.json-language-features
2025-06-22 20:57:21.815 [error] 'ChatApp' Chat stream failed: Error: Cancelled
Error: Cancelled
	at HJ.cancel (/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.482.1/out/extension.js:1107:1079)
	at e.cancelChatStream (/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.482.1/out/extension.js:1107:33491)
	at AD.onUserCancel (/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.482.1/out/extension.js:2185:14612)
	at /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.482.1/out/extension.js:2185:4028
	at ud.value (/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.482.1/out/extension.js:907:4147)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at tq.$onMessage (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:147:90466)
	at $5.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
	at $5.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
	at $5.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
	at $5.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161415)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at lv.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lk.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-22 21:10:03.378 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.vscode-server/data/logs/20250622T205501/exthost1/vscode.markdown-language-features
2025-06-22 21:11:12.442 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi
2025-06-22 21:11:12.465 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances
2025-06-22 21:11:12.465 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/nginx
2025-06-22 21:11:12.465 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/nginx/sites
2025-06-22 21:11:12.465 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template
2025-06-22 21:11:12.469 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/test
2025-06-22 21:11:12.668 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/database
2025-06-22 21:11:12.669 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/download
2025-06-22 21:11:12.669 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/src
2025-06-22 21:11:12.670 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/templates
2025-06-22 21:11:12.673 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/templates/chartink
2025-06-22 21:11:12.673 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/templates/components
2025-06-22 21:11:12.674 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/templates/latency
2025-06-22 21:11:12.674 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/templates/strategy
2025-06-22 21:11:12.675 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/templates/traffic
2025-06-22 21:11:13.263 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/.ebextensions
2025-06-22 21:11:13.264 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker
2025-06-22 21:11:13.277 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/aliceblue
2025-06-22 21:11:13.278 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/aliceblue/api
2025-06-22 21:11:13.278 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/aliceblue/database
2025-06-22 21:11:13.278 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/aliceblue/mapping
2025-06-22 21:11:13.278 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/angel
2025-06-22 21:11:13.278 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/angel/api
2025-06-22 21:11:13.278 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/angel/mapping
2025-06-22 21:11:13.278 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/angel/streaming
2025-06-22 21:11:13.282 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/compositedge
2025-06-22 21:11:13.283 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/compositedge/api
2025-06-22 21:11:13.284 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/compositedge/database
2025-06-22 21:11:13.284 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/compositedge/mapping
2025-06-22 21:11:13.284 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/dhan
2025-06-22 21:11:13.286 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/dhan/api
2025-06-22 21:11:13.286 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/dhan/database
2025-06-22 21:11:13.286 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/dhan/mapping
2025-06-22 21:11:13.287 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/dhan/streaming
2025-06-22 21:11:13.287 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/dhan_sandbox
2025-06-22 21:11:13.291 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/dhan_sandbox/api
2025-06-22 21:11:13.291 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/dhan_sandbox/database
2025-06-22 21:11:13.291 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/dhan_sandbox/mapping
2025-06-22 21:11:13.292 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/firstock
2025-06-22 21:11:13.294 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/firstock/api
2025-06-22 21:11:13.295 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/firstock/database
2025-06-22 21:11:13.297 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/firstock/mapping
2025-06-22 21:11:13.297 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/fivepaisa
2025-06-22 21:11:13.298 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/fivepaisa/api
2025-06-22 21:11:13.298 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/fivepaisa/database
2025-06-22 21:11:13.298 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/fivepaisa/mapping
2025-06-22 21:11:13.298 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/fivepaisaxts
2025-06-22 21:11:13.298 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/fivepaisaxts/api
2025-06-22 21:11:13.298 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/fivepaisaxts/database
2025-06-22 21:11:13.298 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/fivepaisaxts/mapping
2025-06-22 21:11:13.298 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/flattrade
2025-06-22 21:11:13.299 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/flattrade/api
2025-06-22 21:11:13.302 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/flattrade/database
2025-06-22 21:11:13.302 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/flattrade/mapping
2025-06-22 21:11:13.302 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/fyers
2025-06-22 21:11:13.303 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/fyers/api
2025-06-22 21:11:13.304 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/fyers/database
2025-06-22 21:11:13.305 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/fyers/mapping
2025-06-22 21:11:13.306 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/groww
2025-06-22 21:11:13.306 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/groww/api
2025-06-22 21:11:13.307 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/groww/database
2025-06-22 21:11:13.307 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/groww/mapping
2025-06-22 21:11:13.308 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/iifl
2025-06-22 21:11:13.308 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/iifl/api
2025-06-22 21:11:13.308 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/iifl/database
2025-06-22 21:11:13.308 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/iifl/mapping
2025-06-22 21:11:13.309 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/jainam
2025-06-22 21:11:13.310 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/jainam/api
2025-06-22 21:11:13.311 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/jainam/database
2025-06-22 21:11:13.311 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/jainam/mapping
2025-06-22 21:11:13.312 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/jainampro
2025-06-22 21:11:13.312 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/jainampro/api
2025-06-22 21:11:13.313 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/jainampro/database
2025-06-22 21:11:13.313 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/jainampro/mapping
2025-06-22 21:11:13.313 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/kotak
2025-06-22 21:11:13.314 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/kotak/api
2025-06-22 21:11:13.314 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/kotak/database
2025-06-22 21:11:13.314 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/kotak/mapping
2025-06-22 21:11:13.314 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/paytm
2025-06-22 21:11:13.317 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/paytm/api
2025-06-22 21:11:13.317 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/paytm/database
2025-06-22 21:11:13.317 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/paytm/mapping
2025-06-22 21:11:13.317 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/pocketful
2025-06-22 21:11:13.318 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/pocketful/api
2025-06-22 21:11:13.318 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/pocketful/database
2025-06-22 21:11:13.318 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/pocketful/mapping
2025-06-22 21:11:13.318 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/shoonya
2025-06-22 21:11:13.320 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/shoonya/api
2025-06-22 21:11:13.320 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/shoonya/database
2025-06-22 21:11:13.321 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/shoonya/mapping
2025-06-22 21:11:13.321 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/tradejini
2025-06-22 21:11:13.321 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/tradejini/api
2025-06-22 21:11:13.322 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/tradejini/database
2025-06-22 21:11:13.322 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/tradejini/mapping
2025-06-22 21:11:13.322 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/upstox
2025-06-22 21:11:13.322 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/upstox/api
2025-06-22 21:11:13.323 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/upstox/database
2025-06-22 21:11:13.323 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/upstox/mapping
2025-06-22 21:11:13.323 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/wisdom
2025-06-22 21:11:13.323 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/wisdom/api
2025-06-22 21:11:13.324 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/wisdom/database
2025-06-22 21:11:13.324 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/wisdom/mapping
2025-06-22 21:11:13.324 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/zebu
2025-06-22 21:11:13.324 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/zebu/api
2025-06-22 21:11:13.324 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/zebu/mapping
2025-06-22 21:11:13.325 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/zerodha
2025-06-22 21:11:13.326 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/zerodha/api
2025-06-22 21:11:13.328 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/zerodha/database
2025-06-22 21:11:13.328 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/zerodha/mapping
2025-06-22 21:11:13.328 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/broker/zerodha/streaming
2025-06-22 21:11:13.329 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/collections
2025-06-22 21:11:13.329 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/design
2025-06-22 21:11:13.329 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/docs
2025-06-22 21:11:13.330 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/install
2025-06-22 21:11:13.330 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/restx_api
2025-06-22 21:11:13.330 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/static
2025-06-22 21:11:13.334 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/static/css
2025-06-22 21:11:13.336 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/static/images
2025-06-22 21:11:13.336 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/tmp
2025-06-22 21:11:13.338 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/upgrade
2025-06-22 21:11:13.338 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/websocket_proxy
2025-06-22 21:11:13.354 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/blueprints
2025-06-22 21:11:13.355 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/services
2025-06-22 21:11:13.356 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/static/favicon
2025-06-22 21:11:13.356 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/static/images/brokers
2025-06-22 21:11:13.356 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/static/js
2025-06-22 21:11:13.357 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/static/sounds
2025-06-22 21:11:13.357 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/static/uploads
2025-06-22 21:11:13.357 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/strategies
2025-06-22 21:11:13.357 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/template/utils
2025-06-22 21:11:41.035 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010
2025-06-22 21:11:41.047 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/database
2025-06-22 21:11:41.049 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/download
2025-06-22 21:11:41.049 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/templates
2025-06-22 21:11:41.052 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/templates/components
2025-06-22 21:11:41.653 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/.ebextensions
2025-06-22 21:11:41.654 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker
2025-06-22 21:11:41.669 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/aliceblue
2025-06-22 21:11:41.669 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/aliceblue/api
2025-06-22 21:11:41.669 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/aliceblue/mapping
2025-06-22 21:11:41.669 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/angel
2025-06-22 21:11:41.671 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/angel/api
2025-06-22 21:11:41.671 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/angel/mapping
2025-06-22 21:11:41.671 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/angel/streaming
2025-06-22 21:11:41.671 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/compositedge
2025-06-22 21:11:41.672 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/compositedge/api
2025-06-22 21:11:41.672 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/compositedge/mapping
2025-06-22 21:11:41.673 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/dhan
2025-06-22 21:11:41.673 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/dhan/api
2025-06-22 21:11:41.673 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/dhan/mapping
2025-06-22 21:11:41.673 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/dhan/streaming
2025-06-22 21:11:41.674 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/dhan_sandbox
2025-06-22 21:11:41.675 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/dhan_sandbox/api
2025-06-22 21:11:41.676 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/dhan_sandbox/mapping
2025-06-22 21:11:41.676 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/firstock
2025-06-22 21:11:41.677 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/firstock/api
2025-06-22 21:11:41.677 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/firstock/mapping
2025-06-22 21:11:41.677 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/fivepaisa
2025-06-22 21:11:41.678 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/fivepaisa/api
2025-06-22 21:11:41.680 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/fivepaisa/mapping
2025-06-22 21:11:41.680 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/fivepaisaxts
2025-06-22 21:11:41.680 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/fivepaisaxts/api
2025-06-22 21:11:41.680 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/fivepaisaxts/mapping
2025-06-22 21:11:41.680 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/flattrade
2025-06-22 21:11:41.681 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/flattrade/api
2025-06-22 21:11:41.681 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/flattrade/mapping
2025-06-22 21:11:41.681 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/fyers
2025-06-22 21:11:41.683 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/fyers/api
2025-06-22 21:11:41.685 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/fyers/mapping
2025-06-22 21:11:41.686 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/groww
2025-06-22 21:11:41.687 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/groww/api
2025-06-22 21:11:41.687 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/groww/mapping
2025-06-22 21:11:41.688 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/iifl
2025-06-22 21:11:41.692 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/iifl/api
2025-06-22 21:11:41.692 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/iifl/mapping
2025-06-22 21:11:41.693 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/jainam
2025-06-22 21:11:41.693 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/jainam/api
2025-06-22 21:11:41.693 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/jainam/mapping
2025-06-22 21:11:41.693 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/jainampro
2025-06-22 21:11:41.694 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/jainampro/api
2025-06-22 21:11:41.694 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/jainampro/mapping
2025-06-22 21:11:41.694 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/kotak
2025-06-22 21:11:41.695 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/kotak/api
2025-06-22 21:11:41.695 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/kotak/mapping
2025-06-22 21:11:41.695 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/paytm
2025-06-22 21:11:41.696 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/paytm/api
2025-06-22 21:11:41.696 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/paytm/mapping
2025-06-22 21:11:41.696 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/pocketful
2025-06-22 21:11:41.697 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/pocketful/api
2025-06-22 21:11:41.697 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/pocketful/mapping
2025-06-22 21:11:41.697 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/shoonya
2025-06-22 21:11:41.697 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/shoonya/api
2025-06-22 21:11:41.698 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/shoonya/mapping
2025-06-22 21:11:41.698 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/tradejini
2025-06-22 21:11:41.698 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/tradejini/api
2025-06-22 21:11:41.698 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/tradejini/mapping
2025-06-22 21:11:41.698 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/upstox
2025-06-22 21:11:41.699 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/upstox/api
2025-06-22 21:11:41.700 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/upstox/mapping
2025-06-22 21:11:41.700 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/zebu
2025-06-22 21:11:41.700 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/zebu/api
2025-06-22 21:11:41.701 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/zebu/database
2025-06-22 21:11:41.701 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/zebu/mapping
2025-06-22 21:11:41.701 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/zerodha
2025-06-22 21:11:41.702 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/zerodha/api
2025-06-22 21:11:41.702 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/zerodha/mapping
2025-06-22 21:11:41.702 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/zerodha/streaming
2025-06-22 21:11:41.702 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/collections
2025-06-22 21:11:41.702 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/design
2025-06-22 21:11:41.703 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/docs
2025-06-22 21:11:41.704 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/install
2025-06-22 21:11:41.705 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/restx_api
2025-06-22 21:11:41.705 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/src
2025-06-22 21:11:41.706 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/static
2025-06-22 21:11:41.707 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/static/favicon
2025-06-22 21:11:41.707 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/static/images
2025-06-22 21:11:41.708 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/static/images/brokers
2025-06-22 21:11:41.708 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/static/js
2025-06-22 21:11:41.708 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/static/sounds
2025-06-22 21:11:41.708 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/static/uploads
2025-06-22 21:11:41.709 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/templates/chartink
2025-06-22 21:11:41.709 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/templates/latency
2025-06-22 21:11:41.709 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/templates/strategy
2025-06-22 21:11:41.710 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/templates/traffic
2025-06-22 21:11:41.710 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/tmp
2025-06-22 21:11:41.710 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/upgrade
2025-06-22 21:11:41.762 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/websocket_proxy
2025-06-22 21:11:41.763 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/blueprints
2025-06-22 21:11:41.763 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/db
2025-06-22 21:11:41.763 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/logs
2025-06-22 21:11:41.763 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/services
2025-06-22 21:11:41.764 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/strategies
2025-06-22 21:11:41.764 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/utils
2025-06-22 21:12:14.068 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011
2025-06-22 21:12:14.696 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/.ebextensions
2025-06-22 21:12:14.699 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker
2025-06-22 21:12:14.718 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/aliceblue
2025-06-22 21:12:14.719 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/aliceblue/api
2025-06-22 21:12:14.720 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/aliceblue/mapping
2025-06-22 21:12:14.720 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/angel
2025-06-22 21:12:14.720 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/angel/api
2025-06-22 21:12:14.721 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/angel/database
2025-06-22 21:12:14.721 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/angel/mapping
2025-06-22 21:12:14.722 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/angel/streaming
2025-06-22 21:12:14.722 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/compositedge
2025-06-22 21:12:14.722 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/compositedge/api
2025-06-22 21:12:14.723 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/compositedge/mapping
2025-06-22 21:12:14.723 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/dhan
2025-06-22 21:12:14.723 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/dhan/api
2025-06-22 21:12:14.724 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/dhan/mapping
2025-06-22 21:12:14.725 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/dhan/streaming
2025-06-22 21:12:14.725 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/dhan_sandbox
2025-06-22 21:12:14.725 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/dhan_sandbox/api
2025-06-22 21:12:14.726 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/dhan_sandbox/mapping
2025-06-22 21:12:14.726 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/firstock
2025-06-22 21:12:14.726 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/firstock/api
2025-06-22 21:12:14.727 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/firstock/mapping
2025-06-22 21:12:14.727 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/fivepaisa
2025-06-22 21:12:14.728 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/fivepaisa/api
2025-06-22 21:12:14.729 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/fivepaisa/mapping
2025-06-22 21:12:14.729 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/fivepaisaxts
2025-06-22 21:12:14.729 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/fivepaisaxts/api
2025-06-22 21:12:14.729 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/fivepaisaxts/mapping
2025-06-22 21:12:14.729 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/flattrade
2025-06-22 21:12:14.730 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/flattrade/api
2025-06-22 21:12:14.731 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/flattrade/mapping
2025-06-22 21:12:14.731 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/fyers
2025-06-22 21:12:14.731 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/fyers/api
2025-06-22 21:12:14.733 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/fyers/mapping
2025-06-22 21:12:14.733 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/groww
2025-06-22 21:12:14.733 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/groww/api
2025-06-22 21:12:14.733 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/groww/mapping
2025-06-22 21:12:14.734 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/iifl
2025-06-22 21:12:14.735 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/iifl/api
2025-06-22 21:12:14.735 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/iifl/mapping
2025-06-22 21:12:14.736 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/jainam
2025-06-22 21:12:14.736 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/jainam/api
2025-06-22 21:12:14.736 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/jainam/mapping
2025-06-22 21:12:14.736 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/jainampro
2025-06-22 21:12:14.738 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/jainampro/api
2025-06-22 21:12:14.738 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/jainampro/mapping
2025-06-22 21:12:14.738 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/kotak
2025-06-22 21:12:14.738 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/kotak/api
2025-06-22 21:12:14.738 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/kotak/mapping
2025-06-22 21:12:14.739 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/paytm
2025-06-22 21:12:14.739 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/paytm/api
2025-06-22 21:12:14.739 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/paytm/mapping
2025-06-22 21:12:14.740 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/pocketful
2025-06-22 21:12:14.741 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/pocketful/api
2025-06-22 21:12:14.741 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/pocketful/mapping
2025-06-22 21:12:14.741 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/shoonya
2025-06-22 21:12:14.742 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/shoonya/api
2025-06-22 21:12:14.742 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/shoonya/database
2025-06-22 21:12:14.744 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/shoonya/mapping
2025-06-22 21:12:14.744 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/tradejini
2025-06-22 21:12:14.744 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/tradejini/api
2025-06-22 21:12:14.744 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/tradejini/mapping
2025-06-22 21:12:14.744 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/upstox
2025-06-22 21:12:14.745 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/upstox/api
2025-06-22 21:12:14.745 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/upstox/database
2025-06-22 21:12:14.745 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/upstox/mapping
2025-06-22 21:12:14.746 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/zebu
2025-06-22 21:12:14.746 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/zebu/api
2025-06-22 21:12:14.749 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/zebu/mapping
2025-06-22 21:12:14.749 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/zerodha
2025-06-22 21:12:14.750 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/zerodha/api
2025-06-22 21:12:14.750 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/zerodha/mapping
2025-06-22 21:12:14.750 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/broker/zerodha/streaming
2025-06-22 21:12:14.750 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/collections
2025-06-22 21:12:14.750 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/database
2025-06-22 21:12:14.751 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/download
2025-06-22 21:12:14.751 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/restx_api
2025-06-22 21:12:14.755 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/src
2025-06-22 21:12:14.755 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/templates
2025-06-22 21:12:14.757 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/templates/chartink
2025-06-22 21:12:14.757 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/templates/components
2025-06-22 21:12:14.760 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/templates/latency
2025-06-22 21:12:14.760 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/templates/strategy
2025-06-22 21:12:14.760 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/templates/traffic
2025-06-22 21:12:14.760 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/tmp
2025-06-22 21:12:14.760 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/upgrade
2025-06-22 21:12:14.760 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/websocket_proxy
2025-06-22 21:12:15.010 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/blueprints
2025-06-22 21:12:15.012 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/db
2025-06-22 21:12:15.012 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/design
2025-06-22 21:12:15.012 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/docs
2025-06-22 21:12:15.013 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/install
2025-06-22 21:12:15.013 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/logs
2025-06-22 21:12:15.013 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/services
2025-06-22 21:12:15.013 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/static
2025-06-22 21:12:15.014 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/static/css
2025-06-22 21:12:15.014 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/static/favicon
2025-06-22 21:12:15.016 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/static/images
2025-06-22 21:12:15.017 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/static/images/brokers
2025-06-22 21:12:15.017 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/static/js
2025-06-22 21:12:15.017 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/static/sounds
2025-06-22 21:12:15.017 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/static/uploads
2025-06-22 21:12:15.018 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/strategies
2025-06-22 21:12:15.018 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1011/utils
2025-06-22 21:13:11.752 [info] 'ToolFileUtils' Reading file: ubuntu/myproject/algofactory/instance_manager.py
2025-06-22 21:13:11.795 [info] 'ToolFileUtils' Successfully read file: ubuntu/myproject/algofactory/instance_manager.py (13070 bytes)
2025-06-22 21:13:12.208 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.vscode-server/data/User/History/30aa9e21
2025-06-22 21:13:13.031 [info] 'ToolFileUtils' Reading file: ubuntu/myproject/algofactory/instance_manager.py
2025-06-22 21:13:13.031 [info] 'ToolFileUtils' Successfully read file: ubuntu/myproject/algofactory/instance_manager.py (13156 bytes)
2025-06-22 21:13:24.007 [info] 'ToolFileUtils' Reading file: ubuntu/myproject/algofactory/instance_manager.py
2025-06-22 21:13:24.007 [info] 'ToolFileUtils' Successfully read file: ubuntu/myproject/algofactory/instance_manager.py (13156 bytes)
2025-06-22 21:13:24.038 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 1e49245c135abe1c3d4af900b7ea442ebbca499a4598d50a6167e4ebb3a73595: deleted
2025-06-22 21:13:25.180 [info] 'ToolFileUtils' Reading file: ubuntu/myproject/algofactory/instance_manager.py
2025-06-22 21:13:25.180 [info] 'ToolFileUtils' Successfully read file: ubuntu/myproject/algofactory/instance_manager.py (13180 bytes)
2025-06-22 21:13:43.022 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/algofactory-multi/instances/algofactory-1010/db
2025-06-22 21:13:43.025 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/algofactory-multi/instances/algofactory-1010/src
2025-06-22 21:13:43.025 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/algofactory-multi/instances/algofactory-1010/tmp
2025-06-22 21:13:43.025 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/algofactory-multi/instances/algofactory-1010/test
2025-06-22 21:13:43.025 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/algofactory-multi/instances/algofactory-1010/upgrade
2025-06-22 21:13:43.025 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/algofactory-multi/instances/algofactory-1010/database
2025-06-22 21:13:43.025 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/algofactory-multi/instances/algofactory-1010/download
2025-06-22 21:13:43.025 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/algofactory-multi/instances/algofactory-1010/templates
2025-06-22 21:13:43.025 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/algofactory-multi/instances/algofactory-1010/.ebextensions
2025-06-22 21:13:43.025 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/algofactory-multi/instances/algofactory-1010/websocket_proxy
2025-06-22 21:13:43.130 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/algofactory-multi/instances/algofactory-1010
2025-06-22 21:13:53.936 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/algofactory-multi/instances/algofactory-1011/test
2025-06-22 21:13:54.232 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/algofactory-multi/instances/algofactory-1011
2025-06-22 21:14:01.771 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010
2025-06-22 21:14:01.812 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/.ebextensions
2025-06-22 21:14:01.812 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker
2025-06-22 21:14:01.822 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/angel
2025-06-22 21:14:01.823 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/angel/api
2025-06-22 21:14:01.823 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/angel/database
2025-06-22 21:14:01.823 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/angel/mapping
2025-06-22 21:14:01.823 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/angel/streaming
2025-06-22 21:14:01.823 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/dhan
2025-06-22 21:14:01.825 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/dhan/api
2025-06-22 21:14:01.825 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/dhan/database
2025-06-22 21:14:01.825 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/dhan/mapping
2025-06-22 21:14:01.826 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/dhan/streaming
2025-06-22 21:14:01.826 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/fivepaisa
2025-06-22 21:14:01.826 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/fivepaisa/api
2025-06-22 21:14:01.826 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/fivepaisa/database
2025-06-22 21:14:01.826 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/fivepaisa/mapping
2025-06-22 21:14:01.827 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/fivepaisaxts
2025-06-22 21:14:01.827 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/fivepaisaxts/api
2025-06-22 21:14:01.828 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/fivepaisaxts/database
2025-06-22 21:14:01.828 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/fivepaisaxts/mapping
2025-06-22 21:14:01.828 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/fyers
2025-06-22 21:14:01.829 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/fyers/api
2025-06-22 21:14:01.829 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/fyers/database
2025-06-22 21:14:01.829 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/fyers/mapping
2025-06-22 21:14:01.829 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/iifl
2025-06-22 21:14:01.832 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/iifl/api
2025-06-22 21:14:01.832 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/iifl/database
2025-06-22 21:14:01.833 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/iifl/mapping
2025-06-22 21:14:01.833 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/jainampro
2025-06-22 21:14:01.833 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/jainampro/database
2025-06-22 21:14:01.835 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/kotak
2025-06-22 21:14:01.835 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/kotak/api
2025-06-22 21:14:01.836 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/kotak/database
2025-06-22 21:14:01.836 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/kotak/mapping
2025-06-22 21:14:01.836 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/shoonya
2025-06-22 21:14:01.837 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/shoonya/api
2025-06-22 21:14:01.837 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/shoonya/database
2025-06-22 21:14:01.837 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/shoonya/mapping
2025-06-22 21:14:01.837 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/upstox
2025-06-22 21:14:01.838 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/upstox/api
2025-06-22 21:14:01.838 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/upstox/database
2025-06-22 21:14:01.839 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/upstox/mapping
2025-06-22 21:14:01.839 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/wisdom
2025-06-22 21:14:01.839 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/wisdom/api
2025-06-22 21:14:01.840 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/wisdom/database
2025-06-22 21:14:01.840 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/wisdom/mapping
2025-06-22 21:14:01.840 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/zebu
2025-06-22 21:14:01.840 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/zebu/api
2025-06-22 21:14:01.841 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/zebu/database
2025-06-22 21:14:01.841 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/zebu/mapping
2025-06-22 21:14:01.841 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/collections
2025-06-22 21:14:01.841 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/database
2025-06-22 21:14:01.841 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/download
2025-06-22 21:14:01.841 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/src
2025-06-22 21:14:01.843 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/src/css
2025-06-22 21:14:01.843 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/templates
2025-06-22 21:14:01.848 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/templates/chartink
2025-06-22 21:14:01.848 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/templates/components
2025-06-22 21:14:01.849 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/templates/latency
2025-06-22 21:14:01.849 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/templates/strategy
2025-06-22 21:14:01.849 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/templates/traffic
2025-06-22 21:14:01.849 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/tmp
2025-06-22 21:14:01.849 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/upgrade
2025-06-22 21:14:01.849 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/websocket_proxy
2025-06-22 21:14:02.267 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/blueprints
2025-06-22 21:14:02.268 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/aliceblue
2025-06-22 21:14:02.268 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/aliceblue/api
2025-06-22 21:14:02.268 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/aliceblue/database
2025-06-22 21:14:02.269 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/aliceblue/mapping
2025-06-22 21:14:02.269 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/compositedge
2025-06-22 21:14:02.269 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/compositedge/api
2025-06-22 21:14:02.269 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/compositedge/database
2025-06-22 21:14:02.269 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/compositedge/mapping
2025-06-22 21:14:02.270 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/dhan_sandbox
2025-06-22 21:14:02.271 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/dhan_sandbox/api
2025-06-22 21:14:02.271 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/dhan_sandbox/database
2025-06-22 21:14:02.271 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/dhan_sandbox/mapping
2025-06-22 21:14:02.271 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/firstock
2025-06-22 21:14:02.272 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/firstock/api
2025-06-22 21:14:02.272 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/firstock/mapping
2025-06-22 21:14:02.272 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/flattrade
2025-06-22 21:14:02.273 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/flattrade/api
2025-06-22 21:14:02.273 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/flattrade/database
2025-06-22 21:14:02.273 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/flattrade/mapping
2025-06-22 21:14:02.273 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/groww
2025-06-22 21:14:02.273 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/groww/api
2025-06-22 21:14:02.273 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/groww/database
2025-06-22 21:14:02.273 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/groww/mapping
2025-06-22 21:14:02.274 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/jainam
2025-06-22 21:14:02.274 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/jainam/api
2025-06-22 21:14:02.274 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/jainam/database
2025-06-22 21:14:02.274 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/jainam/mapping
2025-06-22 21:14:02.274 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/jainampro/api
2025-06-22 21:14:02.274 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/jainampro/mapping
2025-06-22 21:14:02.275 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/paytm
2025-06-22 21:14:02.275 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/paytm/api
2025-06-22 21:14:02.275 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/paytm/database
2025-06-22 21:14:02.277 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/paytm/mapping
2025-06-22 21:14:02.277 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/pocketful
2025-06-22 21:14:02.278 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/pocketful/api
2025-06-22 21:14:02.278 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/pocketful/database
2025-06-22 21:14:02.278 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/pocketful/mapping
2025-06-22 21:14:02.279 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/tradejini
2025-06-22 21:14:02.279 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/tradejini/api
2025-06-22 21:14:02.279 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/tradejini/database
2025-06-22 21:14:02.279 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/tradejini/mapping
2025-06-22 21:14:02.279 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/zerodha
2025-06-22 21:14:02.280 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/zerodha/api
2025-06-22 21:14:02.280 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/zerodha/mapping
2025-06-22 21:14:02.280 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/broker/zerodha/streaming
2025-06-22 21:14:02.280 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/db
2025-06-22 21:14:02.280 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/design
2025-06-22 21:14:02.280 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/docs
2025-06-22 21:14:02.281 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/install
2025-06-22 21:14:02.281 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/logs
2025-06-22 21:14:02.281 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/restx_api
2025-06-22 21:14:02.281 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/services
2025-06-22 21:14:02.282 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/static
2025-06-22 21:14:02.283 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/static/css
2025-06-22 21:14:02.283 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/static/favicon
2025-06-22 21:14:02.284 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/static/images
2025-06-22 21:14:02.284 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/static/images/brokers
2025-06-22 21:14:02.285 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/static/js
2025-06-22 21:14:02.285 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/static/sounds
2025-06-22 21:14:02.285 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/static/uploads
2025-06-22 21:14:02.285 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/strategies
2025-06-22 21:14:02.285 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-multi/instances/algofactory-1010/utils
2025-06-22 21:15:43.480 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 2179 msec late.
