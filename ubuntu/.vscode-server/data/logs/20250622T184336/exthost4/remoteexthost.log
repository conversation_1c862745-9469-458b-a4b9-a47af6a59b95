2025-06-22 18:48:54.540 [info] Extension host with pid 1707 started
2025-06-22 18:48:54.540 [info] Eager extensions activated
2025-06-22 18:48:54.559 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/8fb0da0c66a291b7cbfae207d20e731c/vscode.lock': Lock acquired.
2025-06-22 18:48:56.109 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-06-22 18:48:56.111 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-06-22 18:48:56.111 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: false, activationEvent: '*', root cause: vscode.git
2025-06-22 18:48:56.112 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-06-22 18:48:56.244 [info] ExtensionService#_doActivateExtension vscode.git, startup: false, activationEvent: '*'
2025-06-22 18:48:56.245 [info] ExtensionService#_doActivateExtension vscode.github, startup: false, activationEvent: '*'
2025-06-22 19:06:44.455 [info] Extension host terminating: received terminate message from renderer
2025-06-22 19:06:44.456 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/8fb0da0c66a291b7cbfae207d20e731c/vscode.lock': Marking the lockfile as scheduled to be released in 6000 ms.
2025-06-22 19:06:44.471 [info] Extension host with pid 1707 exiting with code 0
