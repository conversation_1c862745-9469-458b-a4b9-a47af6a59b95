2025-06-22 18:43:36.149 [info] 




2025-06-22 18:43:36.161 [info] Extension host agent started.
2025-06-22 18:43:36.217 [info] Started initializing default profile extensions in extensions installation folder. file:///home/<USER>/.vscode-server/extensions
2025-06-22 18:43:36.276 [info] ComputeTargetPlatform: linux-x64
2025-06-22 18:43:36.280 [info] [<unknown>][13157b68][ExtensionHostConnection] New connection established.
2025-06-22 18:43:36.284 [info] [<unknown>][81bc3c4c][ManagementConnection] New connection established.
2025-06-22 18:43:36.301 [info] Completed initializing default profile extensions in extensions installation folder. file:///home/<USER>/.vscode-server/extensions
2025-06-22 18:43:36.363 [info] [<unknown>][13157b68][ExtensionHostConnection] <1273> Launched Extension Host Process.
2025-06-22 18:43:38.241 [info] ComputeTargetPlatform: linux-x64
2025-06-22 18:43:49.043 [info] [<unknown>][9ec8f9e0][ExtensionHostConnection] New connection established.
2025-06-22 18:43:49.046 [info] [<unknown>][9ec8f9e0][ExtensionHostConnection] <1434> Launched Extension Host Process.
2025-06-22 18:43:49.063 [info] [<unknown>][32a9f6ba][ManagementConnection] New connection established.
2025-06-22 18:43:51.345 [error] CodeExpectedError: Could not find pty 10 on pty host
    at O.W (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:46:6391)
    at O.updateTitle (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:46:1839)
    at R.s.<computed> (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:45:2933)
    at Object.call (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:28:4204)
    at ml.s (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:81286)
    at ml.q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:80809)
    at ps.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:80211)
    at E.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:2373)
    at E.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:2591)
    at process.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:24:30052)
    at process.emit (node:events:518:28)
    at emit (node:internal/child_process:949:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)
2025-06-22 18:44:04.612 [info] [<unknown>][32a9f6ba][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
2025-06-22 18:44:04.640 [info] [<unknown>][9ec8f9e0][ExtensionHostConnection] <1434> Extension Host Process exited with code: 0, signal: null.
2025-06-22 18:44:04.640 [info] Cancelling previous shutdown timeout
2025-06-22 18:44:22.329 [info] [<unknown>][81bc3c4c][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
2025-06-22 18:44:22.353 [info] [<unknown>][13157b68][ExtensionHostConnection] <1273> Extension Host Process exited with code: 0, signal: null.
2025-06-22 18:44:22.353 [info] Last EH closed, waiting before shutting down
2025-06-22 18:48:38.260 [info] [<unknown>][c23ad415][ManagementConnection] New connection established.
2025-06-22 18:48:38.262 [error] Error: Unexpected SIGPIPE
    at process.<anonymous> (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/server-main.js:191:1060)
    at process.emit (node:events:530:35)
2025-06-22 18:48:38.288 [info] [<unknown>][a0f052f6][ExtensionHostConnection] New connection established.
2025-06-22 18:48:38.293 [info] [<unknown>][a0f052f6][ExtensionHostConnection] <1584> Launched Extension Host Process.
2025-06-22 18:48:50.434 [info] [<unknown>][c23ad415][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
2025-06-22 18:48:50.465 [info] [<unknown>][a0f052f6][ExtensionHostConnection] <1584> Extension Host Process exited with code: 0, signal: null.
2025-06-22 18:48:50.465 [info] Cancelling previous shutdown timeout
2025-06-22 18:48:50.465 [info] Last EH closed, waiting before shutting down
2025-06-22 18:48:53.055 [info] [<unknown>][63707ce2][ManagementConnection] New connection established.
2025-06-22 18:48:53.086 [info] [<unknown>][ce3221d8][ExtensionHostConnection] New connection established.
2025-06-22 18:48:53.091 [info] [<unknown>][ce3221d8][ExtensionHostConnection] <1707> Launched Extension Host Process.
2025-06-22 18:53:50.466 [info] New EH opened, aborting shutdown
2025-06-22 19:06:44.422 [info] [<unknown>][63707ce2][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
2025-06-22 19:06:44.481 [info] [<unknown>][ce3221d8][ExtensionHostConnection] <1707> Extension Host Process exited with code: 0, signal: null.
2025-06-22 19:06:44.482 [info] Last EH closed, waiting before shutting down
2025-06-22 19:07:26.829 [info] [<unknown>][5c9b0196][ManagementConnection] New connection established.
2025-06-22 19:07:26.853 [info] [<unknown>][65c3505c][ExtensionHostConnection] New connection established.
2025-06-22 19:07:26.859 [info] [<unknown>][65c3505c][ExtensionHostConnection] <3534> Launched Extension Host Process.
2025-06-22 19:11:41.149 [warning] [File Watcher (node.js)] Watcher shutdown because watched path got deleted
2025-06-22 19:11:41.156 [warning] [File Watcher (node.js)] Watcher shutdown because watched path got deleted
2025-06-22 19:11:44.482 [info] New EH opened, aborting shutdown
2025-06-22 19:25:17.210 [info] Getting Manifest... augment.vscode-augment
2025-06-22 19:25:17.264 [info] Installing extension: augment.vscode-augment {"isMachineScoped":false,"installPreReleaseVersion":false,"pinned":false,"donotVerifySignature":false,"context":{"clientTargetPlatform":"win32-x64"},"isApplicationScoped":false,"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.vscode-server/extensions/extensions.json","external":"file:///home/<USER>/.vscode-server/extensions/extensions.json","path":"/home/<USER>/.vscode-server/extensions/extensions.json","scheme":"file"},"productVersion":{"version":"1.101.1","date":"2025-06-18T13:35:12.605Z"}}
2025-06-22 19:25:19.121 [info] Extension signature verification result for augment.vscode-augment: Success. Internal Code: 0. Executed: true. Duration: 1341ms.
2025-06-22 19:25:19.889 [info] Extracted extension to file:///home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.482.1: augment.vscode-augment
2025-06-22 19:25:19.987 [info] Renamed to /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.482.1
2025-06-22 19:25:20.000 [info] Extension installed successfully: augment.vscode-augment file:///home/<USER>/.vscode-server/extensions/extensions.json
2025-06-22 19:25:38.655 [info] [<unknown>][5c9b0196][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
2025-06-22 19:25:38.739 [info] [<unknown>][65c3505c][ExtensionHostConnection] <3534> Extension Host Process exited with code: 0, signal: null.
2025-06-22 19:25:38.739 [info] Last EH closed, waiting before shutting down
2025-06-22 19:25:42.204 [info] [<unknown>][b828dfe0][ManagementConnection] New connection established.
2025-06-22 19:25:42.225 [info] [<unknown>][a6f9fe0d][ExtensionHostConnection] New connection established.
2025-06-22 19:25:42.230 [info] [<unknown>][a6f9fe0d][ExtensionHostConnection] <5018> Launched Extension Host Process.
2025-06-22 19:25:44.590 [error] CodeExpectedError: Could not find pty 16 on pty host
    at O.W (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:46:6391)
    at O.updateTitle (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:46:1839)
    at R.s.<computed> (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:45:2933)
    at Object.call (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:28:4204)
    at ml.s (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:81286)
    at ml.q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:80809)
    at ps.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:80211)
    at E.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:2373)
    at E.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:2591)
    at process.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:24:30052)
    at process.emit (node:events:518:28)
    at emit (node:internal/child_process:949:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)
2025-06-22 19:30:38.740 [info] New EH opened, aborting shutdown
