2025-06-22 19:25:45.649 [info] [main] Log level: Info
2025-06-22 19:25:45.649 [info] [main] Validating found git in: "git"
2025-06-22 19:25:45.649 [info] [main] Using git "2.43.0" from "git"
2025-06-22 19:25:45.649 [info] [Model][doInitialScan] Initial repository scan started
2025-06-22 19:25:45.649 [info] > git rev-parse --show-toplevel [809ms]
2025-06-22 19:25:45.649 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-22 19:25:45.649 [info] > git rev-parse --show-toplevel [6ms]
2025-06-22 19:25:45.649 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-22 19:25:45.649 [info] [Model][doInitialScan] Initial repository scan completed - repositories (0), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-22 19:27:31.628 [info] > git rev-parse --show-toplevel [9ms]
2025-06-22 19:27:31.638 [info] > git rev-parse --git-dir --git-common-dir [6ms]
2025-06-22 19:27:31.654 [info] [Model][openRepository] Opened repository (path): /home/<USER>/myproject/algofactory
2025-06-22 19:27:31.654 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/myproject/algofactory
2025-06-22 19:27:31.667 [info] > git config --get commit.template [6ms]
2025-06-22 19:27:31.673 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-22 19:27:31.676 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:27:31.677 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 19:27:31.802 [info] > git status -z -uall [121ms]
2025-06-22 19:27:31.825 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [139ms]
2025-06-22 19:27:32.090 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [101ms]
2025-06-22 19:27:32.097 [info] > git config --get --local branch.main.vscode-merge-base [3ms]
2025-06-22 19:27:32.102 [info] > git config --get commit.template [13ms]
2025-06-22 19:27:32.109 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-22 19:27:32.109 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [7ms]
2025-06-22 19:27:32.117 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:27:32.117 [info] > git merge-base refs/heads/main refs/remotes/origin/main [4ms]
2025-06-22 19:27:32.122 [info] > git rev-parse refs/remotes/origin/main [5ms]
2025-06-22 19:27:32.138 [info] > git diff --name-status -z --diff-filter=ADMR 89ac1fedef89963fb80f591849bbcbe1dc91b13e...refs/remotes/origin/main [16ms]
2025-06-22 19:27:32.141 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-22 19:27:32.237 [info] > git status -z -uall [111ms]
2025-06-22 19:27:32.237 [info] > git show --textconv :.env [8ms]
2025-06-22 19:27:32.257 [info] > git ls-files --stage -- .env [23ms]
2025-06-22 19:27:32.275 [info] > git hash-object -t tree /dev/null [12ms]
2025-06-22 19:27:32.275 [warning] [GitFileSystemProvider][stat] File not found - git:/home/<USER>/myproject/algofactory/.env.git?%7B%22path%22%3A%22%2Fhome%2Fubuntu%2Fmyproject%2Falgofactory%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-22 19:27:32.278 [info] > git hash-object -t tree /dev/null [9ms]
2025-06-22 19:27:32.278 [warning] [GitFileSystemProvider][readFile] File not found - git:/home/<USER>/myproject/algofactory/.env.git?%7B%22path%22%3A%22%2Fhome%2Fubuntu%2Fmyproject%2Falgofactory%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-22 19:27:32.305 [info] > git check-ignore -v -z --stdin [6ms]
2025-06-22 19:27:36.135 [info] > git blame --root --incremental 89ac1fedef89963fb80f591849bbcbe1dc91b13e -- .env [1ms]
2025-06-22 19:27:36.135 [info] fatal: no such path .env in 89ac1fedef89963fb80f591849bbcbe1dc91b13e
2025-06-22 19:27:43.581 [info] > git config --get commit.template [4ms]
2025-06-22 19:27:43.584 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-22 19:27:43.590 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:27:43.592 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-22 19:27:43.606 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-22 19:27:43.631 [info] > git status -z -uall [36ms]
2025-06-22 19:27:50.754 [info] > git config --get commit.template [65ms]
2025-06-22 19:27:50.757 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [63ms]
2025-06-22 19:27:50.762 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:27:50.763 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 19:27:50.781 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-22 19:27:50.802 [info] > git status -z -uall [36ms]
2025-06-22 19:27:55.820 [info] > git config --get commit.template [2ms]
2025-06-22 19:27:55.825 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-22 19:27:55.829 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:27:55.829 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 19:27:55.845 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-22 19:27:55.867 [info] > git status -z -uall [35ms]
2025-06-22 19:28:00.936 [info] > git config --get commit.template [4ms]
2025-06-22 19:28:00.937 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-22 19:28:00.941 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:28:00.945 [info] > git rev-parse refs/remotes/origin/main [4ms]
2025-06-22 19:28:00.956 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-22 19:28:00.980 [info] > git status -z -uall [32ms]
2025-06-22 19:28:06.000 [info] > git config --get commit.template [4ms]
2025-06-22 19:28:06.001 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-22 19:28:06.005 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:28:06.006 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 19:28:06.018 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-22 19:28:06.042 [info] > git status -z -uall [34ms]
2025-06-22 19:28:12.410 [info] > git config --get commit.template [5ms]
2025-06-22 19:28:12.411 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-22 19:28:12.414 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:28:12.415 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-22 19:28:12.427 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-22 19:28:12.451 [info] > git status -z -uall [34ms]
2025-06-22 19:28:17.473 [info] > git config --get commit.template [6ms]
2025-06-22 19:28:17.474 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-22 19:28:17.478 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:28:17.478 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-22 19:28:17.491 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-22 19:28:17.515 [info] > git status -z -uall [33ms]
2025-06-22 19:28:52.554 [info] > git config --get commit.template [1ms]
2025-06-22 19:28:52.559 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-22 19:28:52.564 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:28:52.565 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 19:28:52.577 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-22 19:28:52.600 [info] > git status -z -uall [32ms]
2025-06-22 19:29:14.547 [info] > git config --get commit.template [4ms]
2025-06-22 19:29:14.549 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-22 19:29:14.553 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:29:14.553 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-22 19:29:14.570 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-22 19:29:14.597 [info] > git status -z -uall [39ms]
2025-06-22 19:29:43.538 [info] > git config --get commit.template [1ms]
2025-06-22 19:29:43.543 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-22 19:29:43.546 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:29:43.547 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 19:29:43.561 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-22 19:29:43.585 [info] > git status -z -uall [34ms]
2025-06-22 19:29:48.611 [info] > git config --get commit.template [6ms]
2025-06-22 19:29:48.611 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-22 19:29:48.614 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:29:48.615 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 19:29:48.628 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-22 19:29:48.654 [info] > git status -z -uall [36ms]
2025-06-22 19:32:26.256 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-22 19:33:51.588 [info] > git config --get commit.template [4ms]
2025-06-22 19:33:51.591 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-22 19:33:51.594 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:33:51.595 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 19:33:51.611 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-22 19:33:51.631 [info] > git status -z -uall [33ms]
2025-06-22 19:33:58.503 [info] > git check-ignore -v -z --stdin [3ms]
2025-06-22 19:34:02.919 [info] > git show --textconv :start.sh [5ms]
2025-06-22 19:34:02.921 [info] > git ls-files --stage -- start.sh [2ms]
2025-06-22 19:34:02.925 [info] > git cat-file -s 6fec8e8c92ff7dfbd30745f7218074badabc077b [1ms]
2025-06-22 19:34:19.127 [info] > git config --get commit.template [6ms]
2025-06-22 19:34:19.127 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-22 19:34:19.132 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:34:19.132 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-22 19:34:19.145 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-22 19:34:19.170 [info] > git status -z -uall [36ms]
2025-06-22 19:36:03.967 [info] > git config --get commit.template [9ms]
2025-06-22 19:36:03.971 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-22 19:36:03.975 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:36:03.976 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 19:36:04.000 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-06-22 19:36:04.051 [info] > git status -z -uall [70ms]
2025-06-22 19:36:22.171 [info] > git config --get commit.template [8ms]
2025-06-22 19:36:22.171 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-22 19:36:22.175 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:36:22.175 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-22 19:36:22.194 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-22 19:36:22.229 [info] > git status -z -uall [49ms]
2025-06-22 19:36:35.595 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [115ms]
2025-06-22 19:36:35.598 [info] > git config --get commit.template [124ms]
2025-06-22 19:36:35.606 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:36:35.612 [info] > git rev-parse refs/remotes/origin/main [6ms]
2025-06-22 19:36:35.632 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-06-22 19:36:36.239 [info] > git status -z -uall [622ms]
2025-06-22 19:36:41.272 [info] > git config --get commit.template [6ms]
2025-06-22 19:36:41.275 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-22 19:36:41.279 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:36:41.279 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 19:36:41.288 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-22 19:36:41.554 [info] > git status -z -uall [272ms]
2025-06-22 19:36:54.865 [info] > git config --get commit.template [34ms]
2025-06-22 19:36:54.883 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [48ms]
2025-06-22 19:36:54.887 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:36:54.889 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-22 19:36:54.904 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-22 19:36:55.425 [info] > git status -z -uall [532ms]
2025-06-22 19:37:17.594 [info] > git config --get commit.template [26ms]
2025-06-22 19:37:17.611 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [39ms]
2025-06-22 19:37:17.614 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:37:17.616 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-22 19:37:17.634 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-22 19:37:18.150 [info] > git status -z -uall [531ms]
2025-06-22 19:37:23.179 [info] > git config --get commit.template [6ms]
2025-06-22 19:37:23.184 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-22 19:37:23.190 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:37:23.192 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-22 19:37:23.214 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-22 19:37:23.256 [info] > git status -z -uall [58ms]
2025-06-22 19:37:28.307 [info] > git config --get commit.template [12ms]
2025-06-22 19:37:28.313 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-22 19:37:28.320 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:37:28.322 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-22 19:37:28.342 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-22 19:37:28.908 [info] > git status -z -uall [576ms]
2025-06-22 19:37:33.958 [info] > git config --get commit.template [7ms]
2025-06-22 19:37:33.959 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-22 19:37:33.963 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:37:33.964 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 19:37:33.974 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-22 19:37:34.187 [info] > git status -z -uall [220ms]
2025-06-22 19:37:42.909 [info] > git config --get commit.template [34ms]
2025-06-22 19:37:42.926 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [46ms]
2025-06-22 19:37:42.930 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:37:42.932 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-22 19:37:42.951 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-06-22 19:37:43.470 [info] > git status -z -uall [534ms]
2025-06-22 19:37:48.502 [info] > git config --get commit.template [5ms]
2025-06-22 19:37:48.503 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-22 19:37:48.506 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:37:48.508 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-22 19:37:48.523 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-22 19:37:48.546 [info] > git status -z -uall [34ms]
2025-06-22 19:40:11.256 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-22 19:46:49.732 [info] > git config --get commit.template [5ms]
2025-06-22 19:46:49.734 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-22 19:46:49.737 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:46:49.738 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 19:46:49.752 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-22 19:46:49.777 [info] > git status -z -uall [36ms]
2025-06-22 19:46:57.775 [info] > git config --get commit.template [3ms]
2025-06-22 19:46:57.781 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-22 19:46:57.784 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:46:57.785 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 19:46:57.798 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-22 19:46:57.823 [info] > git status -z -uall [35ms]
2025-06-22 19:47:00.522 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-22 19:48:24.386 [info] > git config --get commit.template [43ms]
2025-06-22 19:48:24.399 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [58ms]
2025-06-22 19:48:24.420 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:48:24.437 [info] > git rev-parse refs/remotes/origin/main [17ms]
2025-06-22 19:48:24.473 [info] > git check-ignore -v -z --stdin [40ms]
2025-06-22 19:48:24.484 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [19ms]
2025-06-22 19:48:25.012 [info] > git status -z -uall [562ms]
2025-06-22 19:50:09.204 [info] > git config --get commit.template [6ms]
2025-06-22 19:50:09.216 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [14ms]
2025-06-22 19:50:09.219 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:50:09.222 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-06-22 19:50:09.238 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-22 19:50:09.750 [info] > git status -z -uall [525ms]
2025-06-22 19:50:49.105 [info] > git config --get commit.template [1ms]
2025-06-22 19:50:49.110 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-22 19:50:49.114 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:50:49.114 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 19:50:49.127 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-22 19:50:49.151 [info] > git status -z -uall [33ms]
2025-06-22 19:51:34.417 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-22 19:51:34.418 [info] > git config --get commit.template [12ms]
2025-06-22 19:51:34.423 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:51:34.427 [info] > git rev-parse refs/remotes/origin/main [4ms]
2025-06-22 19:51:34.450 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [12ms]
2025-06-22 19:51:34.486 [info] > git status -z -uall [53ms]
2025-06-22 19:51:39.506 [info] > git config --get commit.template [2ms]
2025-06-22 19:51:39.512 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-22 19:51:39.515 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:51:39.517 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-22 19:51:39.535 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-22 19:51:39.574 [info] > git status -z -uall [54ms]
2025-06-22 19:51:44.681 [info] > git config --get commit.template [46ms]
2025-06-22 19:51:44.775 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [137ms]
2025-06-22 19:51:44.781 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:51:44.785 [info] > git rev-parse refs/remotes/origin/main [4ms]
2025-06-22 19:51:44.807 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [14ms]
2025-06-22 19:51:45.329 [info] > git status -z -uall [539ms]
2025-06-22 19:52:23.713 [info] > git config --get commit.template [34ms]
2025-06-22 19:52:23.726 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [44ms]
2025-06-22 19:52:23.731 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:52:23.733 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-22 19:52:23.749 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-22 19:52:24.275 [info] > git status -z -uall [539ms]
2025-06-22 19:52:36.298 [info] > git config --get commit.template [7ms]
2025-06-22 19:52:36.298 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-22 19:52:36.301 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:52:36.302 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 19:52:36.314 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-22 19:52:36.703 [info] > git status -z -uall [397ms]
