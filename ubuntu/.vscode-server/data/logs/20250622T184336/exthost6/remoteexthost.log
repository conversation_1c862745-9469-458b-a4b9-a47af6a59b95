2025-06-22 19:25:43.889 [info] Extension host with pid 5018 started
2025-06-22 19:25:43.891 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/vscode.lock': Lock acquired.
2025-06-22 19:25:44.074 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-06-22 19:25:44.076 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: false, activationEvent: '*', root cause: vscode.git
2025-06-22 19:25:44.381 [info] ExtensionService#_doActivateExtension vscode.git, startup: false, activationEvent: '*'
2025-06-22 19:25:44.381 [info] ExtensionService#_doActivateExtension vscode.github, startup: false, activationEvent: '*'
2025-06-22 19:25:44.871 [info] Eager extensions activated
2025-06-22 19:25:44.872 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-06-22 19:25:44.872 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-06-22 19:25:44.872 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onStartupFinished'
2025-06-22 19:25:52.572 [error] CodeExpectedError: cannot open vscode-userdata:c%3A%5CUsers%5Canita%5CAppData%5CRoaming%5CCode%5CUser%5Ckeybindings.json. Detail: Unable to resolve filesystem provider with relative file path 'vscode-userdata:c:\Users\<USER>\AppData\Roaming\Code\User\keybindings.json'
    at Pze.$tryOpenDocument (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/workbench.desktop.main.js:1283:8666)
2025-06-22 19:27:31.917 [warning] [Decorations] CAPPING events from decorations provider vscode.git 457
2025-06-22 19:27:32.248 [warning] [Decorations] CAPPING events from decorations provider vscode.git 457
2025-06-22 19:27:43.639 [warning] [Decorations] CAPPING events from decorations provider vscode.git 457
2025-06-22 19:27:50.809 [warning] [Decorations] CAPPING events from decorations provider vscode.git 457
2025-06-22 19:27:55.872 [warning] [Decorations] CAPPING events from decorations provider vscode.git 457
2025-06-22 19:28:00.986 [warning] [Decorations] CAPPING events from decorations provider vscode.git 457
2025-06-22 19:28:06.071 [warning] [Decorations] CAPPING events from decorations provider vscode.git 457
2025-06-22 19:28:12.457 [warning] [Decorations] CAPPING events from decorations provider vscode.git 457
2025-06-22 19:28:17.520 [warning] [Decorations] CAPPING events from decorations provider vscode.git 457
2025-06-22 19:28:52.612 [warning] [Decorations] CAPPING events from decorations provider vscode.git 457
2025-06-22 19:29:14.606 [warning] [Decorations] CAPPING events from decorations provider vscode.git 457
2025-06-22 19:29:43.592 [warning] [Decorations] CAPPING events from decorations provider vscode.git 457
2025-06-22 19:29:48.664 [warning] [Decorations] CAPPING events from decorations provider vscode.git 457
