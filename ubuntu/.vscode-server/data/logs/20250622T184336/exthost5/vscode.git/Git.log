2025-06-22 19:07:29.294 [info] [main] Log level: Info
2025-06-22 19:07:29.294 [info] [main] Validating found git in: "git"
2025-06-22 19:07:29.294 [info] [main] Using git "2.43.0" from "git"
2025-06-22 19:07:29.294 [info] [Model][doInitialScan] Initial repository scan started
2025-06-22 19:07:29.294 [info] > git rev-parse --show-toplevel [42ms]
2025-06-22 19:07:29.294 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-22 19:07:29.294 [info] > git rev-parse --show-toplevel [96ms]
2025-06-22 19:07:29.294 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-22 19:07:29.295 [info] > git rev-parse --show-toplevel [2ms]
2025-06-22 19:07:29.295 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-22 19:07:29.300 [info] > git rev-parse --show-toplevel [2ms]
2025-06-22 19:07:29.300 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-22 19:07:29.305 [info] > git rev-parse --show-toplevel [2ms]
2025-06-22 19:07:29.305 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-22 19:07:29.308 [info] > git rev-parse --show-toplevel [1ms]
2025-06-22 19:07:29.308 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-22 19:07:29.311 [info] [Model][doInitialScan] Initial repository scan completed - repositories (0), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-22 19:10:45.846 [info] > git rev-parse --show-toplevel [2ms]
2025-06-22 19:10:45.846 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-22 19:10:51.581 [info] > git rev-parse --show-toplevel [3ms]
2025-06-22 19:10:51.588 [info] > git rev-parse --git-dir --git-common-dir [3ms]
2025-06-22 19:10:51.600 [info] [Model][openRepository] Opened repository (path): /home/<USER>/myproject/openalgo
2025-06-22 19:10:51.600 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/myproject/openalgo
2025-06-22 19:10:51.610 [info] > git config --get commit.template [5ms]
2025-06-22 19:10:51.616 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-22 19:10:51.630 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-22 19:10:51.633 [info] > git status -z -uall [13ms]
2025-06-22 19:10:51.853 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [119ms]
2025-06-22 19:10:51.859 [info] > git config --get commit.template [14ms]
2025-06-22 19:10:51.866 [info] > git config --get --local branch.main.vscode-merge-base [9ms]
2025-06-22 19:10:51.869 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [1ms]
2025-06-22 19:10:51.873 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-06-22 19:10:51.880 [info] > git merge-base refs/heads/main refs/remotes/origin/main [7ms]
2025-06-22 19:10:51.890 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-22 19:10:51.891 [info] > git status -z -uall [15ms]
2025-06-22 19:10:51.929 [info] > git merge-base refs/heads/main refs/remotes/origin/main [6ms]
2025-06-22 19:10:51.941 [info] > git diff --name-status -z --diff-filter=ADMR 187a66e4c9f9978396adc75b3bca6d123322e50a...refs/remotes/origin/main [53ms]
2025-06-22 19:10:51.951 [info] > git diff --name-status -z --diff-filter=ADMR 187a66e4c9f9978396adc75b3bca6d123322e50a...refs/remotes/origin/main [9ms]
2025-06-22 19:11:19.551 [info] > git config --get commit.template [1ms]
2025-06-22 19:11:19.554 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-22 19:11:19.572 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-06-22 19:11:19.587 [info] > git status -z -uall [29ms]
2025-06-22 19:11:22.017 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-22 19:11:22.523 [info] > git check-ignore -v -z --stdin [3ms]
2025-06-22 19:11:24.626 [info] > git config --get commit.template [2ms]
2025-06-22 19:11:24.630 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-22 19:11:24.642 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-22 19:11:24.669 [info] > git status -z -uall [36ms]
2025-06-22 19:11:25.195 [info] > git check-ignore -v -z --stdin [3ms]
2025-06-22 19:11:26.051 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-22 19:11:26.615 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-22 19:11:27.771 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-22 19:11:29.692 [info] > git config --get commit.template [0ms]
2025-06-22 19:11:29.696 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-22 19:11:29.709 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-22 19:11:29.735 [info] > git status -z -uall [34ms]
2025-06-22 19:11:42.312 [warning] [Git][getHEAD] Failed to parse HEAD file: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/HEAD'
2025-06-22 19:11:42.317 [warning] [Git][getRemotes] Error: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/config'
2025-06-22 19:11:42.323 [warning] [Git][getHEAD] Failed to parse HEAD file: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/HEAD'
2025-06-22 19:11:42.325 [warning] [Git][getRemotes] Error: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/config'
2025-06-22 19:12:35.311 [info] > git rev-parse --show-toplevel [1ms]
2025-06-22 19:12:35.311 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-22 19:12:40.787 [info] > git rev-parse --show-toplevel [1ms]
2025-06-22 19:12:40.792 [info] > git rev-parse --git-dir --git-common-dir [3ms]
2025-06-22 19:12:40.795 [info] [Model][openRepository] Opened repository (path): /home/<USER>/myproject/openalgo
2025-06-22 19:12:40.795 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/myproject/openalgo
2025-06-22 19:12:40.799 [info] > git config --get commit.template [1ms]
2025-06-22 19:12:40.804 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-22 19:12:40.813 [info] > git status -z -uall [7ms]
2025-06-22 19:12:40.822 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-22 19:12:40.864 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [11ms]
2025-06-22 19:12:40.884 [info] > git config --get --local branch.main.vscode-merge-base [2ms]
2025-06-22 19:12:40.892 [info] > git config --get commit.template [30ms]
2025-06-22 19:12:41.027 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [140ms]
2025-06-22 19:12:41.033 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [11ms]
2025-06-22 19:12:41.035 [info] > git merge-base refs/heads/main refs/remotes/origin/main [3ms]
2025-06-22 19:12:41.049 [info] > git diff --name-status -z --diff-filter=ADMR 187a66e4c9f9978396adc75b3bca6d123322e50a...refs/remotes/origin/main [10ms]
2025-06-22 19:12:41.053 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-22 19:12:41.057 [info] > git status -z -uall [14ms]
2025-06-22 19:12:41.529 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-22 19:12:44.122 [info] > git config --get commit.template [0ms]
2025-06-22 19:12:44.126 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-22 19:12:44.141 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-22 19:12:44.144 [info] > git status -z -uall [14ms]
2025-06-22 19:12:46.548 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-22 19:12:54.520 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-22 19:12:55.557 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-22 19:12:56.318 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-22 19:12:57.335 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-22 19:12:58.854 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-22 19:12:59.874 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-22 19:13:00.920 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-22 19:13:01.929 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-22 19:13:03.395 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-22 19:13:06.051 [info] > git check-ignore -v -z --stdin [5ms]
2025-06-22 19:13:06.847 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-22 19:13:11.084 [info] > git config --get commit.template [0ms]
2025-06-22 19:13:11.089 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-22 19:13:11.105 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-22 19:13:11.134 [info] > git status -z -uall [42ms]
2025-06-22 19:17:30.787 [info] > git config --get commit.template [19ms]
2025-06-22 19:17:30.799 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [28ms]
2025-06-22 19:17:30.816 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-06-22 19:17:31.197 [info] > git status -z -uall [395ms]
2025-06-22 19:19:46.799 [info] > git rev-parse --show-toplevel [4ms]
2025-06-22 19:19:46.799 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-22 19:19:47.500 [info] > git rev-parse --show-toplevel [0ms]
2025-06-22 19:19:47.500 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-22 19:19:49.143 [info] > git rev-parse --show-toplevel [0ms]
2025-06-22 19:19:49.147 [info] > git rev-parse --git-dir --git-common-dir [1ms]
2025-06-22 19:19:49.152 [info] [Model][openRepository] Opened repository (path): /home/<USER>/myproject/algofactory
2025-06-22 19:19:49.152 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/myproject/algofactory
2025-06-22 19:19:49.158 [info] > git config --get commit.template [2ms]
2025-06-22 19:19:49.162 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-22 19:19:49.165 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:19:49.166 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 19:19:49.175 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-22 19:19:49.178 [info] > git status -z -uall [9ms]
2025-06-22 19:19:49.209 [info] > git config --get commit.template [0ms]
2025-06-22 19:19:49.209 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-22 19:19:49.212 [info] > git config --get --local branch.main.vscode-merge-base [1ms]
2025-06-22 19:19:49.218 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [3ms]
2025-06-22 19:19:49.226 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-22 19:19:49.228 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:19:49.239 [info] > git merge-base refs/heads/main refs/remotes/origin/main [17ms]
2025-06-22 19:19:49.396 [info] > git diff --name-status -z --diff-filter=ADMR 89ac1fedef89963fb80f591849bbcbe1dc91b13e...refs/remotes/origin/main [140ms]
2025-06-22 19:19:49.397 [info] > git rev-parse refs/remotes/origin/main [169ms]
2025-06-22 19:19:49.415 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-22 19:19:49.416 [info] > git status -z -uall [15ms]
2025-06-22 19:19:49.732 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-22 19:20:07.808 [info] > git check-ignore -v -z --stdin [6ms]
2025-06-22 19:20:07.808 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-22 19:20:33.171 [info] > git config --get commit.template [0ms]
2025-06-22 19:20:33.175 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-22 19:20:33.178 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:20:33.178 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-22 19:20:33.190 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-22 19:20:33.216 [info] > git status -z -uall [35ms]
2025-06-22 19:20:35.057 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-22 19:20:36.371 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-22 19:20:51.031 [info] > git check-ignore -v -z --stdin [5ms]
2025-06-22 19:20:58.426 [info] > git check-ignore -v -z --stdin [3ms]
2025-06-22 19:21:11.717 [info] > git config --get commit.template [4ms]
2025-06-22 19:21:11.725 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-22 19:21:11.727 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:21:11.728 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 19:21:11.743 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-22 19:21:11.976 [info] > git status -z -uall [244ms]
2025-06-22 19:21:39.218 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-22 19:21:41.995 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-22 19:21:42.877 [info] > git show --textconv :.env [4ms]
2025-06-22 19:21:42.882 [info] > git hash-object -t tree /dev/null [1ms]
2025-06-22 19:21:42.882 [warning] [GitFileSystemProvider][readFile] File not found - git:/home/<USER>/myproject/algofactory/.env.git?%7B%22path%22%3A%22%2Fhome%2Fubuntu%2Fmyproject%2Falgofactory%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-22 19:21:42.885 [info] > git ls-files --stage -- .env [9ms]
2025-06-22 19:21:42.885 [warning] [GitFileSystemProvider][stat] File not found - git:/home/<USER>/myproject/algofactory/.env.git?%7B%22path%22%3A%22%2Fhome%2Fubuntu%2Fmyproject%2Falgofactory%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-22 19:25:20.178 [info] > git rev-parse --show-toplevel [0ms]
2025-06-22 19:25:20.178 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-22 19:25:20.955 [info] > git rev-parse --show-toplevel [16ms]
2025-06-22 19:25:20.955 [info] fatal: not a git repository (or any of the parent directories): .git
