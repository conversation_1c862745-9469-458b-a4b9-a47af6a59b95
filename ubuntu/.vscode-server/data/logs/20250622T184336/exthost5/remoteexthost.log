2025-06-22 19:07:28.357 [info] Extension host with pid 3534 started
2025-06-22 19:07:28.357 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/8fb0da0c66a291b7cbfae207d20e731c/vscode.lock': Lock acquired.
2025-06-22 19:07:28.677 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-06-22 19:07:28.680 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: false, activationEvent: '*', root cause: vscode.git
2025-06-22 19:07:28.848 [info] ExtensionService#_doActivateExtension vscode.git, startup: false, activationEvent: '*'
2025-06-22 19:07:28.848 [info] ExtensionService#_doActivateExtension vscode.github, startup: false, activationEvent: '*'
2025-06-22 19:07:29.139 [info] Eager extensions activated
2025-06-22 19:07:29.141 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-06-22 19:07:29.141 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-06-22 19:10:51.679 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1022
2025-06-22 19:10:51.915 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1022
2025-06-22 19:11:19.608 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1015
2025-06-22 19:11:24.685 [warning] [Decorations] CAPPING events from decorations provider vscode.git 608
2025-06-22 19:11:29.739 [warning] [Decorations] CAPPING events from decorations provider vscode.git 482
2025-06-22 19:12:40.849 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1022
2025-06-22 19:12:41.088 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1022
2025-06-22 19:12:44.166 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1015
2025-06-22 19:13:11.143 [warning] [Decorations] CAPPING events from decorations provider vscode.git 958
2025-06-22 19:17:29.602 [info] ExtensionService#_doActivateExtension vscode.tunnel-forwarding, startup: false, activationEvent: 'onTunnel'
2025-06-22 19:17:31.206 [warning] [Decorations] CAPPING events from decorations provider vscode.git 454
2025-06-22 19:19:49.197 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1016
2025-06-22 19:19:49.430 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1016
2025-06-22 19:20:33.225 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1011
2025-06-22 19:21:11.985 [warning] [Decorations] CAPPING events from decorations provider vscode.git 457
2025-06-22 19:25:20.328 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onStartupFinished'
2025-06-22 19:25:27.589 [error] CodeExpectedError: cannot open vscode-userdata:c%3A%5CUsers%5Canita%5CAppData%5CRoaming%5CCode%5CUser%5Ckeybindings.json. Detail: Unable to resolve filesystem provider with relative file path 'vscode-userdata:c:\Users\<USER>\AppData\Roaming\Code\User\keybindings.json'
    at Pze.$tryOpenDocument (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/workbench.desktop.main.js:1283:8666)
2025-06-22 19:25:38.659 [info] Extension host terminating: received terminate message from renderer
2025-06-22 19:25:38.659 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/8fb0da0c66a291b7cbfae207d20e731c/vscode.lock': Marking the lockfile as scheduled to be released in 6000 ms.
2025-06-22 19:25:38.716 [info] Extension host with pid 3534 exiting with code 0
