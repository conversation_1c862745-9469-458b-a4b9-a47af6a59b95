2025-06-23 23:07:54.735 [info] [main] Log level: Info
2025-06-23 23:07:54.735 [info] [main] Validating found git in: "git"
2025-06-23 23:07:54.735 [info] [main] Using git "2.43.0" from "git"
2025-06-23 23:07:54.735 [info] [Model][doInitialScan] Initial repository scan started
2025-06-23 23:07:54.735 [info] > git rev-parse --show-toplevel [6ms]
2025-06-23 23:07:54.735 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-23 23:07:54.735 [info] > git rev-parse --show-toplevel [1016ms]
2025-06-23 23:07:54.845 [info] > git rev-parse --git-dir --git-common-dir [110ms]
2025-06-23 23:07:54.872 [info] [Model][openRepository] Opened repository (path): /home/<USER>/algofactory-base
2025-06-23 23:07:54.872 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/algofactory-base
2025-06-23 23:07:54.934 [info] > git rev-parse --show-toplevel [43ms]
2025-06-23 23:07:54.934 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-23 23:07:54.951 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-23 23:07:54.958 [info] > git config --get commit.template [75ms]
2025-06-23 23:07:54.993 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [15ms]
2025-06-23 23:07:55.006 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-base/.git/refs/remotes/origin/main'
2025-06-23 23:07:55.038 [info] > git rev-parse refs/remotes/origin/main [32ms]
2025-06-23 23:07:55.074 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-23 23:07:55.171 [info] > git status -z -uall [120ms]
2025-06-23 23:07:55.277 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [18ms]
2025-06-23 23:07:55.407 [info] > git config --get --local branch.main.vscode-merge-base [126ms]
2025-06-23 23:07:55.413 [info] > git config --get commit.template [144ms]
2025-06-23 23:07:55.424 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [11ms]
2025-06-23 23:07:55.569 [info] > git merge-base refs/heads/main refs/remotes/origin/main [140ms]
2025-06-23 23:07:55.573 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [152ms]
2025-06-23 23:07:55.577 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-base/.git/refs/remotes/origin/main'
2025-06-23 23:07:55.590 [info] > git show --textconv :.env [6ms]
2025-06-23 23:07:55.593 [info] > git rev-parse refs/remotes/origin/main [16ms]
2025-06-23 23:07:55.603 [info] > git diff --name-status -z --diff-filter=ADMR 89ac1fedef89963fb80f591849bbcbe1dc91b13e...refs/remotes/origin/main [30ms]
2025-06-23 23:07:55.625 [info] > git status -z -uall [29ms]
2025-06-23 23:07:55.625 [info] > git hash-object -t tree /dev/null [32ms]
2025-06-23 23:07:55.625 [warning] [GitFileSystemProvider][readFile] File not found - git:/home/<USER>/algofactory-base/.env.git?%7B%22path%22%3A%22%2Fhome%2Fubuntu%2Falgofactory-base%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-23 23:07:55.625 [info] > git ls-files --stage -- .env [38ms]
2025-06-23 23:07:55.626 [warning] [GitFileSystemProvider][stat] File not found - git:/home/<USER>/algofactory-base/.env.git?%7B%22path%22%3A%22%2Fhome%2Fubuntu%2Falgofactory-base%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-23 23:07:55.633 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [31ms]
2025-06-23 23:07:55.669 [info] > git check-ignore -v -z --stdin [4ms]
2025-06-23 23:13:18.722 [info] > git rev-parse --show-toplevel [79ms]
2025-06-23 23:13:18.722 [info] fatal: not a git repository (or any of the parent directories): .git
