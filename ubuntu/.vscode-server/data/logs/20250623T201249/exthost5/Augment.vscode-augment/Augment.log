2025-06-23 23:35:36.189 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-23 23:35:36.189 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"disableFocusOnAugmentPanel":false,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-06-23 23:35:36.189 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","enableNewThreadsList":false,"enableUntruncatedContentStorage":false,"maxLinesTerminalProcessOutput":0,"vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","vscodeSupportToolUseStartMinVersion":"","enableAgentAutoMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5,"historySummaryMinVersion":"","historySummaryMaxChars":0,"historySummaryLowerChars":0,"historySummaryPrompt":"","enableSpawnSubAgentTool":false,"enableCommitIndexing":false,"maxCommitsToIndex":0}
2025-06-23 23:35:36.189 [info] 'AugmentExtension' Retrieving model config
2025-06-23 23:35:36.831 [info] 'AugmentExtension' Retrieved model config
2025-06-23 23:35:36.831 [info] 'AugmentExtension' Returning model config
2025-06-23 23:35:36.960 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - vscodeShareMinVersion: "" to "0.314.0"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - enableNewThreadsList: false to true
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 49512
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.472.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - openFileManagerV2Enabled: false to true
  - vscodeTaskListMinVersion: "" to "0.482.0"
  - vscodeSupportToolUseStartMinVersion: "" to "0.485.0"
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
  - historySummaryMaxChars: 0 to 200000
  - historySummaryLowerChars: 0 to 80000
  - historySummaryPrompt: "" to "Your task is to create a detailed summary of the conversation so far, paying close attention to the user's explicit requests and your previous actions.\nThis summary should be thorough in capturing technical details, code patterns, and architectural decisions that would be essential for continuing with the conversation and supporting any continuing tasks.\n\nYour summary should be structured as follows:\nContext: The context to continue the conversation with. If applicable based on the current task, this should include:\n1. Previous Conversation: High level details about what was discussed throughout the entire conversation with the user. This should be written to allow someone to be able to follow the general overarching conversation flow.\n2. Current Work: Describe in detail what was being worked on prior to this request to summarize the conversation. Pay special attention to the more recent messages in the conversation.\n3. Key Technical Concepts: List all important technical concepts, technologies, coding conventions, and frameworks discussed, which might be relevant for continuing with this work.\n4. Relevant Files and Code: If applicable, enumerate specific files and code sections examined, modified, or created for the task continuation. Pay special attention to the most recent messages and changes.\n5. Problem Solving: Document problems solved thus far and any ongoing troubleshooting efforts.\n6. Pending Tasks and Next Steps: Outline all pending tasks that you have explicitly been asked to work on, as well as list the next steps you will take for all outstanding work, if applicable. Include code snippets where they add clarity. For any next steps, include direct quotes from the most recent conversation showing exactly what task you were working on and where you left off. This should be verbatim to ensure there's no information loss in context between tasks.\n\nExample summary structure:\n1. Previous Conversation:\n[Detailed description]\n2. Current Work:\n[Detailed description]\n3. Key Technical Concepts:\n- [Concept 1]\n- [Concept 2]\n- [...]\n4. Relevant Files and Code:\n- [File Name 1]\n    - [Summary of why this file is important]\n    - [Summary of the changes made to this file, if any]\n    - [Important Code Snippet]\n- [File Name 2]\n    - [Important Code Snippet]\n- [...]\n5. Problem Solving:\n[Detailed description]\n6. Pending Tasks and Next Steps:\n- [Task 1 details & next steps]\n- [Task 2 details & next steps]\n- [...]\n\nOutput only the summary of the conversation so far, without any additional commentary or explanation.\n"
2025-06-23 23:35:36.960 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    /home (explicit) at 6/22/2025, 7:25:55 PM
2025-06-23 23:35:36.960 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-06-23 23:35:36.960 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-06-23 23:35:36.960 [info] 'SyncingPermissionTracker' Permission to sync folder /home granted at 6/22/2025, 7:25:55 PM; type = explicit
2025-06-23 23:35:36.960 [info] 'WorkspaceManager' Adding workspace folder home; folderRoot = /home; syncingPermission = granted
2025-06-23 23:35:36.960 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home (explicit) at 6/22/2025, 7:25:55 PM
2025-06-23 23:35:37.024 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-06-23 23:35:37.024 [info] 'HotKeyHints' HotKeyHints initialized
2025-06-23 23:35:37.033 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-06-23 23:35:37.075 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-23 23:35:37.076 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-23 23:35:37.755 [info] 'WorkspaceManager[home]' Start tracking
2025-06-23 23:35:37.783 [info] 'PathMap' Opened source folder /home with id 100
2025-06-23 23:35:37.783 [info] 'OpenFileManager' Opened source folder 100
2025-06-23 23:35:37.801 [info] 'MtimeCache[home]' reading blob name cache from /home/<USER>/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/Augment.vscode-augment/2cc974af6afc822c42a4e914df05c697348b80420941f6b27561b2bf688587b7/mtime-cache.json
2025-06-23 23:35:38.423 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-23 23:35:38.423 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-06-23 23:35:38.513 [info] 'MtimeCache[home]' read 14661 entries from /home/<USER>/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/Augment.vscode-augment/2cc974af6afc822c42a4e914df05c697348b80420941f6b27561b2bf688587b7/mtime-cache.json
2025-06-23 23:35:39.669 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-23 23:35:39.669 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-23 23:35:39.669 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-23 23:35:39.669 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-06-23 23:36:50.291 [info] 'WorkspaceManager[home]' Tracking enabled
2025-06-23 23:36:50.291 [info] 'WorkspaceManager[home]' Path metrics:
  - directories emitted: 3073
  - files emitted: 18844
  - other paths emitted: 5
  - total paths emitted: 21922
  - timing stats:
    - readDir: 60 ms
    - filter: 532 ms
    - yield: 136 ms
    - total: 863 ms
2025-06-23 23:36:50.291 [info] 'WorkspaceManager[home]' File metrics:
  - paths accepted: 15163
  - paths not accessible: 0
  - not plain files: 0
  - large files: 108
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 14649
  - mtime cache misses: 514
  - probe batches: 33
  - blob names probed: 15176
  - files read: 4129
  - blobs uploaded: 15
  - timing stats:
    - ingestPath: 47 ms
    - probe: 25530 ms
    - stat: 124 ms
    - read: 7561 ms
    - upload: 3442 ms
2025-06-23 23:36:50.291 [info] 'WorkspaceManager[home]' Startup metrics:
  - create SourceFolder: 43 ms
  - read MtimeCache: 716 ms
  - pre-populate PathMap: 881 ms
  - create PathFilter: 594 ms
  - create PathNotifier: 0 ms
  - enumerate paths: 867 ms
  - purge stale PathMap entries: 7 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 69397 ms
  - enable persist: 30 ms
  - total: 72535 ms
2025-06-23 23:36:50.291 [info] 'WorkspaceManager' Workspace startup complete in 73346 ms
2025-06-23 23:38:57.115 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.vscode-server/data/logs/20250623T201249/exthost5/vscode.json-language-features
2025-06-23 23:39:19.583 [info] 'WorkspaceManager[home]' Directory created: ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/__pycache__
2025-06-23 23:39:19.584 [info] 'WorkspaceManager[home]' Directory created: ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_distutils/__pycache__
2025-06-23 23:39:19.584 [info] 'WorkspaceManager[home]' Directory created: ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__
2025-06-23 23:39:19.585 [info] 'WorkspaceManager[home]' Directory created: ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_distutils/compat/__pycache__
2025-06-23 23:39:19.585 [info] 'WorkspaceManager[home]' Directory created: ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_distutils/compilers/C/__pycache__
2025-06-23 23:39:19.585 [info] 'WorkspaceManager[home]' Directory created: ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/jaraco/__pycache__
2025-06-23 23:39:19.586 [info] 'WorkspaceManager[home]' Directory created: ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/jaraco/functools/__pycache__
2025-06-23 23:39:19.586 [info] 'WorkspaceManager[home]' Directory created: ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/jaraco/text/__pycache__
2025-06-23 23:39:19.586 [info] 'WorkspaceManager[home]' Directory created: ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/_vendor/more_itertools/__pycache__
2025-06-23 23:39:19.586 [info] 'WorkspaceManager[home]' Directory created: ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/command/__pycache__
2025-06-23 23:39:19.586 [info] 'WorkspaceManager[home]' Directory created: ubuntu/shared-venv/lib/python3.12/site-packages/setuptools/config/__pycache__
2025-06-23 23:39:53.370 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-06-23 23:39:54.489 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-06-23 23:39:54.489 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-06-23 23:39:54.492 [info] 'TaskManager' Setting current root task UUID to abd4627c-60c1-4f48-88a1-5f41783481ba
2025-06-23 23:39:54.818 [error] 'GitReferenceMessenger' Failed to locally get remote url: Failed to get remote url, no remote found
2025-06-23 23:39:55.814 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.vscode-server/data/logs/20250623T201249/exthost5/vscode.html-language-features
2025-06-23 23:44:03.508 [info] 'ToolFileUtils' Reading file: ubuntu/myproject/algofactory/admin/app.py
2025-06-23 23:44:03.664 [info] 'ToolFileUtils' Successfully read file: ubuntu/myproject/algofactory/admin/app.py (21508 bytes)
2025-06-23 23:44:05.034 [info] 'ToolFileUtils' Reading file: ubuntu/myproject/algofactory/admin/app.py
2025-06-23 23:44:05.034 [info] 'ToolFileUtils' Successfully read file: ubuntu/myproject/algofactory/admin/app.py (21454 bytes)
2025-06-23 23:44:30.049 [info] 'ToolFileUtils' Reading file: ubuntu/myproject/algofactory/admin/app.py
2025-06-23 23:44:30.049 [info] 'ToolFileUtils' Successfully read file: ubuntu/myproject/algofactory/admin/app.py (21454 bytes)
2025-06-23 23:44:31.354 [info] 'ToolFileUtils' Reading file: ubuntu/myproject/algofactory/admin/app.py
2025-06-23 23:44:31.354 [info] 'ToolFileUtils' Successfully read file: ubuntu/myproject/algofactory/admin/app.py (24993 bytes)
2025-06-23 23:45:22.922 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 10515 msec late.
2025-06-23 23:45:24.122 [info] 'ToolFileUtils' Reading file: ubuntu/myproject/algofactory/admin/templates/auth/login.html
2025-06-23 23:45:24.262 [info] 'ToolFileUtils' Successfully read file: ubuntu/myproject/algofactory/admin/templates/auth/login.html (11299 bytes)
2025-06-23 23:45:24.793 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.vscode-server/data/User/History/-6d745af2
2025-06-23 23:45:25.796 [info] 'ToolFileUtils' Reading file: ubuntu/myproject/algofactory/admin/templates/auth/login.html
2025-06-23 23:45:25.796 [info] 'ToolFileUtils' Successfully read file: ubuntu/myproject/algofactory/admin/templates/auth/login.html (11788 bytes)
2025-06-23 23:45:45.757 [info] 'ToolFileUtils' Reading file: ubuntu/myproject/algofactory/admin/templates/auth/login.html
2025-06-23 23:45:45.757 [info] 'ToolFileUtils' Successfully read file: ubuntu/myproject/algofactory/admin/templates/auth/login.html (11788 bytes)
2025-06-23 23:45:47.147 [info] 'ToolFileUtils' Reading file: ubuntu/myproject/algofactory/admin/templates/auth/login.html
2025-06-23 23:45:47.147 [info] 'ToolFileUtils' Successfully read file: ubuntu/myproject/algofactory/admin/templates/auth/login.html (12545 bytes)
2025-06-23 23:46:00.655 [info] 'ToolFileUtils' Reading file: ubuntu/myproject/algofactory/admin/templates/base.html
2025-06-23 23:46:00.722 [info] 'ToolFileUtils' Successfully read file: ubuntu/myproject/algofactory/admin/templates/base.html (20521 bytes)
2025-06-23 23:46:02.299 [info] 'ToolFileUtils' Reading file: ubuntu/myproject/algofactory/admin/templates/base.html
2025-06-23 23:46:02.299 [info] 'ToolFileUtils' Successfully read file: ubuntu/myproject/algofactory/admin/templates/base.html (20555 bytes)
2025-06-23 23:46:30.672 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-23 23:46:30.673 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"disableFocusOnAugmentPanel":false,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-06-23 23:46:30.673 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","enableNewThreadsList":false,"enableUntruncatedContentStorage":false,"maxLinesTerminalProcessOutput":0,"vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","vscodeSupportToolUseStartMinVersion":"","enableAgentAutoMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5,"historySummaryMinVersion":"","historySummaryMaxChars":0,"historySummaryLowerChars":0,"historySummaryPrompt":"","enableSpawnSubAgentTool":false,"enableCommitIndexing":false,"maxCommitsToIndex":0}
2025-06-23 23:46:31.466 [info] 'AugmentExtension' Retrieving model config
2025-06-23 23:46:32.283 [info] 'AugmentExtension' Retrieved model config
2025-06-23 23:46:32.283 [info] 'AugmentExtension' Returning model config
2025-06-23 23:46:32.348 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - vscodeShareMinVersion: "" to "0.314.0"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - enableNewThreadsList: false to true
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 49512
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.472.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - openFileManagerV2Enabled: false to true
  - vscodeTaskListMinVersion: "" to "0.482.0"
  - vscodeSupportToolUseStartMinVersion: "" to "0.485.0"
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
  - historySummaryMaxChars: 0 to 200000
  - historySummaryLowerChars: 0 to 80000
  - historySummaryPrompt: "" to "Your task is to create a detailed summary of the conversation so far, paying close attention to the user's explicit requests and your previous actions.\nThis summary should be thorough in capturing technical details, code patterns, and architectural decisions that would be essential for continuing with the conversation and supporting any continuing tasks.\n\nYour summary should be structured as follows:\nContext: The context to continue the conversation with. If applicable based on the current task, this should include:\n1. Previous Conversation: High level details about what was discussed throughout the entire conversation with the user. This should be written to allow someone to be able to follow the general overarching conversation flow.\n2. Current Work: Describe in detail what was being worked on prior to this request to summarize the conversation. Pay special attention to the more recent messages in the conversation.\n3. Key Technical Concepts: List all important technical concepts, technologies, coding conventions, and frameworks discussed, which might be relevant for continuing with this work.\n4. Relevant Files and Code: If applicable, enumerate specific files and code sections examined, modified, or created for the task continuation. Pay special attention to the most recent messages and changes.\n5. Problem Solving: Document problems solved thus far and any ongoing troubleshooting efforts.\n6. Pending Tasks and Next Steps: Outline all pending tasks that you have explicitly been asked to work on, as well as list the next steps you will take for all outstanding work, if applicable. Include code snippets where they add clarity. For any next steps, include direct quotes from the most recent conversation showing exactly what task you were working on and where you left off. This should be verbatim to ensure there's no information loss in context between tasks.\n\nExample summary structure:\n1. Previous Conversation:\n[Detailed description]\n2. Current Work:\n[Detailed description]\n3. Key Technical Concepts:\n- [Concept 1]\n- [Concept 2]\n- [...]\n4. Relevant Files and Code:\n- [File Name 1]\n    - [Summary of why this file is important]\n    - [Summary of the changes made to this file, if any]\n    - [Important Code Snippet]\n- [File Name 2]\n    - [Important Code Snippet]\n- [...]\n5. Problem Solving:\n[Detailed description]\n6. Pending Tasks and Next Steps:\n- [Task 1 details & next steps]\n- [Task 2 details & next steps]\n- [...]\n\nOutput only the summary of the conversation so far, without any additional commentary or explanation.\n"
2025-06-23 23:46:32.348 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    /home (explicit) at 6/22/2025, 7:25:55 PM
2025-06-23 23:46:32.348 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-06-23 23:46:32.348 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-06-23 23:46:32.348 [info] 'SyncingPermissionTracker' Permission to sync folder /home granted at 6/22/2025, 7:25:55 PM; type = explicit
2025-06-23 23:46:32.348 [info] 'WorkspaceManager' Adding workspace folder home; folderRoot = /home; syncingPermission = granted
2025-06-23 23:46:32.348 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home (explicit) at 6/22/2025, 7:25:55 PM
2025-06-23 23:46:32.402 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-06-23 23:46:32.402 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-06-23 23:46:32.402 [info] 'HotKeyHints' HotKeyHints initialized
2025-06-23 23:46:32.407 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-06-23 23:46:32.434 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-23 23:46:32.436 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-23 23:46:32.615 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-06-23 23:46:32.615 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-06-23 23:46:33.111 [info] 'TaskManager' Setting current root task UUID to abd4627c-60c1-4f48-88a1-5f41783481ba
2025-06-23 23:46:33.206 [error] 'GitReferenceMessenger' Failed to locally get remote url: Failed to get remote url, no remote found
2025-06-23 23:46:33.344 [info] 'WorkspaceManager[home]' Start tracking
2025-06-23 23:46:33.613 [info] 'PathMap' Opened source folder /home with id 100
2025-06-23 23:46:33.613 [info] 'OpenFileManager' Opened source folder 100
2025-06-23 23:46:33.934 [error] 'AugmentExtension' API request 0507ced0-7183-426a-82d5-6f27cbd811fe to https://i1.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-23 23:46:33.934 [error] 'AugmentExtension' Dropping error report "Chat stream with ID temp-fe-9d87fb9c-2041-479e-9155-750be3b91fa8 not found" due to error: This operation was aborted
2025-06-23 23:46:33.978 [info] 'MtimeCache[home]' reading blob name cache from /home/<USER>/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/Augment.vscode-augment/2cc974af6afc822c42a4e914df05c697348b80420941f6b27561b2bf688587b7/mtime-cache.json
2025-06-23 23:46:33.991 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-23 23:46:33.991 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-06-23 23:46:34.145 [info] 'MtimeCache[home]' read 15105 entries from /home/<USER>/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/Augment.vscode-augment/2cc974af6afc822c42a4e914df05c697348b80420941f6b27561b2bf688587b7/mtime-cache.json
2025-06-23 23:46:35.065 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-23 23:46:35.065 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-23 23:46:35.065 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-23 23:46:35.065 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-06-23 23:46:38.798 [info] 'StallDetector' Recent work: [{"name":"resolve-file-request","durationMs":5525.020007,"timestamp":"2025-06-23T23:46:38.724Z"},{"name":"resolve-file-request","durationMs":5544.205976,"timestamp":"2025-06-23T23:46:38.744Z"},{"name":"resolve-file-request","durationMs":5554.299125,"timestamp":"2025-06-23T23:46:38.755Z"},{"name":"resolve-file-request","durationMs":5564.29627,"timestamp":"2025-06-23T23:46:38.765Z"},{"name":"resolve-file-request","durationMs":5434.778331,"timestamp":"2025-06-23T23:46:38.776Z"},{"name":"resolve-file-request","durationMs":5444.818033,"timestamp":"2025-06-23T23:46:38.787Z"},{"name":"resolve-file-request","durationMs":5454.681797,"timestamp":"2025-06-23T23:46:38.797Z"}]
2025-06-23 23:46:47.936 [error] 'FuzzySymbolSearcher' Failed to read file tokens for cf175c1c15164d83104390da88eca708a2d388e4ac371932b0f52d62ea14e7ba: deleted
2025-06-23 23:47:34.228 [info] 'WorkspaceManager[home]' Tracking enabled
2025-06-23 23:47:34.228 [info] 'WorkspaceManager[home]' Path metrics:
  - directories emitted: 3087
  - files emitted: 18941
  - other paths emitted: 5
  - total paths emitted: 22033
  - timing stats:
    - readDir: 67 ms
    - filter: 544 ms
    - yield: 156 ms
    - total: 1020 ms
2025-06-23 23:47:34.228 [info] 'WorkspaceManager[home]' File metrics:
  - paths accepted: 15439
  - paths not accessible: 0
  - not plain files: 0
  - large files: 107
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 15090
  - mtime cache misses: 350
  - probe batches: 30
  - blob names probed: 15462
  - files read: 4044
  - blobs uploaded: 22
  - timing stats:
    - ingestPath: 45 ms
    - probe: 18319 ms
    - stat: 149 ms
    - read: 9208 ms
    - upload: 4644 ms
2025-06-23 23:47:34.228 [info] 'WorkspaceManager[home]' Startup metrics:
  - create SourceFolder: 635 ms
  - read MtimeCache: 167 ms
  - pre-populate PathMap: 662 ms
  - create PathFilter: 2837 ms
  - create PathNotifier: 0 ms
  - enumerate paths: 1029 ms
  - purge stale PathMap entries: 7 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 55516 ms
  - enable persist: 32 ms
  - total: 60885 ms
2025-06-23 23:47:34.228 [info] 'WorkspaceManager' Workspace startup complete in 61895 ms
2025-06-23 23:49:30.533 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/algofactory/admin/__pycache__
2025-06-23 23:54:45.193 [info] 'PathMap' Closed source folder /home with id 100
2025-06-23 23:54:45.193 [info] 'OpenFileManager' Closed source folder 100
