2025-06-23 20:12:55.235 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-23 20:12:55.235 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{"enableCommitIndexing":false,"maxCommitsToIndex":100},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-06-23 20:12:55.235 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","enableNewThreadsList":false,"enableUntruncatedContentStorage":false,"maxLinesTerminalProcessOutput":0,"vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","vscodeSupportToolUseStartMinVersion":"","enableAgentAutoMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5,"historySummaryMinVersion":"","historySummaryMaxChars":0,"historySummaryLowerChars":0,"historySummaryPrompt":"","enableSpawnSubAgentTool":false}
2025-06-23 20:12:55.235 [info] 'AugmentExtension' Retrieving model config
2025-06-23 20:12:56.206 [info] 'AugmentExtension' Retrieved model config
2025-06-23 20:12:56.207 [info] 'AugmentExtension' Returning model config
2025-06-23 20:12:56.329 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - vscodeShareMinVersion: "" to "0.314.0"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - enableNewThreadsList: false to true
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 49512
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.472.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - openFileManagerV2Enabled: false to true
  - vscodeTaskListMinVersion: "" to "0.482.0"
  - vscodeSupportToolUseStartMinVersion: "" to "0.485.0"
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
  - historySummaryMaxChars: 0 to 200000
  - historySummaryLowerChars: 0 to 80000
  - historySummaryPrompt: "" to "Your task is to create a detailed summary of the conversation so far, paying close attention to the user's explicit requests and your previous actions.\nThis summary should be thorough in capturing technical details, code patterns, and architectural decisions that would be essential for continuing with the conversation and supporting any continuing tasks.\n\nYour summary should be structured as follows:\nContext: The context to continue the conversation with. If applicable based on the current task, this should include:\n1. Previous Conversation: High level details about what was discussed throughout the entire conversation with the user. This should be written to allow someone to be able to follow the general overarching conversation flow.\n2. Current Work: Describe in detail what was being worked on prior to this request to summarize the conversation. Pay special attention to the more recent messages in the conversation.\n3. Key Technical Concepts: List all important technical concepts, technologies, coding conventions, and frameworks discussed, which might be relevant for continuing with this work.\n4. Relevant Files and Code: If applicable, enumerate specific files and code sections examined, modified, or created for the task continuation. Pay special attention to the most recent messages and changes.\n5. Problem Solving: Document problems solved thus far and any ongoing troubleshooting efforts.\n6. Pending Tasks and Next Steps: Outline all pending tasks that you have explicitly been asked to work on, as well as list the next steps you will take for all outstanding work, if applicable. Include code snippets where they add clarity. For any next steps, include direct quotes from the most recent conversation showing exactly what task you were working on and where you left off. This should be verbatim to ensure there's no information loss in context between tasks.\n\nExample summary structure:\n1. Previous Conversation:\n[Detailed description]\n2. Current Work:\n[Detailed description]\n3. Key Technical Concepts:\n- [Concept 1]\n- [Concept 2]\n- [...]\n4. Relevant Files and Code:\n- [File Name 1]\n    - [Summary of why this file is important]\n    - [Summary of the changes made to this file, if any]\n    - [Important Code Snippet]\n- [File Name 2]\n    - [Important Code Snippet]\n- [...]\n5. Problem Solving:\n[Detailed description]\n6. Pending Tasks and Next Steps:\n- [Task 1 details & next steps]\n- [Task 2 details & next steps]\n- [...]\n\nOutput only the summary of the conversation so far, without any additional commentary or explanation.\n"
2025-06-23 20:12:56.329 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    /home (explicit) at 6/22/2025, 7:25:55 PM
2025-06-23 20:12:56.329 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-06-23 20:12:56.329 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-06-23 20:12:56.329 [info] 'SyncingPermissionTracker' Permission to sync folder /home granted at 6/22/2025, 7:25:55 PM; type = explicit
2025-06-23 20:12:56.329 [info] 'WorkspaceManager' Adding workspace folder home; folderRoot = /home; syncingPermission = granted
2025-06-23 20:12:56.329 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home (explicit) at 6/22/2025, 7:25:55 PM
2025-06-23 20:12:56.408 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-06-23 20:12:56.408 [info] 'HotKeyHints' HotKeyHints initialized
2025-06-23 20:12:56.416 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-06-23 20:12:56.477 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-23 20:12:56.478 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-23 20:12:58.122 [info] 'WorkspaceManager[home]' Start tracking
2025-06-23 20:12:58.922 [info] 'PathMap' Opened source folder /home with id 100
2025-06-23 20:12:58.922 [info] 'OpenFileManager' Opened source folder 100
2025-06-23 20:12:58.928 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-23 20:12:58.928 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-06-23 20:12:58.936 [info] 'MtimeCache[home]' reading blob name cache from /home/<USER>/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/Augment.vscode-augment/2cc974af6afc822c42a4e914df05c697348b80420941f6b27561b2bf688587b7/mtime-cache.json
2025-06-23 20:12:59.027 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-23 20:12:59.027 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-23 20:12:59.027 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-23 20:12:59.027 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-06-23 20:12:59.206 [info] 'MtimeCache[home]' read 14744 entries from /home/<USER>/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/Augment.vscode-augment/2cc974af6afc822c42a4e914df05c697348b80420941f6b27561b2bf688587b7/mtime-cache.json
2025-06-23 20:15:05.502 [info] 'WorkspaceManager[home]' Tracking enabled
2025-06-23 20:15:05.502 [info] 'WorkspaceManager[home]' Path metrics:
  - directories emitted: 4827
  - files emitted: 23554
  - other paths emitted: 4
  - total paths emitted: 28385
  - timing stats:
    - readDir: 94 ms
    - filter: 660 ms
    - yield: 152 ms
    - total: 1119 ms
2025-06-23 20:15:05.502 [info] 'WorkspaceManager[home]' File metrics:
  - paths accepted: 15390
  - paths not accessible: 0
  - not plain files: 0
  - large files: 121
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 14706
  - mtime cache misses: 686
  - probe batches: 47
  - blob names probed: 25742
  - files read: 9383
  - blobs uploaded: 541
  - timing stats:
    - ingestPath: 60 ms
    - probe: 66571 ms
    - stat: 261 ms
    - read: 14671 ms
    - upload: 19176 ms
2025-06-23 20:15:05.502 [info] 'WorkspaceManager[home]' Startup metrics:
  - create SourceFolder: 814 ms
  - read MtimeCache: 272 ms
  - pre-populate PathMap: 1082 ms
  - create PathFilter: 3047 ms
  - create PathNotifier: 0 ms
  - enumerate paths: 1123 ms
  - purge stale PathMap entries: 11 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 120999 ms
  - enable persist: 32 ms
  - total: 127380 ms
2025-06-23 20:15:05.502 [info] 'WorkspaceManager' Workspace startup complete in 129199 ms
2025-06-23 20:19:00.776 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-06-23 20:19:03.023 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-06-23 20:19:03.023 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-06-23 20:19:03.025 [info] 'TaskManager' Setting current root task UUID to abd4627c-60c1-4f48-88a1-5f41783481ba
2025-06-23 20:19:03.351 [error] 'GitReferenceMessenger' Failed to locally get remote url: Failed to get remote url, no remote found
2025-06-23 20:19:50.934 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.vscode-server/data/logs/20250623T201249/exthost1/vscode.html-language-features
2025-06-23 20:19:56.870 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.vscode-server/data/logs/20250623T201249/exthost1/vscode.markdown-language-features
2025-06-23 20:25:21.340 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":60581.805257,"timestamp":"2025-06-23T20:25:21.245Z"}]
2025-06-23 20:26:21.296 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":60626.817029,"timestamp":"2025-06-23T20:26:21.291Z"}]
2025-06-23 20:27:21.040 [error] 'AugmentExtensionSidecar' API request 620b82cf-88f8-4239-b945-53e03ef6063d to https://i1.api.augmentcode.com/remote-agents/list-stream failed: This operation was aborted
2025-06-23 20:27:21.040 [error] 'AugmentExtensionSidecar' AbortError: This operation was aborted
	at node:internal/deps/undici/undici:13510:13
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
2025-06-23 20:27:21.348 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":60690.752154,"timestamp":"2025-06-23T20:27:21.346Z"}]
2025-06-23 20:28:21.265 [error] 'AugmentExtension' API request d2091313-4225-4d38-86d6-e30efdd4e545 to https://i1.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-23 20:28:21.266 [error] 'AugmentExtension' Dropping error report "remote-agents/list-stream call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-23 20:28:21.533 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":60744.466003,"timestamp":"2025-06-23T20:28:21.490Z"}]
2025-06-23 20:29:21.471 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":60800.553366,"timestamp":"2025-06-23T20:29:21.459Z"}]
2025-06-23 20:30:21.309 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":60566.929217,"timestamp":"2025-06-23T20:30:21.214Z"}]
2025-06-23 20:31:21.635 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":60901.001804,"timestamp":"2025-06-23T20:31:21.554Z"}]
2025-06-23 20:32:21.536 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":60858.380174,"timestamp":"2025-06-23T20:32:21.520Z"}]
2025-06-23 20:33:21.378 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":60634.886454,"timestamp":"2025-06-23T20:33:21.292Z"}]
2025-06-23 20:34:21.452 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":60785.523472,"timestamp":"2025-06-23T20:34:21.436Z"}]
2025-06-23 20:35:21.474 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":60805.886421,"timestamp":"2025-06-23T20:35:21.467Z"}]
2025-06-23 20:36:21.427 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":60702.148026,"timestamp":"2025-06-23T20:36:21.365Z"}]
2025-06-23 20:36:33.702 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":13034.048598,"timestamp":"2025-06-23T20:36:33.695Z"}]
2025-06-23 20:38:29.296 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.vscode-server/data/logs/20250623T201249/exthost1/vscode.json-language-features
2025-06-23 20:42:21.373 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":60560.843916,"timestamp":"2025-06-23T20:42:21.329Z"}]
2025-06-23 20:42:55.080 [info] 'AugmentExtension' Retrieving model config
2025-06-23 20:43:21.452 [info] 'AugmentExtension' Retrieved model config
2025-06-23 20:43:21.452 [info] 'AugmentExtension' Returning model config
2025-06-23 20:44:21.504 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":60836.532223,"timestamp":"2025-06-23T20:44:21.489Z"}]
2025-06-23 20:45:22.067 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":61301.56465,"timestamp":"2025-06-23T20:45:21.973Z"}]
2025-06-23 20:45:27.309 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":6630.274521,"timestamp":"2025-06-23T20:45:27.282Z"}]
2025-06-23 20:46:20.765 [error] 'AugmentExtension' API request 24c9b9c9-3cff-4716-92f6-a96e0ae844e5 to https://i1.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-23 20:46:21.240 [info] 'DiskFileManager[home]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-23 20:46:21.849 [info] 'DiskFileManager[home]' Operation succeeded after 1 transient failures
2025-06-23 20:47:43.999 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/myproject/openalgo
2025-06-23 20:54:24.606 [error] 'AugmentExtension' API request 6de5d0a6-ebb1-497c-b78e-1185115dd540 to https://i1.api.augmentcode.com/find-missing response 503: Service Unavailable
2025-06-23 20:54:25.001 [info] 'DiskFileManager[home]' Operation failed with error Error: HTTP error: 503 Service Unavailable, retrying in 100 ms; retries = 0
2025-06-23 20:54:25.692 [info] 'DiskFileManager[home]' Operation succeeded after 1 transient failures
2025-06-23 21:01:24.775 [error] 'AugmentExtension' API request 26155536-69d3-4f7f-b496-a35e1f24cab8 to https://i1.api.augmentcode.com/find-missing response 503: Service Unavailable
2025-06-23 21:01:25.139 [info] 'DiskFileManager[home]' Operation failed with error Error: HTTP error: 503 Service Unavailable, retrying in 100 ms; retries = 0
2025-06-23 21:01:25.675 [info] 'DiskFileManager[home]' Operation succeeded after 1 transient failures
2025-06-23 21:02:58.553 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/openalgo
2025-06-23 21:03:00.952 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/openalgo/.ebextensions
2025-06-23 21:03:00.955 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/openalgo/blueprints
2025-06-23 21:03:00.957 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/openalgo/broker
2025-06-23 21:03:00.983 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/openalgo/collections
2025-06-23 21:03:00.983 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/openalgo/database
2025-06-23 21:03:00.984 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/openalgo/db
2025-06-23 21:03:00.984 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/openalgo/design
2025-06-23 21:03:00.985 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/openalgo/docs
2025-06-23 21:03:00.988 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/openalgo/download
2025-06-23 21:03:00.988 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/openalgo/install
2025-06-23 21:03:00.988 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/openalgo/log
2025-06-23 21:03:00.988 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/openalgo/restx_api
2025-06-23 21:03:00.991 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/openalgo/services
2025-06-23 21:03:00.993 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/openalgo/src
2025-06-23 21:03:00.993 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/openalgo/static
2025-06-23 21:03:01.010 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/openalgo/strategies
2025-06-23 21:03:01.011 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/openalgo/templates
2025-06-23 21:03:01.016 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/openalgo/test
2025-06-23 21:03:01.016 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/openalgo/tmp
2025-06-23 21:03:01.016 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/openalgo/upgrade
2025-06-23 21:03:01.017 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/openalgo/utils
2025-06-23 21:03:01.018 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/openalgo/websocket_proxy
2025-06-23 21:10:11.146 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.vscode-server/data/logs/20250623T201249/exthost2
2025-06-23 21:10:13.260 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.vscode-server/data/logs/20250623T201249/exthost2/vscode.git
2025-06-23 21:10:13.413 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.vscode-server/data/logs/20250623T201249/exthost2/Augment.vscode-augment
2025-06-23 21:10:13.413 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.vscode-server/data/logs/20250623T201249/exthost2/vscode.github
2025-06-23 21:11:05.649 [info] 'WorkspaceManager[home]' Directory created: ubuntu/snap
2025-06-23 21:11:07.466 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv
2025-06-23 21:11:09.433 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/.tmp3oa7JF
2025-06-23 21:11:09.581 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/.tmp5r8lEE
2025-06-23 21:11:09.635 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/.tmpFyD27i
2025-06-23 21:11:09.701 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/.tmpSMZprR
2025-06-23 21:11:09.708 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/.tmpT8K1pF
2025-06-23 21:11:09.770 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/.tmpWAq2tP
2025-06-23 21:11:09.835 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/.tmpXYZzYl
2025-06-23 21:11:09.891 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/.tmpdvTu49
2025-06-23 21:11:09.891 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/.tmpgH5Myc
2025-06-23 21:11:09.961 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/.tmphG9by0
2025-06-23 21:11:10.004 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/.tmphwQ8Py
2025-06-23 21:11:10.085 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/.tmpiisqFX
2025-06-23 21:11:10.085 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/.tmplJGpTk
2025-06-23 21:11:10.140 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/.tmplPEOiK
2025-06-23 21:11:10.197 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/.tmpsXzDdi
2025-06-23 21:11:10.221 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/.tmpvQPM4P
2025-06-23 21:11:10.229 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/.tmpxQGmV8
2025-06-23 21:11:10.233 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/archive-v0
2025-06-23 21:11:11.446 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/.cache/uv/.tmpdvTu49
2025-06-23 21:11:11.446 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/.cache/uv/.tmpiisqFX
2025-06-23 21:11:11.446 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/.cache/uv/.tmpT8K1pF
2025-06-23 21:11:12.213 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/.cache/uv/.tmplJGpTk
2025-06-23 21:11:12.213 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/.cache/uv/.tmpvQPM4P
2025-06-23 21:11:12.213 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/.cache/uv/.tmplPEOiK
2025-06-23 21:11:12.670 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/.cache/uv/.tmp5r8lEE
2025-06-23 21:11:13.018 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/archive-v0/DzdXwM-RlkIpNrONLB0Cc
2025-06-23 21:11:13.021 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/archive-v0/Q-SxUjG_u0AaiSm2WyetC
2025-06-23 21:11:13.024 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/archive-v0/UtNdkTYdGpVCH9DnvnEaE
2025-06-23 21:11:13.025 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/archive-v0/_PWcK5heQwI1F7iu3PJCY
2025-06-23 21:11:13.026 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/archive-v0/uQF4A8oUgahDXWhS3tj0e
2025-06-23 21:11:13.032 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/builds-v0
2025-06-23 21:11:14.634 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/.cache/uv/.tmpFyD27i
2025-06-23 21:11:14.634 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/.cache/uv/.tmp3oa7JF
2025-06-23 21:11:14.636 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/.cache/uv/.tmpSMZprR
2025-06-23 21:11:14.636 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/.cache/uv/.tmpWAq2tP
2025-06-23 21:11:14.636 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/.cache/uv/.tmpXYZzYl
2025-06-23 21:11:14.636 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/.cache/uv/.tmpsXzDdi
2025-06-23 21:11:14.780 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/interpreter-v4
2025-06-23 21:11:14.782 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/simple-v16
2025-06-23 21:11:14.783 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/wheels-v5
2025-06-23 21:11:14.866 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/archive-v0/rFLbYoVftLr3L2ov4KqrM
2025-06-23 21:11:14.881 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/archive-v0/rbuobvyADpgnUUgg1TWQI
2025-06-23 21:11:14.894 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/builds-v0/.tmpd7weJ5
2025-06-23 21:11:14.894 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/builds-v0/.tmpeNrcAX
2025-06-23 21:11:14.972 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/builds-v0/.tmpeNrcAX/bin
2025-06-23 21:11:14.976 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/builds-v0/.tmpeNrcAX/lib
2025-06-23 21:11:15.043 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/builds-v0/.tmpeNrcAX/lib/python3.12
2025-06-23 21:11:15.117 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/archive-v0/TxzKDrndGlLIFjC0tZZgb
2025-06-23 21:11:15.138 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/archive-v0/Z0k9W_6hiR-G_Oqpkb1kq
2025-06-23 21:11:15.185 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/archive-v0/mgyiGErTEn_89C_PHxG36
2025-06-23 21:11:15.200 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/archive-v0/G5W8DH0ZqNIfDg5nEQw-L
2025-06-23 21:11:15.403 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 2071 msec late.
2025-06-23 21:11:15.570 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/archive-v0/MYq9Fk6vHkI2bxbzC4bL0
2025-06-23 21:11:15.584 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/archive-v0/PRgDwkKS9X4MT69z3YPij
2025-06-23 21:11:15.686 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/.tmpWAq2tP/setuptools-80.3.1.dist-info
2025-06-23 21:11:15.688 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/archive-v0/jNOp77hGCjM1QJJxPUUaD
2025-06-23 21:11:15.716 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/archive-v0/FipSnliuUULEfGED9drkv
2025-06-23 21:11:15.782 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/archive-v0/PPbUJB80Fw37g5g4wnAWm
2025-06-23 21:11:15.805 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/archive-v0/R5IUwHLzD4GbLo6PFT_B-
2025-06-23 21:11:15.964 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/archive-v0/TB8F69kFFh2mrU1HA56dQ
2025-06-23 21:11:16.102 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/archive-v0/lkdZl59FfjfqaEmSX6den
2025-06-23 21:11:16.175 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/archive-v0/qZS-AvnIfML6fjSSYNY55
2025-06-23 21:11:17.202 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/archive-v0/mYlKjE991i__HtDGUrsoJ
2025-06-23 21:11:17.202 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/.cache/uv/.tmpxQGmV8
2025-06-23 21:11:17.529 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/builds-v0/.tmpd7weJ5/.tmp-1ppr4ym8
2025-06-23 21:11:18.430 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/.cache/uv/builds-v0/.tmpdm1j56
2025-06-23 21:11:19.388 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/.cache/uv/builds-v0/.tmpd7weJ5/.tmp-1ppr4ym8
2025-06-23 21:11:19.765 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/.cache/uv/builds-v0/.tmpkCwxVs
2025-06-23 21:11:20.313 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/.tmpiT3O1s
2025-06-23 21:11:20.313 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/.cache/uv/builds-v0/.tmpeNrcAX/lib/python3.12/site-packages
2025-06-23 21:11:21.184 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/.cache/uv/builds-v0/.tmpeNrcAX
2025-06-23 21:11:21.184 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/.cache/uv/builds-v0/.tmpUCtZTc
2025-06-23 21:11:21.852 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/.cache/uv/builds-v0/.tmpd7weJ5
2025-06-23 21:11:21.852 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/.cache/uv/builds-v0/.tmponfSV2
2025-06-23 21:11:21.852 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/archive-v0/a3W_KFtIRQ5pesfWHKVKQ
2025-06-23 21:11:21.852 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/.cache/uv/.tmpgH5Myc
2025-06-23 21:11:21.852 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/.cache/uv/.tmpiT3O1s
2025-06-23 21:11:21.945 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/archive-v0/FELeTada9lMMeMrHYbSpc
2025-06-23 21:11:21.946 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/archive-v0/kuNlvtjNiqBR-uuur8zCT
2025-06-23 21:11:21.961 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/archive-v0/w5awF7rzLnObEyMFZT1pP
2025-06-23 21:11:22.249 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/archive-v0/zE6BXuLNJrgiamDcIfo_8
2025-06-23 21:11:22.249 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/.cache/uv/.tmphwQ8Py
2025-06-23 21:11:22.249 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/.cache/uv/.tmphG9by0
2025-06-23 21:11:22.845 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/archive-v0/OK3eo2uLjQ3Fi3RpkLO7M
2025-06-23 21:12:55.078 [info] 'AugmentExtension' Retrieving model config
2025-06-23 21:12:55.464 [info] 'AugmentExtension' Retrieved model config
2025-06-23 21:12:55.464 [info] 'AugmentExtension' Returning model config
2025-06-23 21:13:18.433 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/uv/.tmpytR50q
2025-06-23 21:13:20.289 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/.cache/uv/.tmpytR50q
2025-06-23 21:13:33.126 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.vscode-server/data/User/History/-33f1e88a
2025-06-23 21:13:54.927 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 2974 msec late.
2025-06-23 21:22:42.742 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/shared-venv/lib
2025-06-23 21:22:42.746 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/shared-venv/share
2025-06-23 21:22:42.746 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/shared-venv/include
2025-06-23 21:23:02.503 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/shared-venv/bin
2025-06-23 21:23:36.186 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/shared-venv
2025-06-23 21:23:40.301 [info] 'WorkspaceManager[home]' Directory created: ubuntu/shared-venv
2025-06-23 21:23:40.301 [info] 'WorkspaceManager[home]' Directory created: ubuntu/shared-venv/bin
2025-06-23 21:23:40.301 [info] 'WorkspaceManager[home]' Directory created: ubuntu/shared-venv/include
2025-06-23 21:23:40.301 [info] 'WorkspaceManager[home]' Directory created: ubuntu/shared-venv/include/python3.12
2025-06-23 21:23:40.302 [info] 'WorkspaceManager[home]' Directory created: ubuntu/shared-venv/lib
2025-06-23 21:27:08.493 [info] 'WorkspaceManager[home]' Directory created: ubuntu/shared-venv/share
2025-06-23 21:27:08.907 [info] 'WorkspaceManager[home]' Directory created: ubuntu/shared-venv/include/site
2025-06-23 21:27:09.924 [info] 'WorkspaceManager[home]' Directory created: ubuntu/shared-venv/share/jupyter
2025-06-23 21:27:09.925 [info] 'WorkspaceManager[home]' Directory created: ubuntu/shared-venv/share/jupyter/kernels
2025-06-23 21:40:10.220 [info] 'ViewTool' Tool called with path: ubuntu/myproject/algofactory and view_range: undefined
2025-06-23 21:40:10.246 [info] 'ViewTool' Listing directory: ubuntu/myproject/algofactory (depth: 2, showHidden: false)
2025-06-23 21:42:55.078 [info] 'AugmentExtension' Retrieving model config
2025-06-23 21:42:55.504 [info] 'AugmentExtension' Retrieved model config
2025-06-23 21:42:55.504 [info] 'AugmentExtension' Returning model config
2025-06-23 21:44:01.180 [info] 'ToolFileUtils' Reading file: ubuntu/myproject/algofactory/nginx_manager.py
2025-06-23 21:44:01.180 [info] 'ToolFileUtils' Successfully read file: ubuntu/myproject/algofactory/nginx_manager.py (14339 bytes)
2025-06-23 21:44:01.667 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.vscode-server/data/User/History/-67da37e
2025-06-23 21:44:02.517 [info] 'ToolFileUtils' Reading file: ubuntu/myproject/algofactory/nginx_manager.py
2025-06-23 21:44:02.517 [info] 'ToolFileUtils' Successfully read file: ubuntu/myproject/algofactory/nginx_manager.py (14299 bytes)
2025-06-23 21:44:40.574 [info] 'ToolFileUtils' Reading file: ubuntu/myproject/algofactory/nginx_optimization.conf
2025-06-23 21:44:40.575 [info] 'ToolFileUtils' Successfully read file: ubuntu/myproject/algofactory/nginx_optimization.conf (5543 bytes)
2025-06-23 21:44:41.005 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.vscode-server/data/User/History/-17781493
2025-06-23 21:44:41.930 [info] 'ToolFileUtils' Reading file: ubuntu/myproject/algofactory/nginx_optimization.conf
2025-06-23 21:44:41.930 [info] 'ToolFileUtils' Successfully read file: ubuntu/myproject/algofactory/nginx_optimization.conf (5516 bytes)
2025-06-23 21:46:58.916 [info] 'ToolFileUtils' Reading file: ubuntu/myproject/algofactory/nginx_manager.py
2025-06-23 21:46:58.917 [info] 'ToolFileUtils' Successfully read file: ubuntu/myproject/algofactory/nginx_manager.py (14299 bytes)
2025-06-23 21:47:00.631 [info] 'ToolFileUtils' Reading file: ubuntu/myproject/algofactory/nginx_manager.py
2025-06-23 21:47:00.631 [info] 'ToolFileUtils' Successfully read file: ubuntu/myproject/algofactory/nginx_manager.py (14180 bytes)
2025-06-23 21:48:04.452 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1231.393733,"timestamp":"2025-06-23T21:48:04.396Z"}]
2025-06-23 21:58:55.402 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/algofactory/admin_dashboard
2025-06-23 21:59:59.491 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/algofactory/admin_dashboard/templates
2025-06-23 22:01:05.005 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/0/4/6
2025-06-23 22:01:05.334 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/0/c/9
2025-06-23 22:01:05.336 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/8/7/4
2025-06-23 22:01:05.336 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/e/e/5
2025-06-23 22:01:05.540 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/0/3/d
2025-06-23 22:01:05.541 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/8/f/9
2025-06-23 22:01:05.543 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/9/a
2025-06-23 22:01:05.543 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/a/e
2025-06-23 22:01:05.956 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/1/2/f
2025-06-23 22:01:05.958 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/a/c
2025-06-23 22:01:05.958 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/b/2/2
2025-06-23 22:01:05.961 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/e/9
2025-06-23 22:01:05.963 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/f/9
2025-06-23 22:01:06.278 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/6/0
2025-06-23 22:01:06.279 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/7/c/5
2025-06-23 22:01:06.282 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/4/a/5
2025-06-23 22:01:06.318 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/7/6/1
2025-06-23 22:01:06.455 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/2/3/0
2025-06-23 22:01:06.457 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/5/9/c
2025-06-23 22:01:06.457 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/5/9/c/7
2025-06-23 22:01:06.539 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/8/e/e
2025-06-23 22:01:06.541 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/b/5/7
2025-06-23 22:01:06.656 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/5/9/8
2025-06-23 22:01:06.657 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/5/9/8/a
2025-06-23 22:01:06.657 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/5/9/8/a/0
2025-06-23 22:01:06.850 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/2/6/4
2025-06-23 22:01:06.852 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/d/f
2025-06-23 22:01:06.852 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/3/1/5/3
2025-06-23 22:01:07.683 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/5/b/7
2025-06-23 22:01:07.787 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/0/5/4
2025-06-23 22:01:07.789 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/3/2/4
2025-06-23 22:01:08.068 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/0/3/7
2025-06-23 22:01:08.068 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/0/3/7/3
2025-06-23 22:01:08.068 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/0/3/7/3/e
2025-06-23 22:01:08.068 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/0/5/c
2025-06-23 22:01:08.070 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/0/e
2025-06-23 22:01:08.070 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/2/1/a
2025-06-23 22:01:08.072 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/2/9
2025-06-23 22:01:08.073 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/4/5/a
2025-06-23 22:01:08.073 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/8/a/e
2025-06-23 22:01:08.073 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/b/5/4
2025-06-23 22:01:08.077 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/e/a/5
2025-06-23 22:01:08.078 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/e/f/d
2025-06-23 22:01:08.081 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/f/3/9
2025-06-23 22:01:08.081 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/f/b/8
2025-06-23 22:01:08.188 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/0/d/d
2025-06-23 22:01:08.188 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/5/d/7
2025-06-23 22:01:08.191 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/d/6/3
2025-06-23 22:01:08.191 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/e/8/7
2025-06-23 22:02:04.956 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/algofactory/admin_dashboard/static
2025-06-23 22:08:04.941 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory
2025-06-23 22:08:24.537 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory/admin_dashboard
2025-06-23 22:08:24.537 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/myproject/algofactory/admin_dashboard
2025-06-23 22:09:04.869 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/algofactory/admin_dashboard
2025-06-23 22:09:04.869 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/algofactory/admin_dashboard
2025-06-23 22:10:00.208 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/myproject/algofactory/blueprints
2025-06-23 22:10:00.208 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/myproject/algofactory/.ebextensions
2025-06-23 22:10:01.000 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/myproject/algofactory/db
2025-06-23 22:10:01.000 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/myproject/algofactory/docs
2025-06-23 22:10:01.000 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/myproject/algofactory/broker
2025-06-23 22:10:01.001 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/myproject/algofactory/design
2025-06-23 22:10:01.001 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/myproject/algofactory/database
2025-06-23 22:10:01.001 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/myproject/algofactory/download
2025-06-23 22:10:01.001 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/myproject/algofactory/collections
2025-06-23 22:10:01.381 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/myproject/algofactory/logs
2025-06-23 22:10:01.381 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/myproject/algofactory/install
2025-06-23 22:10:01.381 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/myproject/algofactory/src
2025-06-23 22:10:01.381 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/myproject/algofactory/static
2025-06-23 22:10:01.381 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/myproject/algofactory/services
2025-06-23 22:10:01.381 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/myproject/algofactory/restx_api
2025-06-23 22:10:16.290 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/myproject/algofactory/strategies
2025-06-23 22:10:16.449 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/myproject/algofactory/test
2025-06-23 22:10:16.449 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/myproject/algofactory/templates
2025-06-23 22:10:16.639 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/myproject/algofactory/tmp
2025-06-23 22:10:16.639 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/myproject/algofactory/utils
2025-06-23 22:10:16.639 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/myproject/algofactory/upgrade
2025-06-23 22:10:16.706 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/myproject/algofactory/websocket_proxy
2025-06-23 22:12:32.930 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/myproject/openalgo
2025-06-23 22:12:55.098 [info] 'AugmentExtension' Retrieving model config
2025-06-23 22:12:55.499 [info] 'AugmentExtension' Retrieved model config
2025-06-23 22:12:55.499 [info] 'AugmentExtension' Returning model config
2025-06-23 22:13:12.092 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/algofactory-base
2025-06-23 22:13:34.310 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/algofactory/algofactory-base
2025-06-23 22:13:34.310 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/myproject/algofactory-base
2025-06-23 22:14:12.332 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/algofactory-base
2025-06-23 22:14:12.332 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/myproject/algofactory/algofactory-base
2025-06-23 22:17:21.404 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":60514.143778,"timestamp":"2025-06-23T22:17:21.308Z"}]
2025-06-23 22:19:21.332 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":60552.394246,"timestamp":"2025-06-23T22:19:21.314Z"}]
2025-06-23 22:20:21.295 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":60634.73656,"timestamp":"2025-06-23T22:20:21.292Z"}]
2025-06-23 22:21:21.376 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":60617.936668,"timestamp":"2025-06-23T22:21:21.284Z"}]
2025-06-23 22:22:21.315 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":60641.3393,"timestamp":"2025-06-23T22:22:21.312Z"}]
2025-06-23 22:23:21.359 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":60611.456942,"timestamp":"2025-06-23T22:23:21.262Z"}]
2025-06-23 22:24:05.481 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":44762.861296,"timestamp":"2025-06-23T22:24:05.421Z"}]
2025-06-23 22:24:24.502 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/myproject
2025-06-23 22:25:04.627 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject
2025-06-23 22:25:14.163 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/algofactory
2025-06-23 22:25:24.189 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/openalgo
2025-06-23 22:26:19.351 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/myproject/openalgo
2025-06-23 22:26:26.542 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/myproject
2025-06-23 22:31:26.504 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.vscode-server/data/logs/20250623T201249/exthost3
2025-06-23 22:31:27.557 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.vscode-server/data/logs/20250623T201249/exthost3/vscode.git
2025-06-23 22:31:29.115 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.vscode-server/data/logs/20250623T201249/exthost3/Augment.vscode-augment
2025-06-23 22:31:29.115 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.vscode-server/data/logs/20250623T201249/exthost3/vscode.github
2025-06-23 22:38:52.189 [error] 'AugmentExtension' API request c4e46248-6064-4afa-8c32-3423c0a64dd0 to https://i1.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-23 22:38:52.704 [info] 'DiskFileManager[home]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-23 22:38:53.264 [info] 'DiskFileManager[home]' Operation succeeded after 1 transient failures
2025-06-23 22:40:24.771 [info] 'ViewTool' Tool called with path: ubuntu/algofactory-multi/algofactory-8010/templates and view_range: undefined
2025-06-23 22:40:24.792 [info] 'ViewTool' Listing directory: ubuntu/algofactory-multi/algofactory-8010/templates (depth: 2, showHidden: false)
2025-06-23 22:40:31.010 [info] 'ViewTool' Tool called with path: ubuntu/algofactory-multi/algofactory-8010/templates/base.html and view_range: [1,50]
2025-06-23 22:41:38.473 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject
2025-06-23 22:41:38.473 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/algofactory
2025-06-23 22:41:38.473 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/algofactory/admin
2025-06-23 22:42:42.685 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/algofactory/admin/templates
2025-06-23 22:42:55.096 [info] 'AugmentExtension' Retrieving model config
2025-06-23 22:42:55.479 [info] 'AugmentExtension' Retrieved model config
2025-06-23 22:42:55.479 [info] 'AugmentExtension' Returning model config
2025-06-23 22:43:21.756 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/algofactory/admin/templates/auth
2025-06-23 22:43:36.617 [info] 'ToolFileUtils' Reading file: ubuntu/myproject/algofactory/admin/templates/base.html
2025-06-23 22:43:36.617 [info] 'ToolFileUtils' Successfully read file: ubuntu/myproject/algofactory/admin/templates/base.html (20394 bytes)
2025-06-23 22:43:37.033 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.vscode-server/data/User/History/55c091f
2025-06-23 22:43:37.898 [info] 'ToolFileUtils' Reading file: ubuntu/myproject/algofactory/admin/templates/base.html
2025-06-23 22:43:37.899 [info] 'ToolFileUtils' Successfully read file: ubuntu/myproject/algofactory/admin/templates/base.html (20521 bytes)
2025-06-23 22:45:04.081 [info] 'WorkspaceManager[home]' Directory created: ubuntu/myproject/algofactory/admin/templates/dashboard
2025-06-23 22:52:00.984 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/c/3/5
2025-06-23 22:52:01.144 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/d/5/3
2025-06-23 22:52:01.145 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/e/3/9/b
2025-06-23 22:52:01.145 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/e/d/2
2025-06-23 22:52:01.387 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/4/8/d
2025-06-23 22:52:11.349 [info] 'ToolFileUtils' Reading file: ubuntu/myproject/algofactory/admin/requirements.txt
2025-06-23 22:52:11.424 [info] 'ToolFileUtils' Successfully read file: ubuntu/myproject/algofactory/admin/requirements.txt (143 bytes)
2025-06-23 22:52:11.788 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.vscode-server/data/User/History/-68b1349b
2025-06-23 22:52:12.621 [info] 'ToolFileUtils' Reading file: ubuntu/myproject/algofactory/admin/requirements.txt
2025-06-23 22:52:12.621 [info] 'ToolFileUtils' Successfully read file: ubuntu/myproject/algofactory/admin/requirements.txt (96 bytes)
2025-06-23 22:52:43.468 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/7/7/4
2025-06-23 22:52:43.468 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/8/3/5
2025-06-23 22:52:43.470 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.cache/pip/http-v2/c/1/4
2025-06-23 22:54:41.550 [info] 'ToolFileUtils' Reading file: ubuntu/myproject/algofactory/admin/app.py
2025-06-23 22:54:41.628 [info] 'ToolFileUtils' Successfully read file: ubuntu/myproject/algofactory/admin/app.py (21045 bytes)
2025-06-23 22:54:41.932 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.vscode-server/data/User/History/3071b809
2025-06-23 22:54:42.854 [info] 'ToolFileUtils' Reading file: ubuntu/myproject/algofactory/admin/app.py
2025-06-23 22:54:42.854 [info] 'ToolFileUtils' Successfully read file: ubuntu/myproject/algofactory/admin/app.py (21045 bytes)
2025-06-23 23:04:21.145 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base
2025-06-23 23:04:21.147 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/algofactory
2025-06-23 23:04:37.989 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy
2025-06-23 23:04:38.443 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/.ebextensions
2025-06-23 23:04:38.445 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/__pycache__
2025-06-23 23:04:38.446 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/blueprints
2025-06-23 23:04:38.447 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker
2025-06-23 23:04:38.448 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/collections
2025-06-23 23:04:38.449 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/database
2025-06-23 23:04:38.451 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/db
2025-06-23 23:04:38.452 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/design
2025-06-23 23:04:38.452 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/docs
2025-06-23 23:04:38.453 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/download
2025-06-23 23:04:38.453 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/install
2025-06-23 23:04:38.454 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/log
2025-06-23 23:04:38.454 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/restx_api
2025-06-23 23:04:38.456 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/services
2025-06-23 23:04:38.458 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/src
2025-06-23 23:04:38.458 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/static
2025-06-23 23:04:38.459 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/strategies
2025-06-23 23:04:38.459 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/templates
2025-06-23 23:04:38.466 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/test
2025-06-23 23:04:38.466 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/tmp
2025-06-23 23:04:38.468 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/upgrade
2025-06-23 23:04:38.468 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/utils
2025-06-23 23:04:38.469 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/websocket_proxy
2025-06-23 23:04:38.923 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/blueprints/__pycache__
2025-06-23 23:04:38.923 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/__pycache__
2025-06-23 23:04:38.923 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/aliceblue
2025-06-23 23:04:38.923 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/angel
2025-06-23 23:04:38.923 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/compositedge
2025-06-23 23:04:38.923 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/dhan
2025-06-23 23:04:38.923 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/dhan_sandbox
2025-06-23 23:04:38.923 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/firstock
2025-06-23 23:04:38.923 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/fivepaisa
2025-06-23 23:04:38.923 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/fivepaisaxts
2025-06-23 23:04:38.923 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/flattrade
2025-06-23 23:04:38.923 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/fyers
2025-06-23 23:04:38.925 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/groww
2025-06-23 23:04:38.925 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/iifl
2025-06-23 23:04:38.925 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/jainam
2025-06-23 23:04:38.925 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/jainampro
2025-06-23 23:04:38.925 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/kotak
2025-06-23 23:04:38.925 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/paytm
2025-06-23 23:04:38.925 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/pocketful
2025-06-23 23:04:38.926 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/shoonya
2025-06-23 23:04:38.926 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/tradejini
2025-06-23 23:04:38.926 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/upstox
2025-06-23 23:04:38.926 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/wisdom
2025-06-23 23:04:38.926 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/zebu
2025-06-23 23:04:38.926 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/zerodha
2025-06-23 23:04:38.926 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/database/__pycache__
2025-06-23 23:04:38.926 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/restx_api/__pycache__
2025-06-23 23:04:38.926 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/services/__pycache__
2025-06-23 23:04:38.926 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/src/css
2025-06-23 23:04:38.926 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/static/css
2025-06-23 23:04:38.926 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/static/favicon
2025-06-23 23:04:38.926 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/static/images
2025-06-23 23:04:38.926 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/static/js
2025-06-23 23:04:38.926 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/static/sounds
2025-06-23 23:04:38.928 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/templates/chartink
2025-06-23 23:04:38.928 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/templates/components
2025-06-23 23:04:38.928 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/templates/latency
2025-06-23 23:04:38.928 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/templates/strategy
2025-06-23 23:04:38.928 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/templates/traffic
2025-06-23 23:04:38.928 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/test/logs
2025-06-23 23:04:38.928 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/utils/__pycache__
2025-06-23 23:04:38.928 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/websocket_proxy/__pycache__
2025-06-23 23:04:40.244 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/aliceblue/api
2025-06-23 23:04:40.244 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/aliceblue/database
2025-06-23 23:04:40.244 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/aliceblue/mapping
2025-06-23 23:04:40.244 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/angel/__pycache__
2025-06-23 23:04:40.244 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/angel/api
2025-06-23 23:04:40.244 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/angel/database
2025-06-23 23:04:40.244 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/angel/mapping
2025-06-23 23:04:40.244 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/angel/streaming
2025-06-23 23:04:40.244 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/compositedge/__pycache__
2025-06-23 23:04:40.252 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/compositedge/api
2025-06-23 23:04:40.252 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/compositedge/database
2025-06-23 23:04:40.252 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/compositedge/mapping
2025-06-23 23:04:40.252 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/dhan/__pycache__
2025-06-23 23:04:40.252 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/dhan/api
2025-06-23 23:04:40.252 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/dhan/database
2025-06-23 23:04:40.253 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/dhan/mapping
2025-06-23 23:04:40.253 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/dhan/streaming
2025-06-23 23:04:40.253 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/dhan_sandbox/__pycache__
2025-06-23 23:04:40.253 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/dhan_sandbox/api
2025-06-23 23:04:40.253 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/dhan_sandbox/database
2025-06-23 23:04:40.253 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/dhan_sandbox/mapping
2025-06-23 23:04:40.253 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/firstock/__pycache__
2025-06-23 23:04:40.253 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/firstock/api
2025-06-23 23:04:40.253 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/firstock/database
2025-06-23 23:04:40.253 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/firstock/mapping
2025-06-23 23:04:40.253 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/fivepaisa/__pycache__
2025-06-23 23:04:40.253 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/fivepaisa/api
2025-06-23 23:04:40.253 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/fivepaisa/database
2025-06-23 23:04:40.253 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/fivepaisa/mapping
2025-06-23 23:04:40.253 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/fivepaisaxts/__pycache__
2025-06-23 23:04:40.253 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/fivepaisaxts/api
2025-06-23 23:04:40.253 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/fivepaisaxts/database
2025-06-23 23:04:40.253 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/fivepaisaxts/mapping
2025-06-23 23:04:40.253 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/flattrade/__pycache__
2025-06-23 23:04:40.253 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/flattrade/api
2025-06-23 23:04:40.257 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/flattrade/database
2025-06-23 23:04:40.257 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/flattrade/mapping
2025-06-23 23:04:40.257 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/flattrade/streaming
2025-06-23 23:04:40.257 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/fyers/api
2025-06-23 23:04:40.257 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/fyers/database
2025-06-23 23:04:40.257 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/fyers/mapping
2025-06-23 23:04:40.257 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/groww/__pycache__
2025-06-23 23:04:40.257 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/groww/api
2025-06-23 23:04:40.257 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/groww/database
2025-06-23 23:04:40.257 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/groww/mapping
2025-06-23 23:04:40.257 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/iifl/__pycache__
2025-06-23 23:04:40.257 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/iifl/api
2025-06-23 23:04:40.257 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/iifl/database
2025-06-23 23:04:40.257 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/iifl/mapping
2025-06-23 23:04:40.257 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/jainam/__pycache__
2025-06-23 23:04:40.257 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/jainam/api
2025-06-23 23:04:40.257 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/jainam/database
2025-06-23 23:04:40.260 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/jainam/mapping
2025-06-23 23:04:40.260 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/jainampro/__pycache__
2025-06-23 23:04:40.260 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/jainampro/api
2025-06-23 23:04:40.260 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/jainampro/database
2025-06-23 23:04:40.260 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/jainampro/mapping
2025-06-23 23:04:40.260 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/kotak/__pycache__
2025-06-23 23:04:40.260 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/kotak/api
2025-06-23 23:04:40.260 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/kotak/database
2025-06-23 23:04:40.260 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/kotak/mapping
2025-06-23 23:04:40.260 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/paytm/api
2025-06-23 23:04:40.265 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/paytm/database
2025-06-23 23:04:40.265 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/paytm/mapping
2025-06-23 23:04:40.265 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/pocketful/api
2025-06-23 23:04:40.265 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/pocketful/database
2025-06-23 23:04:40.265 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/pocketful/mapping
2025-06-23 23:04:40.265 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/shoonya/__pycache__
2025-06-23 23:04:40.265 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/shoonya/api
2025-06-23 23:04:40.265 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/shoonya/database
2025-06-23 23:04:40.265 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/shoonya/mapping
2025-06-23 23:04:40.265 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/tradejini/__pycache__
2025-06-23 23:04:40.268 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/tradejini/api
2025-06-23 23:04:40.268 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/tradejini/database
2025-06-23 23:04:40.268 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/tradejini/mapping
2025-06-23 23:04:40.270 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/upstox/__pycache__
2025-06-23 23:04:40.270 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/upstox/api
2025-06-23 23:04:40.270 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/upstox/database
2025-06-23 23:04:40.270 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/upstox/mapping
2025-06-23 23:04:40.270 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/wisdom/__pycache__
2025-06-23 23:04:40.271 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/wisdom/api
2025-06-23 23:04:40.271 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/wisdom/database
2025-06-23 23:04:40.271 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/wisdom/mapping
2025-06-23 23:04:40.271 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/zebu/__pycache__
2025-06-23 23:04:40.271 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/zebu/api
2025-06-23 23:04:40.271 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/zebu/database
2025-06-23 23:04:40.271 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/zebu/mapping
2025-06-23 23:04:40.271 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/zerodha/api
2025-06-23 23:04:40.271 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/zerodha/database
2025-06-23 23:04:40.271 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/zerodha/mapping
2025-06-23 23:04:40.271 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/zerodha/streaming
2025-06-23 23:04:40.271 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/static/images/brokers
2025-06-23 23:04:41.124 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/aliceblue/api/__pycache__
2025-06-23 23:04:41.124 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/angel/api/__pycache__
2025-06-23 23:04:41.125 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/angel/database/__pycache__
2025-06-23 23:04:41.125 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/angel/mapping/__pycache__
2025-06-23 23:04:41.125 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/angel/streaming/__pycache__
2025-06-23 23:04:41.125 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/compositedge/api/__pycache__
2025-06-23 23:04:41.125 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/dhan/api/__pycache__
2025-06-23 23:04:41.125 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/dhan/streaming/__pycache__
2025-06-23 23:04:41.125 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/dhan_sandbox/api/__pycache__
2025-06-23 23:04:41.125 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/firstock/api/__pycache__
2025-06-23 23:04:41.125 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/fivepaisa/api/__pycache__
2025-06-23 23:04:41.125 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/fivepaisaxts/api/__pycache__
2025-06-23 23:04:41.125 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/flattrade/api/__pycache__
2025-06-23 23:04:41.125 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/flattrade/database/__pycache__
2025-06-23 23:04:41.125 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/flattrade/mapping/__pycache__
2025-06-23 23:04:41.125 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/flattrade/streaming/__pycache__
2025-06-23 23:04:41.125 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/fyers/api/__pycache__
2025-06-23 23:04:41.125 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/groww/api/__pycache__
2025-06-23 23:04:41.125 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/iifl/api/__pycache__
2025-06-23 23:04:41.125 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/jainam/api/__pycache__
2025-06-23 23:04:41.125 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/jainampro/api/__pycache__
2025-06-23 23:04:41.125 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/kotak/api/__pycache__
2025-06-23 23:04:41.126 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/paytm/api/__pycache__
2025-06-23 23:04:41.126 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/pocketful/api/__pycache__
2025-06-23 23:04:41.126 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/shoonya/api/__pycache__
2025-06-23 23:04:41.127 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/tradejini/api/__pycache__
2025-06-23 23:04:41.127 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/upstox/api/__pycache__
2025-06-23 23:04:41.127 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/wisdom/api/__pycache__
2025-06-23 23:04:41.127 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/zebu/api/__pycache__
2025-06-23 23:04:41.127 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/zerodha/api/__pycache__
2025-06-23 23:04:41.128 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base copy/broker/zerodha/streaming/__pycache__
2025-06-23 23:05:07.596 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/algofactory-base
2025-06-23 23:05:16.073 [info] 'WorkspaceManager[home]' Directory created: ubuntu/algofactory-base
2025-06-23 23:05:16.073 [info] 'WorkspaceManager[home]' Directory removed: ubuntu/algofactory-base copy
2025-06-23 23:06:16.794 [info] 'PathMap' Closed source folder /home with id 100
2025-06-23 23:06:16.795 [info] 'OpenFileManager' Closed source folder 100
