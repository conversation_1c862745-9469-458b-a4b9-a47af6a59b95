2025-06-23 20:12:55.167 [info] [main] Log level: Info
2025-06-23 20:12:55.167 [info] [main] Validating found git in: "git"
2025-06-23 20:12:55.167 [info] [main] Using git "2.43.0" from "git"
2025-06-23 20:12:55.167 [info] [Model][doInitialScan] Initial repository scan started
2025-06-23 20:12:55.167 [info] > git rev-parse --show-toplevel [806ms]
2025-06-23 20:12:55.167 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-23 20:12:55.167 [info] > git rev-parse --show-toplevel [96ms]
2025-06-23 20:12:55.176 [info] > git rev-parse --git-dir --git-common-dir [15ms]
2025-06-23 20:12:55.212 [info] [Model][openRepository] Opened repository (path): /home/<USER>/algofactory-multi/algofactory-8010
2025-06-23 20:12:55.212 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/algofactory-multi/algofactory-8010
2025-06-23 20:12:55.236 [info] > git rev-parse --show-toplevel [11ms]
2025-06-23 20:12:55.236 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-23 20:12:55.240 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-23 20:12:55.243 [info] > git config --get commit.template [22ms]
2025-06-23 20:12:55.273 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [25ms]
2025-06-23 20:12:55.277 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:12:55.284 [info] > git rev-parse refs/remotes/origin/main [7ms]
2025-06-23 20:12:55.559 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [264ms]
2025-06-23 20:12:55.632 [info] > git show --textconv :test/iteration_test.py [5ms]
2025-06-23 20:12:55.636 [info] > git ls-files --stage -- test/iteration_test.py [4ms]
2025-06-23 20:12:55.643 [info] > git cat-file -s 17a89c8e4837e88ca06ef87b73905f129bd64413 [4ms]
2025-06-23 20:12:55.716 [info] > git blame --root --incremental 89ac1fedef89963fb80f591849bbcbe1dc91b13e -- test/iteration_test.py [5ms]
2025-06-23 20:12:55.779 [info] > git check-ignore -v -z --stdin [4ms]
2025-06-23 20:12:55.798 [info] > git status -z -uall [509ms]
2025-06-23 20:12:56.631 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [729ms]
2025-06-23 20:12:56.697 [info] > git config --get --local branch.main.vscode-merge-base [50ms]
2025-06-23 20:12:56.914 [info] > git config --get commit.template [731ms]
2025-06-23 20:12:56.952 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [216ms]
2025-06-23 20:12:57.068 [info] > git merge-base refs/heads/main refs/remotes/origin/main [105ms]
2025-06-23 20:12:57.090 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [34ms]
2025-06-23 20:12:57.104 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:12:57.104 [info] > git diff --name-status -z --diff-filter=ADMR 89ac1fedef89963fb80f591849bbcbe1dc91b13e...refs/remotes/origin/main [28ms]
2025-06-23 20:12:57.152 [info] > git rev-parse refs/remotes/origin/main [48ms]
2025-06-23 20:12:58.008 [info] > git status -z -uall [846ms]
2025-06-23 20:12:58.008 [info] > git check-ignore -v -z --stdin [146ms]
2025-06-23 20:12:58.054 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [886ms]
2025-06-23 20:13:13.842 [info] > git config --get commit.template [4ms]
2025-06-23 20:13:13.859 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [17ms]
2025-06-23 20:13:13.862 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:13:13.863 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:13:13.874 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 20:13:14.329 [info] > git status -z -uall [463ms]
2025-06-23 20:13:19.358 [info] > git config --get commit.template [5ms]
2025-06-23 20:13:19.360 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:13:19.362 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:13:19.364 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 20:13:19.382 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-23 20:13:19.413 [info] > git status -z -uall [46ms]
2025-06-23 20:13:24.444 [info] > git config --get commit.template [7ms]
2025-06-23 20:13:24.450 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-23 20:13:24.453 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:13:24.453 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 20:13:24.466 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 20:13:24.909 [info] > git status -z -uall [451ms]
2025-06-23 20:13:32.004 [info] > git config --get commit.template [5ms]
2025-06-23 20:13:32.007 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 20:13:32.011 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:13:32.012 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 20:13:32.027 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 20:13:32.049 [info] > git status -z -uall [34ms]
2025-06-23 20:13:42.224 [info] > git config --get commit.template [4ms]
2025-06-23 20:13:42.226 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:13:42.229 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:13:42.230 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:13:42.245 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 20:13:42.269 [info] > git status -z -uall [35ms]
2025-06-23 20:13:47.365 [info] > git config --get commit.template [13ms]
2025-06-23 20:13:47.367 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-23 20:13:47.372 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:13:47.373 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:13:47.399 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-06-23 20:13:47.445 [info] > git status -z -uall [69ms]
2025-06-23 20:13:52.477 [info] > git config --get commit.template [5ms]
2025-06-23 20:13:52.478 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-23 20:13:52.481 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:13:52.482 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:13:52.495 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 20:13:52.519 [info] > git status -z -uall [33ms]
2025-06-23 20:14:00.388 [info] > git config --get commit.template [1ms]
2025-06-23 20:14:00.392 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:14:00.395 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:14:00.397 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 20:14:00.412 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-23 20:14:00.435 [info] > git status -z -uall [34ms]
2025-06-23 20:14:10.567 [info] > git config --get commit.template [0ms]
2025-06-23 20:14:10.573 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:14:10.575 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:14:10.576 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:14:10.590 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-23 20:14:10.615 [info] > git status -z -uall [35ms]
2025-06-23 20:14:15.693 [info] > git config --get commit.template [8ms]
2025-06-23 20:14:15.694 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:14:15.700 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:14:15.701 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 20:14:15.716 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 20:14:15.750 [info] > git status -z -uall [46ms]
2025-06-23 20:14:20.770 [info] > git config --get commit.template [3ms]
2025-06-23 20:14:20.780 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-23 20:14:20.785 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:14:20.785 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 20:14:20.799 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 20:14:20.821 [info] > git status -z -uall [32ms]
2025-06-23 20:14:24.008 [info] > git rev-parse --show-toplevel [6ms]
2025-06-23 20:14:24.018 [info] > git rev-parse --git-dir --git-common-dir [4ms]
2025-06-23 20:14:24.024 [info] [Model][openRepository] Opened repository (path): /home/<USER>/myproject/algofactory
2025-06-23 20:14:24.024 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/myproject/algofactory
2025-06-23 20:14:24.030 [info] > git config --get commit.template [2ms]
2025-06-23 20:14:24.047 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [14ms]
2025-06-23 20:14:24.051 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-23 20:14:24.052 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:14:24.067 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-23 20:14:24.404 [info] > git show --textconv :dashboard.sh [5ms]
2025-06-23 20:14:24.414 [info] > git ls-files --stage -- dashboard.sh [11ms]
2025-06-23 20:14:24.418 [info] > git hash-object -t tree /dev/null [10ms]
2025-06-23 20:14:24.418 [warning] [GitFileSystemProvider][readFile] File not found - git:/home/<USER>/myproject/algofactory/dashboard.sh.git?%7B%22path%22%3A%22%2Fhome%2Fubuntu%2Fmyproject%2Falgofactory%2Fdashboard.sh%22%2C%22ref%22%3A%22%22%7D
2025-06-23 20:14:24.419 [info] > git hash-object -t tree /dev/null [2ms]
2025-06-23 20:14:24.419 [warning] [GitFileSystemProvider][stat] File not found - git:/home/<USER>/myproject/algofactory/dashboard.sh.git?%7B%22path%22%3A%22%2Fhome%2Fubuntu%2Fmyproject%2Falgofactory%2Fdashboard.sh%22%2C%22ref%22%3A%22%22%7D
2025-06-23 20:14:24.598 [info] > git status -z -uall [542ms]
2025-06-23 20:14:24.625 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-06-23 20:14:24.631 [info] > git config --get commit.template [6ms]
2025-06-23 20:14:24.634 [info] > git config --get --local branch.main.vscode-merge-base [4ms]
2025-06-23 20:14:24.643 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-06-23 20:14:24.646 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/algofactory/.git/refs/remotes/origin/main'
2025-06-23 20:14:24.647 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [7ms]
2025-06-23 20:14:24.651 [info] > git rev-parse refs/remotes/origin/main [5ms]
2025-06-23 20:14:24.675 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [16ms]
2025-06-23 20:14:24.675 [info] > git merge-base refs/heads/main refs/remotes/origin/main [25ms]
2025-06-23 20:14:24.683 [info] > git check-ignore -v -z --stdin [15ms]
2025-06-23 20:14:24.684 [info] > git check-ignore -v -z --stdin [10ms]
2025-06-23 20:14:24.687 [info] > git diff --name-status -z --diff-filter=ADMR 89ac1fedef89963fb80f591849bbcbe1dc91b13e...refs/remotes/origin/main [4ms]
2025-06-23 20:14:24.774 [info] > git status -z -uall [119ms]
2025-06-23 20:14:28.697 [info] > git config --get commit.template [1ms]
2025-06-23 20:14:28.702 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:14:28.705 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:14:28.706 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:14:28.721 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 20:14:28.745 [info] > git status -z -uall [36ms]
2025-06-23 20:14:39.034 [info] > git config --get commit.template [5ms]
2025-06-23 20:14:39.036 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:14:39.039 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:14:39.040 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:14:39.052 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 20:14:39.076 [info] > git status -z -uall [32ms]
2025-06-23 20:14:44.171 [info] > git config --get commit.template [4ms]
2025-06-23 20:14:44.182 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-23 20:14:44.186 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:14:44.187 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:14:44.208 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 20:14:44.257 [info] > git status -z -uall [66ms]
2025-06-23 20:14:49.286 [info] > git config --get commit.template [5ms]
2025-06-23 20:14:49.287 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:14:49.292 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:14:49.292 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 20:14:49.307 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 20:14:49.329 [info] > git status -z -uall [33ms]
2025-06-23 20:14:57.124 [info] > git config --get commit.template [5ms]
2025-06-23 20:14:57.125 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:14:57.129 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:14:57.130 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:14:57.143 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 20:14:57.168 [info] > git status -z -uall [34ms]
2025-06-23 20:15:07.294 [info] > git config --get commit.template [2ms]
2025-06-23 20:15:07.299 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:15:07.302 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:15:07.303 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 20:15:07.318 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 20:15:07.339 [info] > git status -z -uall [32ms]
2025-06-23 20:15:12.378 [info] > git config --get commit.template [15ms]
2025-06-23 20:15:12.381 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 20:15:12.386 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:15:12.391 [info] > git rev-parse refs/remotes/origin/main [5ms]
2025-06-23 20:15:12.430 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [12ms]
2025-06-23 20:15:12.474 [info] > git status -z -uall [76ms]
2025-06-23 20:15:17.500 [info] > git config --get commit.template [4ms]
2025-06-23 20:15:17.505 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:15:17.509 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:15:17.510 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:15:17.526 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-23 20:15:17.547 [info] > git status -z -uall [33ms]
2025-06-23 20:15:25.421 [info] > git config --get commit.template [8ms]
2025-06-23 20:15:25.424 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:15:25.427 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:15:25.428 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:15:25.442 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 20:15:25.464 [info] > git status -z -uall [32ms]
2025-06-23 20:16:13.275 [info] > git config --get commit.template [5ms]
2025-06-23 20:16:13.276 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:16:13.280 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:16:13.281 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:16:13.296 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 20:16:13.320 [info] > git status -z -uall [35ms]
2025-06-23 20:16:18.339 [info] > git config --get commit.template [2ms]
2025-06-23 20:16:18.346 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:16:18.350 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:16:18.351 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:16:18.365 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 20:16:18.388 [info] > git status -z -uall [34ms]
2025-06-23 20:16:23.418 [info] > git config --get commit.template [4ms]
2025-06-23 20:16:23.420 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:16:23.424 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:16:23.424 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 20:16:23.440 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 20:16:23.461 [info] > git status -z -uall [33ms]
2025-06-23 20:16:31.970 [info] > git config --get commit.template [2ms]
2025-06-23 20:16:31.974 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:16:31.977 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:16:31.978 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:16:31.991 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 20:16:32.012 [info] > git status -z -uall [31ms]
2025-06-23 20:16:37.107 [info] > git config --get commit.template [2ms]
2025-06-23 20:16:37.114 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:16:37.121 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:16:37.123 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 20:16:37.142 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-23 20:16:37.175 [info] > git status -z -uall [45ms]
2025-06-23 20:16:42.216 [info] > git config --get commit.template [5ms]
2025-06-23 20:16:42.217 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:16:42.222 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:16:42.223 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:16:42.235 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 20:16:42.259 [info] > git status -z -uall [33ms]
2025-06-23 20:16:50.094 [info] > git config --get commit.template [1ms]
2025-06-23 20:16:50.101 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 20:16:50.104 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:16:50.105 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:16:50.120 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 20:16:50.205 [info] > git status -z -uall [96ms]
2025-06-23 20:17:00.227 [info] > git config --get commit.template [1ms]
2025-06-23 20:17:00.232 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:17:00.235 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:17:00.236 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:17:00.248 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 20:17:00.272 [info] > git status -z -uall [32ms]
2025-06-23 20:17:05.434 [info] > git config --get commit.template [19ms]
2025-06-23 20:17:05.487 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [14ms]
2025-06-23 20:17:05.550 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:17:05.786 [info] > git rev-parse refs/remotes/origin/main [236ms]
2025-06-23 20:17:05.940 [info] > git status -z -uall [140ms]
2025-06-23 20:17:05.942 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [124ms]
2025-06-23 20:17:10.980 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:17:10.981 [info] > git config --get commit.template [5ms]
2025-06-23 20:17:10.984 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:17:10.984 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 20:17:10.998 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 20:17:11.022 [info] > git status -z -uall [34ms]
2025-06-23 20:17:18.327 [info] > git config --get commit.template [5ms]
2025-06-23 20:17:18.329 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:17:18.332 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:17:18.333 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:17:18.348 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 20:17:18.369 [info] > git status -z -uall [33ms]
2025-06-23 20:17:28.611 [info] > git config --get commit.template [1ms]
2025-06-23 20:17:28.616 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:17:28.621 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:17:28.621 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 20:17:28.641 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-23 20:17:28.662 [info] > git status -z -uall [38ms]
2025-06-23 20:17:33.698 [info] > git config --get commit.template [9ms]
2025-06-23 20:17:33.700 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 20:17:33.705 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:17:33.705 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 20:17:33.725 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-23 20:17:33.765 [info] > git status -z -uall [55ms]
2025-06-23 20:17:38.798 [info] > git config --get commit.template [1ms]
2025-06-23 20:17:38.803 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:17:38.806 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:17:38.807 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:17:38.820 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 20:17:38.844 [info] > git status -z -uall [33ms]
2025-06-23 20:17:46.545 [info] > git config --get commit.template [8ms]
2025-06-23 20:17:46.545 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 20:17:46.549 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:17:46.550 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:17:46.564 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 20:17:46.587 [info] > git status -z -uall [33ms]
2025-06-23 20:17:56.671 [info] > git config --get commit.template [4ms]
2025-06-23 20:17:56.673 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:17:56.677 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:17:56.678 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:17:56.698 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-23 20:17:56.723 [info] > git status -z -uall [39ms]
2025-06-23 20:18:03.426 [info] > git config --get commit.template [3ms]
2025-06-23 20:18:03.433 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 20:18:03.437 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:18:03.444 [info] > git rev-parse refs/remotes/origin/main [8ms]
2025-06-23 20:18:03.479 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [16ms]
2025-06-23 20:18:03.690 [info] > git status -z -uall [242ms]
2025-06-23 20:18:14.894 [info] > git config --get commit.template [1ms]
2025-06-23 20:18:14.899 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:18:14.903 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:18:14.903 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 20:18:14.916 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-23 20:18:14.941 [info] > git status -z -uall [35ms]
2025-06-23 20:18:24.930 [info] > git config --get commit.template [4ms]
2025-06-23 20:18:24.932 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:18:24.935 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:18:24.936 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:18:24.956 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-23 20:18:24.975 [info] > git status -z -uall [33ms]
2025-06-23 20:18:32.044 [info] > git config --get commit.template [0ms]
2025-06-23 20:18:32.050 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-23 20:18:32.054 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:18:32.055 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:18:32.073 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 20:18:32.096 [info] > git status -z -uall [35ms]
2025-06-23 20:18:43.055 [info] > git config --get commit.template [6ms]
2025-06-23 20:18:43.056 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 20:18:43.059 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:18:43.060 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:18:43.076 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 20:18:43.100 [info] > git status -z -uall [37ms]
2025-06-23 20:18:44.644 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-23 20:18:53.527 [info] > git config --get commit.template [0ms]
2025-06-23 20:18:53.532 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:18:53.536 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:18:53.536 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 20:18:53.551 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 20:18:53.576 [info] > git status -z -uall [36ms]
2025-06-23 20:18:59.827 [info] > git config --get commit.template [5ms]
2025-06-23 20:18:59.828 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:18:59.832 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:18:59.832 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 20:18:59.845 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-23 20:18:59.869 [info] > git status -z -uall [33ms]
2025-06-23 20:19:11.321 [info] > git config --get commit.template [4ms]
2025-06-23 20:19:11.322 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:19:11.326 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:19:11.326 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 20:19:11.340 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 20:19:11.364 [info] > git status -z -uall [34ms]
2025-06-23 20:19:13.498 [info] > git check-ignore -v -z --stdin [6ms]
2025-06-23 20:19:13.499 [info] > git check-ignore -v -z --stdin [3ms]
2025-06-23 20:19:21.550 [info] > git config --get commit.template [1ms]
2025-06-23 20:19:21.555 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:19:21.559 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:19:21.559 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 20:19:21.575 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 20:19:21.600 [info] > git status -z -uall [37ms]
2025-06-23 20:19:26.641 [info] > git config --get commit.template [11ms]
2025-06-23 20:19:26.643 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 20:19:26.649 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:19:26.649 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 20:19:26.677 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 20:19:26.716 [info] > git status -z -uall [60ms]
2025-06-23 20:19:31.742 [info] > git config --get commit.template [1ms]
2025-06-23 20:19:31.751 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-23 20:19:31.754 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:19:31.755 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:19:31.769 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 20:19:31.803 [info] > git status -z -uall [45ms]
2025-06-23 20:19:34.021 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-23 20:19:39.594 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-23 20:19:39.675 [info] > git config --get commit.template [1ms]
2025-06-23 20:19:39.679 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-23 20:19:39.683 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:19:39.686 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-06-23 20:19:39.698 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 20:19:39.724 [info] > git status -z -uall [34ms]
2025-06-23 20:19:49.732 [info] > git config --get commit.template [5ms]
2025-06-23 20:19:49.733 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:19:49.736 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:19:49.737 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:19:49.752 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 20:19:49.773 [info] > git status -z -uall [32ms]
2025-06-23 20:19:54.820 [info] > git config --get commit.template [6ms]
2025-06-23 20:19:54.821 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-23 20:19:54.828 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:19:54.829 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:19:54.861 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-23 20:19:54.907 [info] > git status -z -uall [70ms]
2025-06-23 20:19:59.935 [info] > git config --get commit.template [8ms]
2025-06-23 20:19:59.938 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-23 20:19:59.941 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:19:59.944 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-06-23 20:19:59.963 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [12ms]
2025-06-23 20:20:00.452 [info] > git status -z -uall [505ms]
2025-06-23 20:20:05.730 [info] > git check-ignore -v -z --stdin [7ms]
2025-06-23 20:20:07.967 [info] > git config --get commit.template [4ms]
2025-06-23 20:20:07.969 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:20:07.973 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:20:07.974 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:20:07.983 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-23 20:20:08.011 [info] > git status -z -uall [34ms]
2025-06-23 20:20:16.173 [info] > git check-ignore -v -z --stdin [6ms]
2025-06-23 20:20:18.276 [info] > git config --get commit.template [5ms]
2025-06-23 20:20:18.277 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:20:18.281 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:20:18.282 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:20:18.296 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 20:20:18.320 [info] > git status -z -uall [35ms]
2025-06-23 20:20:23.353 [info] > git config --get commit.template [11ms]
2025-06-23 20:20:23.361 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-23 20:20:23.371 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:20:23.377 [info] > git rev-parse refs/remotes/origin/main [6ms]
2025-06-23 20:20:23.400 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-23 20:20:23.439 [info] > git status -z -uall [58ms]
2025-06-23 20:20:28.478 [info] > git config --get commit.template [5ms]
2025-06-23 20:20:28.479 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:20:28.483 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:20:28.484 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:20:28.496 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 20:20:28.521 [info] > git status -z -uall [33ms]
2025-06-23 20:20:31.268 [info] > git rev-parse --show-toplevel [2ms]
2025-06-23 20:20:31.268 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-23 20:20:36.426 [info] > git config --get commit.template [6ms]
2025-06-23 20:20:36.427 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:20:36.431 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:20:36.431 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 20:20:36.445 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 20:20:36.468 [info] > git status -z -uall [33ms]
2025-06-23 20:20:46.754 [info] > git config --get commit.template [5ms]
2025-06-23 20:20:46.755 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:20:46.759 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:20:46.760 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:20:46.772 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-23 20:20:46.798 [info] > git status -z -uall [35ms]
2025-06-23 20:20:51.816 [info] > git config --get commit.template [2ms]
2025-06-23 20:20:51.823 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:20:51.827 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:20:51.828 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:20:51.845 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-23 20:20:51.890 [info] > git status -z -uall [58ms]
2025-06-23 20:20:56.955 [info] > git config --get commit.template [8ms]
2025-06-23 20:20:56.984 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-23 20:20:57.008 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:20:57.018 [info] > git rev-parse refs/remotes/origin/main [10ms]
2025-06-23 20:20:57.059 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [16ms]
2025-06-23 20:20:57.207 [info] > git status -z -uall [184ms]
2025-06-23 20:36:33.396 [info] > git config --get commit.template [4ms]
2025-06-23 20:36:33.409 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-23 20:36:33.413 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:36:33.603 [info] > git rev-parse refs/remotes/origin/main [190ms]
2025-06-23 20:36:33.622 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 20:36:33.669 [info] > git status -z -uall [59ms]
2025-06-23 20:36:38.705 [info] > git config --get commit.template [7ms]
2025-06-23 20:36:38.705 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:36:38.709 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:36:38.709 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:36:38.722 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 20:36:38.748 [info] > git status -z -uall [35ms]
2025-06-23 20:36:43.065 [info] > git check-ignore -v -z --stdin [5ms]
2025-06-23 20:36:47.263 [info] > git config --get commit.template [5ms]
2025-06-23 20:36:47.265 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:36:47.268 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:36:47.269 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:36:47.282 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 20:36:47.371 [info] > git status -z -uall [97ms]
2025-06-23 20:36:53.877 [info] > git config --get commit.template [3ms]
2025-06-23 20:36:53.884 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:36:53.888 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:36:53.892 [info] > git rev-parse refs/remotes/origin/main [4ms]
2025-06-23 20:36:53.906 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 20:36:53.930 [info] > git status -z -uall [35ms]
2025-06-23 20:37:05.368 [info] > git config --get commit.template [64ms]
2025-06-23 20:37:05.376 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:37:05.381 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:37:05.386 [info] > git rev-parse refs/remotes/origin/main [5ms]
2025-06-23 20:37:05.399 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 20:37:05.422 [info] > git status -z -uall [33ms]
2025-06-23 20:37:15.429 [info] > git config --get commit.template [1ms]
2025-06-23 20:37:15.436 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:37:15.440 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:37:15.440 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:37:15.454 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 20:37:15.481 [info] > git status -z -uall [37ms]
2025-06-23 20:37:22.387 [info] > git config --get commit.template [4ms]
2025-06-23 20:37:22.392 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-23 20:37:22.398 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:37:22.401 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-06-23 20:37:22.418 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-23 20:37:22.441 [info] > git status -z -uall [36ms]
2025-06-23 20:37:30.252 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-23 20:37:31.879 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-23 20:37:33.449 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-23 20:37:33.549 [info] > git config --get commit.template [3ms]
2025-06-23 20:37:33.554 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:37:33.558 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:37:33.559 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:37:33.574 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 20:37:33.598 [info] > git status -z -uall [35ms]
2025-06-23 20:37:34.096 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-23 20:37:43.795 [info] > git config --get commit.template [4ms]
2025-06-23 20:37:43.797 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:37:43.801 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:37:43.801 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:37:43.815 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 20:37:43.838 [info] > git status -z -uall [33ms]
2025-06-23 20:37:50.350 [info] > git config --get commit.template [0ms]
2025-06-23 20:37:50.356 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:37:50.359 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:37:50.422 [info] > git rev-parse refs/remotes/origin/main [63ms]
2025-06-23 20:37:50.437 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 20:37:50.466 [info] > git status -z -uall [39ms]
2025-06-23 20:37:55.267 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-23 20:37:56.386 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-23 20:38:01.825 [info] > git config --get commit.template [5ms]
2025-06-23 20:38:01.831 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:38:01.835 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:38:01.836 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:38:01.848 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-23 20:38:01.872 [info] > git status -z -uall [33ms]
2025-06-23 20:38:06.130 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-23 20:38:07.346 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-23 20:38:11.186 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-23 20:38:11.724 [info] > git check-ignore -v -z --stdin [4ms]
2025-06-23 20:38:12.052 [info] > git config --get commit.template [5ms]
2025-06-23 20:38:12.054 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:38:12.057 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:38:12.058 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:38:12.072 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 20:38:12.097 [info] > git status -z -uall [35ms]
2025-06-23 20:38:17.975 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-23 20:38:18.735 [info] > git config --get commit.template [6ms]
2025-06-23 20:38:18.736 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:38:18.739 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:38:18.740 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:38:18.756 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 20:38:18.778 [info] > git status -z -uall [34ms]
2025-06-23 20:38:25.395 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-23 20:38:29.150 [info] > git show --textconv :package-lock.json [10ms]
2025-06-23 20:38:29.150 [info] > git ls-files --stage -- package-lock.json [4ms]
2025-06-23 20:38:29.155 [info] > git cat-file -s 6a1954088dff52a5b3f5e295de456c10b2217171 [1ms]
2025-06-23 20:38:30.151 [info] > git config --get commit.template [6ms]
2025-06-23 20:38:30.164 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 20:38:30.172 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:38:30.181 [info] > git rev-parse refs/remotes/origin/main [9ms]
2025-06-23 20:38:30.202 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-23 20:38:30.233 [info] > git status -z -uall [45ms]
2025-06-23 20:38:38.250 [info] > git show --textconv :start.sh [6ms]
2025-06-23 20:38:38.251 [info] > git ls-files --stage -- start.sh [2ms]
2025-06-23 20:38:38.256 [info] > git cat-file -s 6fec8e8c92ff7dfbd30745f7218074badabc077b [1ms]
2025-06-23 20:38:40.204 [info] > git config --get commit.template [1ms]
2025-06-23 20:38:40.209 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-23 20:38:40.214 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:38:40.214 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 20:38:40.229 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 20:38:40.254 [info] > git status -z -uall [36ms]
2025-06-23 20:38:45.282 [info] > git config --get commit.template [9ms]
2025-06-23 20:38:45.287 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-23 20:38:45.297 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:38:45.298 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:38:45.321 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-23 20:38:45.352 [info] > git status -z -uall [48ms]
2025-06-23 20:38:50.419 [info] > git config --get commit.template [34ms]
2025-06-23 20:38:50.433 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [40ms]
2025-06-23 20:38:50.438 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:38:50.439 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:38:50.452 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 20:38:50.914 [info] > git status -z -uall [471ms]
2025-06-23 20:38:58.543 [info] > git config --get commit.template [6ms]
2025-06-23 20:38:58.544 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:38:58.548 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:38:58.550 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 20:38:58.566 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 20:38:58.603 [info] > git status -z -uall [49ms]
2025-06-23 20:39:11.982 [info] > git config --get commit.template [1ms]
2025-06-23 20:39:11.987 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:39:11.991 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:39:11.992 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:39:12.006 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 20:39:12.031 [info] > git status -z -uall [36ms]
2025-06-23 20:45:27.000 [info] > git config --get commit.template [61ms]
2025-06-23 20:45:27.016 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-23 20:45:27.022 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:45:27.023 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:45:27.036 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 20:45:27.070 [info] > git status -z -uall [44ms]
2025-06-23 20:45:32.106 [info] > git config --get commit.template [1ms]
2025-06-23 20:45:32.111 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:45:32.114 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:45:32.114 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 20:45:32.131 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 20:45:32.155 [info] > git status -z -uall [36ms]
2025-06-23 20:45:37.182 [info] > git config --get commit.template [5ms]
2025-06-23 20:45:37.183 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:45:37.187 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:45:37.189 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 20:45:37.203 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 20:45:37.225 [info] > git status -z -uall [33ms]
2025-06-23 20:45:44.253 [info] > git config --get commit.template [4ms]
2025-06-23 20:45:44.254 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:45:44.258 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:45:44.259 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:45:44.271 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 20:45:44.296 [info] > git status -z -uall [33ms]
2025-06-23 20:45:50.849 [info] > git config --get commit.template [5ms]
2025-06-23 20:45:50.850 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:45:50.854 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:45:50.855 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:45:50.867 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 20:45:50.893 [info] > git status -z -uall [35ms]
2025-06-23 20:46:02.330 [info] > git config --get commit.template [5ms]
2025-06-23 20:46:02.331 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:46:02.335 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:46:02.336 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:46:02.348 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-23 20:46:02.372 [info] > git status -z -uall [32ms]
2025-06-23 20:46:12.512 [info] > git config --get commit.template [5ms]
2025-06-23 20:46:12.513 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:46:12.517 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:46:12.518 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:46:12.533 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 20:46:12.556 [info] > git status -z -uall [34ms]
2025-06-23 20:46:19.685 [info] > git config --get commit.template [5ms]
2025-06-23 20:46:19.686 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:46:19.689 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:46:19.690 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:46:19.702 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 20:46:19.728 [info] > git status -z -uall [34ms]
2025-06-23 20:46:30.653 [info] > git config --get commit.template [5ms]
2025-06-23 20:46:30.654 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:46:30.658 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:46:30.659 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:46:30.675 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 20:46:30.695 [info] > git status -z -uall [31ms]
2025-06-23 20:46:40.771 [info] > git config --get commit.template [2ms]
2025-06-23 20:46:40.777 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:46:40.780 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:46:40.782 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 20:46:40.794 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 20:46:40.818 [info] > git status -z -uall [33ms]
2025-06-23 20:46:47.533 [info] > git config --get commit.template [3ms]
2025-06-23 20:46:47.535 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:46:47.539 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:46:47.540 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:46:47.553 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 20:46:47.575 [info] > git status -z -uall [32ms]
2025-06-23 20:46:56.268 [info] > git rev-parse --show-toplevel [1ms]
2025-06-23 20:46:56.269 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-23 20:46:58.824 [info] > git config --get commit.template [0ms]
2025-06-23 20:46:58.829 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:46:58.832 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:46:58.834 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 20:46:58.849 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 20:46:58.880 [info] > git status -z -uall [41ms]
2025-06-23 20:47:08.973 [info] > git config --get commit.template [1ms]
2025-06-23 20:47:08.978 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:47:08.982 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:47:08.983 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:47:08.997 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 20:47:09.020 [info] > git status -z -uall [33ms]
2025-06-23 20:47:15.797 [info] > git config --get commit.template [4ms]
2025-06-23 20:47:15.798 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:47:15.803 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:47:15.803 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 20:47:15.822 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 20:47:15.842 [info] > git status -z -uall [36ms]
2025-06-23 20:47:27.057 [info] > git config --get commit.template [1ms]
2025-06-23 20:47:27.062 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:47:27.065 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:47:27.067 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:47:27.087 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 20:47:27.110 [info] > git status -z -uall [39ms]
2025-06-23 20:47:37.274 [info] > git config --get commit.template [4ms]
2025-06-23 20:47:37.276 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:47:37.280 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:47:37.280 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 20:47:37.293 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-23 20:47:37.317 [info] > git status -z -uall [33ms]
2025-06-23 20:47:43.218 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:47:43.218 [info] > git config --get commit.template [6ms]
2025-06-23 20:47:43.220 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:47:43.222 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 20:47:43.235 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 20:47:43.255 [info] > git status -z -uall [30ms]
2025-06-23 20:47:48.277 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 20:47:48.277 [info] > git config --get commit.template [7ms]
2025-06-23 20:47:48.280 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:47:48.281 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:47:48.295 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 20:47:48.319 [info] > git status -z -uall [34ms]
2025-06-23 20:47:54.423 [info] > git rev-parse --show-toplevel [2ms]
2025-06-23 20:47:54.423 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-23 20:47:55.458 [info] > git config --get commit.template [1ms]
2025-06-23 20:47:55.463 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-23 20:47:55.467 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:47:55.468 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:47:55.481 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 20:47:55.505 [info] > git status -z -uall [34ms]
2025-06-23 20:48:05.591 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [29ms]
2025-06-23 20:48:05.591 [info] > git config --get commit.template [34ms]
2025-06-23 20:48:05.637 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:48:05.639 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 20:48:05.655 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 20:48:05.684 [info] > git status -z -uall [41ms]
2025-06-23 20:48:12.321 [info] > git config --get commit.template [3ms]
2025-06-23 20:48:12.329 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-23 20:48:12.335 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:48:12.335 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 20:48:12.349 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 20:48:12.372 [info] > git status -z -uall [34ms]
2025-06-23 20:48:23.620 [info] > git config --get commit.template [2ms]
2025-06-23 20:48:23.626 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:48:23.629 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:48:23.630 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 20:48:23.644 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 20:48:23.667 [info] > git status -z -uall [33ms]
2025-06-23 20:56:08.340 [info] > git config --get commit.template [5ms]
2025-06-23 20:56:08.342 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:56:08.346 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:56:08.347 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:56:08.363 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-23 20:56:08.387 [info] > git status -z -uall [36ms]
2025-06-23 20:56:13.411 [info] > git config --get commit.template [2ms]
2025-06-23 20:56:13.416 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-23 20:56:13.421 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:56:13.422 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:56:13.436 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 20:56:13.460 [info] > git status -z -uall [34ms]
2025-06-23 20:56:24.056 [info] > git config --get commit.template [5ms]
2025-06-23 20:56:24.063 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:56:24.067 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:56:24.069 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 20:56:24.085 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 20:56:24.109 [info] > git status -z -uall [34ms]
2025-06-23 20:56:34.192 [info] > git config --get commit.template [1ms]
2025-06-23 20:56:34.197 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:56:34.200 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:56:34.202 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 20:56:34.215 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-23 20:56:34.239 [info] > git status -z -uall [34ms]
2025-06-23 20:56:40.986 [info] > git config --get commit.template [6ms]
2025-06-23 20:56:40.986 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:56:40.990 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:56:40.991 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:56:41.006 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 20:56:41.037 [info] > git status -z -uall [43ms]
2025-06-23 20:56:42.239 [info] > git show --textconv :start.sh [6ms]
2025-06-23 20:56:42.240 [info] > git ls-files --stage -- start.sh [3ms]
2025-06-23 20:56:42.245 [info] > git cat-file -s 6fec8e8c92ff7dfbd30745f7218074badabc077b [0ms]
2025-06-23 20:56:52.355 [info] > git config --get commit.template [5ms]
2025-06-23 20:56:52.356 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:56:52.360 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:56:52.361 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:56:52.381 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-23 20:56:52.409 [info] > git status -z -uall [45ms]
2025-06-23 20:57:02.443 [info] > git config --get commit.template [1ms]
2025-06-23 20:57:02.450 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:57:02.452 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:57:02.454 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 20:57:02.466 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 20:57:02.488 [info] > git status -z -uall [31ms]
2025-06-23 20:57:07.527 [info] > git config --get commit.template [8ms]
2025-06-23 20:57:07.530 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 20:57:07.537 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:57:07.538 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:57:07.566 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 20:57:07.614 [info] > git status -z -uall [68ms]
2025-06-23 20:57:12.672 [info] > git config --get commit.template [5ms]
2025-06-23 20:57:12.675 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 20:57:12.679 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:57:12.679 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 20:57:12.691 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-23 20:57:12.714 [info] > git status -z -uall [32ms]
2025-06-23 20:57:17.735 [info] > git config --get commit.template [1ms]
2025-06-23 20:57:17.740 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-23 20:57:17.744 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:57:17.745 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:57:17.756 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-23 20:57:17.781 [info] > git status -z -uall [33ms]
2025-06-23 20:57:22.797 [info] > git config --get commit.template [1ms]
2025-06-23 20:57:22.802 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:57:22.806 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:57:22.806 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 20:57:22.820 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 20:57:22.843 [info] > git status -z -uall [33ms]
2025-06-23 20:57:30.719 [info] > git config --get commit.template [3ms]
2025-06-23 20:57:30.725 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-23 20:57:30.728 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:57:30.729 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:57:30.744 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 20:57:30.767 [info] > git status -z -uall [33ms]
2025-06-23 20:57:35.840 [info] > git config --get commit.template [1ms]
2025-06-23 20:57:36.191 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [337ms]
2025-06-23 20:57:36.213 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:57:36.215 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 20:57:36.268 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [22ms]
2025-06-23 20:57:36.317 [info] > git status -z -uall [90ms]
2025-06-23 20:57:41.342 [info] > git config --get commit.template [1ms]
2025-06-23 20:57:41.349 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 20:57:41.353 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:57:41.355 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 20:57:41.370 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 20:57:41.395 [info] > git status -z -uall [34ms]
2025-06-23 20:57:48.833 [info] > git config --get commit.template [1ms]
2025-06-23 20:57:48.839 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:57:48.842 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:57:48.842 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 20:57:48.858 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 20:57:48.879 [info] > git status -z -uall [32ms]
2025-06-23 20:57:59.052 [info] > git config --get commit.template [6ms]
2025-06-23 20:57:59.053 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:57:59.057 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:57:59.058 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:57:59.069 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 20:57:59.094 [info] > git status -z -uall [33ms]
2025-06-23 20:58:04.203 [info] > git config --get commit.template [8ms]
2025-06-23 20:58:04.206 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 20:58:04.211 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:58:04.213 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 20:58:04.235 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-23 20:58:04.271 [info] > git status -z -uall [54ms]
2025-06-23 20:58:09.331 [info] > git config --get commit.template [34ms]
2025-06-23 20:58:09.349 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [46ms]
2025-06-23 20:58:09.353 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:58:09.355 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 20:58:09.370 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-23 20:58:09.890 [info] > git status -z -uall [531ms]
2025-06-23 20:58:17.299 [info] > git config --get commit.template [5ms]
2025-06-23 20:58:17.300 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:58:17.305 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:58:17.305 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 20:58:17.319 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 20:58:17.343 [info] > git status -z -uall [33ms]
2025-06-23 20:58:27.539 [info] > git config --get commit.template [6ms]
2025-06-23 20:58:27.539 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:58:27.544 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:58:27.544 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 20:58:27.559 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 20:58:27.582 [info] > git status -z -uall [34ms]
2025-06-23 20:58:31.207 [info] > git rev-parse --show-toplevel [5ms]
2025-06-23 20:58:31.207 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-23 20:58:32.606 [info] > git config --get commit.template [2ms]
2025-06-23 20:58:32.616 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-23 20:58:32.623 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:58:32.625 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 20:58:32.649 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 20:58:32.706 [info] > git status -z -uall [76ms]
2025-06-23 20:58:33.228 [info] > git rev-parse --show-toplevel [2ms]
2025-06-23 20:58:33.229 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-23 20:58:37.732 [info] > git config --get commit.template [0ms]
2025-06-23 20:58:37.737 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:58:37.742 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:58:37.742 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 20:58:37.754 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 20:58:37.781 [info] > git status -z -uall [36ms]
2025-06-23 20:58:45.664 [info] > git config --get commit.template [6ms]
2025-06-23 20:58:45.669 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:58:45.673 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:58:45.674 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:58:45.688 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 20:58:45.712 [info] > git status -z -uall [35ms]
2025-06-23 20:58:55.694 [info] > git config --get commit.template [1ms]
2025-06-23 20:58:55.699 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 20:58:55.703 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:58:55.703 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 20:58:55.720 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 20:58:55.743 [info] > git status -z -uall [35ms]
2025-06-23 20:59:42.095 [info] > git config --get commit.template [6ms]
2025-06-23 20:59:42.097 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:59:42.100 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:59:42.101 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:59:42.119 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 20:59:42.141 [info] > git status -z -uall [36ms]
2025-06-23 20:59:47.167 [info] > git config --get commit.template [1ms]
2025-06-23 20:59:47.172 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 20:59:47.176 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:59:47.176 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:59:47.193 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 20:59:47.214 [info] > git status -z -uall [32ms]
2025-06-23 20:59:52.333 [info] > git config --get commit.template [5ms]
2025-06-23 20:59:52.336 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 20:59:52.340 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:59:52.341 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 20:59:52.358 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 20:59:52.404 [info] > git status -z -uall [59ms]
2025-06-23 20:59:57.468 [info] > git config --get commit.template [16ms]
2025-06-23 20:59:57.476 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [12ms]
2025-06-23 20:59:57.485 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 20:59:57.491 [info] > git rev-parse refs/remotes/origin/main [6ms]
2025-06-23 20:59:57.528 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [16ms]
2025-06-23 20:59:58.194 [info] > git status -z -uall [691ms]
2025-06-23 21:00:03.223 [info] > git config --get commit.template [8ms]
2025-06-23 21:00:03.226 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 21:00:03.229 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:00:03.230 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:00:03.241 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 21:00:03.622 [info] > git status -z -uall [388ms]
2025-06-23 21:00:10.585 [info] > git config --get commit.template [5ms]
2025-06-23 21:00:10.586 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:00:10.591 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:00:10.592 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:00:10.609 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-23 21:00:10.630 [info] > git status -z -uall [33ms]
2025-06-23 21:00:20.684 [info] > git config --get commit.template [1ms]
2025-06-23 21:00:20.689 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-23 21:00:20.693 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:00:20.694 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:00:20.708 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:00:20.730 [info] > git status -z -uall [33ms]
2025-06-23 21:00:25.761 [info] > git config --get commit.template [11ms]
2025-06-23 21:00:25.763 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:00:25.773 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:00:25.773 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:00:25.795 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:00:25.845 [info] > git status -z -uall [65ms]
2025-06-23 21:00:30.877 [info] > git config --get commit.template [5ms]
2025-06-23 21:00:30.878 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:00:30.882 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:00:30.885 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-06-23 21:00:30.900 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-23 21:00:30.921 [info] > git status -z -uall [33ms]
2025-06-23 21:00:38.915 [info] > git config --get commit.template [4ms]
2025-06-23 21:00:38.917 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:00:38.921 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:00:38.921 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:00:38.936 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:00:38.961 [info] > git status -z -uall [35ms]
2025-06-23 21:00:48.921 [info] > git config --get commit.template [5ms]
2025-06-23 21:00:48.922 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-23 21:00:48.926 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:00:48.926 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:00:48.942 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-23 21:00:48.964 [info] > git status -z -uall [34ms]
2025-06-23 21:00:53.999 [info] > git config --get commit.template [12ms]
2025-06-23 21:00:53.999 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 21:00:54.005 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:00:54.005 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:00:54.028 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-23 21:00:54.188 [info] > git status -z -uall [177ms]
2025-06-23 21:00:59.210 [info] > git config --get commit.template [3ms]
2025-06-23 21:00:59.216 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:00:59.219 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:00:59.220 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:00:59.232 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-23 21:00:59.257 [info] > git status -z -uall [34ms]
2025-06-23 21:01:07.134 [info] > git config --get commit.template [3ms]
2025-06-23 21:01:07.139 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:01:07.143 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:01:07.143 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:01:07.157 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:01:07.180 [info] > git status -z -uall [33ms]
2025-06-23 21:01:17.316 [info] > git config --get commit.template [1ms]
2025-06-23 21:01:17.322 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:01:17.325 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:01:17.326 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:01:17.340 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:01:17.363 [info] > git status -z -uall [33ms]
2025-06-23 21:01:22.395 [info] > git config --get commit.template [9ms]
2025-06-23 21:01:22.399 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-23 21:01:22.407 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:01:22.407 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:01:22.437 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-23 21:01:22.481 [info] > git status -z -uall [67ms]
2025-06-23 21:01:27.512 [info] > git config --get commit.template [4ms]
2025-06-23 21:01:27.514 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:01:27.518 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:01:27.519 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:01:27.533 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-23 21:01:27.555 [info] > git status -z -uall [32ms]
2025-06-23 21:01:35.323 [info] > git config --get commit.template [0ms]
2025-06-23 21:01:35.329 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:01:35.332 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:01:35.333 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:01:35.349 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-23 21:01:35.373 [info] > git status -z -uall [36ms]
2025-06-23 21:01:45.416 [info] > git config --get commit.template [4ms]
2025-06-23 21:01:45.418 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 21:01:45.424 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:01:45.424 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:01:45.445 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 21:01:45.472 [info] > git status -z -uall [41ms]
2025-06-23 21:01:50.537 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:01:50.540 [info] > git config --get commit.template [14ms]
2025-06-23 21:01:50.546 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:01:50.552 [info] > git rev-parse refs/remotes/origin/main [6ms]
2025-06-23 21:01:50.588 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [21ms]
2025-06-23 21:01:50.650 [info] > git status -z -uall [92ms]
2025-06-23 21:01:55.674 [info] > git config --get commit.template [5ms]
2025-06-23 21:01:55.677 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 21:01:55.680 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:01:55.681 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:01:55.694 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:01:55.717 [info] > git status -z -uall [32ms]
2025-06-23 21:02:03.577 [info] > git config --get commit.template [4ms]
2025-06-23 21:02:03.582 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-23 21:02:03.586 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:02:03.587 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:02:03.601 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-23 21:02:03.623 [info] > git status -z -uall [32ms]
2025-06-23 21:02:13.734 [info] > git config --get commit.template [1ms]
2025-06-23 21:02:13.740 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:02:13.743 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:02:13.744 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:02:13.760 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:02:13.783 [info] > git status -z -uall [34ms]
2025-06-23 21:02:19.827 [info] > git config --get commit.template [8ms]
2025-06-23 21:02:19.832 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-23 21:02:19.840 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:02:19.842 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:02:19.871 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-23 21:02:19.915 [info] > git status -z -uall [66ms]
2025-06-23 21:02:24.937 [info] > git config --get commit.template [1ms]
2025-06-23 21:02:24.942 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-23 21:02:24.946 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:02:24.946 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:02:24.961 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:02:24.993 [info] > git status -z -uall [41ms]
2025-06-23 21:02:31.814 [info] > git config --get commit.template [12ms]
2025-06-23 21:02:31.814 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-23 21:02:31.820 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:02:31.822 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:02:31.834 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-23 21:02:31.861 [info] > git status -z -uall [36ms]
2025-06-23 21:02:42.106 [info] > git config --get commit.template [5ms]
2025-06-23 21:02:42.108 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:02:42.111 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:02:42.113 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:02:42.213 [info] > git status -z -uall [96ms]
2025-06-23 21:02:42.217 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [91ms]
2025-06-23 21:02:54.365 [info] > git config --get commit.template [1ms]
2025-06-23 21:02:54.370 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:02:54.374 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:02:54.374 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:02:54.391 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:02:54.413 [info] > git status -z -uall [34ms]
2025-06-23 21:02:59.434 [info] > git config --get commit.template [4ms]
2025-06-23 21:02:59.436 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:02:59.440 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:02:59.440 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:02:59.453 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:02:59.476 [info] > git status -z -uall [32ms]
2025-06-23 21:03:01.439 [info] > git rev-parse --show-toplevel [0ms]
2025-06-23 21:03:01.448 [info] > git rev-parse --git-dir --git-common-dir [1ms]
2025-06-23 21:03:01.485 [info] [Model][openRepository] Opened repository (path): /home/<USER>/myproject/openalgo
2025-06-23 21:03:01.485 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/myproject/openalgo
2025-06-23 21:03:01.493 [info] > git config --get commit.template [2ms]
2025-06-23 21:03:01.498 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:03:01.502 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/refs/remotes/origin/main'
2025-06-23 21:03:01.503 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:03:01.518 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:03:01.541 [info] > git status -z -uall [34ms]
2025-06-23 21:03:01.561 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-23 21:03:01.569 [info] > git config --get commit.template [8ms]
2025-06-23 21:03:01.569 [info] > git config --get --local branch.main.vscode-merge-base [4ms]
2025-06-23 21:03:01.569 [warning] [Git][config] git config failed: Failed to execute git
2025-06-23 21:03:01.581 [info] > git reflog main --grep-reflog=branch: Created from *. [5ms]
2025-06-23 21:03:01.587 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-23 21:03:01.590 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/refs/remotes/origin/main'
2025-06-23 21:03:01.663 [info] > git rev-parse refs/remotes/origin/main [73ms]
2025-06-23 21:03:01.675 [info] > git symbolic-ref --short refs/remotes/origin/HEAD [88ms]
2025-06-23 21:03:01.685 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-23 21:03:01.686 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [3ms]
2025-06-23 21:03:01.695 [info] > git config --add --local branch.main.vscode-merge-base origin/main [3ms]
2025-06-23 21:03:01.706 [info] > git status -z -uall [38ms] (cancelled)
2025-06-23 21:03:01.707 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-23 21:03:01.712 [info] > git config --get commit.template [7ms]
2025-06-23 21:03:01.716 [info] > git config --get --local branch.main.vscode-merge-base [5ms]
2025-06-23 21:03:01.722 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-23 21:03:01.725 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/refs/remotes/origin/main'
2025-06-23 21:03:01.727 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [6ms]
2025-06-23 21:03:01.733 [info] > git rev-parse refs/remotes/origin/main [8ms]
2025-06-23 21:03:01.743 [info] > git merge-base refs/heads/main refs/remotes/origin/main [12ms]
2025-06-23 21:03:01.752 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-23 21:03:01.754 [info] > git diff --name-status -z --diff-filter=ADMR 641266db13b2fbb05a5330c917327dfc12b2313b...refs/remotes/origin/main [3ms]
2025-06-23 21:03:01.774 [info] > git status -z -uall [37ms]
2025-06-23 21:03:02.174 [info] > git check-ignore -v -z --stdin [3ms]
2025-06-23 21:03:02.175 [info] > git check-ignore -v -z --stdin [9ms]
2025-06-23 21:03:04.492 [info] > git config --get commit.template [1ms]
2025-06-23 21:03:04.497 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:03:04.501 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:03:04.502 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:03:04.515 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:03:04.540 [info] > git status -z -uall [34ms]
2025-06-23 21:03:08.977 [info] > git check-ignore -v -z --stdin [5ms]
2025-06-23 21:03:08.979 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-23 21:03:10.075 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-23 21:03:10.244 [info] > git config --get commit.template [1ms]
2025-06-23 21:03:10.248 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-23 21:03:10.253 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:03:10.253 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:03:10.266 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:03:10.291 [info] > git status -z -uall [33ms]
2025-06-23 21:03:13.291 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-23 21:03:14.342 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-23 21:03:15.320 [info] > git config --get commit.template [9ms]
2025-06-23 21:03:15.324 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-23 21:03:15.328 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:03:15.329 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:03:15.349 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 21:03:15.381 [info] > git status -z -uall [45ms]
2025-06-23 21:03:20.417 [info] > git config --get commit.template [3ms]
2025-06-23 21:03:20.423 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:03:20.426 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:03:20.427 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:03:20.442 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 21:03:20.465 [info] > git status -z -uall [34ms]
2025-06-23 21:03:28.386 [info] > git config --get commit.template [1ms]
2025-06-23 21:03:28.392 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-23 21:03:28.395 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:03:28.395 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:03:28.410 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:03:28.434 [info] > git status -z -uall [33ms]
2025-06-23 21:03:38.630 [info] > git config --get commit.template [4ms]
2025-06-23 21:03:38.631 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:03:38.634 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:03:38.635 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:03:38.650 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:03:38.682 [info] > git status -z -uall [43ms]
2025-06-23 21:03:43.705 [info] > git config --get commit.template [7ms]
2025-06-23 21:03:43.708 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 21:03:43.714 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:03:43.716 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:03:43.741 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:03:43.788 [info] > git status -z -uall [65ms]
2025-06-23 21:03:48.830 [info] > git config --get commit.template [5ms]
2025-06-23 21:03:48.830 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:03:48.834 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:03:48.836 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:03:48.852 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 21:03:48.877 [info] > git status -z -uall [36ms]
2025-06-23 21:03:56.637 [info] > git config --get commit.template [5ms]
2025-06-23 21:03:56.638 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:03:56.642 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:03:56.643 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:03:56.660 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:03:56.681 [info] > git status -z -uall [33ms]
2025-06-23 21:04:06.724 [info] > git config --get commit.template [4ms]
2025-06-23 21:04:06.726 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:04:06.729 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:04:06.730 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:04:06.742 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:04:06.767 [info] > git status -z -uall [33ms]
2025-06-23 21:04:11.807 [info] > git config --get commit.template [15ms]
2025-06-23 21:04:11.807 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-23 21:04:11.813 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:04:11.817 [info] > git rev-parse refs/remotes/origin/main [5ms]
2025-06-23 21:04:11.843 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-06-23 21:04:11.875 [info] > git status -z -uall [51ms]
2025-06-23 21:04:16.919 [info] > git config --get commit.template [5ms]
2025-06-23 21:04:16.920 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:04:16.924 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:04:16.925 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:04:17.040 [info] > git status -z -uall [110ms]
2025-06-23 21:04:17.041 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [106ms]
2025-06-23 21:04:24.841 [info] > git config --get commit.template [4ms]
2025-06-23 21:04:24.842 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:04:24.846 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:04:24.847 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:04:24.867 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-06-23 21:04:24.896 [info] > git status -z -uall [45ms]
2025-06-23 21:04:34.984 [info] > git config --get commit.template [2ms]
2025-06-23 21:04:34.990 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:04:34.993 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:04:34.995 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:04:35.012 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-23 21:04:35.034 [info] > git status -z -uall [36ms]
2025-06-23 21:04:40.080 [info] > git config --get commit.template [5ms]
2025-06-23 21:04:40.128 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [11ms]
2025-06-23 21:04:40.144 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:04:40.145 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:04:40.171 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 21:04:40.218 [info] > git status -z -uall [63ms]
2025-06-23 21:04:45.265 [info] > git config --get commit.template [5ms]
2025-06-23 21:04:45.269 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-23 21:04:45.278 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:04:45.280 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:04:45.299 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 21:04:45.320 [info] > git status -z -uall [36ms]
2025-06-23 21:04:53.018 [info] > git config --get commit.template [1ms]
2025-06-23 21:04:53.024 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:04:53.028 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:04:53.030 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:04:53.047 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 21:04:53.072 [info] > git status -z -uall [38ms]
2025-06-23 21:05:03.202 [info] > git config --get commit.template [0ms]
2025-06-23 21:05:03.208 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-23 21:05:03.212 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:05:03.213 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:05:03.229 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:05:03.256 [info] > git status -z -uall [40ms]
2025-06-23 21:05:08.284 [info] > git config --get commit.template [2ms]
2025-06-23 21:05:08.298 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-23 21:05:08.308 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:05:08.311 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-06-23 21:05:08.332 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-23 21:05:08.371 [info] > git status -z -uall [52ms]
2025-06-23 21:05:13.399 [info] > git config --get commit.template [5ms]
2025-06-23 21:05:13.401 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:05:13.403 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:05:13.405 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:05:13.423 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-23 21:05:13.451 [info] > git status -z -uall [42ms]
2025-06-23 21:05:21.334 [info] > git config --get commit.template [4ms]
2025-06-23 21:05:21.335 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:05:21.340 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:05:21.341 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:05:21.361 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-23 21:05:21.389 [info] > git status -z -uall [45ms]
2025-06-23 21:05:31.431 [info] > git config --get commit.template [5ms]
2025-06-23 21:05:31.433 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:05:31.437 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:05:31.437 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:05:31.452 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 21:05:31.491 [info] > git status -z -uall [49ms]
2025-06-23 21:05:36.525 [info] > git config --get commit.template [12ms]
2025-06-23 21:05:36.526 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-23 21:05:36.532 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:05:36.532 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:05:36.568 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-23 21:05:36.615 [info] > git status -z -uall [77ms]
2025-06-23 21:05:41.642 [info] > git config --get commit.template [4ms]
2025-06-23 21:05:41.644 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 21:05:41.648 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:05:41.648 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:05:41.665 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:05:41.687 [info] > git status -z -uall [35ms]
2025-06-23 21:05:49.622 [info] > git config --get commit.template [7ms]
2025-06-23 21:05:49.623 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:05:49.626 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:05:49.628 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:05:49.643 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-23 21:05:49.664 [info] > git status -z -uall [33ms]
2025-06-23 21:06:00.379 [info] > git config --get commit.template [1ms]
2025-06-23 21:06:00.385 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:06:00.388 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:06:00.389 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:06:00.405 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:06:00.427 [info] > git status -z -uall [34ms]
2025-06-23 21:06:05.459 [info] > git config --get commit.template [6ms]
2025-06-23 21:06:05.461 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 21:06:05.464 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:06:05.465 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:06:05.487 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:06:05.538 [info] > git status -z -uall [69ms]
2025-06-23 21:06:45.095 [info] > git config --get commit.template [1ms]
2025-06-23 21:06:45.101 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:06:45.105 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:06:45.107 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:06:45.124 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-23 21:06:45.155 [info] > git status -z -uall [44ms]
2025-06-23 21:07:21.954 [info] > git config --get commit.template [6ms]
2025-06-23 21:07:21.955 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:07:21.958 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:07:21.959 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:07:21.974 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-23 21:07:21.997 [info] > git status -z -uall [35ms]
2025-06-23 21:07:52.133 [info] > git config --get commit.template [6ms]
2025-06-23 21:07:52.134 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:07:52.139 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:07:52.140 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:07:52.156 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:07:52.180 [info] > git status -z -uall [35ms]
2025-06-23 21:07:57.209 [info] > git config --get commit.template [3ms]
2025-06-23 21:07:57.218 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:07:57.224 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:07:57.225 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:07:57.243 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 21:07:57.298 [info] > git status -z -uall [68ms]
2025-06-23 21:08:02.337 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-23 21:08:02.337 [info] > git config --get commit.template [6ms]
2025-06-23 21:08:02.341 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:08:02.342 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:08:02.354 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:08:02.383 [info] > git status -z -uall [38ms]
2025-06-23 21:08:05.804 [info] > git check-ignore -v -z --stdin [6ms]
2025-06-23 21:08:10.526 [info] > git check-ignore -v -z --stdin [4ms]
2025-06-23 21:08:10.843 [info] > git config --get commit.template [1ms]
2025-06-23 21:08:10.851 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:08:10.857 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:08:10.857 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:08:10.873 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:08:10.897 [info] > git status -z -uall [35ms]
2025-06-23 21:08:12.015 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-23 21:08:14.467 [info] > git check-ignore -v -z --stdin [0ms]
2025-06-23 21:08:15.689 [info] > git check-ignore -v -z --stdin [3ms]
2025-06-23 21:08:20.989 [info] > git config --get commit.template [4ms]
2025-06-23 21:08:20.992 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:08:20.996 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:08:20.998 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:08:21.011 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 21:08:21.041 [info] > git status -z -uall [39ms]
2025-06-23 21:08:26.066 [info] > git config --get commit.template [6ms]
2025-06-23 21:08:26.070 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-23 21:08:26.077 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:08:26.077 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:08:26.107 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [12ms]
2025-06-23 21:08:26.156 [info] > git status -z -uall [75ms]
2025-06-23 21:08:31.188 [info] > git config --get commit.template [5ms]
2025-06-23 21:08:31.190 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:08:31.195 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:08:31.195 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:08:31.209 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 21:08:31.243 [info] > git status -z -uall [44ms]
2025-06-23 21:08:39.124 [info] > git config --get commit.template [5ms]
2025-06-23 21:08:39.126 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:08:39.133 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:08:39.135 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:08:39.153 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:08:39.184 [info] > git status -z -uall [43ms]
2025-06-23 21:09:39.820 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-23 21:09:39.820 [info] > git config --get commit.template [7ms]
2025-06-23 21:09:39.824 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:09:39.825 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:09:39.846 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-06-23 21:09:39.876 [info] > git status -z -uall [47ms]
2025-06-23 21:09:44.895 [info] > git config --get commit.template [4ms]
2025-06-23 21:09:44.896 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:09:44.899 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:09:44.900 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:09:44.914 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:09:44.942 [info] > git status -z -uall [39ms]
2025-06-23 21:09:50.094 [info] > git config --get commit.template [7ms]
2025-06-23 21:09:50.097 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 21:09:50.101 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:09:50.102 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:09:50.128 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [14ms]
2025-06-23 21:09:50.160 [info] > git status -z -uall [53ms]
2025-06-23 21:09:55.184 [info] > git config --get commit.template [5ms]
2025-06-23 21:09:55.186 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:09:55.190 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:09:55.190 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:09:55.203 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:09:55.227 [info] > git status -z -uall [33ms]
2025-06-23 21:10:03.862 [info] > git config --get commit.template [6ms]
2025-06-23 21:10:03.863 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:10:03.866 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:10:03.869 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-06-23 21:10:03.881 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-23 21:10:03.906 [info] > git status -z -uall [33ms]
2025-06-23 21:10:27.104 [info] > git config --get commit.template [14ms]
2025-06-23 21:10:27.323 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [28ms]
2025-06-23 21:10:27.330 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:10:27.333 [info] > git rev-parse refs/remotes/origin/main [4ms]
2025-06-23 21:10:27.367 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-23 21:10:27.825 [info] > git status -z -uall [478ms]
2025-06-23 21:10:32.935 [info] > git config --get commit.template [5ms]
2025-06-23 21:10:32.935 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:10:32.940 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:10:32.941 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:10:32.959 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-23 21:10:32.979 [info] > git status -z -uall [34ms]
2025-06-23 21:10:42.495 [info] > git config --get commit.template [1ms]
2025-06-23 21:10:42.503 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-23 21:10:42.508 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:10:42.509 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:10:42.525 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-23 21:10:42.549 [info] > git status -z -uall [36ms]
2025-06-23 21:10:49.173 [info] > git config --get commit.template [1ms]
2025-06-23 21:10:49.178 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-23 21:10:49.183 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:10:49.183 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:10:49.198 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 21:10:49.221 [info] > git status -z -uall [34ms]
2025-06-23 21:11:00.601 [info] > git config --get commit.template [3ms]
2025-06-23 21:11:00.607 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:11:00.615 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:11:00.617 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:11:00.640 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:11:00.662 [info] > git status -z -uall [36ms]
2025-06-23 21:11:12.849 [info] > git config --get commit.template [620ms]
2025-06-23 21:11:14.638 [info] > git config --get commit.template [1791ms]
2025-06-23 21:11:14.638 [info] > git rev-parse --show-toplevel [1800ms]
2025-06-23 21:11:14.639 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-23 21:11:16.353 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [811ms]
2025-06-23 21:11:16.384 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/refs/remotes/origin/main'
2025-06-23 21:11:16.387 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [56ms]
2025-06-23 21:11:16.417 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:11:16.421 [info] > git rev-parse refs/remotes/origin/main [37ms]
2025-06-23 21:11:16.482 [info] > git rev-parse refs/remotes/origin/main [59ms]
2025-06-23 21:11:16.570 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [104ms]
2025-06-23 21:11:16.588 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [34ms]
2025-06-23 21:11:17.815 [info] > git status -z -uall [1379ms]
2025-06-23 21:11:17.815 [info] > git status -z -uall [1307ms]
2025-06-23 21:11:23.054 [info] > git check-ignore -v -z --stdin [781ms]
2025-06-23 21:11:23.111 [info] > git check-ignore -v -z --stdin [12ms]
2025-06-23 21:11:23.145 [info] > git config --get commit.template [35ms]
2025-06-23 21:11:23.151 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:11:23.160 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:11:23.166 [info] > git rev-parse refs/remotes/origin/main [6ms]
2025-06-23 21:11:23.191 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [13ms]
2025-06-23 21:11:24.115 [info] > git status -z -uall [944ms]
2025-06-23 21:11:24.451 [info] > git config --get commit.template [2ms]
2025-06-23 21:11:24.457 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:11:24.462 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/refs/remotes/origin/main'
2025-06-23 21:11:24.462 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:11:24.475 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:11:24.890 [info] > git status -z -uall [424ms]
2025-06-23 21:11:29.254 [info] > git config --get commit.template [1ms]
2025-06-23 21:11:29.262 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:11:29.266 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:11:29.270 [info] > git rev-parse refs/remotes/origin/main [4ms]
2025-06-23 21:11:29.290 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:11:29.317 [info] > git status -z -uall [42ms]
2025-06-23 21:11:39.493 [info] > git config --get commit.template [5ms]
2025-06-23 21:11:39.496 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 21:11:39.501 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:11:39.502 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:11:39.517 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 21:11:39.547 [info] > git status -z -uall [41ms]
2025-06-23 21:11:44.573 [info] > git config --get commit.template [7ms]
2025-06-23 21:11:44.575 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:11:44.578 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:11:44.580 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:11:44.601 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:11:44.645 [info] > git status -z -uall [60ms]
2025-06-23 21:11:46.086 [info] > git check-ignore -v -z --stdin [5ms]
2025-06-23 21:11:48.751 [info] > git check-ignore -v -z --stdin [10ms]
2025-06-23 21:11:49.688 [info] > git config --get commit.template [4ms]
2025-06-23 21:11:49.693 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-23 21:11:49.697 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:11:49.697 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:11:49.712 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:11:49.739 [info] > git status -z -uall [37ms]
2025-06-23 21:11:51.035 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-23 21:11:52.773 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-23 21:11:57.808 [info] > git config --get commit.template [1ms]
2025-06-23 21:11:57.813 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:11:57.816 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:11:57.818 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:11:57.832 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-23 21:11:57.856 [info] > git status -z -uall [34ms]
2025-06-23 21:12:08.087 [info] > git config --get commit.template [7ms]
2025-06-23 21:12:08.094 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-23 21:12:08.098 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:12:08.099 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:12:08.116 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 21:12:08.138 [info] > git status -z -uall [33ms]
2025-06-23 21:12:13.293 [info] > git config --get commit.template [15ms]
2025-06-23 21:12:13.295 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-23 21:12:13.302 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:12:13.311 [info] > git rev-parse refs/remotes/origin/main [9ms]
2025-06-23 21:12:13.341 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [13ms]
2025-06-23 21:12:13.406 [info] > git status -z -uall [88ms]
2025-06-23 21:12:18.507 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 21:12:18.507 [info] > git config --get commit.template [8ms]
2025-06-23 21:12:18.511 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:12:18.520 [info] > git rev-parse refs/remotes/origin/main [9ms]
2025-06-23 21:12:18.536 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 21:12:18.561 [info] > git status -z -uall [37ms]
2025-06-23 21:12:26.268 [info] > git config --get commit.template [4ms]
2025-06-23 21:12:26.268 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:12:26.271 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:12:26.272 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:12:26.286 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 21:12:26.309 [info] > git status -z -uall [33ms]
2025-06-23 21:12:36.510 [info] > git config --get commit.template [0ms]
2025-06-23 21:12:36.516 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:12:36.520 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:12:36.520 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:12:36.535 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 21:12:36.558 [info] > git status -z -uall [34ms]
2025-06-23 21:12:41.598 [info] > git config --get commit.template [7ms]
2025-06-23 21:12:41.601 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-23 21:12:41.610 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:12:41.610 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:12:41.632 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:12:41.665 [info] > git status -z -uall [50ms]
2025-06-23 21:12:46.765 [info] > git config --get commit.template [6ms]
2025-06-23 21:12:46.777 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-23 21:12:46.782 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:12:46.782 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:12:46.798 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 21:12:46.828 [info] > git status -z -uall [42ms]
2025-06-23 21:12:54.672 [info] > git config --get commit.template [0ms]
2025-06-23 21:12:54.677 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:12:54.681 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:12:54.681 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:12:54.694 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 21:12:54.719 [info] > git status -z -uall [33ms]
2025-06-23 21:13:01.369 [info] > git config --get commit.template [2ms]
2025-06-23 21:13:01.387 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [14ms]
2025-06-23 21:13:01.391 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/refs/remotes/origin/main'
2025-06-23 21:13:01.392 [info] > git check-ignore -v -z --stdin [5ms]
2025-06-23 21:13:01.394 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-06-23 21:13:01.412 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-23 21:13:01.967 [info] > git status -z -uall [569ms]
2025-06-23 21:13:04.967 [info] > git config --get commit.template [5ms]
2025-06-23 21:13:04.971 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-23 21:13:04.976 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:13:04.976 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:13:04.990 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-23 21:13:05.014 [info] > git status -z -uall [33ms]
2025-06-23 21:13:10.046 [info] > git config --get commit.template [7ms]
2025-06-23 21:13:10.048 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 21:13:10.052 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:13:10.054 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:13:10.078 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-23 21:13:10.111 [info] > git status -z -uall [52ms]
2025-06-23 21:13:15.143 [info] > git config --get commit.template [5ms]
2025-06-23 21:13:15.145 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:13:15.148 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:13:15.150 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:13:15.163 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:13:15.189 [info] > git status -z -uall [35ms]
2025-06-23 21:13:23.236 [info] > git config --get commit.template [6ms]
2025-06-23 21:13:23.240 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-23 21:13:23.245 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:13:23.245 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:13:23.259 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 21:13:23.289 [info] > git status -z -uall [40ms]
2025-06-23 21:13:26.144 [info] > git show --textconv :.env [37ms]
2025-06-23 21:13:26.152 [info] > git ls-files --stage -- .env [41ms]
2025-06-23 21:13:26.158 [info] > git hash-object -t tree /dev/null [2ms]
2025-06-23 21:13:26.158 [warning] [GitFileSystemProvider][stat] File not found - git:/home/<USER>/myproject/openalgo/.env.git?%7B%22path%22%3A%22%2Fhome%2Fubuntu%2Fmyproject%2Fopenalgo%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-23 21:13:26.161 [info] > git hash-object -t tree /dev/null [12ms]
2025-06-23 21:13:26.161 [warning] [GitFileSystemProvider][readFile] File not found - git:/home/<USER>/myproject/openalgo/.env.git?%7B%22path%22%3A%22%2Fhome%2Fubuntu%2Fmyproject%2Fopenalgo%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-23 21:13:28.866 [info] > git blame --root --incremental 641266db13b2fbb05a5330c917327dfc12b2313b -- .env [3ms]
2025-06-23 21:13:28.866 [info] fatal: no such path .env in 641266db13b2fbb05a5330c917327dfc12b2313b
2025-06-23 21:13:33.476 [info] > git config --get commit.template [6ms]
2025-06-23 21:13:33.477 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:13:33.481 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:13:33.482 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:13:33.502 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-23 21:13:33.526 [info] > git status -z -uall [41ms]
2025-06-23 21:13:34.012 [info] > git config --get commit.template [5ms]
2025-06-23 21:13:34.014 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:13:34.017 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/refs/remotes/origin/main'
2025-06-23 21:13:34.018 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:13:34.033 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-23 21:13:34.707 [info] > git status -z -uall [685ms]
2025-06-23 21:13:38.619 [info] > git config --get commit.template [16ms]
2025-06-23 21:13:38.620 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-23 21:13:38.629 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:13:38.631 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:13:38.670 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-23 21:13:38.757 [info] > git status -z -uall [114ms]
2025-06-23 21:13:39.761 [info] > git config --get commit.template [18ms]
2025-06-23 21:13:39.766 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-23 21:13:39.776 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/refs/remotes/origin/main'
2025-06-23 21:13:39.779 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-06-23 21:13:39.814 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-23 21:13:40.099 [info] > git status -z -uall [310ms]
2025-06-23 21:13:43.976 [info] > git config --get commit.template [37ms]
2025-06-23 21:13:43.994 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [45ms]
2025-06-23 21:13:44.004 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:13:44.007 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-06-23 21:13:44.033 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-23 21:13:44.548 [info] > git status -z -uall [533ms]
2025-06-23 21:13:45.268 [info] > git config --get commit.template [4ms]
2025-06-23 21:13:45.307 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [26ms]
2025-06-23 21:13:45.317 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/refs/remotes/origin/main'
2025-06-23 21:13:45.318 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:13:45.346 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [15ms]
2025-06-23 21:13:45.956 [info] > git status -z -uall [631ms]
2025-06-23 21:13:50.060 [info] > git config --get commit.template [180ms]
2025-06-23 21:13:50.084 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-23 21:13:50.097 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:13:50.100 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-06-23 21:13:50.137 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [12ms]
2025-06-23 21:13:54.894 [info] > git status -z -uall [4785ms]
2025-06-23 21:13:54.940 [info] > git config --get commit.template [117ms]
2025-06-23 21:13:55.075 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [124ms]
2025-06-23 21:13:55.081 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/refs/remotes/origin/main'
2025-06-23 21:13:55.085 [info] > git rev-parse refs/remotes/origin/main [4ms]
2025-06-23 21:13:55.105 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-23 21:13:55.504 [info] > git status -z -uall [412ms]
2025-06-23 21:14:02.357 [info] > git config --get commit.template [0ms]
2025-06-23 21:14:02.364 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:14:02.369 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:14:02.371 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:14:02.386 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 21:14:02.411 [info] > git status -z -uall [36ms]
2025-06-23 21:14:41.708 [info] > git config --get commit.template [11ms]
2025-06-23 21:14:41.717 [info] > git config --get commit.template [12ms]
2025-06-23 21:14:41.728 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [16ms]
2025-06-23 21:14:41.733 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:14:41.733 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [17ms]
2025-06-23 21:14:41.737 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/refs/remotes/origin/main'
2025-06-23 21:14:41.739 [info] > git rev-parse refs/remotes/origin/main [7ms]
2025-06-23 21:14:41.748 [info] > git rev-parse refs/remotes/origin/main [11ms]
2025-06-23 21:14:41.760 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [12ms]
2025-06-23 21:14:41.769 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-06-23 21:14:42.540 [info] > git status -z -uall [796ms]
2025-06-23 21:14:42.565 [info] > git status -z -uall [813ms]
2025-06-23 21:14:47.571 [info] > git config --get commit.template [7ms]
2025-06-23 21:14:47.576 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-23 21:14:47.581 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:14:47.581 [info] > git config --get commit.template [6ms]
2025-06-23 21:14:47.585 [info] > git rev-parse refs/remotes/origin/main [4ms]
2025-06-23 21:14:47.594 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-23 21:14:47.604 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/refs/remotes/origin/main'
2025-06-23 21:14:47.604 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-23 21:14:47.608 [info] > git rev-parse refs/remotes/origin/main [4ms]
2025-06-23 21:14:47.627 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-23 21:14:47.654 [info] > git status -z -uall [66ms]
2025-06-23 21:14:47.681 [info] > git status -z -uall [66ms]
2025-06-23 21:14:52.684 [info] > git config --get commit.template [2ms]
2025-06-23 21:14:52.689 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-23 21:14:52.694 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:14:52.694 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:14:52.709 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 21:14:52.730 [info] > git status -z -uall [32ms]
2025-06-23 21:14:59.281 [info] > git config --get commit.template [1ms]
2025-06-23 21:14:59.286 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:14:59.290 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:14:59.291 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:14:59.304 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:14:59.328 [info] > git status -z -uall [33ms]
2025-06-23 21:15:04.366 [info] > git config --get commit.template [9ms]
2025-06-23 21:15:04.368 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:15:04.376 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:15:04.376 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:15:04.407 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:15:04.460 [info] > git status -z -uall [75ms]
2025-06-23 21:15:09.513 [info] > git config --get commit.template [8ms]
2025-06-23 21:15:09.516 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 21:15:09.520 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:15:09.521 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:15:09.535 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 21:15:09.563 [info] > git status -z -uall [38ms]
2025-06-23 21:15:15.810 [info] > git config --get commit.template [5ms]
2025-06-23 21:15:15.811 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:15:15.816 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/refs/remotes/origin/main'
2025-06-23 21:15:15.816 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:15:15.828 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 21:15:16.262 [info] > git status -z -uall [442ms]
2025-06-23 21:15:17.604 [info] > git config --get commit.template [7ms]
2025-06-23 21:15:17.605 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 21:15:17.609 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:15:17.616 [info] > git rev-parse refs/remotes/origin/main [7ms]
2025-06-23 21:15:17.629 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:15:17.654 [info] > git status -z -uall [35ms]
2025-06-23 21:15:21.276 [info] > git config --get commit.template [5ms]
2025-06-23 21:15:21.278 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:15:21.282 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/refs/remotes/origin/main'
2025-06-23 21:15:21.284 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:15:21.299 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:15:21.323 [info] > git status -z -uall [36ms]
2025-06-23 21:15:26.333 [info] > git config --get commit.template [1ms]
2025-06-23 21:15:26.341 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 21:15:26.345 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/refs/remotes/origin/main'
2025-06-23 21:15:26.347 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:15:26.364 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:15:26.384 [info] > git status -z -uall [34ms]
2025-06-23 21:15:27.744 [info] > git config --get commit.template [1ms]
2025-06-23 21:15:27.750 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-23 21:15:27.754 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:15:27.755 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:15:27.980 [info] > git status -z -uall [220ms]
2025-06-23 21:15:27.980 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [211ms]
2025-06-23 21:15:31.399 [info] > git config --get commit.template [6ms]
2025-06-23 21:15:31.399 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:15:31.403 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/refs/remotes/origin/main'
2025-06-23 21:15:31.404 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:15:31.422 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-23 21:15:31.442 [info] > git status -z -uall [34ms]
2025-06-23 21:15:33.013 [info] > git config --get commit.template [12ms]
2025-06-23 21:15:33.015 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 21:15:33.022 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:15:33.023 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:15:33.045 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 21:15:33.182 [info] > git status -z -uall [155ms]
2025-06-23 21:15:38.230 [info] > git config --get commit.template [2ms]
2025-06-23 21:15:38.236 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-23 21:15:38.241 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:15:38.241 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:15:38.256 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-23 21:15:38.283 [info] > git status -z -uall [37ms]
2025-06-23 21:15:45.685 [info] > git config --get commit.template [9ms]
2025-06-23 21:15:45.685 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-23 21:15:45.689 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/refs/remotes/origin/main'
2025-06-23 21:15:45.690 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:15:45.704 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:15:45.728 [info] > git status -z -uall [34ms]
2025-06-23 21:15:45.934 [info] > git config --get commit.template [2ms]
2025-06-23 21:15:45.944 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-23 21:15:45.948 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:15:45.951 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-06-23 21:15:45.967 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:15:45.997 [info] > git status -z -uall [42ms]
2025-06-23 21:15:50.742 [info] > git config --get commit.template [2ms]
2025-06-23 21:15:50.747 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:15:50.751 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/refs/remotes/origin/main'
2025-06-23 21:15:50.756 [info] > git rev-parse refs/remotes/origin/main [5ms]
2025-06-23 21:15:50.784 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:15:50.803 [info] > git status -z -uall [36ms]
2025-06-23 21:15:55.818 [info] > git config --get commit.template [5ms]
2025-06-23 21:15:55.820 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:15:55.825 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/refs/remotes/origin/main'
2025-06-23 21:15:55.825 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:15:55.845 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-23 21:15:55.866 [info] > git status -z -uall [36ms]
2025-06-23 21:15:56.060 [info] > git config --get commit.template [1ms]
2025-06-23 21:15:56.071 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-23 21:15:56.076 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:15:56.077 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:15:56.097 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-23 21:15:56.117 [info] > git status -z -uall [35ms]
2025-06-23 21:16:00.890 [info] > git config --get commit.template [8ms]
2025-06-23 21:16:00.893 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-23 21:16:00.898 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/refs/remotes/origin/main'
2025-06-23 21:16:00.899 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:16:00.924 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-23 21:16:00.950 [info] > git status -z -uall [44ms]
2025-06-23 21:16:01.154 [info] > git config --get commit.template [13ms]
2025-06-23 21:16:01.158 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-06-23 21:16:01.174 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:16:01.177 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-06-23 21:16:01.211 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [12ms]
2025-06-23 21:16:01.254 [info] > git status -z -uall [71ms]
2025-06-23 21:16:06.391 [info] > git config --get commit.template [17ms]
2025-06-23 21:16:06.392 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [12ms]
2025-06-23 21:16:06.397 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:16:06.400 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-06-23 21:16:06.416 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:16:06.449 [info] > git status -z -uall [44ms]
2025-06-23 21:16:10.103 [info] > git config --get commit.template [3ms]
2025-06-23 21:16:10.112 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-23 21:16:10.117 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/refs/remotes/origin/main'
2025-06-23 21:16:10.118 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:16:10.131 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-23 21:16:10.596 [info] > git status -z -uall [474ms]
2025-06-23 21:16:14.241 [info] > git config --get commit.template [1ms]
2025-06-23 21:16:14.250 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 21:16:14.255 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:16:14.255 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:16:14.270 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:16:14.717 [info] > git status -z -uall [456ms]
2025-06-23 21:16:15.613 [info] > git config --get commit.template [5ms]
2025-06-23 21:16:15.614 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:16:15.620 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/refs/remotes/origin/main'
2025-06-23 21:16:15.620 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:16:15.636 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 21:16:15.733 [info] > git status -z -uall [108ms]
2025-06-23 21:16:20.746 [info] > git config --get commit.template [1ms]
2025-06-23 21:16:20.753 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:16:20.758 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/refs/remotes/origin/main'
2025-06-23 21:16:20.759 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:16:20.777 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-23 21:16:20.799 [info] > git status -z -uall [35ms]
2025-06-23 21:16:24.455 [info] > git config --get commit.template [1ms]
2025-06-23 21:16:24.462 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:16:24.466 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:16:24.467 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:16:24.487 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 21:16:24.507 [info] > git status -z -uall [35ms]
2025-06-23 21:16:29.530 [info] > git config --get commit.template [2ms]
2025-06-23 21:16:29.540 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:16:29.546 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:16:29.548 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:16:29.578 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-23 21:16:29.621 [info] > git status -z -uall [64ms]
2025-06-23 21:16:34.655 [info] > git config --get commit.template [6ms]
2025-06-23 21:16:34.655 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:16:34.660 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:16:34.660 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:16:34.677 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 21:16:34.700 [info] > git status -z -uall [34ms]
2025-06-23 21:16:34.943 [info] > git check-ignore -v -z --stdin [5ms]
2025-06-23 21:17:03.684 [info] > git config --get commit.template [5ms]
2025-06-23 21:17:03.695 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-23 21:17:03.696 [info] > git config --get commit.template [12ms]
2025-06-23 21:17:03.701 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:17:03.701 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-23 21:17:03.706 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/refs/remotes/origin/main'
2025-06-23 21:17:03.706 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:17:03.720 [info] > git rev-parse refs/remotes/origin/main [19ms]
2025-06-23 21:17:03.738 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [18ms]
2025-06-23 21:17:03.754 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [17ms]
2025-06-23 21:17:03.779 [info] > git status -z -uall [68ms]
2025-06-23 21:17:03.792 [info] > git status -z -uall [63ms]
2025-06-23 21:17:30.744 [info] > git config --get commit.template [25ms]
2025-06-23 21:17:30.745 [info] > git config --get commit.template [31ms]
2025-06-23 21:17:30.758 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [30ms]
2025-06-23 21:17:30.758 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [17ms]
2025-06-23 21:17:30.763 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:17:30.768 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/refs/remotes/origin/main'
2025-06-23 21:17:30.768 [info] > git rev-parse refs/remotes/origin/main [5ms]
2025-06-23 21:17:30.777 [info] > git rev-parse refs/remotes/origin/main [9ms]
2025-06-23 21:17:30.793 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [16ms]
2025-06-23 21:17:30.802 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-06-23 21:17:31.267 [info] > git status -z -uall [494ms]
2025-06-23 21:17:31.351 [info] > git status -z -uall [567ms]
2025-06-23 21:17:32.922 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-23 21:17:36.306 [info] > git config --get commit.template [6ms]
2025-06-23 21:17:36.308 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:17:36.312 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:17:36.313 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:17:36.327 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-23 21:17:36.372 [info] > git status -z -uall [55ms]
2025-06-23 21:17:36.372 [info] > git config --get commit.template [11ms]
2025-06-23 21:17:36.374 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:17:36.390 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/refs/remotes/origin/main'
2025-06-23 21:17:36.392 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:17:36.410 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 21:17:36.429 [info] > git status -z -uall [32ms]
2025-06-23 21:17:41.399 [info] > git config --get commit.template [6ms]
2025-06-23 21:17:41.400 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:17:41.406 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:17:41.406 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:17:41.424 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 21:17:41.445 [info] > git status -z -uall [33ms]
2025-06-23 21:17:49.782 [info] > git config --get commit.template [6ms]
2025-06-23 21:17:49.784 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:17:49.789 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:17:49.789 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:17:49.810 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:17:49.831 [info] > git status -z -uall [36ms]
2025-06-23 21:18:15.858 [info] > git config --get commit.template [8ms]
2025-06-23 21:18:15.869 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-23 21:18:15.869 [info] > git config --get commit.template [12ms]
2025-06-23 21:18:15.874 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:18:15.874 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-23 21:18:15.880 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/refs/remotes/origin/main'
2025-06-23 21:18:15.880 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:18:15.892 [info] > git rev-parse refs/remotes/origin/main [18ms]
2025-06-23 21:18:15.917 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [25ms]
2025-06-23 21:18:15.929 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [12ms]
2025-06-23 21:18:15.961 [info] > git status -z -uall [75ms]
2025-06-23 21:18:15.973 [info] > git status -z -uall [70ms]
2025-06-23 21:18:20.979 [info] > git config --get commit.template [6ms]
2025-06-23 21:18:20.981 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:18:20.986 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/refs/remotes/origin/main'
2025-06-23 21:18:20.988 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:18:21.012 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [12ms]
2025-06-23 21:18:21.015 [info] > git config --get commit.template [3ms]
2025-06-23 21:18:21.026 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:18:21.036 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:18:21.037 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:18:21.058 [info] > git status -z -uall [65ms]
2025-06-23 21:18:21.060 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:18:21.086 [info] > git status -z -uall [39ms]
2025-06-23 21:18:26.121 [info] > git config --get commit.template [1ms]
2025-06-23 21:18:26.130 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-23 21:18:26.135 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:18:26.136 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:18:26.156 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 21:18:26.176 [info] > git status -z -uall [36ms]
2025-06-23 21:18:31.211 [info] > git config --get commit.template [1ms]
2025-06-23 21:18:31.222 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-23 21:18:31.232 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:18:31.234 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:18:31.253 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-23 21:18:31.273 [info] > git status -z -uall [34ms]
2025-06-23 21:18:36.479 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:18:36.481 [info] > git config --get commit.template [9ms]
2025-06-23 21:18:36.489 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:18:36.489 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:18:36.508 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:18:36.530 [info] > git status -z -uall [35ms]
2025-06-23 21:21:59.236 [info] > git config --get commit.template [7ms]
2025-06-23 21:21:59.237 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:21:59.242 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:21:59.242 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:21:59.263 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 21:21:59.284 [info] > git status -z -uall [37ms]
2025-06-23 21:22:04.305 [info] > git config --get commit.template [3ms]
2025-06-23 21:22:04.312 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:22:04.316 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:22:04.317 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:22:04.334 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-23 21:22:04.357 [info] > git status -z -uall [35ms]
2025-06-23 21:22:09.396 [info] > git config --get commit.template [3ms]
2025-06-23 21:22:09.411 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:22:09.425 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:22:09.428 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-06-23 21:22:09.468 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-23 21:22:09.515 [info] > git status -z -uall [77ms]
2025-06-23 21:22:14.565 [info] > git config --get commit.template [6ms]
2025-06-23 21:22:14.570 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-23 21:22:14.576 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:22:14.579 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-06-23 21:22:14.600 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:22:14.626 [info] > git status -z -uall [38ms]
2025-06-23 21:22:22.570 [info] > git config --get commit.template [0ms]
2025-06-23 21:22:22.578 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 21:22:22.583 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:22:22.583 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:22:22.602 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 21:22:22.630 [info] > git status -z -uall [43ms]
2025-06-23 21:22:32.719 [info] > git config --get commit.template [6ms]
2025-06-23 21:22:32.721 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:22:32.726 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:22:32.727 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:22:32.743 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 21:22:32.765 [info] > git status -z -uall [33ms]
2025-06-23 21:22:37.826 [info] > git config --get commit.template [2ms]
2025-06-23 21:22:37.904 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [73ms]
2025-06-23 21:22:37.910 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:22:37.912 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:22:37.933 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-23 21:22:37.959 [info] > git status -z -uall [42ms]
2025-06-23 21:22:42.980 [info] > git config --get commit.template [6ms]
2025-06-23 21:22:42.981 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:22:42.987 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:22:42.987 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:22:43.006 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:22:43.026 [info] > git status -z -uall [34ms]
2025-06-23 21:22:50.973 [info] > git config --get commit.template [6ms]
2025-06-23 21:22:50.974 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:22:50.979 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:22:50.980 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:22:51.002 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 21:22:51.022 [info] > git status -z -uall [37ms]
2025-06-23 21:22:56.362 [info] > git config --get commit.template [1ms]
2025-06-23 21:22:56.368 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-23 21:22:56.374 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:22:56.374 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:22:56.651 [info] > git status -z -uall [269ms]
2025-06-23 21:22:56.651 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [262ms]
2025-06-23 21:23:09.234 [info] > git config --get commit.template [11ms]
2025-06-23 21:23:09.235 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:23:09.243 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:23:09.249 [info] > git rev-parse refs/remotes/origin/main [5ms]
2025-06-23 21:23:09.275 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-06-23 21:23:09.302 [info] > git status -z -uall [49ms]
2025-06-23 21:24:00.137 [info] > git config --get commit.template [5ms]
2025-06-23 21:24:00.142 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-23 21:24:00.148 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:24:00.150 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:24:00.166 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 21:24:00.226 [info] > git status -z -uall [71ms]
2025-06-23 21:24:13.225 [info] > git config --get commit.template [0ms]
2025-06-23 21:24:13.232 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:24:13.236 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:24:13.237 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:24:13.256 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 21:24:13.278 [info] > git status -z -uall [37ms]
2025-06-23 21:24:18.302 [info] > git config --get commit.template [7ms]
2025-06-23 21:24:18.304 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:24:18.309 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:24:18.310 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:24:18.328 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:24:18.349 [info] > git status -z -uall [34ms]
2025-06-23 21:24:31.504 [info] > git config --get commit.template [7ms]
2025-06-23 21:24:31.507 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 21:24:31.512 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:24:31.512 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:24:31.530 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:24:31.551 [info] > git status -z -uall [34ms]
2025-06-23 21:24:37.659 [info] > git config --get commit.template [8ms]
2025-06-23 21:24:37.660 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:24:37.665 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:24:37.666 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:24:37.687 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:24:37.708 [info] > git status -z -uall [37ms]
2025-06-23 21:24:39.465 [info] > git config --get commit.template [1ms]
2025-06-23 21:24:39.478 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-23 21:24:39.483 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/refs/remotes/origin/main'
2025-06-23 21:24:39.484 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:24:39.500 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 21:24:39.874 [info] > git status -z -uall [385ms]
2025-06-23 21:24:49.680 [info] > git config --get commit.template [6ms]
2025-06-23 21:24:49.681 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:24:49.686 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:24:49.686 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:24:49.705 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:24:49.739 [info] > git status -z -uall [47ms]
2025-06-23 21:24:54.802 [info] > git config --get commit.template [1ms]
2025-06-23 21:24:54.809 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:24:54.814 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:24:54.816 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:24:54.835 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 21:24:54.860 [info] > git status -z -uall [38ms]
2025-06-23 21:25:07.954 [info] > git config --get commit.template [5ms]
2025-06-23 21:25:07.956 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:25:07.960 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:25:07.962 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:25:08.070 [info] > git status -z -uall [100ms]
2025-06-23 21:25:08.079 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [95ms]
2025-06-23 21:25:13.264 [info] > git config --get commit.template [7ms]
2025-06-23 21:25:13.264 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:25:13.270 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:25:13.272 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:25:13.287 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:25:13.311 [info] > git status -z -uall [34ms]
2025-06-23 21:25:26.201 [info] > git config --get commit.template [8ms]
2025-06-23 21:25:26.202 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 21:25:26.206 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:25:26.208 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:25:26.226 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-23 21:25:26.248 [info] > git status -z -uall [35ms]
2025-06-23 21:25:31.268 [info] > git config --get commit.template [1ms]
2025-06-23 21:25:31.274 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:25:31.279 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:25:31.280 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:25:31.298 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:25:31.318 [info] > git status -z -uall [34ms]
2025-06-23 21:25:50.923 [info] > git config --get commit.template [6ms]
2025-06-23 21:25:50.925 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:25:50.930 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:25:50.930 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:25:50.947 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 21:25:50.971 [info] > git status -z -uall [36ms]
2025-06-23 21:25:56.005 [info] > git config --get commit.template [6ms]
2025-06-23 21:25:56.010 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-23 21:25:56.015 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:25:56.016 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:25:56.032 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:25:56.058 [info] > git status -z -uall [37ms]
2025-06-23 21:26:02.820 [info] > git config --get commit.template [2ms]
2025-06-23 21:26:02.827 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:26:02.832 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:26:02.832 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:26:02.850 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:26:02.871 [info] > git status -z -uall [34ms]
2025-06-23 21:26:07.917 [info] > git config --get commit.template [0ms]
2025-06-23 21:26:07.924 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:26:07.929 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:26:07.994 [info] > git rev-parse refs/remotes/origin/main [65ms]
2025-06-23 21:26:08.021 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 21:26:08.052 [info] > git status -z -uall [48ms]
2025-06-23 21:26:20.943 [info] > git config --get commit.template [2ms]
2025-06-23 21:26:20.949 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:26:20.954 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:26:20.956 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:26:20.975 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:26:20.997 [info] > git status -z -uall [35ms]
2025-06-23 21:26:43.581 [info] > git config --get commit.template [1ms]
2025-06-23 21:26:43.588 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 21:26:43.593 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:26:43.594 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:26:43.615 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:26:43.635 [info] > git status -z -uall [37ms]
2025-06-23 21:26:48.659 [info] > git config --get commit.template [7ms]
2025-06-23 21:26:48.660 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:26:48.665 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:26:48.665 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:26:48.683 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-23 21:26:48.705 [info] > git status -z -uall [35ms]
2025-06-23 21:26:57.521 [info] > git config --get commit.template [6ms]
2025-06-23 21:26:57.523 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:26:57.528 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:26:57.528 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:26:57.546 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:26:57.571 [info] > git status -z -uall [37ms]
2025-06-23 21:27:02.609 [info] > git config --get commit.template [9ms]
2025-06-23 21:27:02.609 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:27:02.619 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:27:02.624 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-06-23 21:27:02.641 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:27:02.663 [info] > git status -z -uall [35ms]
2025-06-23 21:27:15.805 [info] > git config --get commit.template [25ms]
2025-06-23 21:27:15.819 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [34ms]
2025-06-23 21:27:15.825 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:27:15.827 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:27:15.846 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-23 21:27:16.341 [info] > git status -z -uall [509ms]
2025-06-23 21:27:21.376 [info] > git config --get commit.template [5ms]
2025-06-23 21:27:21.388 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:27:21.397 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:27:21.398 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:27:21.445 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-23 21:27:21.481 [info] > git status -z -uall [74ms]
2025-06-23 21:27:26.553 [info] > git config --get commit.template [16ms]
2025-06-23 21:27:26.558 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-23 21:27:26.567 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:27:26.575 [info] > git rev-parse refs/remotes/origin/main [8ms]
2025-06-23 21:27:26.600 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-23 21:27:27.118 [info] > git status -z -uall [535ms]
2025-06-23 21:27:32.143 [info] > git config --get commit.template [1ms]
2025-06-23 21:27:32.149 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:27:32.154 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:27:32.154 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:27:32.171 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 21:27:32.221 [info] > git status -z -uall [62ms]
2025-06-23 21:27:49.681 [info] > git config --get commit.template [20ms]
2025-06-23 21:27:49.685 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-23 21:27:49.693 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:27:49.695 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:27:49.740 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [13ms]
2025-06-23 21:27:49.777 [info] > git status -z -uall [70ms]
2025-06-23 21:27:57.939 [info] > git config --get commit.template [10ms]
2025-06-23 21:27:57.943 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-23 21:27:57.948 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:27:57.949 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:27:57.966 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 21:27:58.120 [info] > git status -z -uall [166ms]
2025-06-23 21:28:03.148 [info] > git config --get commit.template [5ms]
2025-06-23 21:28:03.152 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-23 21:28:03.159 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:28:03.160 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:28:03.175 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-23 21:28:03.201 [info] > git status -z -uall [36ms]
2025-06-23 21:28:08.243 [info] > git config --get commit.template [7ms]
2025-06-23 21:28:08.244 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:28:08.250 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:28:08.251 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:28:08.280 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [12ms]
2025-06-23 21:28:08.311 [info] > git status -z -uall [55ms]
2025-06-23 21:28:13.332 [info] > git config --get commit.template [0ms]
2025-06-23 21:28:13.338 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:28:13.344 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:28:13.344 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:28:13.363 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-23 21:28:13.386 [info] > git status -z -uall [37ms]
2025-06-23 21:28:30.544 [info] > git config --get commit.template [12ms]
2025-06-23 21:28:30.548 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-23 21:28:30.561 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:28:30.561 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:28:30.577 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-23 21:28:30.601 [info] > git status -z -uall [35ms]
2025-06-23 21:28:35.634 [info] > git config --get commit.template [12ms]
2025-06-23 21:28:35.636 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-23 21:28:35.646 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:28:35.648 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:28:35.676 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-23 21:28:35.717 [info] > git status -z -uall [63ms]
2025-06-23 21:28:44.122 [info] > git config --get commit.template [6ms]
2025-06-23 21:28:44.126 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-23 21:28:44.131 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:28:44.132 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:28:44.147 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 21:28:44.610 [info] > git status -z -uall [472ms]
2025-06-23 21:29:14.575 [info] > git config --get commit.template [7ms]
2025-06-23 21:29:14.585 [info] > git config --get commit.template [10ms]
2025-06-23 21:29:14.589 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-23 21:29:14.601 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:29:14.601 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [20ms]
2025-06-23 21:29:14.612 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/refs/remotes/origin/main'
2025-06-23 21:29:14.612 [info] > git rev-parse refs/remotes/origin/main [11ms]
2025-06-23 21:29:14.632 [info] > git rev-parse refs/remotes/origin/main [20ms]
2025-06-23 21:29:14.653 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [21ms]
2025-06-23 21:29:14.664 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-06-23 21:29:14.701 [info] > git status -z -uall [77ms]
2025-06-23 21:29:15.244 [info] > git status -z -uall [600ms]
2025-06-23 21:29:19.721 [info] > git config --get commit.template [1ms]
2025-06-23 21:29:19.728 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:29:19.732 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:29:19.733 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:29:19.749 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:29:19.772 [info] > git status -z -uall [35ms]
2025-06-23 21:29:20.254 [info] > git config --get commit.template [1ms]
2025-06-23 21:29:20.261 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:29:20.265 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/myproject/openalgo/.git/refs/remotes/origin/main'
2025-06-23 21:29:20.266 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:29:20.282 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-23 21:29:20.305 [info] > git status -z -uall [35ms]
2025-06-23 21:29:24.797 [info] > git config --get commit.template [1ms]
2025-06-23 21:29:24.804 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:29:24.809 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:29:24.809 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:29:24.826 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 21:29:24.848 [info] > git status -z -uall [34ms]
2025-06-23 21:29:35.188 [info] > git config --get commit.template [5ms]
2025-06-23 21:29:35.190 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:29:35.196 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:29:35.197 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:29:35.219 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-23 21:29:35.241 [info] > git status -z -uall [39ms]
2025-06-23 21:29:40.341 [info] > git config --get commit.template [1ms]
2025-06-23 21:29:40.358 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-23 21:29:40.370 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:29:40.372 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:29:40.406 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-23 21:29:40.468 [info] > git status -z -uall [85ms]
2025-06-23 21:29:45.498 [info] > git config --get commit.template [5ms]
2025-06-23 21:29:45.501 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 21:29:45.505 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:29:45.506 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:29:45.522 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 21:29:45.545 [info] > git status -z -uall [35ms]
2025-06-23 21:29:53.509 [info] > git config --get commit.template [6ms]
2025-06-23 21:29:53.510 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:29:53.515 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:29:53.516 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:29:53.538 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:29:53.556 [info] > git status -z -uall [34ms]
2025-06-23 21:29:58.587 [info] > git config --get commit.template [8ms]
2025-06-23 21:29:58.588 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:29:58.593 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:29:58.594 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:29:58.622 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-23 21:29:58.651 [info] > git status -z -uall [51ms]
2025-06-23 21:30:02.658 [info] > git rev-parse --show-toplevel [4ms]
2025-06-23 21:30:02.671 [info] > git rev-parse --git-dir --git-common-dir [2ms]
2025-06-23 21:30:02.714 [info] [Model][openRepository] Opened repository (path): /home/<USER>/algofactory-multi/algofactory-8012
2025-06-23 21:30:02.714 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/algofactory-multi/algofactory-8012
2025-06-23 21:30:02.725 [info] > git config --get commit.template [1ms]
2025-06-23 21:30:02.745 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [15ms]
2025-06-23 21:30:02.751 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8012/.git/refs/remotes/origin/main'
2025-06-23 21:30:02.751 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:30:02.770 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-23 21:30:03.131 [info] > git show --textconv :nginx-algofactory.conf [74ms]
2025-06-23 21:30:03.138 [info] > git ls-files --stage -- nginx-algofactory.conf [75ms]
2025-06-23 21:30:03.145 [info] > git hash-object -t tree /dev/null [1ms]
2025-06-23 21:30:03.145 [warning] [GitFileSystemProvider][stat] File not found - git:/home/<USER>/algofactory-multi/algofactory-8012/nginx-algofactory.conf.git?%7B%22path%22%3A%22%2Fhome%2Fubuntu%2Falgofactory-multi%2Falgofactory-8012%2Fnginx-algofactory.conf%22%2C%22ref%22%3A%22%22%7D
2025-06-23 21:30:03.148 [info] > git hash-object -t tree /dev/null [10ms]
2025-06-23 21:30:03.148 [warning] [GitFileSystemProvider][readFile] File not found - git:/home/<USER>/algofactory-multi/algofactory-8012/nginx-algofactory.conf.git?%7B%22path%22%3A%22%2Fhome%2Fubuntu%2Falgofactory-multi%2Falgofactory-8012%2Fnginx-algofactory.conf%22%2C%22ref%22%3A%22%22%7D
2025-06-23 21:30:03.310 [info] > git status -z -uall [554ms]
2025-06-23 21:30:03.310 [info] > git check-ignore -v -z --stdin [20ms]
2025-06-23 21:30:03.310 [info] > git check-ignore -v -z --stdin [14ms]
2025-06-23 21:30:03.310 [info] > git check-ignore -v -z --stdin [7ms]
2025-06-23 21:30:03.347 [info] > git check-ignore -v -z --stdin [40ms]
2025-06-23 21:30:03.348 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [16ms]
2025-06-23 21:30:03.356 [info] > git config --get --local branch.main.vscode-merge-base [2ms]
2025-06-23 21:30:03.360 [info] > git config --get commit.template [13ms]
2025-06-23 21:30:03.369 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [9ms]
2025-06-23 21:30:03.379 [info] > git merge-base refs/heads/main refs/remotes/origin/main [3ms]
2025-06-23 21:30:03.384 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [15ms]
2025-06-23 21:30:03.394 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8012/.git/refs/remotes/origin/main'
2025-06-23 21:30:03.395 [info] > git diff --name-status -z --diff-filter=ADMR 89ac1fedef89963fb80f591849bbcbe1dc91b13e...refs/remotes/origin/main [11ms]
2025-06-23 21:30:03.397 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-06-23 21:30:03.501 [info] > git status -z -uall [99ms]
2025-06-23 21:30:03.503 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [95ms]
2025-06-23 21:30:03.682 [info] > git config --get commit.template [6ms]
2025-06-23 21:30:03.683 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-23 21:30:03.688 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:30:03.688 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:30:03.706 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 21:30:03.727 [info] > git status -z -uall [34ms]
2025-06-23 21:30:08.107 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-23 21:30:11.395 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-23 21:30:11.755 [info] > git config --get commit.template [6ms]
2025-06-23 21:30:11.756 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:30:11.761 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:30:11.762 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:30:11.788 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [12ms]
2025-06-23 21:30:11.809 [info] > git status -z -uall [42ms]
2025-06-23 21:30:14.119 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-23 21:30:14.120 [info] > git check-ignore -v -z --stdin [8ms]
2025-06-23 21:30:16.835 [info] > git config --get commit.template [7ms]
2025-06-23 21:30:16.836 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:30:16.843 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:30:16.845 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:30:16.868 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:30:17.002 [info] > git status -z -uall [151ms]
2025-06-23 21:30:22.025 [info] > git config --get commit.template [6ms]
2025-06-23 21:30:22.025 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-23 21:30:22.031 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:30:22.031 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:30:22.049 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 21:30:22.072 [info] > git status -z -uall [36ms]
2025-06-23 21:30:29.969 [info] > git config --get commit.template [0ms]
2025-06-23 21:30:29.975 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:30:29.981 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:30:29.984 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-06-23 21:30:30.003 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-23 21:30:30.024 [info] > git status -z -uall [35ms]
2025-06-23 21:30:35.063 [info] > git config --get commit.template [10ms]
2025-06-23 21:30:35.069 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-23 21:30:35.078 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:30:35.081 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-06-23 21:30:35.110 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:30:35.148 [info] > git status -z -uall [59ms]
2025-06-23 21:30:40.170 [info] > git config --get commit.template [5ms]
2025-06-23 21:30:40.171 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:30:40.176 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:30:40.177 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:30:40.196 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:30:40.215 [info] > git status -z -uall [33ms]
2025-06-23 21:30:48.177 [info] > git config --get commit.template [5ms]
2025-06-23 21:30:48.179 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 21:30:48.184 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:30:48.185 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:30:48.201 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:30:48.222 [info] > git status -z -uall [33ms]
2025-06-23 21:30:53.351 [info] > git config --get commit.template [8ms]
2025-06-23 21:30:53.352 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:30:53.357 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:30:53.361 [info] > git rev-parse refs/remotes/origin/main [4ms]
2025-06-23 21:30:53.395 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-23 21:30:53.432 [info] > git status -z -uall [54ms]
2025-06-23 21:30:58.470 [info] > git config --get commit.template [6ms]
2025-06-23 21:30:58.471 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:30:58.477 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:30:58.477 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:30:58.494 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:30:58.516 [info] > git status -z -uall [34ms]
2025-06-23 21:31:06.490 [info] > git config --get commit.template [6ms]
2025-06-23 21:31:06.491 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:31:06.497 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:31:06.497 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:31:06.513 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:31:06.547 [info] > git status -z -uall [45ms]
2025-06-23 21:31:11.581 [info] > git config --get commit.template [11ms]
2025-06-23 21:31:11.584 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 21:31:11.595 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:31:11.596 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:31:11.627 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-23 21:31:11.671 [info] > git status -z -uall [64ms]
2025-06-23 21:31:16.700 [info] > git config --get commit.template [5ms]
2025-06-23 21:31:16.701 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:31:16.706 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:31:16.706 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:31:16.725 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:31:16.745 [info] > git status -z -uall [33ms]
2025-06-23 21:31:24.739 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:31:24.739 [info] > git config --get commit.template [7ms]
2025-06-23 21:31:24.744 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:31:24.744 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:31:24.760 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:31:24.782 [info] > git status -z -uall [33ms]
2025-06-23 21:31:29.811 [info] > git config --get commit.template [7ms]
2025-06-23 21:31:29.813 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:31:29.820 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:31:29.822 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:31:29.846 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-23 21:31:29.873 [info] > git status -z -uall [46ms]
2025-06-23 21:31:34.896 [info] > git config --get commit.template [6ms]
2025-06-23 21:31:34.925 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [11ms]
2025-06-23 21:31:34.937 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:31:35.279 [info] > git rev-parse refs/remotes/origin/main [342ms]
2025-06-23 21:31:35.304 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-23 21:31:35.336 [info] > git status -z -uall [49ms]
2025-06-23 21:31:43.008 [info] > git config --get commit.template [0ms]
2025-06-23 21:31:43.015 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:31:43.021 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:31:43.024 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-06-23 21:31:43.043 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 21:31:43.067 [info] > git status -z -uall [36ms]
2025-06-23 21:31:48.185 [info] > git config --get commit.template [13ms]
2025-06-23 21:31:48.194 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-23 21:31:48.204 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:31:48.206 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:31:48.234 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 21:31:48.267 [info] > git status -z -uall [50ms]
2025-06-23 21:31:53.293 [info] > git config --get commit.template [7ms]
2025-06-23 21:31:53.293 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:31:53.298 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:31:53.299 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:31:53.316 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:31:53.337 [info] > git status -z -uall [33ms]
2025-06-23 21:32:01.241 [info] > git config --get commit.template [7ms]
2025-06-23 21:32:01.243 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 21:32:01.252 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:32:01.256 [info] > git rev-parse refs/remotes/origin/main [4ms]
2025-06-23 21:32:01.274 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 21:32:01.296 [info] > git status -z -uall [34ms]
2025-06-23 21:32:06.333 [info] > git config --get commit.template [14ms]
2025-06-23 21:32:06.335 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:32:06.345 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:32:06.346 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:32:06.389 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-23 21:32:06.439 [info] > git status -z -uall [83ms]
2025-06-23 21:32:11.465 [info] > git config --get commit.template [5ms]
2025-06-23 21:32:11.466 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:32:11.472 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:32:11.472 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:32:11.489 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-23 21:32:11.512 [info] > git status -z -uall [36ms]
2025-06-23 21:32:19.413 [info] > git config --get commit.template [0ms]
2025-06-23 21:32:19.421 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:32:19.425 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:32:19.426 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:32:19.442 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 21:32:19.464 [info] > git status -z -uall [34ms]
2025-06-23 21:32:24.505 [info] > git config --get commit.template [13ms]
2025-06-23 21:32:24.511 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-23 21:32:24.526 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:32:24.526 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:32:24.560 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-23 21:32:24.622 [info] > git status -z -uall [85ms]
2025-06-23 21:32:29.647 [info] > git config --get commit.template [1ms]
2025-06-23 21:32:29.654 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:32:29.658 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:32:29.659 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:32:29.675 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-23 21:32:29.698 [info] > git status -z -uall [34ms]
2025-06-23 21:32:37.789 [info] > git config --get commit.template [1ms]
2025-06-23 21:32:37.796 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-23 21:32:37.801 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:32:37.802 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:32:37.823 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 21:32:37.843 [info] > git status -z -uall [36ms]
2025-06-23 21:32:42.883 [info] > git config --get commit.template [12ms]
2025-06-23 21:32:42.884 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 21:32:42.891 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:32:42.892 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:32:42.922 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:32:42.958 [info] > git status -z -uall [57ms]
2025-06-23 21:32:47.996 [info] > git config --get commit.template [1ms]
2025-06-23 21:32:48.002 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:32:48.007 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:32:48.007 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:32:48.026 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:32:48.117 [info] > git status -z -uall [105ms]
2025-06-23 21:32:55.988 [info] > git config --get commit.template [4ms]
2025-06-23 21:32:55.994 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-23 21:32:55.999 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:32:55.999 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:32:56.015 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:32:56.037 [info] > git status -z -uall [33ms]
2025-06-23 21:33:01.076 [info] > git config --get commit.template [13ms]
2025-06-23 21:33:01.079 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 21:33:01.091 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:33:01.092 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:33:01.132 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-23 21:33:01.176 [info] > git status -z -uall [72ms]
2025-06-23 21:33:06.211 [info] > git config --get commit.template [7ms]
2025-06-23 21:33:06.212 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:33:06.217 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:33:06.217 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:33:06.234 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-23 21:33:06.257 [info] > git status -z -uall [34ms]
2025-06-23 21:33:14.245 [info] > git config --get commit.template [1ms]
2025-06-23 21:33:14.252 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:33:14.256 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:33:14.257 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:33:14.271 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-23 21:33:14.295 [info] > git status -z -uall [34ms]
2025-06-23 21:33:19.358 [info] > git config --get commit.template [12ms]
2025-06-23 21:33:19.372 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 21:33:19.385 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:33:19.387 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:33:19.415 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-23 21:33:19.460 [info] > git status -z -uall [66ms]
2025-06-23 21:33:24.487 [info] > git config --get commit.template [6ms]
2025-06-23 21:33:24.488 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:33:24.494 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:33:24.494 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:33:24.509 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-23 21:33:24.534 [info] > git status -z -uall [35ms]
2025-06-23 21:33:32.423 [info] > git config --get commit.template [5ms]
2025-06-23 21:33:32.425 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:33:32.429 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:33:32.430 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:33:32.449 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-23 21:33:32.471 [info] > git status -z -uall [36ms]
2025-06-23 21:33:37.502 [info] > git config --get commit.template [1ms]
2025-06-23 21:33:37.514 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:33:37.523 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:33:37.530 [info] > git rev-parse refs/remotes/origin/main [9ms]
2025-06-23 21:33:37.562 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-23 21:33:37.619 [info] > git status -z -uall [79ms]
2025-06-23 21:33:42.640 [info] > git config --get commit.template [1ms]
2025-06-23 21:33:42.646 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:33:42.651 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:33:42.652 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:33:42.667 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-23 21:33:42.692 [info] > git status -z -uall [36ms]
2025-06-23 21:33:50.702 [info] > git config --get commit.template [2ms]
2025-06-23 21:33:50.709 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:33:50.714 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:33:50.715 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:33:50.730 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 21:33:50.756 [info] > git status -z -uall [36ms]
2025-06-23 21:33:55.784 [info] > git config --get commit.template [9ms]
2025-06-23 21:33:55.786 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:33:55.791 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:33:55.793 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:33:55.827 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-23 21:33:55.863 [info] > git status -z -uall [64ms]
2025-06-23 21:34:00.887 [info] > git config --get commit.template [3ms]
2025-06-23 21:34:00.893 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:34:00.898 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:34:00.899 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:34:00.915 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 21:34:00.939 [info] > git status -z -uall [36ms]
2025-06-23 21:34:09.002 [info] > git config --get commit.template [5ms]
2025-06-23 21:34:09.004 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:34:09.009 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:34:09.009 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:34:09.027 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:34:09.050 [info] > git status -z -uall [35ms]
2025-06-23 21:34:14.102 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 21:34:14.102 [info] > git config --get commit.template [15ms]
2025-06-23 21:34:14.112 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:34:14.113 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:34:14.150 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:34:14.194 [info] > git status -z -uall [72ms]
2025-06-23 21:34:19.238 [info] > git config --get commit.template [5ms]
2025-06-23 21:34:19.240 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:34:19.245 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:34:19.245 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:34:19.262 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 21:34:19.283 [info] > git status -z -uall [34ms]
2025-06-23 21:34:27.208 [info] > git config --get commit.template [6ms]
2025-06-23 21:34:27.209 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:34:27.214 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:34:27.214 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:34:27.232 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 21:34:27.262 [info] > git status -z -uall [42ms]
2025-06-23 21:34:33.660 [info] > git config --get commit.template [3ms]
2025-06-23 21:34:33.667 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:34:33.671 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:34:33.673 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:34:33.691 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:34:33.713 [info] > git status -z -uall [36ms]
2025-06-23 21:34:45.588 [info] > git config --get commit.template [2ms]
2025-06-23 21:34:45.594 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-23 21:34:45.599 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:34:45.600 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:34:45.618 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:34:45.641 [info] > git status -z -uall [36ms]
2025-06-23 21:34:50.692 [info] > git config --get commit.template [17ms]
2025-06-23 21:34:50.692 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-23 21:34:50.709 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:34:50.713 [info] > git rev-parse refs/remotes/origin/main [4ms]
2025-06-23 21:34:50.746 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:34:50.775 [info] > git status -z -uall [53ms]
2025-06-23 21:34:55.805 [info] > git config --get commit.template [0ms]
2025-06-23 21:34:55.811 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:34:55.814 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:34:55.816 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:34:55.833 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:34:55.856 [info] > git status -z -uall [35ms]
2025-06-23 21:35:03.726 [info] > git config --get commit.template [3ms]
2025-06-23 21:35:03.732 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-23 21:35:03.738 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:35:03.738 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:35:03.756 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:35:03.778 [info] > git status -z -uall [34ms]
2025-06-23 21:35:08.821 [info] > git config --get commit.template [13ms]
2025-06-23 21:35:08.827 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-23 21:35:08.837 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:35:08.842 [info] > git rev-parse refs/remotes/origin/main [5ms]
2025-06-23 21:35:08.886 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 21:35:08.943 [info] > git status -z -uall [86ms]
2025-06-23 21:35:14.002 [info] > git config --get commit.template [3ms]
2025-06-23 21:35:14.011 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 21:35:14.016 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:35:14.017 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:35:14.036 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:35:14.058 [info] > git status -z -uall [36ms]
2025-06-23 21:35:21.970 [info] > git config --get commit.template [5ms]
2025-06-23 21:35:21.972 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:35:21.976 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:35:21.977 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:35:21.992 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-23 21:35:22.017 [info] > git status -z -uall [35ms]
2025-06-23 21:35:28.424 [info] > git config --get commit.template [5ms]
2025-06-23 21:35:28.427 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 21:35:28.430 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:35:28.432 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:35:28.447 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:35:28.474 [info] > git status -z -uall [37ms]
2025-06-23 21:35:40.253 [info] > git config --get commit.template [1ms]
2025-06-23 21:35:40.259 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:35:40.263 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:35:40.265 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:35:40.280 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:35:40.304 [info] > git status -z -uall [34ms]
2025-06-23 21:35:46.624 [info] > git config --get commit.template [6ms]
2025-06-23 21:35:46.625 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:35:46.630 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:35:46.630 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:35:46.650 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-23 21:35:46.669 [info] > git status -z -uall [34ms]
2025-06-23 21:35:58.501 [info] > git config --get commit.template [6ms]
2025-06-23 21:35:58.503 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:35:58.508 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:35:58.509 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:35:58.525 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-23 21:35:58.552 [info] > git status -z -uall [39ms]
2025-06-23 21:36:03.589 [info] > git config --get commit.template [5ms]
2025-06-23 21:36:03.599 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:36:03.605 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:36:03.606 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:36:03.628 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:36:03.659 [info] > git status -z -uall [47ms]
2025-06-23 21:36:08.690 [info] > git config --get commit.template [7ms]
2025-06-23 21:36:08.691 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:36:08.696 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:36:08.697 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:36:08.714 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:36:08.735 [info] > git status -z -uall [33ms]
2025-06-23 21:36:16.730 [info] > git config --get commit.template [6ms]
2025-06-23 21:36:16.731 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:36:16.736 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:36:16.737 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:36:16.753 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:36:16.784 [info] > git status -z -uall [42ms]
2025-06-23 21:36:23.174 [info] > git config --get commit.template [6ms]
2025-06-23 21:36:23.178 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-23 21:36:23.184 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:36:23.184 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:36:23.202 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-23 21:36:23.224 [info] > git status -z -uall [35ms]
2025-06-23 21:36:34.973 [info] > git config --get commit.template [5ms]
2025-06-23 21:36:34.975 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-23 21:36:34.980 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:36:34.980 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-23 21:36:34.998 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:36:35.022 [info] > git status -z -uall [37ms]
2025-06-23 21:36:40.050 [info] > git config --get commit.template [7ms]
2025-06-23 21:36:40.062 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-23 21:36:40.072 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:36:40.073 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:36:40.098 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:36:40.132 [info] > git status -z -uall [53ms]
2025-06-23 21:36:45.158 [info] > git config --get commit.template [7ms]
2025-06-23 21:36:45.159 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:36:45.164 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:36:45.165 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:36:45.183 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:36:45.203 [info] > git status -z -uall [33ms]
2025-06-23 21:36:53.220 [info] > git config --get commit.template [1ms]
2025-06-23 21:36:53.226 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:36:53.230 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:36:53.232 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-23 21:36:53.250 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-23 21:36:53.271 [info] > git status -z -uall [34ms]
2025-06-23 21:36:59.737 [info] > git config --get commit.template [5ms]
2025-06-23 21:36:59.738 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:36:59.744 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:36:59.745 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:36:59.849 [info] > git status -z -uall [100ms]
2025-06-23 21:36:59.851 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [96ms]
2025-06-23 21:37:11.474 [info] > git config --get commit.template [6ms]
2025-06-23 21:37:11.475 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:37:11.480 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:37:11.481 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:37:11.505 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-06-23 21:37:11.525 [info] > git status -z -uall [40ms]
2025-06-23 21:37:17.863 [info] > git config --get commit.template [6ms]
2025-06-23 21:37:17.864 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:37:17.869 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:37:17.870 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:37:17.888 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-23 21:37:17.910 [info] > git status -z -uall [35ms]
2025-06-23 21:37:29.836 [info] > git config --get commit.template [0ms]
2025-06-23 21:37:29.843 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:37:29.847 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:37:29.848 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:37:29.870 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:37:29.891 [info] > git status -z -uall [38ms]
2025-06-23 21:37:36.080 [info] > git config --get commit.template [1ms]
2025-06-23 21:37:36.086 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-23 21:37:36.090 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:37:36.091 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:37:36.108 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-23 21:37:36.129 [info] > git status -z -uall [33ms]
2025-06-23 21:37:47.968 [info] > git config --get commit.template [6ms]
2025-06-23 21:37:47.969 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-23 21:37:47.974 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/algofactory-8010/.git/refs/remotes/origin/main'
2025-06-23 21:37:47.975 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-23 21:37:47.997 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-23 21:37:48.018 [info] > git status -z -uall [37ms]
