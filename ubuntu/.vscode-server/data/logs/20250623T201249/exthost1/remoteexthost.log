2025-06-23 20:12:53.058 [info] Extension host with pid 243612 started
2025-06-23 20:12:53.064 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/vscode.lock': Lock acquired.
2025-06-23 20:12:53.421 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-06-23 20:12:53.433 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: false, activationEvent: '*', root cause: vscode.git
2025-06-23 20:12:53.433 [info] ExtensionService#_doActivateExtension vscode.tunnel-forwarding, startup: false, activationEvent: 'onTunnel'
2025-06-23 20:12:53.614 [info] ExtensionService#_doActivateExtension vscode.git, startup: false, activationEvent: '*'
2025-06-23 20:12:53.614 [info] ExtensionService#_doActivateExtension vscode.github, startup: false, activationEvent: '*'
2025-06-23 20:12:54.240 [info] Eager extensions activated
2025-06-23 20:12:54.241 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-06-23 20:12:54.241 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-06-23 20:12:54.241 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onStartupFinished'
2025-06-23 20:12:55.816 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:12:58.070 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:13:02.996 [error] CodeExpectedError: cannot open vscode-userdata:c%3A%5CUsers%5Canita%5CAppData%5CRoaming%5CCode%5CUser%5Ckeybindings.json. Detail: Unable to resolve filesystem provider with relative file path 'vscode-userdata:c:\Users\<USER>\AppData\Roaming\Code\User\keybindings.json'
    at Pze.$tryOpenDocument (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/workbench.desktop.main.js:1283:8666)
2025-06-23 20:13:14.336 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:13:19.430 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:13:24.915 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:13:32.056 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:13:42.293 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:13:47.459 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:13:52.532 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:14:00.440 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:14:10.621 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:14:15.756 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:14:20.827 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:14:24.609 [warning] [Decorations] CAPPING events from decorations provider vscode.git 475
2025-06-23 20:14:24.780 [warning] [Decorations] CAPPING events from decorations provider vscode.git 475
2025-06-23 20:14:28.758 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:14:39.087 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:14:44.270 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:14:49.334 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:14:57.173 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:15:07.346 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:15:12.486 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:15:17.554 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:15:25.477 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:16:13.324 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:16:18.400 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:16:23.465 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:16:32.023 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:16:37.188 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:16:42.264 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:16:50.211 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:17:00.276 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:17:05.952 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:17:11.032 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:17:18.375 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:17:28.677 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:17:33.778 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:17:38.854 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:17:46.593 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:17:56.732 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:18:03.694 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:18:14.953 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:18:24.981 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:18:32.112 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:18:43.106 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:18:53.582 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:18:59.876 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:19:03.730 [info] ExtensionService#_doActivateExtension vscode.extension-editing, startup: false, activationEvent: 'onLanguage:markdown'
2025-06-23 20:19:03.731 [info] ExtensionService#_doActivateExtension vscode.markdown-language-features, startup: false, activationEvent: 'onLanguage:markdown'
2025-06-23 20:19:11.372 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:19:21.614 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:19:26.724 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:19:31.809 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:19:39.733 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:19:49.787 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:19:50.553 [info] ExtensionService#_doActivateExtension vscode.html-language-features, startup: false, activationEvent: 'onLanguage:html'
2025-06-23 20:19:54.914 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:19:56.396 [info] ExtensionService#_doActivateExtension vscode.markdown-math, startup: false, activationEvent: 'api', root cause: vscode.markdown-language-features
2025-06-23 20:20:00.458 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:20:08.018 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:20:18.327 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:20:23.452 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:20:28.528 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:20:36.475 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:20:46.802 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:20:51.900 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:20:57.215 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:36:33.685 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:36:38.756 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:36:47.382 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:36:53.938 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:37:05.428 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:37:15.489 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:37:22.456 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:37:33.609 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:37:43.845 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:37:50.471 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:38:01.877 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:38:12.105 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:38:18.786 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:38:28.834 [info] ExtensionService#_doActivateExtension vscode.configuration-editing, startup: false, activationEvent: 'onLanguage:json'
2025-06-23 20:38:28.836 [info] ExtensionService#_doActivateExtension vscode.json-language-features, startup: false, activationEvent: 'onLanguage:json'
2025-06-23 20:38:28.836 [info] ExtensionService#_doActivateExtension vscode.npm, startup: false, activationEvent: 'onLanguage:json'
2025-06-23 20:38:30.241 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:38:40.263 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:38:45.361 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:38:50.921 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:38:58.611 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:39:12.039 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:45:27.092 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:45:32.162 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:45:37.230 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:45:44.309 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:45:50.898 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:46:02.378 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:46:12.571 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:46:19.734 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:46:30.702 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:46:40.826 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:46:47.591 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:46:56.166 [info] ExtensionService#_doActivateExtension vscode.typescript-language-features, startup: false, activationEvent: 'onLanguage:jsonc'
2025-06-23 20:46:58.886 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:47:09.025 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:47:15.848 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:47:27.119 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:47:37.329 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:47:43.261 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:47:48.328 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:47:55.516 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:48:05.688 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:48:12.382 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:48:23.674 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:56:08.394 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:56:13.472 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:56:24.118 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:56:34.254 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:56:41.041 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:56:52.417 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:57:02.498 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:57:07.635 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:57:12.727 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:57:17.788 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:57:22.849 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:57:30.771 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:57:36.328 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:57:41.400 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:57:48.887 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:57:59.110 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:58:04.281 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:58:09.898 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:58:17.355 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:58:27.588 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:58:32.718 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:58:37.785 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:58:45.718 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:58:55.755 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:59:42.151 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:59:47.222 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:59:52.408 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 20:59:58.204 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:00:03.628 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:00:10.643 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:00:20.736 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:00:25.856 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:00:30.926 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:00:38.965 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:00:48.972 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:00:54.195 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:00:59.263 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:01:07.188 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:01:17.370 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:01:22.496 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:01:27.559 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:01:35.377 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:01:45.478 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:01:50.660 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:01:55.722 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:02:03.633 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:02:13.791 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:02:19.923 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:02:24.999 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:02:31.866 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:02:42.228 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:02:54.419 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:02:59.483 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:03:04.545 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:03:10.298 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:03:15.401 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:03:20.468 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:03:28.441 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:03:38.687 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:03:43.806 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:03:48.884 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:03:56.687 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:04:06.774 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:04:11.893 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:04:17.046 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:04:24.902 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:04:35.043 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:04:40.238 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:04:45.327 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:04:53.081 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:05:03.267 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:05:08.379 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:05:13.460 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:05:21.393 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:05:31.497 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:05:36.626 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:05:41.693 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:05:49.672 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:06:00.441 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:06:05.549 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:06:45.167 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:07:22.002 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:07:52.190 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:07:57.310 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:08:02.454 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:08:10.901 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:08:21.046 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:08:26.166 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:08:31.250 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:08:39.190 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:09:39.881 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:09:44.954 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:09:50.167 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:09:55.232 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:10:03.912 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:10:27.874 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:10:32.990 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:10:42.556 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:10:49.232 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:11:00.669 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:11:17.827 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:11:24.125 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:11:29.323 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:11:39.554 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:11:44.664 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:11:48.769 [error] Error: Git error
	at ChildProcess.<anonymous> (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:604689)
	at ChildProcess.emit (node:events:530:35)
	at ChildProcess._handle.onexit (node:internal/child_process:293:12)
2025-06-23 21:11:48.771 [error] Error: Git error
	at ChildProcess.<anonymous> (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:604689)
	at ChildProcess.emit (node:events:530:35)
	at ChildProcess._handle.onexit (node:internal/child_process:293:12)
2025-06-23 21:11:48.771 [error] Error: Git error
	at ChildProcess.<anonymous> (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:604689)
	at ChildProcess.emit (node:events:530:35)
	at ChildProcess._handle.onexit (node:internal/child_process:293:12)
2025-06-23 21:11:48.771 [error] Error: Git error
	at ChildProcess.<anonymous> (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:604689)
	at ChildProcess.emit (node:events:530:35)
	at ChildProcess._handle.onexit (node:internal/child_process:293:12)
2025-06-23 21:11:48.771 [error] Error: Git error
	at ChildProcess.<anonymous> (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:604689)
	at ChildProcess.emit (node:events:530:35)
	at ChildProcess._handle.onexit (node:internal/child_process:293:12)
2025-06-23 21:11:48.771 [error] Error: Git error
	at ChildProcess.<anonymous> (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:604689)
	at ChildProcess.emit (node:events:530:35)
	at ChildProcess._handle.onexit (node:internal/child_process:293:12)
2025-06-23 21:11:48.771 [error] Error: Git error
	at ChildProcess.<anonymous> (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:604689)
	at ChildProcess.emit (node:events:530:35)
	at ChildProcess._handle.onexit (node:internal/child_process:293:12)
2025-06-23 21:11:48.771 [error] Error: Git error
	at ChildProcess.<anonymous> (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:604689)
	at ChildProcess.emit (node:events:530:35)
	at ChildProcess._handle.onexit (node:internal/child_process:293:12)
2025-06-23 21:11:48.771 [error] Error: Git error
	at ChildProcess.<anonymous> (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:604689)
	at ChildProcess.emit (node:events:530:35)
	at ChildProcess._handle.onexit (node:internal/child_process:293:12)
2025-06-23 21:11:48.771 [error] Error: Git error
	at ChildProcess.<anonymous> (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:604689)
	at ChildProcess.emit (node:events:530:35)
	at ChildProcess._handle.onexit (node:internal/child_process:293:12)
2025-06-23 21:11:48.771 [error] Error: Git error
	at ChildProcess.<anonymous> (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:604689)
	at ChildProcess.emit (node:events:530:35)
	at ChildProcess._handle.onexit (node:internal/child_process:293:12)
2025-06-23 21:11:48.771 [error] Error: Git error
	at ChildProcess.<anonymous> (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:604689)
	at ChildProcess.emit (node:events:530:35)
	at ChildProcess._handle.onexit (node:internal/child_process:293:12)
2025-06-23 21:11:48.771 [error] Error: Git error
	at ChildProcess.<anonymous> (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:604689)
	at ChildProcess.emit (node:events:530:35)
	at ChildProcess._handle.onexit (node:internal/child_process:293:12)
2025-06-23 21:11:48.771 [error] Error: Git error
	at ChildProcess.<anonymous> (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:604689)
	at ChildProcess.emit (node:events:530:35)
	at ChildProcess._handle.onexit (node:internal/child_process:293:12)
2025-06-23 21:11:49.746 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:11:49.777 [error] Error: Git error
	at ChildProcess.<anonymous> (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:604689)
	at ChildProcess.emit (node:events:530:35)
	at ChildProcess._handle.onexit (node:internal/child_process:293:12)
2025-06-23 21:11:49.816 [error] Error: Git error
	at ChildProcess.<anonymous> (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:604689)
	at ChildProcess.emit (node:events:530:35)
	at ChildProcess._handle.onexit (node:internal/child_process:293:12)
2025-06-23 21:11:49.816 [error] Error: Git error
	at ChildProcess.<anonymous> (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:604689)
	at ChildProcess.emit (node:events:530:35)
	at ChildProcess._handle.onexit (node:internal/child_process:293:12)
2025-06-23 21:11:49.817 [error] Error: Git error
	at ChildProcess.<anonymous> (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:604689)
	at ChildProcess.emit (node:events:530:35)
	at ChildProcess._handle.onexit (node:internal/child_process:293:12)
2025-06-23 21:11:49.818 [error] Error: Git error
	at ChildProcess.<anonymous> (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:604689)
	at ChildProcess.emit (node:events:530:35)
	at ChildProcess._handle.onexit (node:internal/child_process:293:12)
2025-06-23 21:11:49.819 [error] Error: Git error
	at ChildProcess.<anonymous> (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:604689)
	at ChildProcess.emit (node:events:530:35)
	at ChildProcess._handle.onexit (node:internal/child_process:293:12)
2025-06-23 21:11:49.820 [error] Error: Git error
	at ChildProcess.<anonymous> (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:604689)
	at ChildProcess.emit (node:events:530:35)
	at ChildProcess._handle.onexit (node:internal/child_process:293:12)
2025-06-23 21:11:49.820 [error] Error: Git error
	at ChildProcess.<anonymous> (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:604689)
	at ChildProcess.emit (node:events:530:35)
	at ChildProcess._handle.onexit (node:internal/child_process:293:12)
2025-06-23 21:11:49.820 [error] Error: Git error
	at ChildProcess.<anonymous> (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:604689)
	at ChildProcess.emit (node:events:530:35)
	at ChildProcess._handle.onexit (node:internal/child_process:293:12)
2025-06-23 21:11:49.823 [error] Error: Git error
	at ChildProcess.<anonymous> (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:604689)
	at ChildProcess.emit (node:events:530:35)
	at ChildProcess._handle.onexit (node:internal/child_process:293:12)
2025-06-23 21:11:49.823 [error] Error: Git error
	at ChildProcess.<anonymous> (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:604689)
	at ChildProcess.emit (node:events:530:35)
	at ChildProcess._handle.onexit (node:internal/child_process:293:12)
2025-06-23 21:11:49.823 [error] Error: Git error
	at ChildProcess.<anonymous> (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:604689)
	at ChildProcess.emit (node:events:530:35)
	at ChildProcess._handle.onexit (node:internal/child_process:293:12)
2025-06-23 21:11:49.824 [error] Error: Git error
	at ChildProcess.<anonymous> (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:604689)
	at ChildProcess.emit (node:events:530:35)
	at ChildProcess._handle.onexit (node:internal/child_process:293:12)
2025-06-23 21:11:49.824 [error] Error: Git error
	at ChildProcess.<anonymous> (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:604689)
	at ChildProcess.emit (node:events:530:35)
	at ChildProcess._handle.onexit (node:internal/child_process:293:12)
2025-06-23 21:11:57.862 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:12:08.143 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:12:13.420 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:12:18.566 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:12:26.321 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:12:36.565 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:12:41.685 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:12:46.834 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:12:54.730 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:13:05.018 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:13:10.126 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:13:15.196 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:13:23.298 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:13:33.532 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:13:38.783 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:13:44.568 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:13:54.909 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:14:02.422 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:14:42.550 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:14:47.665 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:14:52.741 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:14:59.339 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:15:04.470 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:15:09.567 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:15:17.665 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:15:27.985 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:15:33.204 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:15:38.287 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:15:46.002 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:15:56.123 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:16:01.259 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:16:06.460 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:16:14.723 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:16:24.514 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:16:29.630 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:16:34.707 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:17:03.813 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:17:31.277 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:17:36.382 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:17:41.457 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:17:49.839 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:18:15.993 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:18:21.093 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:18:26.186 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:18:31.278 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:18:36.535 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:21:59.289 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:22:04.365 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:22:09.523 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:22:14.645 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:22:22.637 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:22:32.772 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:22:37.964 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:22:43.030 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:22:51.032 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:22:56.760 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:23:09.326 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:24:00.240 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:24:13.282 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:24:18.366 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:24:31.559 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:24:37.725 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:24:49.744 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:24:54.864 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:25:08.091 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:25:13.320 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:25:26.255 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:25:31.323 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:25:50.985 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:25:56.064 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:26:02.876 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:26:08.056 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:26:21.005 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:26:43.642 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:26:48.711 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:26:57.585 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:27:02.668 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:27:16.351 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:27:21.494 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:27:27.128 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:27:32.226 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:27:49.789 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:27:58.126 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:28:03.211 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:28:08.322 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:28:13.390 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:28:30.606 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:28:35.728 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:28:44.625 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:29:14.706 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:29:19.781 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:29:24.855 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:29:35.246 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:29:40.479 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:29:45.549 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:29:53.562 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:29:58.657 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:30:03.323 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:30:03.516 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:30:03.733 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:30:11.814 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:30:17.008 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:30:22.077 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:30:30.031 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:30:35.156 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:30:40.222 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:30:48.235 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:30:53.449 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:30:58.524 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:31:06.551 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:31:11.683 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:31:16.750 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:31:24.789 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:31:29.879 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:31:35.342 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:31:43.085 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:31:48.275 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:31:53.342 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:32:01.303 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:32:06.446 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:32:11.517 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:32:19.471 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:32:24.635 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:32:29.705 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:32:37.852 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:32:42.982 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:32:48.123 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:32:56.045 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:33:01.189 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:33:06.262 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:33:14.314 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:33:19.467 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:33:24.538 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:33:32.477 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:33:37.629 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:33:42.700 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:33:50.763 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:33:55.873 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:34:00.944 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:34:09.063 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:34:14.212 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:34:19.291 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:34:27.270 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:34:33.717 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:34:45.648 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:34:50.790 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:34:55.865 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:35:03.785 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:35:08.954 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:35:14.063 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:35:22.027 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:35:28.477 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:35:40.309 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:35:46.673 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:35:58.561 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:36:03.668 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:36:08.742 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:36:16.792 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:36:23.230 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:36:35.028 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:36:40.140 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:36:45.216 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:36:53.278 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:36:59.855 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:37:11.537 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:37:17.926 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:37:29.902 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:37:36.142 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:37:48.026 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:37:53.341 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:37:58.445 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:38:06.288 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:38:12.745 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:38:24.577 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:38:29.704 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:38:34.777 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:38:42.728 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:38:47.981 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:38:53.052 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:39:01.886 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:39:06.962 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:39:12.035 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:39:19.258 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:39:24.401 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:39:29.471 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:39:37.568 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:39:42.721 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:39:47.875 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:39:55.809 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:40:02.166 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:40:14.152 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:40:19.302 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:40:24.408 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:40:32.278 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:40:37.452 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:40:42.528 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:41:00.536 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:41:07.364 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:41:08.749 [warning] [Decorations] CAPPING events from decorations provider vscode.git 476
2025-06-23 21:41:17.882 [warning] [Decorations] CAPPING events from decorations provider vscode.git 476
2025-06-23 21:41:18.794 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:41:23.931 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:41:29.100 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:41:47.130 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:41:52.276 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:41:57.350 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:42:05.417 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:42:08.891 [warning] [Decorations] CAPPING events from decorations provider vscode.git 477
2025-06-23 21:42:10.590 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:42:15.673 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:42:18.917 [warning] [Decorations] CAPPING events from decorations provider vscode.git 477
2025-06-23 21:42:23.601 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:42:28.749 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:42:33.820 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:42:41.758 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:42:48.165 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:42:51.187 [warning] [Decorations] CAPPING events from decorations provider vscode.git 478
2025-06-23 21:42:59.969 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:43:07.194 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:43:18.433 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:43:23.567 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:43:26.586 [warning] [Decorations] CAPPING events from decorations provider vscode.git 479
2025-06-23 21:43:28.649 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:43:46.627 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:43:53.747 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:44:01.287 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at pq.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:147:106094)
	at $5.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
	at $5.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
	at $5.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
	at $5.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at lv.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lk.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-23 21:44:01.294 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at pq.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
	at $5.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
	at $5.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
	at $5.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
	at $5.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at lv.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lk.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-23 21:44:01.510 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at pq.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
	at $5.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
	at $5.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
	at $5.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
	at $5.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at lv.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lk.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-23 21:44:01.511 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at pq.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
	at pq.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105427)
	at $5.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
	at $5.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
	at $5.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
	at $5.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at lv.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lk.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-23 21:44:02.746 [warning] [Decorations] CAPPING events from decorations provider vscode.git 479
2025-06-23 21:44:04.781 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:44:09.923 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:44:14.997 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:44:23.049 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:44:29.605 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:44:40.606 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at pq.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:147:106094)
	at $5.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
	at $5.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
	at $5.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
	at $5.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at lv.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lk.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-23 21:44:40.608 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at pq.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
	at $5.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
	at $5.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
	at $5.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
	at $5.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at lv.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lk.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-23 21:44:40.866 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at pq.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
	at $5.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
	at $5.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
	at $5.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
	at $5.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at lv.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lk.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-23 21:44:40.869 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at pq.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
	at pq.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105427)
	at $5.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
	at $5.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
	at $5.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
	at $5.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at lv.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lk.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-23 21:44:41.653 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:44:42.079 [warning] [Decorations] CAPPING events from decorations provider vscode.git 479
2025-06-23 21:44:46.800 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:44:51.874 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:44:59.703 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:45:06.070 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:45:17.767 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:45:22.565 [warning] [Decorations] CAPPING events from decorations provider vscode.git 480
2025-06-23 21:45:23.619 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:45:28.693 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:45:36.047 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:45:41.210 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:45:46.307 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:45:54.290 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:46:00.840 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:46:12.595 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:46:17.777 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:46:22.858 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:46:30.768 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:46:36.152 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:46:41.372 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:46:59.440 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:47:00.800 [warning] [Decorations] CAPPING events from decorations provider vscode.git 480
2025-06-23 21:47:04.663 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:47:09.732 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:47:27.311 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:47:32.716 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:47:37.805 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:47:45.643 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:47:50.901 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:47:55.975 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:48:13.791 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:48:19.037 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:48:24.123 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:48:42.162 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:48:47.280 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:48:52.356 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:49:00.275 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:49:06.694 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:49:18.478 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:49:24.975 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:50:15.304 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:50:20.375 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:50:31.518 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:50:36.643 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:50:41.713 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:51:00.162 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:51:05.290 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:51:10.374 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:51:17.973 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:51:23.108 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:51:28.185 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:51:29.472 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at pq.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:147:106094)
	at $5.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
	at $5.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
	at $5.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
	at $5.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at lv.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lk.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-23 21:51:29.475 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at pq.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
	at $5.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
	at $5.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
	at $5.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
	at $5.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at lv.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lk.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-23 21:51:29.703 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at pq.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
	at $5.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
	at $5.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
	at $5.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
	at $5.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at lv.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lk.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-23 21:51:29.704 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at pq.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
	at pq.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105427)
	at $5.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
	at $5.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
	at $5.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
	at $5.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at lv.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lk.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-23 21:51:36.271 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:51:41.418 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:51:46.489 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:52:04.477 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:52:11.136 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:52:22.752 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:52:29.511 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:52:41.043 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:52:46.216 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:52:51.313 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:54:11.160 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:54:16.329 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:54:21.654 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:54:30.529 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:54:36.906 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:54:48.844 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:54:55.238 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:55:07.109 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:55:13.582 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:55:25.315 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:55:31.859 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:55:43.540 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:55:50.214 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:56:01.754 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:56:07.038 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:56:12.123 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:56:19.984 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:56:26.410 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:56:38.280 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:56:44.747 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:56:56.545 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:57:01.660 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:57:06.737 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:57:14.870 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:57:21.163 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:57:33.113 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:57:39.414 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:57:51.330 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:57:58.035 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:58:09.555 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:58:16.179 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:58:27.787 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:58:34.396 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:58:46.040 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:58:51.217 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:58:56.294 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:58:57.092 [warning] [Decorations] CAPPING events from decorations provider vscode.git 481
2025-06-23 21:59:14.292 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:59:19.430 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:59:24.522 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:59:42.749 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:59:47.861 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 21:59:53.176 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:00:00.931 [warning] [Decorations] CAPPING events from decorations provider vscode.git 482
2025-06-23 22:00:10.221 [warning] [Decorations] CAPPING events from decorations provider vscode.git 483
2025-06-23 22:00:10.777 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:00:16.226 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:00:21.307 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:00:28.975 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:00:35.547 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:00:45.306 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:00:47.295 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:00:53.794 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:00:57.622 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:01:05.616 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:01:10.795 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:01:15.877 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:01:33.787 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:01:40.427 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:01:52.081 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:01:57.304 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:02:02.500 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:02:06.864 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:02:13.324 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:02:20.016 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:02:20.817 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:02:26.024 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:02:26.186 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:02:31.539 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:02:32.074 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:02:37.545 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:02:43.680 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:02:49.095 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:03:23.705 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:03:23.720 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:03:28.820 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:03:28.861 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:03:33.907 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:03:34.986 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:03:39.066 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:04:07.670 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:04:07.683 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:04:12.984 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:04:13.163 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:04:18.168 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:04:18.437 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:04:23.242 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:04:23.507 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:04:28.785 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:04:33.876 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:04:38.947 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:04:39.760 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:04:44.341 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:04:44.906 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:04:49.984 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:04:50.319 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:04:56.345 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:04:58.044 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:05:02.448 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:05:03.260 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:05:08.335 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:05:08.410 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:05:14.430 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:05:16.372 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:05:20.495 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:05:21.653 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:05:26.488 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:05:26.731 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:05:32.542 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:05:34.506 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:05:38.782 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:05:39.788 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:05:45.146 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:05:45.334 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:05:50.604 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:05:56.632 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:06:02.661 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:06:02.941 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:07:10.636 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:07:10.660 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:07:15.831 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:07:15.850 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:07:20.999 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:07:21.332 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:07:26.125 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:07:27.335 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:07:33.353 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:07:34.222 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:07:39.415 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:07:39.639 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:07:44.486 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:07:45.436 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:07:51.589 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:07:52.323 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:07:57.548 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:07:58.995 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:08:03.552 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:08:09.582 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:08:10.542 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:08:15.694 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:08:15.918 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:08:20.986 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:08:21.588 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:08:26.659 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:08:28.777 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:08:34.292 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:08:43.685 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:08:48.756 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:08:53.836 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:08:58.915 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:09:05.235 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:09:05.953 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:09:32.692 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:09:32.702 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:09:37.828 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:09:37.846 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:09:42.992 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:09:43.007 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:09:48.116 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:09:48.136 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:09:53.244 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:09:53.286 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:09:58.695 [warning] [Decorations] CAPPING events from decorations provider vscode.git 484
2025-06-23 22:10:00.279 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:10:03.764 [warning] [Decorations] CAPPING events from decorations provider vscode.git 936
2025-06-23 22:10:05.426 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:10:08.902 [warning] [Decorations] CAPPING events from decorations provider vscode.git 936
2025-06-23 22:10:10.578 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:10:15.298 [warning] [Decorations] CAPPING events from decorations provider vscode.git 936
2025-06-23 22:10:18.276 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:10:20.372 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1036
2025-06-23 22:10:23.390 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:10:25.437 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1036
2025-06-23 22:10:28.466 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:10:30.499 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1036
2025-06-23 22:10:35.557 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1036
2025-06-23 22:10:36.561 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:10:40.682 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1036
2025-06-23 22:10:41.706 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:10:46.776 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:10:47.601 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1044
2025-06-23 22:10:52.715 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1042
2025-06-23 22:10:54.733 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:10:58.368 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1042
2025-06-23 22:11:01.441 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:11:04.402 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1042
2025-06-23 22:11:10.423 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1042
2025-06-23 22:11:13.012 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:11:16.462 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1042
2025-06-23 22:11:18.114 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:11:23.912 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1042
2025-06-23 22:11:23.923 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:11:28.972 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1050
2025-06-23 22:11:31.273 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:11:34.544 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1046
2025-06-23 22:11:36.444 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:11:41.854 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1046
2025-06-23 22:11:41.871 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:11:46.916 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:11:49.451 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:11:52.625 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:11:54.555 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:11:58.716 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:11:59.626 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:12:04.684 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:12:07.800 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:12:10.714 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:12:12.975 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:12:16.740 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:12:18.047 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:12:22.765 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:12:25.980 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:12:28.801 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:12:32.271 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:12:34.923 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:12:37.341 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:12:40.889 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:12:44.229 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:12:46.901 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:12:49.438 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:12:52.912 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:12:54.511 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:12:58.943 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:13:02.532 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:13:05.027 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:13:07.754 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:13:10.996 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:13:12.841 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:13:16.972 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:13:20.817 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:13:23.004 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:13:25.934 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:13:29.167 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:13:31.005 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:13:35.386 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:13:41.174 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:13:47.195 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:13:49.240 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:13:53.288 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:13:54.378 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:13:59.220 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:13:59.450 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:14:05.272 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:14:07.287 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:14:12.009 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:14:12.438 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:14:17.067 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:14:17.508 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:14:22.192 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:14:25.521 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:14:27.261 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:14:30.663 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:14:32.317 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:14:35.772 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:14:37.376 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:14:42.435 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:14:43.844 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:14:47.493 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:14:49.360 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:24:05.261 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:24:05.271 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:24:10.388 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:24:10.400 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:24:15.468 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:24:16.006 [warning] [Decorations] CAPPING events from decorations provider vscode.git 467
2025-06-23 22:24:20.830 [warning] [Decorations] CAPPING events from decorations provider vscode.git 1051
2025-06-23 22:31:34.603 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at pq.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:147:106094)
	at $5.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
	at $5.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
	at $5.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
	at $5.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at lv.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lk.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-23 22:31:34.862 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at pq.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:147:106094)
	at $5.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
	at $5.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
	at $5.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
	at $5.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at lv.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lk.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-23 22:43:36.692 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at pq.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:147:106094)
	at $5.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
	at $5.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
	at $5.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
	at $5.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at lv.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lk.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-23 22:43:36.696 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at pq.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
	at $5.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
	at $5.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
	at $5.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
	at $5.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at lv.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lk.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-23 22:43:36.891 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at pq.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
	at $5.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
	at $5.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
	at $5.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
	at $5.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at lv.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lk.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-23 22:43:36.893 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at pq.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
	at pq.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105427)
	at $5.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
	at $5.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
	at $5.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
	at $5.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at lv.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lk.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-23 22:52:11.461 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at pq.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:147:106094)
	at $5.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
	at $5.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
	at $5.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
	at $5.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at lv.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lk.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-23 22:52:11.464 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at pq.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
	at $5.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
	at $5.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
	at $5.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
	at $5.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at lv.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lk.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-23 22:52:11.617 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at pq.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
	at $5.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
	at $5.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
	at $5.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
	at $5.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at lv.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lk.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-23 22:52:11.618 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at pq.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
	at pq.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105427)
	at $5.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
	at $5.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
	at $5.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
	at $5.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at lv.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lk.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-23 22:54:41.681 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at pq.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:147:106094)
	at $5.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
	at $5.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
	at $5.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
	at $5.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at lv.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lk.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-23 22:54:41.685 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at pq.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
	at $5.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
	at $5.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
	at $5.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
	at $5.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at lv.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lk.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-23 22:54:41.849 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at pq.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
	at $5.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
	at $5.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
	at $5.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
	at $5.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at lv.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lk.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-23 22:54:41.850 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at pq.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
	at pq.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105427)
	at $5.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
	at $5.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
	at $5.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
	at $5.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at po.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at lv.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ud.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lk.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-23 23:04:40.660 [error] Error: Failed to execute git
	at t.Git._exec (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:465561)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async t.Git.exec (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:464680)
	at async V.exec (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:470354)
	at async V.getRefs (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:491992)
	at async O.retryRun (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:605797)
	at async O.run (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:605401)
	at async O.getRefs (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:595437)
	at async Promise.all (index 1)
	at async O._updateModelState (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:607104)
	at async O.updateModelState (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:606394)
	at async O.run (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:605602)
	at async O.status (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:585257)
2025-06-23 23:04:40.669 [error] Error: Failed to execute git
	at t.Git._exec (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:465561)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async t.Git.exec (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:464680)
	at async V.exec (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:470354)
	at async V.getRefs (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:491992)
	at async O.retryRun (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:605797)
	at async O.run (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:605401)
	at async O.getRefs (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:595437)
	at async Promise.all (index 1)
	at async O._updateModelState (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:607104)
	at async O.updateModelState (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:606394)
	at async O.run (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:605602)
	at async O.status (/home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js:2:585257)
2025-06-23 23:05:16.246 [warning] [Decorations] CAPPING events from decorations provider vscode.git 466
2025-06-23 23:05:16.439 [warning] [Decorations] CAPPING events from decorations provider vscode.git 466
2025-06-23 23:05:16.849 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:16.850 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:16.850 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:16.850 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:16.850 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:16.850 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:16.850 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:16.850 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:16.850 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:16.850 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:16.850 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:16.850 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:16.850 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:16.850 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:16.850 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:16.850 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:16.850 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:16.850 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:16.850 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:17.850 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:17.850 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:17.850 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:17.850 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:17.850 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:17.850 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:17.850 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:17.850 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:17.850 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:17.850 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:17.850 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:17.851 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:17.851 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:17.851 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:17.851 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:17.851 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:17.851 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:17.852 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:05:17.852 [error] Error: spawn git ENOENT
	at ChildProcess._handle.onexit (node:internal/child_process:285:19)
	at onErrorNT (node:internal/child_process:483:16)
	at processTicksAndRejections (node:internal/process/task_queues:90:21)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processTimers (node:internal/timers:520:9)
2025-06-23 23:06:16.430 [info] Extension host terminating: received terminate message from renderer
2025-06-23 23:06:16.435 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/95ac9d7926bc5ac4ec545ed2b247397a/vscode.lock': Marking the lockfile as scheduled to be released in 6000 ms.
2025-06-23 23:06:16.808 [info] Extension host with pid 243612 exiting with code 0
