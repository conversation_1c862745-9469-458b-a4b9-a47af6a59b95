2025-06-23 22:31:26.316 [info] Extension host with pid 296796 started
2025-06-23 22:31:26.329 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/8fb0da0c66a291b7cbfae207d20e731c/vscode.lock': Lock acquired.
2025-06-23 22:31:26.621 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-06-23 22:31:26.627 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: false, activationEvent: '*', root cause: vscode.git
2025-06-23 22:31:26.629 [info] ExtensionService#_doActivateExtension vscode.tunnel-forwarding, startup: false, activationEvent: 'onTunnel'
2025-06-23 22:31:26.838 [info] ExtensionService#_doActivateExtension vscode.git, startup: false, activationEvent: '*'
2025-06-23 22:31:26.838 [info] ExtensionService#_doActivateExtension vscode.github, startup: false, activationEvent: '*'
2025-06-23 22:31:27.553 [info] Eager extensions activated
2025-06-23 22:31:27.554 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-06-23 22:31:27.557 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-06-23 22:31:27.558 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onStartupFinished'
2025-06-23 22:31:36.121 [error] CodeExpectedError: cannot open vscode-userdata:c%3A%5CUsers%5Canita%5CAppData%5CRoaming%5CCode%5CUser%5Ckeybindings.json. Detail: Unable to resolve filesystem provider with relative file path 'vscode-userdata:c:\Users\<USER>\AppData\Roaming\Code\User\keybindings.json'
    at Pze.$tryOpenDocument (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/workbench.desktop.main.js:1283:8666)
2025-06-23 22:31:38.326 [info] Extension host terminating: received terminate message from renderer
2025-06-23 22:31:38.327 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/8fb0da0c66a291b7cbfae207d20e731c/vscode.lock': Marking the lockfile as scheduled to be released in 6000 ms.
2025-06-23 22:31:38.359 [info] Extension host with pid 296796 exiting with code 0
