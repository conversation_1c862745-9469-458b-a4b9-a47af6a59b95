2025-06-23 20:12:49.802 [info] 




2025-06-23 20:12:49.842 [info] Extension host agent started.
2025-06-23 20:12:50.117 [info] [<unknown>][bad11561][ManagementConnection] New connection established.
2025-06-23 20:12:50.128 [info] [<unknown>][9eb7aae0][ExtensionHostConnection] New connection established.
2025-06-23 20:12:50.378 [info] ComputeTargetPlatform: linux-x64
2025-06-23 20:12:50.517 [info] [<unknown>][9eb7aae0][ExtensionHostConnection] <243612> Launched Extension Host Process.
2025-06-23 20:12:54.295 [info] ComputeTargetPlatform: linux-x64
2025-06-23 20:12:55.406 [info] Getting Manifest... augment.vscode-augment
2025-06-23 20:12:55.471 [info] Installing extension: augment.vscode-augment {"productVersion":{"version":"1.101.1","date":"2025-06-18T13:35:12.605Z"},"pinned":false,"operation":3,"isApplicationScoped":false,"donotVerifySignature":false,"context":{"clientTargetPlatform":"win32-x64"},"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.vscode-server/extensions/extensions.json","external":"file:///home/<USER>/.vscode-server/extensions/extensions.json","path":"/home/<USER>/.vscode-server/extensions/extensions.json","scheme":"file"}}
2025-06-23 20:13:00.437 [info] Extension signature verification result for augment.vscode-augment: Success. Internal Code: 0. Executed: true. Duration: 4220ms.
2025-06-23 20:13:02.036 [info] Extracted extension to file:///home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.487.1: augment.vscode-augment
2025-06-23 20:13:02.140 [info] Renamed to /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.487.1
2025-06-23 20:13:02.175 [info] Extension installed successfully: augment.vscode-augment file:///home/<USER>/.vscode-server/extensions/extensions.json
2025-06-23 20:13:02.183 [info] Marked extension as removed augment.vscode-augment-0.482.1
2025-06-23 20:17:49.843 [info] New EH opened, aborting shutdown
2025-06-23 21:10:09.172 [info] [<unknown>][be2e4bdf][ExtensionHostConnection] New connection established.
2025-06-23 21:10:09.192 [info] [<unknown>][be2e4bdf][ExtensionHostConnection] <272435> Launched Extension Host Process.
2025-06-23 21:10:09.196 [info] [<unknown>][66349b79][ManagementConnection] New connection established.
2025-06-23 21:10:11.651 [error] CodeExpectedError: Could not find pty 38 on pty host
    at O.W (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:46:6391)
    at O.updateTitle (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:46:1839)
    at R.s.<computed> (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:45:2933)
    at Object.call (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:28:4204)
    at ml.s (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:81286)
    at ml.q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:80809)
    at ps.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:80211)
    at E.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:2373)
    at E.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:2591)
    at process.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:24:30052)
    at process.emit (node:events:518:28)
    at emit (node:internal/child_process:949:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)
2025-06-23 21:10:18.652 [error] [File Watcher ('parcel')] Inotify limit reached (ENOSPC) (path: /home/<USER>
2025-06-23 21:10:26.835 [info] [<unknown>][66349b79][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
2025-06-23 21:10:26.969 [info] [<unknown>][be2e4bdf][ExtensionHostConnection] <272435> Extension Host Process exited with code: 0, signal: null.
2025-06-23 22:12:32.656 [warning] [File Watcher (node.js)] Watcher shutdown because watched path got deleted
2025-06-23 22:24:24.402 [warning] [File Watcher (node.js)] Watcher shutdown because watched path got deleted
2025-06-23 22:31:23.666 [info] [<unknown>][0dc81362][ExtensionHostConnection] New connection established.
2025-06-23 22:31:23.678 [info] [<unknown>][d7a2b750][ManagementConnection] New connection established.
2025-06-23 22:31:23.769 [info] [<unknown>][0dc81362][ExtensionHostConnection] <296796> Launched Extension Host Process.
2025-06-23 22:31:33.386 [error] [File Watcher ('parcel')] Inotify limit reached (ENOSPC) (path: /home/<USER>
2025-06-23 22:31:38.324 [info] [<unknown>][d7a2b750][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
2025-06-23 22:31:38.423 [info] [<unknown>][0dc81362][ExtensionHostConnection] <296796> Extension Host Process exited with code: 0, signal: null.
2025-06-23 23:06:16.423 [info] [<unknown>][bad11561][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
2025-06-23 23:06:17.177 [info] [<unknown>][9eb7aae0][ExtensionHostConnection] <243612> Extension Host Process exited with code: 0, signal: null.
2025-06-23 23:06:17.177 [info] Last EH closed, waiting before shutting down
2025-06-23 23:07:50.901 [info] [<unknown>][627adf65][ManagementConnection] New connection established.
2025-06-23 23:07:50.917 [error] Error: Unexpected SIGPIPE
    at process.<anonymous> (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/server-main.js:191:1060)
    at process.emit (node:events:530:35)
2025-06-23 23:07:50.929 [info] [<unknown>][665fb219][ExtensionHostConnection] New connection established.
2025-06-23 23:07:50.943 [info] [<unknown>][665fb219][ExtensionHostConnection] <299667> Launched Extension Host Process.
2025-06-23 23:11:17.180 [info] New EH opened, aborting shutdown
2025-06-23 23:12:24.181 [error] Error: connect ECONNREFUSED 127.0.0.1:9001
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
2025-06-23 23:12:24.184 [error] Error: connect ECONNREFUSED 127.0.0.1:9001
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
2025-06-23 23:12:32.757 [error] Error: connect ECONNREFUSED 127.0.0.1:9001
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
2025-06-23 23:12:33.702 [error] Error: connect ECONNREFUSED 127.0.0.1:9001
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
2025-06-23 23:12:34.124 [error] Error: connect ECONNREFUSED 127.0.0.1:9001
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
2025-06-23 23:12:54.914 [error] Error: connect ECONNREFUSED 127.0.0.1:9001
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
2025-06-23 23:12:59.034 [error] Error: connect ECONNREFUSED 127.0.0.1:9001
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
2025-06-23 23:32:59.717 [info] [<unknown>][627adf65][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
2025-06-23 23:33:00.244 [info] [<unknown>][665fb219][ExtensionHostConnection] <299667> Extension Host Process exited with code: 0, signal: null.
2025-06-23 23:33:00.257 [info] Last EH closed, waiting before shutting down
2025-06-23 23:35:31.020 [info] [<unknown>][b6e1e803][ExtensionHostConnection] New connection established.
2025-06-23 23:35:31.044 [info] [<unknown>][1dd40083][ManagementConnection] New connection established.
2025-06-23 23:35:31.113 [info] [<unknown>][b6e1e803][ExtensionHostConnection] <303860> Launched Extension Host Process.
2025-06-23 23:35:33.308 [error] CodeExpectedError: Could not find pty 49 on pty host
    at O.W (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:46:6391)
    at O.updateTitle (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:46:1839)
    at R.s.<computed> (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:45:2933)
    at Object.call (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:28:4204)
    at ml.s (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:81286)
    at ml.q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:80809)
    at ps.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:80211)
    at E.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:2373)
    at E.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:2591)
    at process.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:24:30052)
    at process.emit (node:events:518:28)
    at emit (node:internal/child_process:949:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)
2025-06-23 23:35:36.760 [error] Error: connect ECONNREFUSED 127.0.0.1:9001
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
2025-06-23 23:36:03.706 [error] Error: connect ECONNREFUSED 127.0.0.1:9001
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
2025-06-23 23:36:30.697 [error] Error: connect ECONNREFUSED 127.0.0.1:9001
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
2025-06-23 23:37:21.712 [error] Error: connect ECONNREFUSED 127.0.0.1:9001
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
2025-06-23 23:38:00.262 [info] New EH opened, aborting shutdown
2025-06-23 23:39:21.701 [error] Error: connect ECONNREFUSED 127.0.0.1:9001
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
2025-06-23 23:41:21.726 [error] Error: connect ECONNREFUSED 127.0.0.1:9001
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
