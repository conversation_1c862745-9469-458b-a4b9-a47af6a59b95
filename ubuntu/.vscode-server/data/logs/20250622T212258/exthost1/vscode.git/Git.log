2025-06-22 21:23:03.308 [info] [main] Log level: Info
2025-06-22 21:23:03.308 [info] [main] Validating found git in: "git"
2025-06-22 21:23:03.308 [info] [main] Using git "2.43.0" from "git"
2025-06-22 21:23:03.308 [info] [Model][doInitialScan] Initial repository scan started
2025-06-22 21:23:03.308 [info] > git rev-parse --show-toplevel [789ms]
2025-06-22 21:23:03.308 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-22 21:23:03.308 [info] > git rev-parse --show-toplevel [92ms]
2025-06-22 21:23:03.315 [info] > git rev-parse --git-dir --git-common-dir [3ms]
2025-06-22 21:23:03.339 [info] [Model][openRepository] Opened repository (path): /home/<USER>/algofactory-multi/instances/algofactory-1010
2025-06-22 21:23:03.339 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/algofactory-multi/instances/algofactory-1010
2025-06-22 21:23:03.368 [info] > git rev-parse --show-toplevel [20ms]
2025-06-22 21:23:03.368 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-22 21:23:03.371 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-22 21:23:03.371 [info] > git config --get commit.template [26ms]
2025-06-22 21:23:03.409 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [33ms]
2025-06-22 21:23:03.414 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/instances/algofactory-1010/.git/refs/remotes/origin/main'
2025-06-22 21:23:03.630 [info] > git rev-parse refs/remotes/origin/main [216ms]
2025-06-22 21:23:03.674 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [32ms]
2025-06-22 21:23:03.725 [info] > git show --textconv :.env [6ms]
2025-06-22 21:23:03.731 [info] > git ls-files --stage -- .env [8ms]
2025-06-22 21:23:03.737 [info] > git hash-object -t tree /dev/null [6ms]
2025-06-22 21:23:03.737 [warning] [GitFileSystemProvider][readFile] File not found - git:/home/<USER>/algofactory-multi/instances/algofactory-1010/.env.git?%7B%22path%22%3A%22%2Fhome%2Fubuntu%2Falgofactory-multi%2Finstances%2Falgofactory-1010%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-22 21:23:03.739 [info] > git hash-object -t tree /dev/null [3ms]
2025-06-22 21:23:03.739 [warning] [GitFileSystemProvider][stat] File not found - git:/home/<USER>/algofactory-multi/instances/algofactory-1010/.env.git?%7B%22path%22%3A%22%2Fhome%2Fubuntu%2Falgofactory-multi%2Finstances%2Falgofactory-1010%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-22 21:23:03.900 [info] > git check-ignore -v -z --stdin [4ms]
2025-06-22 21:23:04.360 [info] > git status -z -uall [724ms]
2025-06-22 21:23:04.659 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [185ms]
2025-06-22 21:23:04.907 [info] > git config --get --local branch.main.vscode-merge-base [243ms]
2025-06-22 21:23:04.925 [info] > git config --get commit.template [439ms]
2025-06-22 21:23:04.961 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [36ms]
2025-06-22 21:23:05.221 [info] > git merge-base refs/heads/main refs/remotes/origin/main [234ms]
2025-06-22 21:23:05.233 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [229ms]
2025-06-22 21:23:05.242 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory-multi/instances/algofactory-1010/.git/refs/remotes/origin/main'
2025-06-22 21:23:05.242 [info] > git diff --name-status -z --diff-filter=ADMR 89ac1fedef89963fb80f591849bbcbe1dc91b13e...refs/remotes/origin/main [10ms]
2025-06-22 21:23:05.248 [info] > git rev-parse refs/remotes/origin/main [7ms]
2025-06-22 21:23:05.279 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [20ms]
2025-06-22 21:23:05.823 [info] > git status -z -uall [571ms]
