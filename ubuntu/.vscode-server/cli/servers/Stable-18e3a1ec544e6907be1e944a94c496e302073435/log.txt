*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[20:12:49] 




[20:12:49] Extension host agent started.
[20:12:50] [<unknown>][bad11561][ManagementConnection] New connection established.
[20:12:50] [<unknown>][9eb7aae0][ExtensionHostConnection] New connection established.
[20:12:50] ComputeTargetPlatform: linux-x64
[20:12:50] [<unknown>][9eb7aae0][ExtensionHostConnection] <243612> Launched Extension Host Process.
[20:12:54] ComputeTargetPlatform: linux-x64
[20:12:55] Getting Manifest... augment.vscode-augment
[20:12:55] Installing extension: augment.vscode-augment {
  productVersion: { version: '1.101.1', date: '2025-06-18T13:35:12.605Z' },
  pinned: false,
  operation: 3,
  isApplicationScoped: false,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'win32-x64' },
  profileLocation: Br {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  }
}
[20:13:00] Extension signature verification result for augment.vscode-augment: Success. Internal Code: 0. Executed: true. Duration: 4220ms.
[20:13:02] Extracted extension to file:///home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.487.1: augment.vscode-augment
[20:13:02] Renamed to /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.487.1
[20:13:02] Extension installed successfully: augment.vscode-augment file:///home/<USER>/.vscode-server/extensions/extensions.json
[20:13:02] Marked extension as removed augment.vscode-augment-0.482.1
New EH opened, aborting shutdown
[20:17:49] New EH opened, aborting shutdown
[21:10:09] [<unknown>][be2e4bdf][ExtensionHostConnection] New connection established.
[21:10:09] [<unknown>][be2e4bdf][ExtensionHostConnection] <272435> Launched Extension Host Process.
[21:10:09] [<unknown>][66349b79][ManagementConnection] New connection established.
rejected promise not handled within 1 second: CodeExpectedError: Could not find pty 38 on pty host
stack trace: CodeExpectedError: Could not find pty 38 on pty host
    at O.W (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:46:6391)
    at O.updateTitle (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:46:1839)
    at R.s.<computed> (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:45:2933)
    at Object.call (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:28:4204)
    at ml.s (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:81286)
    at ml.q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:80809)
    at ps.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:80211)
    at E.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:2373)
    at E.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:2591)
    at process.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:24:30052)
    at process.emit (node:events:518:28)
    at emit (node:internal/child_process:949:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)
[21:10:11] Error [CodeExpectedError]: Could not find pty 38 on pty host
    at O.W (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:46:6391)
    at O.updateTitle (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:46:1839)
    at R.s.<computed> (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:45:2933)
    at Object.call (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:28:4204)
    at ml.s (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:81286)
    at ml.q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:80809)
    at ps.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:80211)
    at E.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:2373)
    at E.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:2591)
    at process.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:24:30052)
    at process.emit (node:events:518:28)
    at emit (node:internal/child_process:949:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)
[21:10:18] [File Watcher ('parcel')] Inotify limit reached (ENOSPC) (path: /home/<USER>
[21:10:26] [<unknown>][66349b79][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
[21:10:26] [<unknown>][be2e4bdf][ExtensionHostConnection] <272435> Extension Host Process exited with code: 0, signal: null.
[22:12:32] [File Watcher (node.js)] Watcher shutdown because watched path got deleted
[node.js fs] readdir with filetypes failed with error:  Error: ENOENT: no such file or directory, scandir '/home/<USER>/myproject/algofactory-base'
    at async Object.readdir (node:internal/fs/promises:949:18)
    at async oO (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/server-main.js:27:88121)
    at async Object.bh (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/server-main.js:27:88050)
    at async zu.readdir (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/server-main.js:67:15999) {
  errno: -2,
  code: 'ENOENT',
  syscall: 'scandir',
  path: '/home/<USER>/myproject/algofactory-base'
}
[22:24:24] [File Watcher (node.js)] Watcher shutdown because watched path got deleted
[22:31:23] [<unknown>][0dc81362][ExtensionHostConnection] New connection established.
[22:31:23] [<unknown>][d7a2b750][ManagementConnection] New connection established.
[22:31:23] [<unknown>][0dc81362][ExtensionHostConnection] <296796> Launched Extension Host Process.
[22:31:33] [File Watcher ('parcel')] Inotify limit reached (ENOSPC) (path: /home/<USER>
[22:31:38] [<unknown>][d7a2b750][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
[22:31:38] [<unknown>][0dc81362][ExtensionHostConnection] <296796> Extension Host Process exited with code: 0, signal: null.
