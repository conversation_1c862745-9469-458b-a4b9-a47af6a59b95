*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[18:43:36] 




[18:43:36] Extension host agent started.
[18:43:36] Started initializing default profile extensions in extensions installation folder. file:///home/<USER>/.vscode-server/extensions
[18:43:36] ComputeTargetPlatform: linux-x64
[18:43:36] [<unknown>][13157b68][ExtensionHostConnection] New connection established.
[18:43:36] [<unknown>][81bc3c4c][ManagementConnection] New connection established.
[18:43:36] Completed initializing default profile extensions in extensions installation folder. file:///home/<USER>/.vscode-server/extensions
[18:43:36] [<unknown>][13157b68][ExtensionHostConnection] <1273> Launched Extension Host Process.
[18:43:38] ComputeTargetPlatform: linux-x64
[18:43:49] [<unknown>][9ec8f9e0][ExtensionHostConnection] New connection established.
[18:43:49] [<unknown>][9ec8f9e0][ExtensionHostConnection] <1434> Launched Extension Host Process.
[18:43:49] [<unknown>][32a9f6ba][ManagementConnection] New connection established.
rejected promise not handled within 1 second: CodeExpectedError: Could not find pty 10 on pty host
stack trace: CodeExpectedError: Could not find pty 10 on pty host
    at O.W (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:46:6391)
    at O.updateTitle (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:46:1839)
    at R.s.<computed> (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:45:2933)
    at Object.call (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:28:4204)
    at ml.s (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:81286)
    at ml.q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:80809)
    at ps.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:80211)
    at E.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:2373)
    at E.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:2591)
    at process.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:24:30052)
    at process.emit (node:events:518:28)
    at emit (node:internal/child_process:949:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)
[18:43:51] Error [CodeExpectedError]: Could not find pty 10 on pty host
    at O.W (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:46:6391)
    at O.updateTitle (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:46:1839)
    at R.s.<computed> (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:45:2933)
    at Object.call (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:28:4204)
    at ml.s (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:81286)
    at ml.q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:80809)
    at ps.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:80211)
    at E.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:2373)
    at E.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:2591)
    at process.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:24:30052)
    at process.emit (node:events:518:28)
    at emit (node:internal/child_process:949:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)
[18:44:04] [<unknown>][32a9f6ba][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
[18:44:04] [<unknown>][9ec8f9e0][ExtensionHostConnection] <1434> Extension Host Process exited with code: 0, signal: null.
Cancelling previous shutdown timeout
[18:44:04] Cancelling previous shutdown timeout
[18:44:22] [<unknown>][81bc3c4c][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
[18:44:22] [<unknown>][13157b68][ExtensionHostConnection] <1273> Extension Host Process exited with code: 0, signal: null.
Last EH closed, waiting before shutting down
[18:44:22] Last EH closed, waiting before shutting down
