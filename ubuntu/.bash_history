sudo apt update
sudo apt list --upgradable
sudo apt update -all
sudo apt update
sudo apt list --upgradable
sudo apt upgrade -y
sudo apt full-upgrade -y
sudo apt autoremove -y
sudo apt clean
sudo reeboot
sudo restart
bash
sudo reboot
bash
cd /
ls
pwd
bash
cd..
python3 -m venv shared-venv
sudo python3 -m venv shared-venv
bash
sudo apt install python3.12-venv -y
ls
sudo mv /shared-venv /home/<USER>/
source /home/<USER>/shared-venv/bin/activate
bash
source /home/<USER>/shared-venv/bin/activate
bash
sudo apt update
sudo apt install python3.12-venv
python3 -m venv /home/<USER>/shared-venv
bash
python3 -m venv /home/<USER>/shared-venv
sudo rm -rf /home/<USER>/shared-venv
python3 -m venv /home/<USER>/shared-venv
source /home/<USER>/shared-venv/bin/activate
echo 'Terminal capability test'
echo 'Terminal capability test'
cd /home/<USER>/myproject
source /home/<USER>/shared-venv/bin/activate
pip install -r openalgo/requirements-nginx.txt
bash
cd openalgo
source /home/<USER>/shared-venv/bin/activate
python app.py
echo 'Terminal capability test'
echo 'Terminal capability test'
echo 'Terminal capability test'
echo 'Terminal capability test'
echo 'Terminal capability test'
echo 'Terminal capability test'
ls
ls
cash
bash
bash
sudo ufw status
sudo ufw enable
echo "y" | sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 8080/tcp
sudo ufw status numbered
cd /home/<USER>/myproject/algofactory && python3 monitoring.py health
source /home/<USER>/shared-venv/bin/activate && pip install psutil
/home/<USER>/shared-venv/bin/python3 monitoring.py health
./start.sh status
./start.sh start
/home/<USER>/shared-venv/bin/python3 monitoring.py health
/home/<USER>/shared-venv/bin/python3 monitoring.py full
curl -I http://localhost
chmod +x /home/<USER>/myproject/algofactory/setup_24x7.sh
./setup_24x7.sh status
echo 'Terminal capability test'
echo 'Terminal capability test'
curl -k -I https://localhost
curl -s ifconfig.me
nslookup algo.algofactory.in
sudo certbot --nginx -d algo.algofactory.in --non-interactive --agree-tos --email <EMAIL>
curl -I https://algo.algofactory.in
./setup_24x7.sh enable
openssl s_client -connect algo.algofactory.in:443 -servername algo.algofactory.in < /dev/null
curl -I https://www.ssllabs.com/ssltest/analyze.html?d=algo.algofactory.in
echo 'Terminal capability test'
echo 'Terminal capability test'
