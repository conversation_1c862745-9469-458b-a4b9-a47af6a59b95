sudo apt update
sudo apt list --upgradable
sudo apt update -all
sudo apt update
sudo apt list --upgradable
sudo apt upgrade -y
sudo apt full-upgrade -y
sudo apt autoremove -y
sudo apt clean
sudo reeboot
sudo restart
bash
sudo reboot
bash
cd /
ls
pwd
bash
cd..
python3 -m venv shared-venv
sudo python3 -m venv shared-venv
bash
sudo apt install python3.12-venv -y
ls
sudo mv /shared-venv /home/<USER>/
source /home/<USER>/shared-venv/bin/activate
bash
source /home/<USER>/shared-venv/bin/activate
bash
sudo apt update
sudo apt install python3.12-venv
python3 -m venv /home/<USER>/shared-venv
bash
python3 -m venv /home/<USER>/shared-venv
sudo rm -rf /home/<USER>/shared-venv
python3 -m venv /home/<USER>/shared-venv
source /home/<USER>/shared-venv/bin/activate
echo 'Terminal capability test'
echo 'Terminal capability test'
cd /home/<USER>/myproject
source /home/<USER>/shared-venv/bin/activate
pip install -r openalgo/requirements-nginx.txt
bash
cd openalgo
source /home/<USER>/shared-venv/bin/activate
python app.py
echo 'Terminal capability test'
echo 'Terminal capability test'
echo 'Terminal capability test'
echo 'Terminal capability test'
echo 'Terminal capability test'
echo 'Terminal capability test'
ls
ls
cash
bash
bash
sudo ufw status
sudo ufw enable
echo "y" | sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 8080/tcp
sudo ufw status numbered
cd /home/<USER>/myproject/algofactory && python3 monitoring.py health
source /home/<USER>/shared-venv/bin/activate && pip install psutil
/home/<USER>/shared-venv/bin/python3 monitoring.py health
./start.sh status
./start.sh start
/home/<USER>/shared-venv/bin/python3 monitoring.py health
/home/<USER>/shared-venv/bin/python3 monitoring.py full
curl -I http://localhost
chmod +x /home/<USER>/myproject/algofactory/setup_24x7.sh
./setup_24x7.sh status
echo 'Terminal capability test'
echo 'Terminal capability test'
curl -k -I https://localhost
curl -s ifconfig.me
nslookup algo.algofactory.in
sudo certbot --nginx -d algo.algofactory.in --non-interactive --agree-tos --email <EMAIL>
curl -I https://algo.algofactory.in
./setup_24x7.sh enable
openssl s_client -connect algo.algofactory.in:443 -servername algo.algofactory.in < /dev/null
curl -I https://www.ssllabs.com/ssltest/analyze.html?d=algo.algofactory.in
echo 'Terminal capability test'
echo 'Terminal capability test'
python iteration_test.py
python3 iteration_test.py
source /home/<USER>/shared-venv/bin/activate
python iteration_test.py
python simple_depth_test.py
python iteration_test.py
chmod +x /home/<USER>/myproject/algofactory/optimize_memory.sh
cd /home/<USER>/myproject/algofactory && ./optimize_memory.sh optimize
chmod +x quick_instance.sh
./quick_instance.sh create 1010
rm -rf /home/<USER>/algofactory-multi/instances/algofactory-1010
./quick_instance.sh create 1010
./quick_instance.sh start 1010
rm -rf /home/<USER>/algofactory-multi/instances/algofactory-1010 && ./quick_instance.sh create 1010
./quick_instance.sh start 1010
rm -rf /home/<USER>/algofactory-multi/instances/algofactory-1010 && ./quick_instance.sh create 1010
./quick_instance.sh start 1010
curl -I http://localhost:1010
./quick_instance.sh list
cat /home/<USER>/algofactory-multi/instances/algofactory-1010/logs/error.log
cat /home/<USER>/algofactory-multi/instances/algofactory-1010/logs/app.log
rm -rf /home/<USER>/algofactory-multi/instances/algofactory-1010 && ./quick_instance.sh create 1010 && ./quick_instance.sh start 1010
sleep 3 && curl -I http://localhost:1010
./quick_instance.sh list && echo "=== LOG ===" && cat /home/<USER>/algofactory-multi/instances/algofactory-1010/logs/app.log
./optimize_memory.sh memory
rm -rf /home/<USER>/algofactory-multi/instances/algofactory-1010 && ./quick_instance.sh create 1010
./quick_instance.sh start 1010
rm -rf /home/<USER>/algofactory-multi/instances/algofactory-1010 && ./quick_instance.sh create 1010
./quick_instance.sh start 1010
sleep 5 && curl -I http://localhost:1010
./quick_instance.sh list
cat /home/<USER>/algofactory-multi/instances/algofactory-1010/logs/app.log | tail -20
ps aux | grep gunicorn | grep 1010
ps aux | grep gunicorn
./quick_instance.sh start 1010
cd /home/<USER>/myproject/algofactory && ./quick_instance.sh nginx 1010
sudo certbot --nginx -d 1010.algofactory.in --non-interactive --agree-tos --email <EMAIL>
sudo rm -f /etc/nginx/sites-enabled/1010.algofactory.in.conf /etc/nginx/sites-available/1010.algofactory.in.conf
./quick_instance.sh nginx 1010
sudo certbot --nginx -d 1010.algofactory.in --non-interactive --agree-tos --email <EMAIL>
ps aux | grep gunicorn | grep 1010
./quick_instance.sh start 1010
sleep 10 && curl -I https://1010.algofactory.in
ps aux | grep gunicorn | grep 1010
cat /home/<USER>/algofactory-multi/instances/algofactory-1010/logs/app.log | tail -20
./optimize_memory.sh memory
nslookup 1010.algofactory.in
./optimize_memory.sh clean
rm -rf /home/<USER>/algofactory-multi/instances/algofactory-1010 && ./quick_instance.sh create 1010
./quick_instance.sh start 1010
sleep 15 && curl -I https://1010.algofactory.in
ps aux | grep python | grep 1010
tail -30 /home/<USER>/algofactory-multi/instances/algofactory-1010/logs/app.log
./quick_instance.sh stop 1010
rm -rf /home/<USER>/algofactory-multi/instances/algofactory-1010 && ./quick_instance.sh create 8010
sudo ls -la /etc/nginx/sites-available/ | grep algofactory
sudo cat /etc/nginx/sites-available/1010.algofactory.in.conf
sudo sed -i 's/proxy_pass http:\/\/127.0.0.1:1010;/proxy_pass http:\/\/127.0.0.1:8010;/' /etc/nginx/sites-available/1010.algofactory.in.conf
sudo nginx -t && sudo systemctl reload nginx
./quick_instance.sh start 8010
sleep 20 && curl -I https://1010.algofactory.in
ps aux | grep python | grep 8010
./quick_instance.sh list
./quick_instance.sh stop 8010
./quick_instance.sh start 8010
sleep 10 && curl -I https://1010.algofactory.in
cd /home/<USER>/algofactory-multi/instances/algofactory-8010 && python3 -c "
import sqlite3
import os

# Check if database exists and has users
db_path = 'db/algofactory-8010.db'
if os.path.exists(db_path):
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Check if users table exists
    cursor.execute(\"SELECT name FROM sqlite_master WHERE type='table' AND name='users';\")
    table_exists = cursor.fetchone()
    
    if table_exists:
        cursor.execute('SELECT username FROM users;')
        users = cursor.fetchall()
        print(f'Users in database: {users}')
    else:
        print('Users table does not exist')
    
    conn.close()
else:
    print('Database does not exist')
"
cd /home/<USER>/myproject/algofactory && ./quick_instance.sh stop 8010
./quick_instance.sh start 8010
sleep 10 && curl -I https://1010.algofactory.in
curl -s https://1010.algofactory.in | grep -A 10 -B 5 "8010_"
curl -s https://1010.algofactory.in/auth/login | grep -i "8010"
./quick_instance.sh stop 8010
rm -rf /home/<USER>/algofactory-multi
sudo rm -f /etc/nginx/sites-enabled/1010.algofactory.in.conf /etc/nginx/sites-available/1010.algofactory.in.conf
sudo nginx -t && sudo systemctl reload nginx
chmod +x multi_instance.sh
./multi_instance.sh create 1010
./multi_instance.sh start 1010
sleep 5 && curl -I http://localhost:1010
tail -20 /home/<USER>/algofactory-multi/algofactory-1010/logs/app.log
cd /home/<USER>/myproject/algofactory && python3 -c "import dotenv; print('dotenv available')"
./multi_instance.sh stop 1010
./multi_instance.sh start 1010
ls -la /home/<USER>/shared-venv
./multi_instance.sh start 1010
sleep 5 && curl -I http://localhost:1010
tail -20 /home/<USER>/algofactory-multi/algofactory-1010/logs/app.log
./multi_instance.sh stop 1010
./multi_instance.sh create 8010
./multi_instance.sh start 8010
sleep 5 && curl -I http://localhost:8010
./multi_instance.sh nginx 8010
./multi_instance.sh list
./multi_instance.sh create 8011
./multi_instance.sh start 8011
sleep 5 && curl -I http://localhost:8011
./multi_instance.sh list
./multi_instance.sh create 8012
./multi_instance.sh start 8012
sleep 5 && curl -I http://localhost:8012
./multi_instance.sh nginx 8012
./multi_instance.sh list
sudo certbot --nginx -d 8010.algofactory.in --non-interactive --agree-tos --email <EMAIL>
sudo certbot --nginx -d 8011.algofactory.in --non-interactive --agree-tos --email <EMAIL>
sudo certbot install --cert-name 8011.algofactory.in
./multi_instance.sh nginx 8011
sudo certbot install --cert-name 8011.algofactory.in
sudo certbot --nginx -d 8012.algofactory.in --non-interactive --agree-tos --email <EMAIL>
curl -I https://8010.algofactory.in
chmod +x monitor_instances.sh
./monitor_instances.sh status
./monitor_instances.sh check
./monitor_instances.sh install
sudo systemctl start algofactory-monitor
sudo systemctl status algofactory-monitor
curl -I https://8011.algofactory.in
curl -I https://8012.algofactory.in
chmod +x dashboard.sh
./dashboard.sh
./multi_instance.sh list
tail -50 /home/<USER>/algofactory-multi/algofactory-8010/logs/app.log | grep -i websocket
tail -50 /home/<USER>/algofactory-multi/algofactory-8010/logs/app.log
netstat -tlnp | grep 20010
ss -tlnp | grep 20010
find /home/<USER>/algofactory-multi/algofactory-8010 -name "*.log" -type f
head -50 /home/<USER>/algofactory-multi/algofactory-8010/logs/app.log
curl -I --http1.1 --upgrade WebSocket --connection Upgrade --header "Sec-WebSocket-Key: test" --header "Sec-WebSocket-Version: 13" http://localhost:20010
ss -tlnp | grep -E "(20010|20011|20012)"
ss -tlnp | grep -E "(23010|23011|23012)"
grep -E "(WEBSOCKET_PORT|ZMQ_PORT)" /home/<USER>/algofactory-multi/algofactory-*/. env
grep -E "(WEBSOCKET_PORT|ZMQ_PORT)" /home/<USER>/algofactory-multi/algofactory-*/.env
chmod +x test_websocket.py
python3 test_websocket.py 8010
source /home/<USER>/shared-venv/bin/activate && python3 test_websocket.py 8010
chmod +x simple_websocket_test.py && python3 simple_websocket_test.py
ss -tlnp | grep -E "(8765|:20[^0-9])"
ss -tlnp | grep -E "(8765|:20$)"
ss -tlnp | grep LISTEN
grep -r "8765" /home/<USER>/algofactory-multi/algofactory-8010/ --include="*.py" --include="*.js" --include="*.env"
ss -tlnp | grep ":20 "
chmod +x fix_websocket_ports.sh
./fix_websocket_ports.sh status
./fix_websocket_ports.sh fix
./fix_websocket_ports.sh test
python3 simple_websocket_test.py
chmod +x websocket_summary.sh && ./websocket_summary.sh
sed -i 's/ws:\/\/127.0.0.1:8765/ws:\/\/127.0.0.1:20011/g' /home/<USER>/algofactory-multi/algofactory-8011/test/iteration_test.py
sed -i 's/ws:\/\/127.0.0.1:8765/ws:\/\/127.0.0.1:20012/g' /home/<USER>/algofactory-multi/algofactory-8012/test/iteration_test.py
echo 'Terminal capability test'
echo 'Terminal capability test'
echo 'Terminal capability test'
git clone https://github.com/marketcalls/openalgo.git
uv run app.py
sudo snap install astral-uv
sudo snap install astral-uv --classic
cd ./openalgo/
uv run app.py
cp .sample.env .env
uv run app.py
bash
./dashboard.sh 
./monitormonitor_instances.sh
./monitor_instances.sh
uv run app.py
sudo snap install astral-uv
sudo apt update
sudo apt install git -y
cd/
bash
cd/
cd /
cd myproject
cd ./myproject
cd /myproject
bash
cd ./myproject
# 1. Create a shared venv manually (only once)
python3 -m venv ~/shared-venv
source ~/shared-venv/bin/activate
# 2. Use uv globally inside this shared environment
uv pip install -r requirements.txt  # or any package you want
echo 'Terminal capability test'
