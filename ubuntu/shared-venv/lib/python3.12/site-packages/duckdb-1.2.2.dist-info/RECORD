adbc_driver_duckdb/__init__.py,sha256=fcUnSQJtm70xAhjl7h-Eb5pwe0yDWVgM8sOyxu0jY28,1627
adbc_driver_duckdb/dbapi.py,sha256=PGFYF85WK-ALL97DkxYbgfX0nUl730gQOmMibBcMy5k,3442
duckdb-1.2.2.dist-info/INSTALLER,sha256=5hhM4Q4mYTT9z6QB6PGpUAW81PGNFrYrdXMj4oM_6ak,2
duckdb-1.2.2.dist-info/METADATA,sha256=CLsFQKYdDPcsLk3YZHU8ROqq4LeKVX9-W3ipv4SyMkU,966
duckdb-1.2.2.dist-info/RECORD,,
duckdb-1.2.2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
duckdb-1.2.2.dist-info/WHEEL,sha256=1_iH4_8X7Kuw94CBAcFinOYqUQRz3Xf0LCQcca2i-Jw,151
duckdb-1.2.2.dist-info/top_level.txt,sha256=PJ5o847brKVXDprOLYKxfLtb6iH7NVZtotg7NxIkG-4,39
duckdb-stubs/__init__.pyi,sha256=C-MLYgoiaSDWstOyeoTW9oa_GfJROJGk0u6gYykFTpI,47145
duckdb-stubs/functional/__init__.pyi,sha256=4eAm-wfyVBAdBNOY_0SeMdLxu_Nh-alLsRgoRNy5U9g,809
duckdb-stubs/typing/__init__.pyi,sha256=3A1tJSjc7CRukUGdgkJKPWG7J94ap2TuFGl7rRMJfpY,948
duckdb-stubs/value/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
duckdb-stubs/value/constant/__init__.pyi,sha256=6wkUpixPnj8HmTGXveUDEjDkJEFugRr2AihGF6_Rj1E,3310
duckdb/__init__.py,sha256=yELt9G2txDWuJo8kJq-abl9xweebrILa2PfrQPqPP8s,7255
duckdb/bytes_io_wrapper.py,sha256=6tEb7dzuokqSEuQrtBGc546YyOncitfi6gUOO3OLIus,2984
duckdb/duckdb.cpython-312-x86_64-linux-gnu.so,sha256=CNP8pjVWkld1ighZt-MsZoQWBnPUS1LLLKVbKAEUL80,61943888
duckdb/experimental/__init__.py,sha256=dsaou_Axm4W4uMylH4NVC6KtR1JHjR0Zdni0lilAih8,44
duckdb/experimental/spark/LICENSE,sha256=_1qknFLpgwYnWC6YVPp2gn0Zw7BAuFYWLRctcqnpUGc,13387
duckdb/experimental/spark/__init__.py,sha256=z8_tXnY5VCEUu6fjNVH8ZllFq_Y4iPAcrWHmPg1v1So,284
duckdb/experimental/spark/_globals.py,sha256=EGZPzdav7a5g51-U2SXqHCA11aJU35kwjUbQNqSzttk,2434
duckdb/experimental/spark/_typing.py,sha256=EZufFA9FUhowxCjdEWt_KM5vb3SKwoM917K9_3YG4mc,1525
duckdb/experimental/spark/conf.py,sha256=Z9WueVizlE-8DSRp9q5wiJbNvcCBIQJhHW9XyUpwioY,1398
duckdb/experimental/spark/context.py,sha256=QxyKtpi01ZV6wTqRM-k-gMVbzoJDG1pj66eP-TK3XhA,6209
duckdb/experimental/spark/errors/__init__.py,sha256=RDhcD08gQIzO1JVFZUu-wRj1td2NOvro_Qsyy3MIexs,2146
duckdb/experimental/spark/errors/error_classes.py,sha256=ZIhQcpvC2wH1FzM7fa7UQBUIeQ1bRT-VArbC6TT0kx0,27249
duckdb/experimental/spark/errors/exceptions/__init__.py,sha256=c0bKYjIaffoqkm7ZgaR53O64pH33BYzSzFxNFPweSjU,784
duckdb/experimental/spark/errors/exceptions/base.py,sha256=k0WMP7EoIhtYqbYSufyYLYnKY2uzCB8WMg74-JZjnqI,5360
duckdb/experimental/spark/errors/utils.py,sha256=gHmg1yrN6rATUoHGuzsoDeAl-LULRnML6iR_dlMwUTk,4438
duckdb/experimental/spark/exception.py,sha256=XLo4lz4wra6MJsbZ0MWnU0HWJx2UfUHUhN_q5dzFVyM,535
duckdb/experimental/spark/sql/__init__.py,sha256=R83MKgikp6bwNv9MV_MV14gQFwOJA5f_BhaTWNu0B4w,256
duckdb/experimental/spark/sql/_typing.py,sha256=5CwczuoxRLcIwnfGT4OWC19n6ctsC8JFKNjY4yQ-pFE,2361
duckdb/experimental/spark/sql/catalog.py,sha256=K8W85ccXlN8LzRZAkh5zpFvErpNiDySgAOL0rAfEAEw,2284
duckdb/experimental/spark/sql/column.py,sha256=J0RJrR74WN-8WWXMWHtCkqztcrYQ8WuSfwOc4zJlUlU,10708
duckdb/experimental/spark/sql/conf.py,sha256=8yIcAD8S4T1f5L1MnjMf86LXxBABu6lbJJJrd_l15Y8,656
duckdb/experimental/spark/sql/dataframe.py,sha256=STL0DZWH3QfRAWkITKvqj1s88FM0_LSIqodXyEdFJVk,45610
duckdb/experimental/spark/sql/functions.py,sha256=YQp47teLMJO0enWz5Z9szeGSDuw4_5wacLa_jl6IrrA,168284
duckdb/experimental/spark/sql/group.py,sha256=d2rojDeTlB1aRZU0TFFSdGwO6biXO8o-gLLiKmFFnNA,13275
duckdb/experimental/spark/sql/readwriter.py,sha256=q6Nldqeg5FEzqcc0ydzlJPfOkpq8Wrx9-527mWPP9Ts,17301
duckdb/experimental/spark/sql/session.py,sha256=TWrUyepTAIFWMavYwguPiNHVGpc3RHQXK63MQfhQS1A,8998
duckdb/experimental/spark/sql/streaming.py,sha256=XsXZjtpbeuGHQGXogzvApEUQxe4o6-YHvy6z4AcdT5o,1023
duckdb/experimental/spark/sql/type_utils.py,sha256=YoUxq5KhwfHb4CCIKaEAIbM3R3Ol5_XErjbRJ30eICA,2954
duckdb/experimental/spark/sql/types.py,sha256=ArGYPPFDIV8seol7EdxiJPZttV5Ld8dzN3TEmCUDNaI,39159
duckdb/experimental/spark/sql/udf.py,sha256=wul2wqnbV4d9zFmDw4G6ZEiRddUkPHat3HY1xL5u5dQ,1081
duckdb/filesystem.py,sha256=9lhkbYbJ8pa__FeZaXe6xDyHRJP-z4t9W7rMe8Adz34,996
duckdb/functional/__init__.py,sha256=MnFUHJ-3Xxq5aAjgykw_u2qJa_98iCZ2dlWqA7ffDjk,218
duckdb/query_graph/__main__.py,sha256=82OHL1N6aB9TlbmKJny8w_EdxJnijq_pEto1NOT0hCQ,11259
duckdb/typing/__init__.py,sha256=FfoaCgiokB9H40IZ-Y6r_sg7oTrvNsGjFBJw-6hkHV4,860
duckdb/udf.py,sha256=c4eYs6p9R5UMyQjcHNoLTkshSoKutkVF4xwMlq0fljg,674
duckdb/value/constant.py,sha256=XMKbHy9xVtsA59G5053m_UTNAGzthAVxJbA8ghKqmJw,5516
