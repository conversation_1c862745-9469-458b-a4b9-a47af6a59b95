/* PrismJS theme with DaisyUI theme support */
code[class*=language-],
pre[class*=language-] {
    color: hsl(var(--bc));
    background: 0 0;
    font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
    font-size: 1em;
    text-align: left;
    white-space: pre;
    word-spacing: normal;
    word-break: normal;
    word-wrap: normal;
    line-height: 1.5;
    -moz-tab-size: 4;
    -o-tab-size: 4;
    tab-size: 4;
    -webkit-hyphens: none;
    -moz-hyphens: none;
    -ms-hyphens: none;
    hyphens: none;
}

pre[class*=language-] {
    padding: 1em;
    margin: .5em 0;
    overflow: auto;
    border-radius: var(--rounded-box, 1rem);
    background: hsl(var(--b2));
}

:not(pre) > code[class*=language-] {
    padding: .1em;
    border-radius: .3em;
    white-space: normal;
}

/* Light theme */
[data-theme="light"] {
    --prism-comment: #6e7781;
    --prism-string: #0a3069;
    --prism-literal: #0550ae;
    --prism-keyword: #cf222e;
    --prism-function: #6639ba;
    --prism-deleted: #ff0000;
    --prism-class: #6639ba;
    --prism-builtin: #0550ae;
    --prism-property: #8250df;
    --prism-namespace: #6639ba;
    --prism-punctuation: #24292f;
    --prism-decorator: #6639ba;
    --prism-number: #0550ae;
    --prism-boolean: #0550ae;
    --prism-variable: #953800;
    --prism-regex: #116329;
    --prism-json-property: #24292f;
}

/* Dark theme */
[data-theme="dark"] {
    --prism-comment: #8292a2;
    --prism-string: #a6e22e;
    --prism-literal: #ae81ff;
    --prism-keyword: #66d9ef;
    --prism-function: #e6db74;
    --prism-deleted: #f92672;
    --prism-class: #e6db74;
    --prism-builtin: #a6e22e;
    --prism-property: #f92672;
    --prism-namespace: #f8f8f2;
    --prism-punctuation: #f8f8f2;
    --prism-decorator: #75af00;
    --prism-number: #ae81ff;
    --prism-boolean: #ae81ff;
    --prism-variable: #f8f8f2;
    --prism-regex: #fd971f;
    --prism-json-property: #f92672;
}

/* Garden theme */
[data-theme="garden"] {
    --prism-comment: #5c7b5c;
    --prism-string: #2c5a2c;
    --prism-literal: #1a4d1a;
    --prism-keyword: #326932;
    --prism-function: #1f571f;
    --prism-deleted: #c23934;
    --prism-class: #1f571f;
    --prism-builtin: #2c5a2c;
    --prism-property: #326932;
    --prism-namespace: #1a4d1a;
    --prism-punctuation: #14401a;
    --prism-decorator: #1f571f;
    --prism-number: #1a4d1a;
    --prism-boolean: #1a4d1a;
    --prism-variable: #2c5a2c;
    --prism-regex: #1f571f;
    --prism-json-property: #326932;
}

/* Token colors */
.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
    color: var(--prism-comment);
}

.token.punctuation {
    color: var(--prism-punctuation);
}

.token.namespace {
    color: var(--prism-namespace);
}

.token.property,
.token.tag,
.token.constant,
.token.symbol,
.token.deleted {
    color: var(--prism-property);
}

.token.boolean,
.token.number {
    color: var(--prism-number);
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin,
.token.inserted {
    color: var(--prism-string);
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string,
.token.variable {
    color: var(--prism-variable);
}

.token.atrule,
.token.attr-value,
.token.function,
.token.class-name {
    color: var(--prism-function);
}

.token.keyword {
    color: var(--prism-keyword);
}

.token.regex,
.token.important {
    color: var(--prism-regex);
}

.token.important,
.token.bold {
    font-weight: bold;
}

.token.italic {
    font-style: italic;
}

.token.entity {
    cursor: help;
}

/* JSON specific */
.language-json .token.property {
    color: var(--prism-json-property);
}

.language-json .token.string {
    color: var(--prism-string);
}

.language-json .token.number {
    color: var(--prism-number);
}

.language-json .token.boolean {
    color: var(--prism-boolean);
}

.language-json .token.null {
    color: var(--prism-literal);
}
