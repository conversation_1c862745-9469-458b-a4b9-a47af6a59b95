#!/usr/bin/env python3

"""
AlgoFactory Advanced Admin Dashboard
Scalable server management system with modular architecture
"""

from flask import Flask, render_template, request, jsonify, session, redirect, url_for, flash
from flask_socketio import Socket<PERSON>, emit, join_room, leave_room
from werkzeug.security import generate_password_hash, check_password_hash
import os
import sys
import json
import sqlite3
import psutil
import subprocess
from datetime import datetime, timedelta
from pathlib import Path
import logging
import asyncio
from threading import Thread
import time

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'algofactory-admin-secret-key-change-in-production'
app.config['TEMPLATES_AUTO_RELOAD'] = True

# Initialize SocketIO
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')

# Database setup
BASE_DIR = Path(__file__).parent
DB_PATH = BASE_DIR / "admin.db"

class DatabaseManager:
    """Centralized database management"""
    
    def __init__(self, db_path):
        self.db_path = db_path
        self.init_database()
    
    def get_connection(self):
        conn = sqlite3.connect(str(self.db_path))
        conn.row_factory = sqlite3.Row
        return conn
    
    def init_database(self):
        """Initialize all database tables"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Admin users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS admin_users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                email TEXT,
                role TEXT DEFAULT 'admin',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_login DATETIME
            )
        ''')
        
        # System metrics table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                cpu_percent REAL,
                memory_percent REAL,
                memory_used_gb REAL,
                memory_total_gb REAL,
                disk_percent REAL,
                disk_used_gb REAL,
                disk_total_gb REAL,
                load_average REAL,
                network_bytes_sent INTEGER,
                network_bytes_recv INTEGER,
                active_connections INTEGER,
                process_count INTEGER
            )
        ''')
        
        # AlgoFactory instances table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS algofactory_instances (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                instance_id TEXT UNIQUE NOT NULL,
                port INTEGER NOT NULL,
                websocket_port INTEGER,
                zmq_port INTEGER,
                status TEXT DEFAULT 'stopped',
                pid INTEGER,
                memory_mb REAL,
                cpu_percent REAL,
                uptime_seconds INTEGER DEFAULT 0,
                last_check DATETIME DEFAULT CURRENT_TIMESTAMP,
                auto_restart BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Nginx sites table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS nginx_sites (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                domain TEXT UNIQUE NOT NULL,
                port INTEGER NOT NULL,
                ssl_enabled BOOLEAN DEFAULT 0,
                ssl_cert_path TEXT,
                ssl_expiry_date DATETIME,
                status TEXT DEFAULT 'active',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_check DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # System events/logs table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                event_type TEXT NOT NULL,
                category TEXT DEFAULT 'system',
                message TEXT NOT NULL,
                severity TEXT DEFAULT 'info',
                source TEXT,
                details TEXT,
                user_id INTEGER,
                FOREIGN KEY (user_id) REFERENCES admin_users (id)
            )
        ''')
        
        # Tasks/jobs table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS admin_tasks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_name TEXT NOT NULL,
                task_type TEXT NOT NULL,
                status TEXT DEFAULT 'pending',
                progress INTEGER DEFAULT 0,
                result TEXT,
                error_message TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                started_at DATETIME,
                completed_at DATETIME,
                created_by INTEGER,
                FOREIGN KEY (created_by) REFERENCES admin_users (id)
            )
        ''')
        
        # Settings table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS admin_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_key TEXT UNIQUE NOT NULL,
                setting_value TEXT,
                setting_type TEXT DEFAULT 'string',
                description TEXT,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_by INTEGER,
                FOREIGN KEY (updated_by) REFERENCES admin_users (id)
            )
        ''')
        
        conn.commit()
        
        # Create default admin user if none exists
        cursor.execute('SELECT COUNT(*) FROM admin_users')
        if cursor.fetchone()[0] == 0:
            default_password = generate_password_hash('admin123')
            cursor.execute('''
                INSERT INTO admin_users (username, password_hash, email, role)
                VALUES (?, ?, ?, ?)
            ''', ('admin', default_password, '<EMAIL>', 'superadmin'))
            conn.commit()
            logger.info("Created default admin user: admin/admin123")
        
        # Insert default settings
        default_settings = [
            ('monitoring_interval', '5', 'integer', 'System monitoring interval in seconds'),
            ('max_log_entries', '1000', 'integer', 'Maximum log entries to keep'),
            ('auto_cleanup_days', '30', 'integer', 'Auto cleanup logs older than X days'),
            ('alert_email', '<EMAIL>', 'string', 'Email for system alerts'),
            ('maintenance_mode', 'false', 'boolean', 'Enable maintenance mode'),
        ]
        
        for key, value, type_, desc in default_settings:
            cursor.execute('''
                INSERT OR IGNORE INTO admin_settings (setting_key, setting_value, setting_type, description)
                VALUES (?, ?, ?, ?)
            ''', (key, value, type_, desc))
        
        conn.commit()
        conn.close()

# Initialize database
db_manager = DatabaseManager(DB_PATH)

class SystemMonitor:
    """Real-time system monitoring"""
    
    def __init__(self, socketio_instance):
        self.socketio = socketio_instance
        self.monitoring = False
        self.monitor_thread = None
    
    def start_monitoring(self):
        """Start the monitoring thread"""
        if not self.monitoring:
            self.monitoring = True
            self.monitor_thread = Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            logger.info("System monitoring started")
    
    def stop_monitoring(self):
        """Stop the monitoring thread"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("System monitoring stopped")
    
    def _monitor_loop(self):
        """Main monitoring loop"""
        while self.monitoring:
            try:
                # Collect system metrics
                metrics = self.collect_system_metrics()
                
                # Store in database
                self.store_metrics(metrics)
                
                # Emit to connected clients
                self.socketio.emit('system_update', {
                    'type': 'metrics',
                    'data': metrics,
                    'timestamp': datetime.now().isoformat()
                }, namespace='/admin')
                
                # Check for alerts
                self.check_alerts(metrics)
                
                # Sleep for monitoring interval
                time.sleep(5)  # 5 seconds default
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(10)  # Wait longer on error
    
    def collect_system_metrics(self):
        """Collect comprehensive system metrics"""
        try:
            # CPU and Memory
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            load_avg = os.getloadavg()[0] if hasattr(os, 'getloadavg') else 0
            
            # Network
            network = psutil.net_io_counters()
            
            # Process information
            process_count = len(psutil.pids())
            
            # AlgoFactory instances
            instances = self.get_algofactory_instances()
            
            # Nginx status
            nginx_status = self.get_nginx_status()
            
            return {
                'system': {
                    'timestamp': datetime.now().isoformat(),
                    'cpu': {
                        'percent': round(cpu_percent, 1),
                        'count': psutil.cpu_count(),
                        'load_average': round(load_avg, 2)
                    },
                    'memory': {
                        'percent': round(memory.percent, 1),
                        'used_gb': round(memory.used / (1024**3), 2),
                        'total_gb': round(memory.total / (1024**3), 2),
                        'available_gb': round(memory.available / (1024**3), 2)
                    },
                    'disk': {
                        'percent': round((disk.used / disk.total) * 100, 1),
                        'used_gb': round(disk.used / (1024**3), 2),
                        'total_gb': round(disk.total / (1024**3), 2),
                        'free_gb': round(disk.free / (1024**3), 2)
                    },
                    'network': {
                        'bytes_sent': network.bytes_sent,
                        'bytes_recv': network.bytes_recv
                    },
                    'processes': process_count
                },
                'instances': instances,
                'nginx': nginx_status
            }
        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")
            return {}
    
    def get_algofactory_instances(self):
        """Get status of AlgoFactory instances"""
        instances = []
        try:
            # Check running processes
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'memory_info', 'cpu_percent', 'create_time']):
                try:
                    if 'python' in proc.info['name'] and proc.info['cmdline']:
                        cmdline = ' '.join(proc.info['cmdline'])
                        if 'app.py' in cmdline and any(port in cmdline for port in ['8010', '8011', '8012']):
                            # Extract port from command line
                            port = None
                            for arg in proc.info['cmdline']:
                                if arg.isdigit() and 8000 <= int(arg) <= 9999:
                                    port = int(arg)
                                    break
                            
                            if port:
                                uptime = datetime.now() - datetime.fromtimestamp(proc.create_time())
                                instances.append({
                                    'instance_id': str(port),
                                    'port': port,
                                    'websocket_port': port + 12000,
                                    'zmq_port': port + 15000,
                                    'pid': proc.info['pid'],
                                    'status': 'running',
                                    'uptime_seconds': int(uptime.total_seconds()),
                                    'memory_mb': round(proc.memory_info().rss / (1024**2), 1),
                                    'cpu_percent': round(proc.cpu_percent(), 1)
                                })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception as e:
            logger.error(f"Error getting AlgoFactory instances: {e}")
        
        return instances
    
    def get_nginx_status(self):
        """Get Nginx status and sites"""
        try:
            # Check if Nginx is running
            nginx_running = False
            nginx_pid = None
            
            for proc in psutil.process_iter(['pid', 'name']):
                if proc.info['name'] == 'nginx':
                    nginx_running = True
                    nginx_pid = proc.info['pid']
                    break
            
            # Get configured sites
            sites = []
            sites_enabled_dir = Path('/etc/nginx/sites-enabled')
            if sites_enabled_dir.exists():
                for site_file in sites_enabled_dir.glob('*.algofactory.in.conf'):
                    domain = site_file.stem
                    ssl_cert_path = Path(f'/etc/letsencrypt/live/{domain}')
                    
                    sites.append({
                        'domain': domain,
                        'ssl_enabled': ssl_cert_path.exists(),
                        'config_file': str(site_file)
                    })
            
            return {
                'running': nginx_running,
                'pid': nginx_pid,
                'sites_count': len(sites),
                'sites': sites
            }
        except Exception as e:
            logger.error(f"Error getting Nginx status: {e}")
            return {'running': False, 'error': str(e)}
    
    def store_metrics(self, metrics):
        """Store metrics in database"""
        try:
            if not metrics or 'system' not in metrics:
                return
            
            system = metrics['system']
            conn = db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO system_metrics 
                (cpu_percent, memory_percent, memory_used_gb, memory_total_gb,
                 disk_percent, disk_used_gb, disk_total_gb, load_average,
                 network_bytes_sent, network_bytes_recv, process_count)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                system.get('cpu', {}).get('percent', 0),
                system.get('memory', {}).get('percent', 0),
                system.get('memory', {}).get('used_gb', 0),
                system.get('memory', {}).get('total_gb', 0),
                system.get('disk', {}).get('percent', 0),
                system.get('disk', {}).get('used_gb', 0),
                system.get('disk', {}).get('total_gb', 0),
                system.get('cpu', {}).get('load_average', 0),
                system.get('network', {}).get('bytes_sent', 0),
                system.get('network', {}).get('bytes_recv', 0),
                system.get('processes', 0)
            ))
            
            conn.commit()
            conn.close()
        except Exception as e:
            logger.error(f"Error storing metrics: {e}")
    
    def check_alerts(self, metrics):
        """Check for system alerts"""
        try:
            if not metrics or 'system' not in metrics:
                return
            
            system = metrics['system']
            alerts = []
            
            # CPU alert
            cpu_percent = system.get('cpu', {}).get('percent', 0)
            if cpu_percent > 90:
                alerts.append({
                    'type': 'cpu_high',
                    'severity': 'critical',
                    'message': f'High CPU usage: {cpu_percent}%'
                })
            
            # Memory alert
            memory_percent = system.get('memory', {}).get('percent', 0)
            if memory_percent > 85:
                alerts.append({
                    'type': 'memory_high',
                    'severity': 'warning' if memory_percent < 95 else 'critical',
                    'message': f'High memory usage: {memory_percent}%'
                })
            
            # Disk alert
            disk_percent = system.get('disk', {}).get('percent', 0)
            if disk_percent > 85:
                alerts.append({
                    'type': 'disk_high',
                    'severity': 'warning' if disk_percent < 95 else 'critical',
                    'message': f'High disk usage: {disk_percent}%'
                })
            
            # Emit alerts
            for alert in alerts:
                self.socketio.emit('system_alert', alert, namespace='/admin')
                
        except Exception as e:
            logger.error(f"Error checking alerts: {e}")

# Initialize system monitor
system_monitor = SystemMonitor(socketio)

# Authentication decorator
def login_required(f):
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

# Routes
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM admin_users WHERE username = ?', (username,))
        user = cursor.fetchone()
        
        if user and check_password_hash(user['password_hash'], password):
            session['user_id'] = user['id']
            session['username'] = user['username']
            session['role'] = user['role']
            
            # Update last login
            cursor.execute('UPDATE admin_users SET last_login = ? WHERE id = ?', 
                         (datetime.now(), user['id']))
            conn.commit()
            conn.close()
            
            flash('Login successful!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('Invalid username or password', 'error')
            conn.close()
    
    return render_template('auth/login.html')

@app.route('/logout')
def logout():
    session.clear()
    flash('Logged out successfully', 'success')
    return redirect(url_for('login'))

@app.route('/')
@login_required
def dashboard():
    return render_template('dashboard/index.html')

@app.route('/system')
@login_required
def system_monitor():
    return render_template('system/monitor.html')

@app.route('/instances')
@login_required
def instances():
    return render_template('instances/manage.html')

@app.route('/nginx')
@login_required
def nginx_management():
    return render_template('nginx/manage.html')

@app.route('/ssl')
@login_required
def ssl_certificates():
    return render_template('ssl/manage.html')

@app.route('/logs')
@login_required
def system_logs():
    return render_template('logs/view.html')

@app.route('/metrics')
@login_required
def performance_metrics():
    return render_template('metrics/view.html')

@app.route('/alerts')
@login_required
def alerts():
    return render_template('alerts/manage.html')

@app.route('/backup')
@login_required
def backup():
    return render_template('backup/manage.html')

@app.route('/tasks')
@login_required
def tasks():
    return render_template('tasks/manage.html')

@app.route('/terminal')
@login_required
def web_terminal():
    return render_template('terminal/index.html')

@app.route('/settings')
@login_required
def settings():
    return render_template('settings/index.html')

@app.route('/users')
@login_required
def users():
    return render_template('users/manage.html')

# API Routes
@app.route('/api/system/metrics')
@login_required
def api_system_metrics():
    metrics = system_monitor.collect_system_metrics()
    return jsonify(metrics)

@app.route('/api/system/metrics/history')
@login_required
def api_metrics_history():
    hours = request.args.get('hours', 24, type=int)

    conn = db_manager.get_connection()
    cursor = conn.cursor()

    since = datetime.now() - timedelta(hours=hours)
    cursor.execute('''
        SELECT * FROM system_metrics
        WHERE timestamp > ?
        ORDER BY timestamp DESC
        LIMIT 1000
    ''', (since,))

    rows = cursor.fetchall()
    conn.close()

    return jsonify({
        'metrics': [dict(row) for row in rows]
    })

@app.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    if request.method == 'POST':
        current_password = request.form.get('current_password')
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')

        if not all([current_password, new_password, confirm_password]):
            flash('All fields are required', 'error')
            return redirect(url_for('change_password'))

        if new_password != confirm_password:
            flash('New passwords do not match', 'error')
            return redirect(url_for('change_password'))

        if len(new_password) < 6:
            flash('Password must be at least 6 characters long', 'error')
            return redirect(url_for('change_password'))

        # Verify current password
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT password_hash FROM admin_users WHERE id = ?', (session['user_id'],))
        user = cursor.fetchone()

        if not user or not check_password_hash(user['password_hash'], current_password):
            flash('Current password is incorrect', 'error')
            conn.close()
            return redirect(url_for('change_password'))

        # Update password
        new_password_hash = generate_password_hash(new_password)
        cursor.execute('UPDATE admin_users SET password_hash = ? WHERE id = ?',
                      (new_password_hash, session['user_id']))
        conn.commit()
        conn.close()

        flash('Password changed successfully!', 'success')
        return redirect(url_for('dashboard'))

    return render_template('auth/change_password.html')

@app.route('/api/instances/manage', methods=['POST'])
@login_required
def manage_instances():
    action = request.json.get('action')
    instance_id = request.json.get('instance_id')

    if action == 'stop' and instance_id:
        try:
            # Find and stop the instance
            for proc in psutil.process_iter(['pid', 'cmdline']):
                if 'python' in proc.info['cmdline'][0] and str(instance_id) in ' '.join(proc.info['cmdline']):
                    proc.terminate()
                    return jsonify({'success': True, 'message': f'Instance {instance_id} stopped'})
            return jsonify({'success': False, 'message': 'Instance not found'})
        except Exception as e:
            return jsonify({'success': False, 'message': str(e)})

    elif action == 'restart' and instance_id:
        try:
            # This would need to be implemented based on your instance management
            return jsonify({'success': True, 'message': f'Instance {instance_id} restart initiated'})
        except Exception as e:
            return jsonify({'success': False, 'message': str(e)})

    return jsonify({'success': False, 'message': 'Invalid action'})

@app.route('/api/system/cleanup', methods=['POST'])
@login_required
def system_cleanup():
    try:
        # Run the simple cleanup script
        result = subprocess.run(['/home/<USER>/myproject/algofactory/simple_cleanup.sh'],
                              capture_output=True, text=True)

        if result.returncode == 0:
            return jsonify({'success': True, 'message': 'System cleanup completed', 'output': result.stdout})
        else:
            return jsonify({'success': False, 'message': 'Cleanup failed', 'error': result.stderr})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# SocketIO Events
@socketio.on('connect', namespace='/admin')
def handle_connect():
    if 'user_id' not in session:
        return False
    
    join_room('admin_room')
    emit('connected', {'message': 'Connected to admin dashboard'})
    logger.info(f"Admin user {session.get('username')} connected")

@socketio.on('disconnect', namespace='/admin')
def handle_disconnect():
    leave_room('admin_room')
    logger.info(f"Admin user {session.get('username')} disconnected")

if __name__ == '__main__':
    # Start system monitoring
    system_monitor.start_monitoring()
    
    # Run the app with production settings
    try:
        # Use threading mode for better compatibility
        socketio.run(app,
                    host='0.0.0.0',
                    port=9001,
                    debug=False,
                    use_reloader=False,
                    log_output=False,
                    allow_unsafe_werkzeug=True)
    except Exception as e:
        logger.error(f"Failed to start admin dashboard: {e}")
        import traceback
        traceback.print_exc()
    finally:
        system_monitor.stop_monitoring()
