{% extends "base.html" %}

{% block title %}Performance Metrics - AlgoFactory Admin{% endblock %}
{% block page_title %}Performance Metrics{% endblock %}

{% block content %}
<!-- Metrics Overview -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="stat bg-base-200 rounded-xl shadow-sm">
        <div class="stat-figure text-primary">
            <i class="fas fa-tachometer-alt text-3xl"></i>
        </div>
        <div class="stat-title">Avg Response Time</div>
        <div class="stat-value text-primary">245ms</div>
        <div class="stat-desc">Last 24 hours</div>
    </div>
    
    <div class="stat bg-base-200 rounded-xl shadow-sm">
        <div class="stat-figure text-secondary">
            <i class="fas fa-users text-3xl"></i>
        </div>
        <div class="stat-title">Active Users</div>
        <div class="stat-value text-secondary">12</div>
        <div class="stat-desc">Currently online</div>
    </div>
    
    <div class="stat bg-base-200 rounded-xl shadow-sm">
        <div class="stat-figure text-accent">
            <i class="fas fa-exchange-alt text-3xl"></i>
        </div>
        <div class="stat-title">Requests/min</div>
        <div class="stat-value text-accent">1,234</div>
        <div class="stat-desc">Peak: 2,456</div>
    </div>
    
    <div class="stat bg-base-200 rounded-xl shadow-sm">
        <div class="stat-figure text-info">
            <i class="fas fa-check-circle text-3xl"></i>
        </div>
        <div class="stat-title">Uptime</div>
        <div class="stat-value text-info">99.9%</div>
        <div class="stat-desc">This month</div>
    </div>
</div>

<!-- Performance Charts -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <div class="card bg-base-200 shadow-sm">
        <div class="card-body">
            <h2 class="card-title">
                <i class="fas fa-chart-line text-primary"></i>
                Response Time Trends
            </h2>
            <div class="h-64">
                <canvas id="response-time-chart"></canvas>
            </div>
        </div>
    </div>
    
    <div class="card bg-base-200 shadow-sm">
        <div class="card-body">
            <h2 class="card-title">
                <i class="fas fa-chart-bar text-secondary"></i>
                Request Volume
            </h2>
            <div class="h-64">
                <canvas id="request-volume-chart"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Metrics -->
<div class="card bg-base-200 shadow-sm">
    <div class="card-body">
        <h2 class="card-title mb-6">
            <i class="fas fa-table text-primary"></i>
            Detailed Performance Metrics
        </h2>
        
        <div class="overflow-x-auto">
            <table class="table table-zebra w-full">
                <thead>
                    <tr>
                        <th>Metric</th>
                        <th>Current</th>
                        <th>Average</th>
                        <th>Peak</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>CPU Usage</td>
                        <td>15.2%</td>
                        <td>12.8%</td>
                        <td>89.5%</td>
                        <td><div class="badge badge-success">Good</div></td>
                    </tr>
                    <tr>
                        <td>Memory Usage</td>
                        <td>78.4%</td>
                        <td>75.2%</td>
                        <td>94.1%</td>
                        <td><div class="badge badge-warning">High</div></td>
                    </tr>
                    <tr>
                        <td>Disk I/O</td>
                        <td>2.1 MB/s</td>
                        <td>1.8 MB/s</td>
                        <td>15.6 MB/s</td>
                        <td><div class="badge badge-success">Good</div></td>
                    </tr>
                    <tr>
                        <td>Network I/O</td>
                        <td>5.4 MB/s</td>
                        <td>4.2 MB/s</td>
                        <td>25.8 MB/s</td>
                        <td><div class="badge badge-success">Good</div></td>
                    </tr>
                    <tr>
                        <td>Active Connections</td>
                        <td>156</td>
                        <td>142</td>
                        <td>892</td>
                        <td><div class="badge badge-success">Good</div></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        initializeCharts();
    });
    
    function initializeCharts() {
        // Response Time Chart
        const responseTimeCtx = document.getElementById('response-time-chart').getContext('2d');
        new Chart(responseTimeCtx, {
            type: 'line',
            data: {
                labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
                datasets: [{
                    label: 'Response Time (ms)',
                    data: [180, 220, 245, 280, 320, 290, 245],
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        
        // Request Volume Chart
        const requestVolumeCtx = document.getElementById('request-volume-chart').getContext('2d');
        new Chart(requestVolumeCtx, {
            type: 'bar',
            data: {
                labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                datasets: [{
                    label: 'Requests (thousands)',
                    data: [45, 52, 48, 61, 58, 42, 38],
                    backgroundColor: 'rgba(16, 185, 129, 0.8)',
                    borderColor: 'rgb(16, 185, 129)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
</script>
{% endblock %}
