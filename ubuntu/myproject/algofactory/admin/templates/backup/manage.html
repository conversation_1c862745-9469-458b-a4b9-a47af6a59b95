{% extends "base.html" %}

{% block title %}Backup & Restore - AlgoFactory Admin{% endblock %}
{% block page_title %}Backup & Restore{% endblock %}

{% block content %}
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
    <div class="stat bg-base-200 rounded-xl shadow-sm">
        <div class="stat-figure text-success">
            <i class="fas fa-database text-3xl"></i>
        </div>
        <div class="stat-title">Total Backups</div>
        <div class="stat-value text-success">15</div>
        <div class="stat-desc">Available backups</div>
    </div>
    
    <div class="stat bg-base-200 rounded-xl shadow-sm">
        <div class="stat-figure text-info">
            <i class="fas fa-clock text-3xl"></i>
        </div>
        <div class="stat-title">Last Backup</div>
        <div class="stat-value text-info">2h ago</div>
        <div class="stat-desc">Automatic backup</div>
    </div>
    
    <div class="stat bg-base-200 rounded-xl shadow-sm">
        <div class="stat-figure text-warning">
            <i class="fas fa-hdd text-3xl"></i>
        </div>
        <div class="stat-title">Backup Size</div>
        <div class="stat-value text-warning">2.4GB</div>
        <div class="stat-desc">Total storage used</div>
    </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <div class="card bg-base-200 shadow-sm">
        <div class="card-body">
            <h2 class="card-title mb-4">
                <i class="fas fa-plus text-success"></i>
                Create Backup
            </h2>
            <div class="space-y-4">
                <button class="btn btn-success w-full">
                    <i class="fas fa-database"></i>
                    Create Full Backup
                </button>
                <button class="btn btn-info w-full">
                    <i class="fas fa-file-archive"></i>
                    Create Configuration Backup
                </button>
                <button class="btn btn-warning w-full">
                    <i class="fas fa-download"></i>
                    Download Backup
                </button>
            </div>
        </div>
    </div>
    
    <div class="card bg-base-200 shadow-sm">
        <div class="card-body">
            <h2 class="card-title mb-4">
                <i class="fas fa-upload text-primary"></i>
                Restore from Backup
            </h2>
            <div class="space-y-4">
                <input type="file" class="file-input file-input-bordered w-full" />
                <button class="btn btn-primary w-full">
                    <i class="fas fa-upload"></i>
                    Upload & Restore
                </button>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>Restore will overwrite current data</span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
