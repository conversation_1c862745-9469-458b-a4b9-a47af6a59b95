{% extends "base.html" %}

{% block title %}SSL Certificates - AlgoFactory Admin{% endblock %}
{% block page_title %}SSL Certificate Management{% endblock %}

{% block content %}
<!-- SSL Overview -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="stat bg-base-200 rounded-xl shadow-sm">
        <div class="stat-figure text-success">
            <i class="fas fa-shield-alt text-3xl"></i>
        </div>
        <div class="stat-title">Active Certificates</div>
        <div class="stat-value text-success" id="active-certs">0</div>
        <div class="stat-desc">Valid SSL certificates</div>
    </div>
    
    <div class="stat bg-base-200 rounded-xl shadow-sm">
        <div class="stat-figure text-warning">
            <i class="fas fa-exclamation-triangle text-3xl"></i>
        </div>
        <div class="stat-title">Expiring Soon</div>
        <div class="stat-value text-warning" id="expiring-certs">0</div>
        <div class="stat-desc">Within 30 days</div>
    </div>
    
    <div class="stat bg-base-200 rounded-xl shadow-sm">
        <div class="stat-figure text-error">
            <i class="fas fa-times-circle text-3xl"></i>
        </div>
        <div class="stat-title">No SSL</div>
        <div class="stat-value text-error" id="no-ssl-sites">0</div>
        <div class="stat-desc">Sites without SSL</div>
    </div>
    
    <div class="stat bg-base-200 rounded-xl shadow-sm">
        <div class="stat-figure text-info">
            <i class="fas fa-sync-alt text-3xl"></i>
        </div>
        <div class="stat-title">Auto-Renewal</div>
        <div class="stat-value text-info">ON</div>
        <div class="stat-desc">Certbot enabled</div>
    </div>
</div>

<!-- Quick Actions -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
    <div class="card bg-base-200 shadow-sm">
        <div class="card-body">
            <h2 class="card-title">
                <i class="fas fa-plus text-success"></i>
                Install New Certificate
            </h2>
            <div class="space-y-3">
                <input type="text" placeholder="Domain (e.g., 8014.algofactory.in)" class="input input-bordered input-sm w-full" id="new-ssl-domain">
                <select class="select select-bordered select-sm w-full" id="ssl-method">
                    <option value="webroot">Webroot</option>
                    <option value="nginx">Nginx Plugin</option>
                    <option value="standalone">Standalone</option>
                </select>
                <button class="btn btn-success btn-sm w-full" onclick="installSSL()">
                    <i class="fas fa-lock"></i>
                    Install SSL
                </button>
            </div>
        </div>
    </div>
    
    <div class="card bg-base-200 shadow-sm">
        <div class="card-body">
            <h2 class="card-title">
                <i class="fas fa-sync text-primary"></i>
                Bulk Operations
            </h2>
            <div class="space-y-3">
                <button class="btn btn-primary btn-sm w-full" onclick="renewAllCertificates()">
                    <i class="fas fa-sync"></i>
                    Renew All
                </button>
                <button class="btn btn-info btn-sm w-full" onclick="bulkInstallSSL()">
                    <i class="fas fa-certificate"></i>
                    Bulk Install
                </button>
                <button class="btn btn-warning btn-sm w-full" onclick="checkAllExpiry()">
                    <i class="fas fa-calendar-check"></i>
                    Check Expiry
                </button>
            </div>
        </div>
    </div>
    
    <div class="card bg-base-200 shadow-sm">
        <div class="card-body">
            <h2 class="card-title">
                <i class="fas fa-cog text-accent"></i>
                Certbot Settings
            </h2>
            <div class="space-y-3">
                <button class="btn btn-accent btn-sm w-full" onclick="configureCertbot()">
                    <i class="fas fa-cog"></i>
                    Configure
                </button>
                <button class="btn btn-ghost btn-sm w-full" onclick="viewCertbotLogs()">
                    <i class="fas fa-file-alt"></i>
                    View Logs
                </button>
                <button class="btn btn-ghost btn-sm w-full" onclick="testCertbot()">
                    <i class="fas fa-vial"></i>
                    Test Setup
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Certificates Table -->
<div class="card bg-base-200 shadow-sm">
    <div class="card-body">
        <div class="flex justify-between items-center mb-6">
            <h2 class="card-title">
                <i class="fas fa-certificate text-primary"></i>
                SSL Certificates
            </h2>
            <div class="flex gap-2">
                <button class="btn btn-ghost btn-sm" onclick="refreshCertificates()">
                    <i class="fas fa-sync-alt"></i>
                    Refresh
                </button>
                <div class="dropdown dropdown-end">
                    <div tabindex="0" role="button" class="btn btn-ghost btn-sm">
                        <i class="fas fa-filter"></i>
                        Filter
                    </div>
                    <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                        <li><a onclick="filterCertificates('all')">All Certificates</a></li>
                        <li><a onclick="filterCertificates('valid')">Valid Only</a></li>
                        <li><a onclick="filterCertificates('expiring')">Expiring Soon</a></li>
                        <li><a onclick="filterCertificates('expired')">Expired</a></li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="table table-zebra w-full">
                <thead>
                    <tr>
                        <th>Domain</th>
                        <th>Status</th>
                        <th>Issued Date</th>
                        <th>Expiry Date</th>
                        <th>Days Left</th>
                        <th>Issuer</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="certificates-table">
                    <tr>
                        <td colspan="7" class="text-center text-base-content/50">
                            <i class="fas fa-spinner fa-spin mr-2"></i>
                            Loading certificates...
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Sites Without SSL -->
<div class="card bg-base-200 shadow-sm mt-8">
    <div class="card-body">
        <div class="flex justify-between items-center mb-6">
            <h2 class="card-title">
                <i class="fas fa-unlock text-warning"></i>
                Sites Without SSL
            </h2>
            <button class="btn btn-success btn-sm" onclick="installAllSSL()">
                <i class="fas fa-lock"></i>
                Install SSL for All
            </button>
        </div>
        
        <div class="overflow-x-auto">
            <table class="table table-zebra w-full">
                <thead>
                    <tr>
                        <th>Domain</th>
                        <th>Port</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="no-ssl-table">
                    <tr>
                        <td colspan="4" class="text-center text-base-content/50">
                            <i class="fas fa-spinner fa-spin mr-2"></i>
                            Loading sites...
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Certificate Details Modal -->
<dialog id="cert-details-modal" class="modal">
    <div class="modal-box w-11/12 max-w-4xl">
        <h3 class="font-bold text-lg mb-4">
            <i class="fas fa-certificate"></i>
            Certificate Details - <span id="cert-domain"></span>
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
                <div>
                    <label class="label">
                        <span class="label-text font-bold">Subject</span>
                    </label>
                    <div class="bg-base-300 p-3 rounded font-mono text-sm" id="cert-subject">Loading...</div>
                </div>
                <div>
                    <label class="label">
                        <span class="label-text font-bold">Issuer</span>
                    </label>
                    <div class="bg-base-300 p-3 rounded font-mono text-sm" id="cert-issuer">Loading...</div>
                </div>
                <div>
                    <label class="label">
                        <span class="label-text font-bold">Serial Number</span>
                    </label>
                    <div class="bg-base-300 p-3 rounded font-mono text-sm" id="cert-serial">Loading...</div>
                </div>
            </div>
            <div class="space-y-4">
                <div>
                    <label class="label">
                        <span class="label-text font-bold">Valid From</span>
                    </label>
                    <div class="bg-base-300 p-3 rounded font-mono text-sm" id="cert-valid-from">Loading...</div>
                </div>
                <div>
                    <label class="label">
                        <span class="label-text font-bold">Valid Until</span>
                    </label>
                    <div class="bg-base-300 p-3 rounded font-mono text-sm" id="cert-valid-until">Loading...</div>
                </div>
                <div>
                    <label class="label">
                        <span class="label-text font-bold">Fingerprint (SHA256)</span>
                    </label>
                    <div class="bg-base-300 p-3 rounded font-mono text-sm break-all" id="cert-fingerprint">Loading...</div>
                </div>
            </div>
        </div>
        <div class="modal-action">
            <button class="btn btn-primary" onclick="downloadCertificate()">
                <i class="fas fa-download"></i>
                Download
            </button>
            <button class="btn btn-ghost" onclick="closeCertDetailsModal()">Close</button>
        </div>
    </div>
</dialog>

<!-- Certbot Configuration Modal -->
<dialog id="certbot-config-modal" class="modal">
    <div class="modal-box">
        <h3 class="font-bold text-lg mb-4">
            <i class="fas fa-cog"></i>
            Certbot Configuration
        </h3>
        <div class="space-y-4">
            <div class="form-control">
                <label class="label">
                    <span class="label-text">Email Address</span>
                </label>
                <input type="email" placeholder="<EMAIL>" class="input input-bordered w-full" id="certbot-email">
            </div>
            <div class="form-control">
                <label class="label cursor-pointer">
                    <span class="label-text">Agree to Terms of Service</span>
                    <input type="checkbox" class="checkbox" id="certbot-agree-tos" checked>
                </label>
            </div>
            <div class="form-control">
                <label class="label cursor-pointer">
                    <span class="label-text">Enable automatic renewal</span>
                    <input type="checkbox" class="checkbox" id="certbot-auto-renew" checked>
                </label>
            </div>
            <div class="form-control">
                <label class="label">
                    <span class="label-text">Renewal Check Frequency</span>
                </label>
                <select class="select select-bordered w-full" id="certbot-frequency">
                    <option value="daily">Daily</option>
                    <option value="weekly" selected>Weekly</option>
                    <option value="monthly">Monthly</option>
                </select>
            </div>
        </div>
        <div class="modal-action">
            <button class="btn btn-primary" onclick="saveCertbotConfig()">
                <i class="fas fa-save"></i>
                Save Configuration
            </button>
            <button class="btn btn-ghost" onclick="closeCertbotConfigModal()">Cancel</button>
        </div>
    </div>
</dialog>
{% endblock %}

{% block scripts %}
<script>
    let certificatesData = [];
    let noSSLSites = [];
    
    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
        loadCertificates();
        loadNoSSLSites();
    });
    
    async function loadCertificates() {
        // Mock certificate data - in real implementation, this would come from certbot
        certificatesData = [
            {
                domain: 'admin.algofactory.in',
                status: 'valid',
                issued_date: '2025-03-24',
                expiry_date: '2025-06-24',
                days_left: 30,
                issuer: "Let's Encrypt",
                subject: 'CN=admin.algofactory.in',
                serial: '03:A1:B2:C3:D4:E5:F6:07:08:09',
                fingerprint: 'SHA256:1234567890ABCDEF...'
            },
            {
                domain: '8010.algofactory.in',
                status: 'valid',
                issued_date: '2025-03-20',
                expiry_date: '2025-06-20',
                days_left: 26,
                issuer: "Let's Encrypt",
                subject: 'CN=8010.algofactory.in',
                serial: '03:A1:B2:C3:D4:E5:F6:07:08:10',
                fingerprint: 'SHA256:ABCDEF1234567890...'
            },
            {
                domain: '8011.algofactory.in',
                status: 'expiring',
                issued_date: '2025-03-15',
                expiry_date: '2025-06-15',
                days_left: 21,
                issuer: "Let's Encrypt",
                subject: 'CN=8011.algofactory.in',
                serial: '03:A1:B2:C3:D4:E5:F6:07:08:11',
                fingerprint: 'SHA256:567890ABCDEF1234...'
            },
            {
                domain: '8012.algofactory.in',
                status: 'valid',
                issued_date: '2025-04-01',
                expiry_date: '2025-07-01',
                days_left: 37,
                issuer: "Let's Encrypt",
                subject: 'CN=8012.algofactory.in',
                serial: '03:A1:B2:C3:D4:E5:F6:07:08:12',
                fingerprint: 'SHA256:DEF1234567890ABC...'
            }
        ];
        
        updateCertificatesData(certificatesData);
    }
    
    function updateCertificatesData(certificates) {
        certificatesData = certificates;
        
        // Update overview stats
        const activeCerts = certificates.filter(cert => cert.status === 'valid').length;
        const expiringCerts = certificates.filter(cert => cert.days_left <= 30).length;
        
        document.getElementById('active-certs').textContent = activeCerts;
        document.getElementById('expiring-certs').textContent = expiringCerts;
        
        // Update table
        renderCertificatesTable(certificates);
    }
    
    function renderCertificatesTable(certificates) {
        const tbody = document.getElementById('certificates-table');
        
        if (certificates.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center text-base-content/50">
                        <i class="fas fa-info-circle mr-2"></i>
                        No SSL certificates found
                    </td>
                </tr>
            `;
            return;
        }
        
        tbody.innerHTML = certificates.map(cert => `
            <tr>
                <td>
                    <div class="font-bold">${cert.domain}</div>
                    <div class="text-sm text-base-content/70">
                        <a href="https://${cert.domain}" target="_blank" class="link">
                            <i class="fas fa-external-link-alt"></i>
                            Test SSL
                        </a>
                    </div>
                </td>
                <td>
                    <div class="badge ${getCertStatusBadge(cert.status, cert.days_left)}">
                        <i class="fas ${getCertStatusIcon(cert.status)} mr-1"></i>
                        ${getCertStatusText(cert.status, cert.days_left)}
                    </div>
                </td>
                <td>
                    <div class="text-sm">${formatDate(cert.issued_date)}</div>
                </td>
                <td>
                    <div class="text-sm">${formatDate(cert.expiry_date)}</div>
                </td>
                <td>
                    <div class="text-sm ${cert.days_left <= 30 ? 'text-warning font-bold' : ''}">${cert.days_left} days</div>
                </td>
                <td>
                    <div class="text-sm">${cert.issuer}</div>
                </td>
                <td>
                    <div class="flex gap-1">
                        <button class="btn btn-info btn-xs" onclick="viewCertificateDetails('${cert.domain}')" title="View Details">
                            <i class="fas fa-info-circle"></i>
                        </button>
                        <button class="btn btn-primary btn-xs" onclick="renewCertificate('${cert.domain}')" title="Renew">
                            <i class="fas fa-sync"></i>
                        </button>
                        <button class="btn btn-warning btn-xs" onclick="testCertificate('${cert.domain}')" title="Test">
                            <i class="fas fa-vial"></i>
                        </button>
                        <button class="btn btn-error btn-xs" onclick="revokeCertificate('${cert.domain}')" title="Revoke">
                            <i class="fas fa-ban"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    async function loadNoSSLSites() {
        // Mock data for sites without SSL
        noSSLSites = [
            { domain: '8013.algofactory.in', port: 8013, status: 'No SSL' },
            { domain: '8014.algofactory.in', port: 8014, status: 'No SSL' },
            { domain: '8015.algofactory.in', port: 8015, status: 'No SSL' }
        ];
        
        document.getElementById('no-ssl-sites').textContent = noSSLSites.length;
        renderNoSSLTable(noSSLSites);
    }
    
    function renderNoSSLTable(sites) {
        const tbody = document.getElementById('no-ssl-table');
        
        if (sites.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="4" class="text-center text-base-content/50">
                        <i class="fas fa-check-circle mr-2"></i>
                        All sites have SSL certificates!
                    </td>
                </tr>
            `;
            return;
        }
        
        tbody.innerHTML = sites.map(site => `
            <tr>
                <td>
                    <div class="font-bold">${site.domain}</div>
                    <div class="text-sm text-base-content/70">
                        <a href="http://${site.domain}" target="_blank" class="link">
                            <i class="fas fa-external-link-alt"></i>
                            Visit Site
                        </a>
                    </div>
                </td>
                <td>
                    <div class="badge badge-neutral">${site.port}</div>
                </td>
                <td>
                    <div class="badge badge-warning">
                        <i class="fas fa-unlock mr-1"></i>
                        ${site.status}
                    </div>
                </td>
                <td>
                    <button class="btn btn-success btn-xs" onclick="installSSLForSite('${site.domain}')">
                        <i class="fas fa-lock"></i>
                        Install SSL
                    </button>
                </td>
            </tr>
        `).join('');
    }
    
    // Action functions
    async function installSSL() {
        const domain = document.getElementById('new-ssl-domain').value;
        const method = document.getElementById('ssl-method').value;
        
        if (!domain) {
            showToast('Please enter a domain name', 'error');
            return;
        }
        
        showToast(`Installing SSL certificate for ${domain}...`, 'info');
        
        // This would call certbot
        setTimeout(() => {
            showToast(`SSL certificate installed for ${domain}!`, 'success');
            document.getElementById('new-ssl-domain').value = '';
            loadCertificates();
            loadNoSSLSites();
        }, 3000);
    }
    
    async function installSSLForSite(domain) {
        showToast(`Installing SSL certificate for ${domain}...`, 'info');
        
        // This would call certbot for the specific domain
        setTimeout(() => {
            showToast(`SSL certificate installed for ${domain}!`, 'success');
            loadCertificates();
            loadNoSSLSites();
        }, 3000);
    }
    
    async function renewCertificate(domain) {
        if (confirm(`Renew SSL certificate for ${domain}?`)) {
            showToast(`Renewing certificate for ${domain}...`, 'info');
            
            // This would call certbot renew
            setTimeout(() => {
                showToast(`Certificate renewed for ${domain}!`, 'success');
                loadCertificates();
            }, 2000);
        }
    }
    
    async function renewAllCertificates() {
        if (confirm('Renew all SSL certificates?')) {
            showToast('Renewing all certificates...', 'info');
            
            // This would call certbot renew --force-renewal
            setTimeout(() => {
                showToast('All certificates renewed successfully!', 'success');
                loadCertificates();
            }, 5000);
        }
    }
    
    function viewCertificateDetails(domain) {
        const cert = certificatesData.find(c => c.domain === domain);
        if (!cert) return;
        
        document.getElementById('cert-domain').textContent = domain;
        document.getElementById('cert-subject').textContent = cert.subject;
        document.getElementById('cert-issuer').textContent = cert.issuer;
        document.getElementById('cert-serial').textContent = cert.serial;
        document.getElementById('cert-valid-from').textContent = cert.issued_date;
        document.getElementById('cert-valid-until').textContent = cert.expiry_date;
        document.getElementById('cert-fingerprint').textContent = cert.fingerprint;
        
        document.getElementById('cert-details-modal').showModal();
    }
    
    function closeCertDetailsModal() {
        document.getElementById('cert-details-modal').close();
    }
    
    function configureCertbot() {
        document.getElementById('certbot-config-modal').showModal();
    }
    
    function closeCertbotConfigModal() {
        document.getElementById('certbot-config-modal').close();
    }
    
    function saveCertbotConfig() {
        showToast('Certbot configuration saved!', 'success');
        closeCertbotConfigModal();
    }
    
    // Utility functions
    function getCertStatusBadge(status, daysLeft) {
        if (daysLeft <= 7) return 'badge-error';
        if (daysLeft <= 30) return 'badge-warning';
        return 'badge-success';
    }
    
    function getCertStatusIcon(status) {
        switch (status) {
            case 'valid': return 'fa-check-circle';
            case 'expiring': return 'fa-exclamation-triangle';
            case 'expired': return 'fa-times-circle';
            default: return 'fa-question-circle';
        }
    }
    
    function getCertStatusText(status, daysLeft) {
        if (daysLeft <= 7) return 'Expires Soon';
        if (daysLeft <= 30) return 'Expiring';
        return 'Valid';
    }
    
    function formatDate(dateString) {
        return new Date(dateString).toLocaleDateString();
    }
    
    function filterCertificates(filter) {
        let filteredCerts = certificatesData;
        
        switch (filter) {
            case 'valid':
                filteredCerts = certificatesData.filter(cert => cert.status === 'valid' && cert.days_left > 30);
                break;
            case 'expiring':
                filteredCerts = certificatesData.filter(cert => cert.days_left <= 30);
                break;
            case 'expired':
                filteredCerts = certificatesData.filter(cert => cert.days_left <= 0);
                break;
        }
        
        renderCertificatesTable(filteredCerts);
    }
    
    function refreshCertificates() {
        showToast('Refreshing certificates...', 'info');
        loadCertificates();
    }
</script>
{% endblock %}
