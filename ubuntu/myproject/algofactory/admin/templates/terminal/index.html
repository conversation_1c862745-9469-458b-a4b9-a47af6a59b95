{% extends "base.html" %}

{% block title %}Web Terminal - AlgoFactory Admin{% endblock %}
{% block page_title %}Web Terminal{% endblock %}

{% block content %}
<div class="card bg-base-200 shadow-sm">
    <div class="card-body">
        <div class="flex justify-between items-center mb-6">
            <h2 class="card-title">
                <i class="fas fa-terminal text-primary"></i>
                Web Terminal
            </h2>
            <div class="flex gap-2">
                <button class="btn btn-ghost btn-sm" onclick="clearTerminal()">
                    <i class="fas fa-trash"></i>
                    Clear
                </button>
                <button class="btn btn-primary btn-sm" onclick="newSession()">
                    <i class="fas fa-plus"></i>
                    New Session
                </button>
            </div>
        </div>
        
        <div class="bg-black text-green-400 p-4 rounded-lg h-96 overflow-y-auto font-mono text-sm" id="terminal">
            <div class="mb-2">AlgoFactory Web Terminal v1.0</div>
            <div class="mb-2">Type 'help' for available commands</div>
            <div class="mb-4">ubuntu@algofactory:~$ <span class="animate-pulse">_</span></div>
        </div>
        
        <div class="mt-4">
            <div class="flex gap-2">
                <input type="text" placeholder="Enter command..." class="input input-bordered flex-1" id="command-input" onkeypress="handleKeyPress(event)">
                <button class="btn btn-primary" onclick="executeCommand()">
                    <i class="fas fa-play"></i>
                    Execute
                </button>
            </div>
        </div>
        
        <div class="mt-4">
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                <span>Web terminal is for demonstration purposes. Use with caution in production.</span>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function handleKeyPress(event) {
        if (event.key === 'Enter') {
            executeCommand();
        }
    }
    
    function executeCommand() {
        const input = document.getElementById('command-input');
        const terminal = document.getElementById('terminal');
        const command = input.value.trim();
        
        if (!command) return;
        
        // Add command to terminal
        terminal.innerHTML += `<div>ubuntu@algofactory:~$ ${command}</div>`;
        
        // Mock command execution
        setTimeout(() => {
            let output = '';
            
            switch (command.toLowerCase()) {
                case 'help':
                    output = `Available commands:
- ls: List files
- ps: Show processes
- df: Show disk usage
- free: Show memory usage
- uptime: Show system uptime
- clear: Clear terminal`;
                    break;
                case 'ls':
                    output = 'admin  algofactory-1010  algofactory-1011  algofactory-1012';
                    break;
                case 'ps':
                    output = `PID   COMMAND
1234  nginx
5678  python3 app.py
9012  systemd`;
                    break;
                case 'df':
                    output = `Filesystem     Size  Used Avail Use%
/dev/root      6.8G  5.3G  1.5G  78%`;
                    break;
                case 'free':
                    output = `              total        used        free
Mem:           957         750          70
Swap:         2048         744        1304`;
                    break;
                case 'uptime':
                    output = '06:35:12 up 2 days, 14:32, 1 user, load average: 0.15, 0.12, 0.08';
                    break;
                case 'clear':
                    clearTerminal();
                    input.value = '';
                    return;
                default:
                    output = `bash: ${command}: command not found`;
            }
            
            terminal.innerHTML += `<div class="mb-2">${output}</div>`;
            terminal.innerHTML += `<div>ubuntu@algofactory:~$ <span class="animate-pulse">_</span></div>`;
            terminal.scrollTop = terminal.scrollHeight;
        }, 500);
        
        input.value = '';
    }
    
    function clearTerminal() {
        const terminal = document.getElementById('terminal');
        terminal.innerHTML = `
            <div class="mb-2">AlgoFactory Web Terminal v1.0</div>
            <div class="mb-2">Type 'help' for available commands</div>
            <div class="mb-4">ubuntu@algofactory:~$ <span class="animate-pulse">_</span></div>
        `;
    }
    
    function newSession() {
        showToast('New terminal session created', 'info');
        clearTerminal();
    }
</script>
{% endblock %}
