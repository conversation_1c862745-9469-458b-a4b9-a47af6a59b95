{% extends "base.html" %}

{% block title %}Task Manager - AlgoFactory Admin{% endblock %}
{% block page_title %}Task Manager{% endblock %}

{% block content %}
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="stat bg-base-200 rounded-xl shadow-sm">
        <div class="stat-figure text-info">
            <i class="fas fa-tasks text-3xl"></i>
        </div>
        <div class="stat-title">Active Tasks</div>
        <div class="stat-value text-info">3</div>
        <div class="stat-desc">Currently running</div>
    </div>
    
    <div class="stat bg-base-200 rounded-xl shadow-sm">
        <div class="stat-figure text-warning">
            <i class="fas fa-clock text-3xl"></i>
        </div>
        <div class="stat-title">Scheduled</div>
        <div class="stat-value text-warning">7</div>
        <div class="stat-desc">Pending execution</div>
    </div>
    
    <div class="stat bg-base-200 rounded-xl shadow-sm">
        <div class="stat-figure text-success">
            <i class="fas fa-check-circle text-3xl"></i>
        </div>
        <div class="stat-title">Completed</div>
        <div class="stat-value text-success">45</div>
        <div class="stat-desc">Today</div>
    </div>
    
    <div class="stat bg-base-200 rounded-xl shadow-sm">
        <div class="stat-figure text-error">
            <i class="fas fa-times-circle text-3xl"></i>
        </div>
        <div class="stat-title">Failed</div>
        <div class="stat-value text-error">2</div>
        <div class="stat-desc">Require attention</div>
    </div>
</div>

<div class="card bg-base-200 shadow-sm">
    <div class="card-body">
        <div class="flex justify-between items-center mb-6">
            <h2 class="card-title">
                <i class="fas fa-list text-primary"></i>
                Task Queue
            </h2>
            <button class="btn btn-primary">
                <i class="fas fa-plus"></i>
                New Task
            </button>
        </div>
        
        <div class="overflow-x-auto">
            <table class="table table-zebra w-full">
                <thead>
                    <tr>
                        <th>Task</th>
                        <th>Status</th>
                        <th>Progress</th>
                        <th>Started</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <div class="font-bold">System Backup</div>
                            <div class="text-sm text-base-content/70">Full system backup</div>
                        </td>
                        <td><div class="badge badge-info">Running</div></td>
                        <td>
                            <div class="flex items-center gap-2">
                                <progress class="progress progress-primary w-20" value="65" max="100"></progress>
                                <span class="text-sm">65%</span>
                            </div>
                        </td>
                        <td>2 min ago</td>
                        <td>
                            <button class="btn btn-error btn-xs">
                                <i class="fas fa-stop"></i>
                            </button>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div class="font-bold">SSL Renewal</div>
                            <div class="text-sm text-base-content/70">Renew expiring certificates</div>
                        </td>
                        <td><div class="badge badge-warning">Scheduled</div></td>
                        <td>
                            <div class="text-sm">Waiting...</div>
                        </td>
                        <td>In 2 hours</td>
                        <td>
                            <button class="btn btn-primary btn-xs">
                                <i class="fas fa-play"></i>
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
