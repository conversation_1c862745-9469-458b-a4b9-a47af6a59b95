{% extends "base.html" %}

{% block title %}System Monitor - AlgoFactory Admin{% endblock %}
{% block page_title %}System Monitoring{% endblock %}

{% block content %}
<!-- System Overview -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="stat bg-base-200 rounded-xl shadow-sm">
        <div class="stat-figure text-primary">
            <i class="fas fa-microchip text-3xl"></i>
        </div>
        <div class="stat-title">CPU Usage</div>
        <div class="stat-value text-primary" id="cpu-usage">0%</div>
        <div class="stat-desc">Load average: <span id="load-average">0.00</span></div>
    </div>
    
    <div class="stat bg-base-200 rounded-xl shadow-sm">
        <div class="stat-figure text-secondary">
            <i class="fas fa-memory text-3xl"></i>
        </div>
        <div class="stat-title">Memory Usage</div>
        <div class="stat-value text-secondary" id="memory-usage">0%</div>
        <div class="stat-desc"><span id="memory-used">0</span>MB / <span id="memory-total">0</span>MB</div>
    </div>
    
    <div class="stat bg-base-200 rounded-xl shadow-sm">
        <div class="stat-figure text-accent">
            <i class="fas fa-hdd text-3xl"></i>
        </div>
        <div class="stat-title">Disk Usage</div>
        <div class="stat-value text-accent" id="disk-usage">0%</div>
        <div class="stat-desc"><span id="disk-used">0</span>GB / <span id="disk-total">0</span>GB</div>
    </div>
    
    <div class="stat bg-base-200 rounded-xl shadow-sm">
        <div class="stat-figure text-info">
            <i class="fas fa-network-wired text-3xl"></i>
        </div>
        <div class="stat-title">Network I/O</div>
        <div class="stat-value text-info" id="network-io">0 KB/s</div>
        <div class="stat-desc">↑ <span id="network-sent">0</span> ↓ <span id="network-recv">0</span></div>
    </div>
</div>

<!-- Performance Charts -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <div class="card bg-base-200 shadow-sm">
        <div class="card-body">
            <h2 class="card-title">
                <i class="fas fa-chart-line text-primary"></i>
                CPU & Memory Usage
            </h2>
            <div class="h-64">
                <canvas id="cpu-memory-chart"></canvas>
            </div>
        </div>
    </div>
    
    <div class="card bg-base-200 shadow-sm">
        <div class="card-body">
            <h2 class="card-title">
                <i class="fas fa-chart-area text-secondary"></i>
                Network Activity
            </h2>
            <div class="h-64">
                <canvas id="network-chart"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Process Management -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <div class="card bg-base-200 shadow-sm">
        <div class="card-body">
            <div class="flex justify-between items-center mb-4">
                <h2 class="card-title">
                    <i class="fas fa-tasks text-warning"></i>
                    Top Processes
                </h2>
                <button class="btn btn-ghost btn-sm" onclick="refreshProcesses()">
                    <i class="fas fa-sync-alt"></i>
                    Refresh
                </button>
            </div>
            <div class="overflow-x-auto">
                <table class="table table-zebra table-compact w-full">
                    <thead>
                        <tr>
                            <th>Process</th>
                            <th>CPU%</th>
                            <th>Memory</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody id="processes-table">
                        <tr>
                            <td colspan="4" class="text-center">
                                <i class="fas fa-spinner fa-spin mr-2"></i>
                                Loading processes...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <div class="card bg-base-200 shadow-sm">
        <div class="card-body">
            <div class="flex justify-between items-center mb-4">
                <h2 class="card-title">
                    <i class="fas fa-server text-success"></i>
                    System Services
                </h2>
                <button class="btn btn-ghost btn-sm" onclick="refreshServices()">
                    <i class="fas fa-sync-alt"></i>
                    Refresh
                </button>
            </div>
            <div class="space-y-2" id="services-list">
                <div class="text-center">
                    <i class="fas fa-spinner fa-spin mr-2"></i>
                    Loading services...
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Information -->
<div class="card bg-base-200 shadow-sm">
    <div class="card-body">
        <h2 class="card-title mb-6">
            <i class="fas fa-info-circle text-info"></i>
            System Information
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div class="space-y-3">
                <h3 class="font-bold text-lg">Hardware</h3>
                <div class="space-y-2 text-sm">
                    <div><strong>CPU:</strong> <span id="cpu-info">Loading...</span></div>
                    <div><strong>Cores:</strong> <span id="cpu-cores">0</span></div>
                    <div><strong>Architecture:</strong> <span id="cpu-arch">Loading...</span></div>
                    <div><strong>Total RAM:</strong> <span id="total-ram">0 MB</span></div>
                    <div><strong>Swap:</strong> <span id="swap-info">0 MB</span></div>
                </div>
            </div>
            
            <div class="space-y-3">
                <h3 class="font-bold text-lg">System</h3>
                <div class="space-y-2 text-sm">
                    <div><strong>OS:</strong> <span id="os-info">Loading...</span></div>
                    <div><strong>Kernel:</strong> <span id="kernel-info">Loading...</span></div>
                    <div><strong>Uptime:</strong> <span id="system-uptime">Loading...</span></div>
                    <div><strong>Hostname:</strong> <span id="hostname">Loading...</span></div>
                    <div><strong>IP Address:</strong> <span id="ip-address">Loading...</span></div>
                </div>
            </div>
            
            <div class="space-y-3">
                <h3 class="font-bold text-lg">Storage</h3>
                <div class="space-y-2 text-sm">
                    <div><strong>Root Filesystem:</strong> <span id="root-fs">Loading...</span></div>
                    <div><strong>Total Space:</strong> <span id="total-space">0 GB</span></div>
                    <div><strong>Available:</strong> <span id="available-space">0 GB</span></div>
                    <div><strong>Inodes Used:</strong> <span id="inodes-used">0%</span></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="fixed bottom-6 right-6 z-50">
    <div class="dropdown dropdown-top dropdown-end">
        <div tabindex="0" role="button" class="btn btn-primary btn-circle btn-lg shadow-lg">
            <i class="fas fa-cog text-xl"></i>
        </div>
        <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52 mb-2">
            <li><a onclick="optimizeSystem()"><i class="fas fa-magic"></i> Optimize System</a></li>
            <li><a onclick="clearCache()"><i class="fas fa-broom"></i> Clear Cache</a></li>
            <li><a onclick="restartServices()"><i class="fas fa-redo"></i> Restart Services</a></li>
            <li><a onclick="emergencyCleanup()"><i class="fas fa-exclamation-triangle"></i> Emergency Cleanup</a></li>
        </ul>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    let cpuMemoryChart, networkChart;
    let systemData = {
        cpu: [],
        memory: [],
        network_sent: [],
        network_recv: [],
        timestamps: []
    };
    
    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
        initializeCharts();
        loadSystemInfo();
        loadProcesses();
        loadServices();
        
        // Start real-time updates
        startRealTimeUpdates();
    });
    
    function initializeCharts() {
        // CPU & Memory Chart
        const cpuMemoryCtx = document.getElementById('cpu-memory-chart').getContext('2d');
        cpuMemoryChart = new Chart(cpuMemoryCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'CPU %',
                    data: [],
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4
                }, {
                    label: 'Memory %',
                    data: [],
                    borderColor: 'rgb(16, 185, 129)',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                },
                plugins: {
                    legend: {
                        position: 'top'
                    }
                }
            }
        });
        
        // Network Chart
        const networkCtx = document.getElementById('network-chart').getContext('2d');
        networkChart = new Chart(networkCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Sent (KB/s)',
                    data: [],
                    borderColor: 'rgb(245, 101, 101)',
                    backgroundColor: 'rgba(245, 101, 101, 0.1)',
                    tension: 0.4
                }, {
                    label: 'Received (KB/s)',
                    data: [],
                    borderColor: 'rgb(251, 191, 36)',
                    backgroundColor: 'rgba(251, 191, 36, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        position: 'top'
                    }
                }
            }
        });
    }
    
    function startRealTimeUpdates() {
        // Listen for real-time updates from WebSocket
        window.addEventListener('systemUpdate', function(event) {
            if (event.detail.data) {
                updateSystemMetrics(event.detail.data);
            }
        });
        
        // Fallback: poll every 5 seconds
        setInterval(async () => {
            try {
                const response = await fetch('/api/system/metrics');
                const data = await response.json();
                updateSystemMetrics(data);
            } catch (error) {
                console.error('Error fetching system metrics:', error);
            }
        }, 5000);
    }
    
    function updateSystemMetrics(data) {
        // Update overview cards
        if (data.cpu_percent !== undefined) {
            document.getElementById('cpu-usage').textContent = data.cpu_percent.toFixed(1) + '%';
        }
        
        if (data.memory_percent !== undefined) {
            document.getElementById('memory-usage').textContent = data.memory_percent.toFixed(1) + '%';
            document.getElementById('memory-used').textContent = Math.round(data.memory_used_mb || 0);
            document.getElementById('memory-total').textContent = Math.round(data.memory_total_mb || 0);
        }
        
        if (data.disk_percent !== undefined) {
            document.getElementById('disk-usage').textContent = data.disk_percent.toFixed(1) + '%';
            document.getElementById('disk-used').textContent = (data.disk_used_gb || 0).toFixed(1);
            document.getElementById('disk-total').textContent = (data.disk_total_gb || 0).toFixed(1);
        }
        
        if (data.load_average !== undefined) {
            document.getElementById('load-average').textContent = data.load_average.toFixed(2);
        }
        
        if (data.network_io !== undefined) {
            document.getElementById('network-io').textContent = (data.network_io || 0).toFixed(1) + ' KB/s';
            document.getElementById('network-sent').textContent = (data.network_sent || 0).toFixed(1) + ' KB/s';
            document.getElementById('network-recv').textContent = (data.network_recv || 0).toFixed(1) + ' KB/s';
        }
        
        // Update charts
        updateCharts(data);
    }
    
    function updateCharts(data) {
        const now = new Date().toLocaleTimeString();
        
        // Add new data point
        systemData.timestamps.push(now);
        systemData.cpu.push(data.cpu_percent || 0);
        systemData.memory.push(data.memory_percent || 0);
        systemData.network_sent.push(data.network_sent || 0);
        systemData.network_recv.push(data.network_recv || 0);
        
        // Keep only last 20 data points
        if (systemData.timestamps.length > 20) {
            systemData.timestamps.shift();
            systemData.cpu.shift();
            systemData.memory.shift();
            systemData.network_sent.shift();
            systemData.network_recv.shift();
        }
        
        // Update CPU & Memory chart
        cpuMemoryChart.data.labels = systemData.timestamps;
        cpuMemoryChart.data.datasets[0].data = systemData.cpu;
        cpuMemoryChart.data.datasets[1].data = systemData.memory;
        cpuMemoryChart.update('none');
        
        // Update Network chart
        networkChart.data.labels = systemData.timestamps;
        networkChart.data.datasets[0].data = systemData.network_sent;
        networkChart.data.datasets[1].data = systemData.network_recv;
        networkChart.update('none');
    }
    
    async function loadSystemInfo() {
        // Mock system information - in real implementation, this would come from the server
        const systemInfo = {
            cpu_info: 'Intel(R) Xeon(R) CPU E5-2686 v4 @ 2.30GHz',
            cpu_cores: 1,
            cpu_arch: 'x86_64',
            total_ram: 957,
            swap_info: '2048 MB',
            os_info: 'Ubuntu 24.04 LTS',
            kernel_info: '6.8.0-1014-aws',
            system_uptime: '2 days, 14 hours, 32 minutes',
            hostname: 'ip-172-31-14-191',
            ip_address: '*************',
            root_fs: 'ext4',
            total_space: 6.8,
            available_space: 1.5,
            inodes_used: 25
        };
        
        // Update system information
        document.getElementById('cpu-info').textContent = systemInfo.cpu_info;
        document.getElementById('cpu-cores').textContent = systemInfo.cpu_cores;
        document.getElementById('cpu-arch').textContent = systemInfo.cpu_arch;
        document.getElementById('total-ram').textContent = systemInfo.total_ram + ' MB';
        document.getElementById('swap-info').textContent = systemInfo.swap_info;
        document.getElementById('os-info').textContent = systemInfo.os_info;
        document.getElementById('kernel-info').textContent = systemInfo.kernel_info;
        document.getElementById('system-uptime').textContent = systemInfo.system_uptime;
        document.getElementById('hostname').textContent = systemInfo.hostname;
        document.getElementById('ip-address').textContent = systemInfo.ip_address;
        document.getElementById('root-fs').textContent = systemInfo.root_fs;
        document.getElementById('total-space').textContent = systemInfo.total_space + ' GB';
        document.getElementById('available-space').textContent = systemInfo.available_space + ' GB';
        document.getElementById('inodes-used').textContent = systemInfo.inodes_used + '%';
    }
    
    async function loadProcesses() {
        // Mock process data
        const processes = [
            { name: 'python3 (admin)', cpu: 2.1, memory: '37MB', pid: 306179, killable: false },
            { name: 'python3 (8010)', cpu: 1.8, memory: '45MB', pid: 123456, killable: true },
            { name: 'python3 (8011)', cpu: 1.5, memory: '42MB', pid: 123457, killable: true },
            { name: 'nginx', cpu: 0.8, memory: '12MB', pid: 5678, killable: false },
            { name: 'systemd', cpu: 0.3, memory: '8MB', pid: 1, killable: false },
            { name: 'ssh', cpu: 0.1, memory: '4MB', pid: 1234, killable: false }
        ];
        
        const tbody = document.getElementById('processes-table');
        tbody.innerHTML = processes.map(proc => `
            <tr>
                <td>
                    <div class="font-medium">${proc.name}</div>
                    <div class="text-xs text-base-content/70">PID: ${proc.pid}</div>
                </td>
                <td>
                    <div class="text-sm">${proc.cpu}%</div>
                </td>
                <td>
                    <div class="text-sm">${proc.memory}</div>
                </td>
                <td>
                    ${proc.killable ? `
                        <button class="btn btn-error btn-xs" onclick="killProcess(${proc.pid}, '${proc.name}')" title="Kill Process">
                            <i class="fas fa-times"></i>
                        </button>
                    ` : `
                        <span class="text-xs text-base-content/50">System</span>
                    `}
                </td>
            </tr>
        `).join('');
    }
    
    async function loadServices() {
        // Mock service data
        const services = [
            { name: 'algofactory-admin', status: 'active', description: 'AlgoFactory Admin Dashboard' },
            { name: 'nginx', status: 'active', description: 'Web Server' },
            { name: 'ssh', status: 'active', description: 'SSH Daemon' },
            { name: 'algofactory-optimizer', status: 'active', description: 'System Optimizer' },
            { name: 'snapd', status: 'inactive', description: 'Snap Package Manager' }
        ];
        
        const servicesList = document.getElementById('services-list');
        servicesList.innerHTML = services.map(service => `
            <div class="flex items-center justify-between p-3 bg-base-300 rounded-lg">
                <div class="flex items-center gap-3">
                    <div class="badge ${service.status === 'active' ? 'badge-success' : 'badge-error'} badge-sm">
                        <i class="fas ${service.status === 'active' ? 'fa-check' : 'fa-times'} mr-1"></i>
                        ${service.status}
                    </div>
                    <div>
                        <div class="font-medium">${service.name}</div>
                        <div class="text-xs text-base-content/70">${service.description}</div>
                    </div>
                </div>
                <div class="flex gap-1">
                    ${service.status === 'active' ? `
                        <button class="btn btn-warning btn-xs" onclick="restartService('${service.name}')" title="Restart">
                            <i class="fas fa-redo"></i>
                        </button>
                        <button class="btn btn-error btn-xs" onclick="stopService('${service.name}')" title="Stop">
                            <i class="fas fa-stop"></i>
                        </button>
                    ` : `
                        <button class="btn btn-success btn-xs" onclick="startService('${service.name}')" title="Start">
                            <i class="fas fa-play"></i>
                        </button>
                    `}
                </div>
            </div>
        `).join('');
    }
    
    // Action functions
    async function killProcess(pid, name) {
        if (confirm(`Kill process "${name}" (PID: ${pid})?`)) {
            showToast(`Killing process ${name}...`, 'warning');
            // This would call the API to kill the process
            setTimeout(() => {
                showToast(`Process ${name} terminated`, 'success');
                loadProcesses();
            }, 1000);
        }
    }
    
    async function restartService(serviceName) {
        if (confirm(`Restart service "${serviceName}"?`)) {
            showToast(`Restarting ${serviceName}...`, 'info');
            // This would call systemctl restart
            setTimeout(() => {
                showToast(`Service ${serviceName} restarted`, 'success');
                loadServices();
            }, 2000);
        }
    }
    
    async function stopService(serviceName) {
        if (confirm(`Stop service "${serviceName}"?`)) {
            showToast(`Stopping ${serviceName}...`, 'warning');
            // This would call systemctl stop
            setTimeout(() => {
                showToast(`Service ${serviceName} stopped`, 'success');
                loadServices();
            }, 1000);
        }
    }
    
    async function startService(serviceName) {
        showToast(`Starting ${serviceName}...`, 'info');
        // This would call systemctl start
        setTimeout(() => {
            showToast(`Service ${serviceName} started`, 'success');
            loadServices();
        }, 1000);
    }
    
    async function optimizeSystem() {
        if (confirm('Run system optimization? This will clean cache and optimize performance.')) {
            showToast('Running system optimization...', 'info');
            
            try {
                const response = await fetch('/api/system/cleanup', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                const result = await response.json();
                showToast(result.message, result.success ? 'success' : 'error');
            } catch (error) {
                showToast('System optimization failed', 'error');
            }
        }
    }
    
    function refreshProcesses() {
        showToast('Refreshing processes...', 'info');
        loadProcesses();
    }
    
    function refreshServices() {
        showToast('Refreshing services...', 'info');
        loadServices();
    }
</script>
{% endblock %}
