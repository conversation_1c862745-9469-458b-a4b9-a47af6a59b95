{% extends "base.html" %}

{% block title %}Settings - AlgoFactory Admin{% endblock %}
{% block page_title %}System Settings{% endblock %}

{% block content %}
<!-- Settings Categories -->
<div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8">
    <div class="card bg-base-200 shadow-sm cursor-pointer" onclick="showSettingsCategory('general')">
        <div class="card-body text-center">
            <i class="fas fa-cog text-4xl text-primary mb-2"></i>
            <h3 class="font-bold">General</h3>
            <p class="text-sm text-base-content/70">Basic system settings</p>
        </div>
    </div>
    
    <div class="card bg-base-200 shadow-sm cursor-pointer" onclick="showSettingsCategory('security')">
        <div class="card-body text-center">
            <i class="fas fa-shield-alt text-4xl text-secondary mb-2"></i>
            <h3 class="font-bold">Security</h3>
            <p class="text-sm text-base-content/70">Authentication & access</p>
        </div>
    </div>
    
    <div class="card bg-base-200 shadow-sm cursor-pointer" onclick="showSettingsCategory('monitoring')">
        <div class="card-body text-center">
            <i class="fas fa-chart-line text-4xl text-accent mb-2"></i>
            <h3 class="font-bold">Monitoring</h3>
            <p class="text-sm text-base-content/70">Alerts & thresholds</p>
        </div>
    </div>
    
    <div class="card bg-base-200 shadow-sm cursor-pointer" onclick="showSettingsCategory('backup')">
        <div class="card-body text-center">
            <i class="fas fa-database text-4xl text-info mb-2"></i>
            <h3 class="font-bold">Backup</h3>
            <p class="text-sm text-base-content/70">Automated backups</p>
        </div>
    </div>
</div>

<!-- Settings Content -->
<div class="card bg-base-200 shadow-sm">
    <div class="card-body">
        <div class="flex justify-between items-center mb-6">
            <h2 class="card-title">
                <i class="fas fa-cog text-primary"></i>
                <span id="settings-title">General Settings</span>
            </h2>
            <button class="btn btn-primary" onclick="saveSettings()">
                <i class="fas fa-save"></i>
                Save Changes
            </button>
        </div>
        
        <!-- General Settings -->
        <div id="general-settings" class="settings-panel">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <h3 class="font-bold text-lg">System Information</h3>
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">System Name</span>
                        </label>
                        <input type="text" value="AlgoFactory Production Server" class="input input-bordered">
                    </div>
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Admin Email</span>
                        </label>
                        <input type="email" value="<EMAIL>" class="input input-bordered">
                    </div>
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Timezone</span>
                        </label>
                        <select class="select select-bordered">
                            <option>UTC</option>
                            <option>Asia/Kolkata</option>
                            <option>America/New_York</option>
                            <option>Europe/London</option>
                        </select>
                    </div>
                </div>
                
                <div class="space-y-4">
                    <h3 class="font-bold text-lg">Performance</h3>
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Memory Threshold (%)</span>
                        </label>
                        <input type="range" min="50" max="95" value="85" class="range range-primary">
                        <div class="w-full flex justify-between text-xs px-2">
                            <span>50%</span>
                            <span>95%</span>
                        </div>
                    </div>
                    <div class="form-control">
                        <label class="label cursor-pointer">
                            <span class="label-text">Auto-optimization</span>
                            <input type="checkbox" class="toggle toggle-primary" checked>
                        </label>
                    </div>
                    <div class="form-control">
                        <label class="label cursor-pointer">
                            <span class="label-text">Auto-restart failed services</span>
                            <input type="checkbox" class="toggle toggle-primary" checked>
                        </label>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Security Settings -->
        <div id="security-settings" class="settings-panel hidden">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <h3 class="font-bold text-lg">Authentication</h3>
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Session Timeout (minutes)</span>
                        </label>
                        <input type="number" value="60" class="input input-bordered">
                    </div>
                    <div class="form-control">
                        <label class="label cursor-pointer">
                            <span class="label-text">Require password change on first login</span>
                            <input type="checkbox" class="toggle toggle-secondary" checked>
                        </label>
                    </div>
                    <div class="form-control">
                        <label class="label cursor-pointer">
                            <span class="label-text">Enable two-factor authentication</span>
                            <input type="checkbox" class="toggle toggle-secondary">
                        </label>
                    </div>
                </div>
                
                <div class="space-y-4">
                    <h3 class="font-bold text-lg">Access Control</h3>
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Allowed IP Addresses</span>
                        </label>
                        <textarea class="textarea textarea-bordered" placeholder="0.0.0.0/0 (all IPs)">0.0.0.0/0</textarea>
                    </div>
                    <div class="form-control">
                        <label class="label cursor-pointer">
                            <span class="label-text">Log all login attempts</span>
                            <input type="checkbox" class="toggle toggle-secondary" checked>
                        </label>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Monitoring Settings -->
        <div id="monitoring-settings" class="settings-panel hidden">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <h3 class="font-bold text-lg">Alert Thresholds</h3>
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">CPU Alert Threshold (%)</span>
                        </label>
                        <input type="range" min="50" max="100" value="90" class="range range-accent">
                    </div>
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Memory Alert Threshold (%)</span>
                        </label>
                        <input type="range" min="50" max="100" value="85" class="range range-accent">
                    </div>
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Disk Alert Threshold (%)</span>
                        </label>
                        <input type="range" min="50" max="100" value="90" class="range range-accent">
                    </div>
                </div>
                
                <div class="space-y-4">
                    <h3 class="font-bold text-lg">Notifications</h3>
                    <div class="form-control">
                        <label class="label cursor-pointer">
                            <span class="label-text">Email notifications</span>
                            <input type="checkbox" class="toggle toggle-accent" checked>
                        </label>
                    </div>
                    <div class="form-control">
                        <label class="label cursor-pointer">
                            <span class="label-text">SMS notifications</span>
                            <input type="checkbox" class="toggle toggle-accent">
                        </label>
                    </div>
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Check Interval (seconds)</span>
                        </label>
                        <select class="select select-bordered">
                            <option value="30">30 seconds</option>
                            <option value="60" selected>1 minute</option>
                            <option value="300">5 minutes</option>
                            <option value="600">10 minutes</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Backup Settings -->
        <div id="backup-settings" class="settings-panel hidden">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <h3 class="font-bold text-lg">Automated Backups</h3>
                    <div class="form-control">
                        <label class="label cursor-pointer">
                            <span class="label-text">Enable automatic backups</span>
                            <input type="checkbox" class="toggle toggle-info" checked>
                        </label>
                    </div>
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Backup Frequency</span>
                        </label>
                        <select class="select select-bordered">
                            <option value="daily" selected>Daily</option>
                            <option value="weekly">Weekly</option>
                            <option value="monthly">Monthly</option>
                        </select>
                    </div>
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Backup Time</span>
                        </label>
                        <input type="time" value="02:00" class="input input-bordered">
                    </div>
                </div>
                
                <div class="space-y-4">
                    <h3 class="font-bold text-lg">Retention Policy</h3>
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Keep backups for (days)</span>
                        </label>
                        <input type="number" value="30" class="input input-bordered">
                    </div>
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Backup Location</span>
                        </label>
                        <input type="text" value="/var/backups/algofactory" class="input input-bordered">
                    </div>
                    <div class="form-control">
                        <label class="label cursor-pointer">
                            <span class="label-text">Compress backups</span>
                            <input type="checkbox" class="toggle toggle-info" checked>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function showSettingsCategory(category) {
        // Hide all panels
        document.querySelectorAll('.settings-panel').forEach(panel => {
            panel.classList.add('hidden');
        });
        
        // Show selected panel
        document.getElementById(category + '-settings').classList.remove('hidden');
        
        // Update title
        const titles = {
            'general': 'General Settings',
            'security': 'Security Settings',
            'monitoring': 'Monitoring Settings',
            'backup': 'Backup Settings'
        };
        
        document.getElementById('settings-title').textContent = titles[category];
    }
    
    function saveSettings() {
        showToast('Saving settings...', 'info');
        
        // Simulate saving
        setTimeout(() => {
            showToast('Settings saved successfully!', 'success');
        }, 1000);
    }
    
    // Initialize with general settings
    document.addEventListener('DOMContentLoaded', function() {
        showSettingsCategory('general');
    });
</script>
{% endblock %}
