{% extends "base.html" %}

{% block title %}Nginx Management - AlgoFactory Admin{% endblock %}
{% block page_title %}Nginx Management{% endblock %}

{% block content %}
<!-- Nginx Status Overview -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="stat bg-base-200 rounded-xl shadow-sm">
        <div class="stat-figure text-primary">
            <i class="fas fa-server text-3xl"></i>
        </div>
        <div class="stat-title">Service Status</div>
        <div class="stat-value text-primary" id="nginx-service-status">Loading...</div>
        <div class="stat-desc">Nginx process</div>
    </div>
    
    <div class="stat bg-base-200 rounded-xl shadow-sm">
        <div class="stat-figure text-secondary">
            <i class="fas fa-globe text-3xl"></i>
        </div>
        <div class="stat-title">Total Sites</div>
        <div class="stat-value text-secondary" id="total-sites">0</div>
        <div class="stat-desc">Configured domains</div>
    </div>
    
    <div class="stat bg-base-200 rounded-xl shadow-sm">
        <div class="stat-figure text-success">
            <i class="fas fa-lock text-3xl"></i>
        </div>
        <div class="stat-title">SSL Enabled</div>
        <div class="stat-value text-success" id="ssl-enabled">0</div>
        <div class="stat-desc">With certificates</div>
    </div>
    
    <div class="stat bg-base-200 rounded-xl shadow-sm">
        <div class="stat-figure text-warning">
            <i class="fas fa-exclamation-triangle text-3xl"></i>
        </div>
        <div class="stat-title">Needs SSL</div>
        <div class="stat-value text-warning" id="needs-ssl">0</div>
        <div class="stat-desc">Without certificates</div>
    </div>
</div>

<!-- Quick Actions -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
    <div class="card bg-base-200 shadow-sm">
        <div class="card-body">
            <h2 class="card-title">
                <i class="fas fa-cog text-primary"></i>
                Nginx Control
            </h2>
            <div class="space-y-3">
                <button class="btn btn-primary btn-sm w-full" onclick="testNginxConfig()">
                    <i class="fas fa-check"></i>
                    Test Configuration
                </button>
                <button class="btn btn-secondary btn-sm w-full" onclick="reloadNginx()">
                    <i class="fas fa-redo"></i>
                    Reload Nginx
                </button>
                <button class="btn btn-warning btn-sm w-full" onclick="restartNginx()">
                    <i class="fas fa-power-off"></i>
                    Restart Nginx
                </button>
            </div>
        </div>
    </div>
    
    <div class="card bg-base-200 shadow-sm">
        <div class="card-body">
            <h2 class="card-title">
                <i class="fas fa-plus text-success"></i>
                Add New Site
            </h2>
            <div class="space-y-3">
                <input type="text" placeholder="Subdomain (e.g., 8014)" class="input input-bordered input-sm w-full" id="new-subdomain">
                <input type="number" placeholder="Port (e.g., 8014)" class="input input-bordered input-sm w-full" id="new-port">
                <button class="btn btn-success btn-sm w-full" onclick="createNewSite()">
                    <i class="fas fa-plus"></i>
                    Create Site
                </button>
            </div>
        </div>
    </div>
    
    <div class="card bg-base-200 shadow-sm">
        <div class="card-body">
            <h2 class="card-title">
                <i class="fas fa-certificate text-info"></i>
                SSL Management
            </h2>
            <div class="space-y-3">
                <button class="btn btn-info btn-sm w-full" onclick="bulkSSLInstall()">
                    <i class="fas fa-lock"></i>
                    Bulk SSL Install
                </button>
                <button class="btn btn-accent btn-sm w-full" onclick="renewSSL()">
                    <i class="fas fa-sync"></i>
                    Renew Certificates
                </button>
                <button class="btn btn-ghost btn-sm w-full" onclick="checkSSLExpiry()">
                    <i class="fas fa-calendar"></i>
                    Check Expiry
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Sites Management -->
<div class="card bg-base-200 shadow-sm">
    <div class="card-body">
        <div class="flex justify-between items-center mb-6">
            <h2 class="card-title">
                <i class="fas fa-list text-primary"></i>
                Configured Sites
            </h2>
            <div class="flex gap-2">
                <button class="btn btn-ghost btn-sm" onclick="refreshSites()">
                    <i class="fas fa-sync-alt"></i>
                    Refresh
                </button>
                <div class="dropdown dropdown-end">
                    <div tabindex="0" role="button" class="btn btn-ghost btn-sm">
                        <i class="fas fa-filter"></i>
                        Filter
                    </div>
                    <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                        <li><a onclick="filterSites('all')">All Sites</a></li>
                        <li><a onclick="filterSites('ssl')">SSL Enabled</a></li>
                        <li><a onclick="filterSites('no-ssl')">No SSL</a></li>
                        <li><a onclick="filterSites('algofactory')">AlgoFactory Only</a></li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="table table-zebra w-full">
                <thead>
                    <tr>
                        <th>Domain</th>
                        <th>Port</th>
                        <th>SSL Status</th>
                        <th>Certificate Expiry</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="sites-table">
                    <tr>
                        <td colspan="6" class="text-center text-base-content/50">
                            <i class="fas fa-spinner fa-spin mr-2"></i>
                            Loading sites...
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Configuration Editor Modal -->
<dialog id="config-editor-modal" class="modal">
    <div class="modal-box w-11/12 max-w-5xl">
        <h3 class="font-bold text-lg mb-4">
            <i class="fas fa-edit"></i>
            Edit Nginx Configuration
        </h3>
        <div class="form-control">
            <label class="label">
                <span class="label-text">Configuration File:</span>
                <span class="label-text-alt" id="config-file-path"></span>
            </label>
            <textarea class="textarea textarea-bordered h-96 font-mono text-sm" id="config-content" placeholder="Nginx configuration..."></textarea>
        </div>
        <div class="modal-action">
            <button class="btn btn-primary" onclick="saveConfiguration()">
                <i class="fas fa-save"></i>
                Save & Test
            </button>
            <button class="btn btn-ghost" onclick="closeConfigEditor()">Cancel</button>
        </div>
    </div>
</dialog>

<!-- Logs Modal -->
<dialog id="nginx-logs-modal" class="modal">
    <div class="modal-box w-11/12 max-w-4xl">
        <h3 class="font-bold text-lg mb-4">
            <i class="fas fa-file-alt"></i>
            Nginx Logs
        </h3>
        <div class="tabs tabs-boxed mb-4">
            <a class="tab tab-active" onclick="switchLogTab('access')">Access Log</a>
            <a class="tab" onclick="switchLogTab('error')">Error Log</a>
        </div>
        <div class="bg-base-300 rounded-lg p-4 h-96 overflow-y-auto font-mono text-sm" id="nginx-logs-content">
            Loading logs...
        </div>
        <div class="modal-action">
            <button class="btn btn-primary" onclick="downloadLogs()">
                <i class="fas fa-download"></i>
                Download
            </button>
            <button class="btn btn-ghost" onclick="closeLogsModal()">Close</button>
        </div>
    </div>
</dialog>
{% endblock %}

{% block scripts %}
<script>
    let sitesData = [];
    
    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
        loadNginxStatus();
        loadSites();
        
        // Listen for real-time updates
        window.addEventListener('systemUpdate', function(event) {
            if (event.detail.data && event.detail.data.nginx) {
                updateNginxStatus(event.detail.data.nginx);
            }
        });
    });
    
    async function loadNginxStatus() {
        try {
            const response = await fetch('/api/system/metrics');
            const data = await response.json();
            
            if (data.nginx) {
                updateNginxStatus(data.nginx);
            }
        } catch (error) {
            console.error('Error loading Nginx status:', error);
            showToast('Failed to load Nginx status', 'error');
        }
    }
    
    function updateNginxStatus(nginx) {
        // Service status
        const statusElement = document.getElementById('nginx-service-status');
        if (nginx.running) {
            statusElement.textContent = 'Running';
            statusElement.className = 'stat-value text-success';
        } else {
            statusElement.textContent = 'Stopped';
            statusElement.className = 'stat-value text-error';
        }
        
        // Sites count
        document.getElementById('total-sites').textContent = nginx.sites_count || 0;
        
        // SSL counts
        const sslEnabled = nginx.sites ? nginx.sites.filter(site => site.ssl_enabled).length : 0;
        const needsSSL = (nginx.sites_count || 0) - sslEnabled;
        
        document.getElementById('ssl-enabled').textContent = sslEnabled;
        document.getElementById('needs-ssl').textContent = needsSSL;
    }
    
    async function loadSites() {
        try {
            const response = await fetch('/api/system/metrics');
            const data = await response.json();
            
            if (data.nginx && data.nginx.sites) {
                sitesData = data.nginx.sites;
                renderSitesTable(sitesData);
            }
        } catch (error) {
            console.error('Error loading sites:', error);
            showToast('Failed to load sites', 'error');
        }
    }
    
    function renderSitesTable(sites) {
        const tbody = document.getElementById('sites-table');
        
        if (sites.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center text-base-content/50">
                        <i class="fas fa-info-circle mr-2"></i>
                        No sites configured
                    </td>
                </tr>
            `;
            return;
        }
        
        tbody.innerHTML = sites.map(site => `
            <tr>
                <td>
                    <div class="font-bold">${site.domain}</div>
                    <div class="text-sm text-base-content/70">
                        <a href="https://${site.domain}" target="_blank" class="link">
                            <i class="fas fa-external-link-alt"></i>
                            Visit Site
                        </a>
                    </div>
                </td>
                <td>
                    <div class="badge badge-neutral">Auto-detected</div>
                </td>
                <td>
                    <div class="badge ${site.ssl_enabled ? 'badge-success' : 'badge-warning'}">
                        <i class="fas ${site.ssl_enabled ? 'fa-lock' : 'fa-unlock'} mr-1"></i>
                        ${site.ssl_enabled ? 'SSL Enabled' : 'No SSL'}
                    </div>
                </td>
                <td>
                    ${site.ssl_enabled ? 
                        '<span class="text-sm">Auto-renew enabled</span>' : 
                        '<span class="text-sm text-base-content/50">N/A</span>'
                    }
                </td>
                <td>
                    <div class="badge badge-success">Active</div>
                </td>
                <td>
                    <div class="flex gap-1">
                        ${!site.ssl_enabled ? `
                            <button class="btn btn-success btn-xs" onclick="installSSL('${site.domain}')" title="Install SSL">
                                <i class="fas fa-lock"></i>
                            </button>
                        ` : ''}
                        <button class="btn btn-info btn-xs" onclick="editSiteConfig('${site.domain}')" title="Edit Config">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-warning btn-xs" onclick="viewSiteLogs('${site.domain}')" title="View Logs">
                            <i class="fas fa-file-alt"></i>
                        </button>
                        <button class="btn btn-error btn-xs" onclick="deleteSite('${site.domain}')" title="Delete Site">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    // Action functions
    async function testNginxConfig() {
        showToast('Testing Nginx configuration...', 'info');
        // This would call the API to test nginx config
        setTimeout(() => {
            showToast('Nginx configuration test passed!', 'success');
        }, 1000);
    }
    
    async function reloadNginx() {
        showToast('Reloading Nginx...', 'info');
        // This would call the API to reload nginx
        setTimeout(() => {
            showToast('Nginx reloaded successfully!', 'success');
        }, 1000);
    }
    
    async function restartNginx() {
        if (confirm('Restart Nginx? This may cause brief downtime.')) {
            showToast('Restarting Nginx...', 'warning');
            // This would call the API to restart nginx
            setTimeout(() => {
                showToast('Nginx restarted successfully!', 'success');
            }, 2000);
        }
    }
    
    async function createNewSite() {
        const subdomain = document.getElementById('new-subdomain').value;
        const port = document.getElementById('new-port').value;
        
        if (!subdomain || !port) {
            showToast('Please enter both subdomain and port', 'error');
            return;
        }
        
        showToast(`Creating site ${subdomain}.algofactory.in...`, 'info');
        
        // This would call the nginx_manager.py script
        setTimeout(() => {
            showToast(`Site ${subdomain}.algofactory.in created successfully!`, 'success');
            document.getElementById('new-subdomain').value = '';
            document.getElementById('new-port').value = '';
            loadSites();
        }, 2000);
    }
    
    async function installSSL(domain) {
        if (confirm(`Install SSL certificate for ${domain}?`)) {
            showToast(`Installing SSL for ${domain}...`, 'info');
            // This would call certbot
            setTimeout(() => {
                showToast(`SSL certificate installed for ${domain}!`, 'success');
                loadSites();
            }, 3000);
        }
    }
    
    function filterSites(filter) {
        let filteredSites = sitesData;
        
        switch (filter) {
            case 'ssl':
                filteredSites = sitesData.filter(site => site.ssl_enabled);
                break;
            case 'no-ssl':
                filteredSites = sitesData.filter(site => !site.ssl_enabled);
                break;
            case 'algofactory':
                filteredSites = sitesData.filter(site => site.domain.includes('algofactory.in'));
                break;
        }
        
        renderSitesTable(filteredSites);
    }
    
    function refreshSites() {
        showToast('Refreshing sites...', 'info');
        loadSites();
    }
    
    function editSiteConfig(domain) {
        document.getElementById('config-file-path').textContent = `/etc/nginx/sites-available/${domain}.conf`;
        document.getElementById('config-content').value = '# Loading configuration...';
        document.getElementById('config-editor-modal').showModal();
        
        // Load actual config content
        setTimeout(() => {
            document.getElementById('config-content').value = `# Nginx configuration for ${domain}
server {
    listen 80;
    server_name ${domain};
    
    location / {
        proxy_pass http://127.0.0.1:8010;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}`;
        }, 500);
    }
    
    function closeConfigEditor() {
        document.getElementById('config-editor-modal').close();
    }
    
    function saveConfiguration() {
        showToast('Saving configuration...', 'info');
        // This would save the config and test it
        setTimeout(() => {
            showToast('Configuration saved and tested successfully!', 'success');
            closeConfigEditor();
        }, 1000);
    }
    
    function viewSiteLogs(domain) {
        document.getElementById('nginx-logs-modal').showModal();
        document.getElementById('nginx-logs-content').textContent = 'Loading logs...';
        
        // Load logs
        setTimeout(() => {
            document.getElementById('nginx-logs-content').innerHTML = `
                <div class="text-sm">
                    <div class="text-success">[2025-06-24 00:15:23] 200 GET / - ${domain}</div>
                    <div class="text-info">[2025-06-24 00:15:20] 200 GET /static/css/style.css - ${domain}</div>
                    <div class="text-warning">[2025-06-24 00:15:18] 404 GET /favicon.ico - ${domain}</div>
                    <div class="text-success">[2025-06-24 00:15:15] 200 GET / - ${domain}</div>
                </div>
            `;
        }, 500);
    }
    
    function closeLogsModal() {
        document.getElementById('nginx-logs-modal').close();
    }
    
    function bulkSSLInstall() {
        if (confirm('Install SSL certificates for all sites without SSL?')) {
            showToast('Starting bulk SSL installation...', 'info');
            // This would run bulk SSL installation
            setTimeout(() => {
                showToast('Bulk SSL installation completed!', 'success');
                loadSites();
            }, 5000);
        }
    }
</script>
{% endblock %}
