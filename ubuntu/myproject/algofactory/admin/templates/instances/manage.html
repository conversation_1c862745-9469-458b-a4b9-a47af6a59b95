{% extends "base.html" %}

{% block title %}AlgoFactory Instances - AlgoFactory Admin{% endblock %}
{% block page_title %}AlgoFactory Instance Management{% endblock %}

{% block content %}
<!-- Instance Overview -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="stat bg-base-200 rounded-xl shadow-sm">
        <div class="stat-figure text-success">
            <i class="fas fa-play-circle text-3xl"></i>
        </div>
        <div class="stat-title">Running Instances</div>
        <div class="stat-value text-success" id="running-count">0</div>
        <div class="stat-desc">Active processes</div>
    </div>
    
    <div class="stat bg-base-200 rounded-xl shadow-sm">
        <div class="stat-figure text-error">
            <i class="fas fa-stop-circle text-3xl"></i>
        </div>
        <div class="stat-title">Stopped Instances</div>
        <div class="stat-value text-error" id="stopped-count">0</div>
        <div class="stat-desc">Inactive processes</div>
    </div>
    
    <div class="stat bg-base-200 rounded-xl shadow-sm">
        <div class="stat-figure text-info">
            <i class="fas fa-memory text-3xl"></i>
        </div>
        <div class="stat-title">Total Memory</div>
        <div class="stat-value text-info" id="total-memory">0MB</div>
        <div class="stat-desc">Used by instances</div>
    </div>
    
    <div class="stat bg-base-200 rounded-xl shadow-sm">
        <div class="stat-figure text-warning">
            <i class="fas fa-network-wired text-3xl"></i>
        </div>
        <div class="stat-title">Active Ports</div>
        <div class="stat-value text-warning" id="active-ports">0</div>
        <div class="stat-desc">In use</div>
    </div>
</div>

<!-- Quick Actions -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
    <div class="card bg-base-200 shadow-sm">
        <div class="card-body">
            <h2 class="card-title">
                <i class="fas fa-plus text-success"></i>
                Create New Instance
            </h2>
            <div class="space-y-3">
                <input type="text" placeholder="Instance ID (e.g., 8014)" class="input input-bordered input-sm w-full" id="new-instance-id">
                <input type="number" placeholder="Flask Port (e.g., 8014)" class="input input-bordered input-sm w-full" id="new-flask-port">
                <input type="number" placeholder="WebSocket Port (e.g., 20014)" class="input input-bordered input-sm w-full" id="new-ws-port">
                <button class="btn btn-success btn-sm w-full" onclick="createNewInstance()">
                    <i class="fas fa-plus"></i>
                    Create Instance
                </button>
            </div>
        </div>
    </div>
    
    <div class="card bg-base-200 shadow-sm">
        <div class="card-body">
            <h2 class="card-title">
                <i class="fas fa-cogs text-primary"></i>
                Bulk Operations
            </h2>
            <div class="space-y-3">
                <button class="btn btn-primary btn-sm w-full" onclick="startAllInstances()">
                    <i class="fas fa-play"></i>
                    Start All
                </button>
                <button class="btn btn-warning btn-sm w-full" onclick="restartAllInstances()">
                    <i class="fas fa-redo"></i>
                    Restart All
                </button>
                <button class="btn btn-error btn-sm w-full" onclick="stopAllInstances()">
                    <i class="fas fa-stop"></i>
                    Stop All
                </button>
            </div>
        </div>
    </div>
    
    <div class="card bg-base-200 shadow-sm">
        <div class="card-body">
            <h2 class="card-title">
                <i class="fas fa-search text-info"></i>
                Port Scanner
            </h2>
            <div class="space-y-3">
                <input type="number" placeholder="Start Port (e.g., 8000)" class="input input-bordered input-sm w-full" id="scan-start-port">
                <input type="number" placeholder="End Port (e.g., 9000)" class="input input-bordered input-sm w-full" id="scan-end-port">
                <button class="btn btn-info btn-sm w-full" onclick="scanPorts()">
                    <i class="fas fa-search"></i>
                    Scan Ports
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Instances Table -->
<div class="card bg-base-200 shadow-sm">
    <div class="card-body">
        <div class="flex justify-between items-center mb-6">
            <h2 class="card-title">
                <i class="fas fa-cubes text-primary"></i>
                Instance Management
            </h2>
            <div class="flex gap-2">
                <button class="btn btn-ghost btn-sm" onclick="refreshInstances()">
                    <i class="fas fa-sync-alt"></i>
                    Refresh
                </button>
                <div class="dropdown dropdown-end">
                    <div tabindex="0" role="button" class="btn btn-ghost btn-sm">
                        <i class="fas fa-filter"></i>
                        Filter
                    </div>
                    <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                        <li><a onclick="filterInstances('all')">All Instances</a></li>
                        <li><a onclick="filterInstances('running')">Running Only</a></li>
                        <li><a onclick="filterInstances('stopped')">Stopped Only</a></li>
                        <li><a onclick="filterInstances('high-memory')">High Memory</a></li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="table table-zebra w-full">
                <thead>
                    <tr>
                        <th>Instance ID</th>
                        <th>Status</th>
                        <th>Ports</th>
                        <th>Resources</th>
                        <th>Uptime</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="instances-table">
                    <tr>
                        <td colspan="6" class="text-center text-base-content/50">
                            <i class="fas fa-spinner fa-spin mr-2"></i>
                            Loading instances...
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Port Usage Table -->
<div class="card bg-base-200 shadow-sm mt-8">
    <div class="card-body">
        <div class="flex justify-between items-center mb-6">
            <h2 class="card-title">
                <i class="fas fa-network-wired text-warning"></i>
                Port Usage
            </h2>
            <button class="btn btn-ghost btn-sm" onclick="refreshPorts()">
                <i class="fas fa-sync-alt"></i>
                Refresh
            </button>
        </div>
        
        <div class="overflow-x-auto">
            <table class="table table-zebra w-full">
                <thead>
                    <tr>
                        <th>Port</th>
                        <th>Process</th>
                        <th>PID</th>
                        <th>Type</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="ports-table">
                    <tr>
                        <td colspan="5" class="text-center text-base-content/50">
                            <i class="fas fa-spinner fa-spin mr-2"></i>
                            Loading port information...
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Instance Logs Modal -->
<dialog id="instance-logs-modal" class="modal">
    <div class="modal-box w-11/12 max-w-4xl">
        <h3 class="font-bold text-lg mb-4">
            <i class="fas fa-file-alt"></i>
            Instance Logs - <span id="logs-instance-id"></span>
        </h3>
        <div class="bg-base-300 rounded-lg p-4 h-96 overflow-y-auto font-mono text-sm" id="instance-logs-content">
            Loading logs...
        </div>
        <div class="modal-action">
            <button class="btn btn-primary" onclick="downloadInstanceLogs()">
                <i class="fas fa-download"></i>
                Download
            </button>
            <button class="btn btn-ghost" onclick="closeLogsModal()">Close</button>
        </div>
    </div>
</dialog>

<!-- Create Instance Modal -->
<dialog id="create-instance-modal" class="modal">
    <div class="modal-box">
        <h3 class="font-bold text-lg mb-4">
            <i class="fas fa-plus"></i>
            Create New AlgoFactory Instance
        </h3>
        <div class="space-y-4">
            <div class="form-control">
                <label class="label">
                    <span class="label-text">Instance ID</span>
                </label>
                <input type="text" placeholder="e.g., 8014" class="input input-bordered w-full" id="modal-instance-id">
            </div>
            <div class="form-control">
                <label class="label">
                    <span class="label-text">Flask Port</span>
                </label>
                <input type="number" placeholder="e.g., 8014" class="input input-bordered w-full" id="modal-flask-port">
            </div>
            <div class="form-control">
                <label class="label">
                    <span class="label-text">WebSocket Port</span>
                </label>
                <input type="number" placeholder="e.g., 20014" class="input input-bordered w-full" id="modal-ws-port">
            </div>
            <div class="form-control">
                <label class="label">
                    <span class="label-text">ZMQ Port</span>
                </label>
                <input type="number" placeholder="e.g., 23014" class="input input-bordered w-full" id="modal-zmq-port">
            </div>
            <div class="form-control">
                <label class="label cursor-pointer">
                    <span class="label-text">Auto-start on boot</span>
                    <input type="checkbox" class="checkbox" id="modal-auto-start" checked>
                </label>
            </div>
            <div class="form-control">
                <label class="label cursor-pointer">
                    <span class="label-text">Create Nginx configuration</span>
                    <input type="checkbox" class="checkbox" id="modal-create-nginx" checked>
                </label>
            </div>
        </div>
        <div class="modal-action">
            <button class="btn btn-primary" onclick="confirmCreateInstance()">
                <i class="fas fa-plus"></i>
                Create Instance
            </button>
            <button class="btn btn-ghost" onclick="closeCreateModal()">Cancel</button>
        </div>
    </div>
</dialog>
{% endblock %}

{% block scripts %}
<script>
    let instancesData = [];
    let portsData = [];
    
    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
        loadInstances();
        loadPorts();
        
        // Listen for real-time updates
        window.addEventListener('systemUpdate', function(event) {
            if (event.detail.data && event.detail.data.instances) {
                updateInstancesData(event.detail.data.instances);
            }
        });
    });
    
    async function loadInstances() {
        try {
            const response = await fetch('/api/system/metrics');
            const data = await response.json();
            
            if (data.instances) {
                instancesData = data.instances;
                updateInstancesData(data.instances);
            }
        } catch (error) {
            console.error('Error loading instances:', error);
            showToast('Failed to load instances', 'error');
        }
    }
    
    function updateInstancesData(instances) {
        instancesData = instances;
        
        // Update overview stats
        const runningCount = instances.length;
        const stoppedCount = 0; // This would come from database
        const totalMemory = instances.reduce((sum, inst) => sum + (inst.memory_mb || 0), 0);
        const activePorts = instances.length * 3; // Flask + WebSocket + ZMQ
        
        document.getElementById('running-count').textContent = runningCount;
        document.getElementById('stopped-count').textContent = stoppedCount;
        document.getElementById('total-memory').textContent = totalMemory + 'MB';
        document.getElementById('active-ports').textContent = activePorts;
        
        // Update table
        renderInstancesTable(instances);
    }
    
    function renderInstancesTable(instances) {
        const tbody = document.getElementById('instances-table');
        
        if (instances.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center text-base-content/50">
                        <i class="fas fa-info-circle mr-2"></i>
                        No AlgoFactory instances running
                    </td>
                </tr>
            `;
            return;
        }
        
        tbody.innerHTML = instances.map(instance => `
            <tr>
                <td>
                    <div class="font-bold">${instance.instance_id}</div>
                    <div class="text-sm text-base-content/70">PID: ${instance.pid}</div>
                </td>
                <td>
                    <div class="badge badge-success">
                        <i class="fas fa-circle text-xs mr-1"></i>
                        Running
                    </div>
                </td>
                <td>
                    <div class="text-sm">
                        <div><strong>Flask:</strong> ${instance.port}</div>
                        <div><strong>WS:</strong> ${instance.websocket_port}</div>
                        <div><strong>ZMQ:</strong> ${instance.zmq_port}</div>
                    </div>
                </td>
                <td>
                    <div class="text-sm">
                        <div><strong>CPU:</strong> ${instance.cpu_percent}%</div>
                        <div><strong>RAM:</strong> ${instance.memory_mb}MB</div>
                    </div>
                </td>
                <td>
                    <div class="text-sm">
                        ${formatUptime(instance.uptime_seconds)}
                    </div>
                </td>
                <td>
                    <div class="flex gap-1">
                        <button class="btn btn-info btn-xs" onclick="viewInstanceLogs('${instance.instance_id}')" title="View Logs">
                            <i class="fas fa-file-alt"></i>
                        </button>
                        <button class="btn btn-warning btn-xs" onclick="restartInstance('${instance.instance_id}')" title="Restart">
                            <i class="fas fa-redo"></i>
                        </button>
                        <button class="btn btn-error btn-xs" onclick="stopInstance('${instance.instance_id}')" title="Stop">
                            <i class="fas fa-stop"></i>
                        </button>
                        <button class="btn btn-ghost btn-xs" onclick="openInstanceUrl('${instance.port}')" title="Open">
                            <i class="fas fa-external-link-alt"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    async function loadPorts() {
        // Mock port data - in real implementation, this would come from the server
        portsData = [
            { port: 22, process: 'sshd', pid: 1234, type: 'System' },
            { port: 80, process: 'nginx', pid: 5678, type: 'Web Server' },
            { port: 443, process: 'nginx', pid: 5678, type: 'Web Server' },
            { port: 8010, process: 'python3', pid: 9012, type: 'AlgoFactory' },
            { port: 8011, process: 'python3', pid: 9013, type: 'AlgoFactory' },
            { port: 8012, process: 'python3', pid: 9014, type: 'AlgoFactory' },
            { port: 9001, process: 'python3', pid: 9015, type: 'Admin Panel' }
        ];
        
        renderPortsTable(portsData);
    }
    
    function renderPortsTable(ports) {
        const tbody = document.getElementById('ports-table');
        
        tbody.innerHTML = ports.map(port => `
            <tr>
                <td>
                    <div class="font-bold">${port.port}</div>
                </td>
                <td>
                    <div class="font-medium">${port.process}</div>
                </td>
                <td>
                    <div class="text-sm">${port.pid}</div>
                </td>
                <td>
                    <div class="badge ${getPortTypeBadge(port.type)}">${port.type}</div>
                </td>
                <td>
                    <div class="flex gap-1">
                        ${port.type === 'AlgoFactory' ? `
                            <button class="btn btn-warning btn-xs" onclick="killProcess(${port.pid})" title="Kill Process">
                                <i class="fas fa-times"></i>
                            </button>
                        ` : ''}
                        <button class="btn btn-info btn-xs" onclick="testPort(${port.port})" title="Test Connection">
                            <i class="fas fa-plug"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    function getPortTypeBadge(type) {
        switch (type) {
            case 'AlgoFactory': return 'badge-primary';
            case 'Admin Panel': return 'badge-secondary';
            case 'Web Server': return 'badge-accent';
            case 'System': return 'badge-neutral';
            default: return 'badge-ghost';
        }
    }
    
    // Action functions
    function createNewInstance() {
        document.getElementById('create-instance-modal').showModal();
    }
    
    function closeCreateModal() {
        document.getElementById('create-instance-modal').close();
    }
    
    async function confirmCreateInstance() {
        const instanceId = document.getElementById('modal-instance-id').value;
        const flaskPort = document.getElementById('modal-flask-port').value;
        const wsPort = document.getElementById('modal-ws-port').value;
        const zmqPort = document.getElementById('modal-zmq-port').value;
        
        if (!instanceId || !flaskPort) {
            showToast('Please fill in required fields', 'error');
            return;
        }
        
        showToast(`Creating instance ${instanceId}...`, 'info');
        
        // This would call the API to create the instance
        setTimeout(() => {
            showToast(`Instance ${instanceId} created successfully!`, 'success');
            closeCreateModal();
            loadInstances();
        }, 2000);
    }
    
    async function stopInstance(instanceId) {
        if (confirm(`Stop instance ${instanceId}?`)) {
            showToast(`Stopping instance ${instanceId}...`, 'warning');
            
            try {
                const response = await fetch('/api/instances/manage', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'stop', instance_id: instanceId })
                });
                
                const result = await response.json();
                if (result.success) {
                    showToast(result.message, 'success');
                    loadInstances();
                } else {
                    showToast(result.message, 'error');
                }
            } catch (error) {
                showToast('Failed to stop instance', 'error');
            }
        }
    }
    
    async function restartInstance(instanceId) {
        if (confirm(`Restart instance ${instanceId}?`)) {
            showToast(`Restarting instance ${instanceId}...`, 'info');
            
            try {
                const response = await fetch('/api/instances/manage', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'restart', instance_id: instanceId })
                });
                
                const result = await response.json();
                showToast(result.message, result.success ? 'success' : 'error');
                if (result.success) loadInstances();
            } catch (error) {
                showToast('Failed to restart instance', 'error');
            }
        }
    }
    
    function viewInstanceLogs(instanceId) {
        document.getElementById('logs-instance-id').textContent = instanceId;
        document.getElementById('instance-logs-modal').showModal();
        document.getElementById('instance-logs-content').textContent = 'Loading logs...';
        
        // Load logs
        setTimeout(() => {
            document.getElementById('instance-logs-content').innerHTML = `
                <div class="text-sm">
                    <div class="text-info">[2025-06-24 00:15:23] INFO: AlgoFactory instance ${instanceId} started</div>
                    <div class="text-success">[2025-06-24 00:15:20] INFO: WebSocket server listening on port 20${instanceId}</div>
                    <div class="text-success">[2025-06-24 00:15:18] INFO: Flask server listening on port ${instanceId}</div>
                    <div class="text-warning">[2025-06-24 00:15:15] WARNING: High memory usage detected</div>
                    <div class="text-success">[2025-06-24 00:15:10] INFO: Database connection established</div>
                </div>
            `;
        }, 500);
    }
    
    function closeLogsModal() {
        document.getElementById('instance-logs-modal').close();
    }
    
    function openInstanceUrl(port) {
        window.open(`http://localhost:${port}`, '_blank');
    }
    
    function killProcess(pid) {
        if (confirm(`Kill process ${pid}? This will stop the associated service.`)) {
            showToast(`Killing process ${pid}...`, 'warning');
            // This would call the API to kill the process
            setTimeout(() => {
                showToast(`Process ${pid} terminated`, 'success');
                loadPorts();
                loadInstances();
            }, 1000);
        }
    }
    
    function testPort(port) {
        showToast(`Testing connection to port ${port}...`, 'info');
        // This would test the port connection
        setTimeout(() => {
            showToast(`Port ${port} is responding`, 'success');
        }, 1000);
    }
    
    function scanPorts() {
        const startPort = document.getElementById('scan-start-port').value;
        const endPort = document.getElementById('scan-end-port').value;
        
        if (!startPort || !endPort) {
            showToast('Please enter start and end ports', 'error');
            return;
        }
        
        showToast(`Scanning ports ${startPort}-${endPort}...`, 'info');
        // This would scan the port range
        setTimeout(() => {
            showToast(`Port scan completed. Found 5 open ports.`, 'success');
        }, 2000);
    }
    
    function refreshInstances() {
        showToast('Refreshing instances...', 'info');
        loadInstances();
    }
    
    function refreshPorts() {
        showToast('Refreshing port information...', 'info');
        loadPorts();
    }
    
    // Utility functions
    function formatUptime(seconds) {
        const days = Math.floor(seconds / 86400);
        const hours = Math.floor((seconds % 86400) / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        
        if (days > 0) return `${days}d ${hours}h`;
        if (hours > 0) return `${hours}h ${minutes}m`;
        return `${minutes}m`;
    }
</script>
{% endblock %}
