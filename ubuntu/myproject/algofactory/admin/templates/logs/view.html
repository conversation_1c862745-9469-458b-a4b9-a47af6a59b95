{% extends "base.html" %}

{% block title %}System Logs - AlgoFactory Admin{% endblock %}
{% block page_title %}System Logs{% endblock %}

{% block content %}
<!-- Log Sources -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="stat bg-base-200 rounded-xl shadow-sm cursor-pointer" onclick="loadLogs('admin')">
        <div class="stat-figure text-primary">
            <i class="fas fa-user-shield text-3xl"></i>
        </div>
        <div class="stat-title">Admin Logs</div>
        <div class="stat-value text-primary" id="admin-log-count">0</div>
        <div class="stat-desc">Recent entries</div>
    </div>
    
    <div class="stat bg-base-200 rounded-xl shadow-sm cursor-pointer" onclick="loadLogs('nginx')">
        <div class="stat-figure text-secondary">
            <i class="fas fa-globe text-3xl"></i>
        </div>
        <div class="stat-title">Nginx Logs</div>
        <div class="stat-value text-secondary" id="nginx-log-count">0</div>
        <div class="stat-desc">Access & Error</div>
    </div>
    
    <div class="stat bg-base-200 rounded-xl shadow-sm cursor-pointer" onclick="loadLogs('system')">
        <div class="stat-figure text-accent">
            <i class="fas fa-server text-3xl"></i>
        </div>
        <div class="stat-title">System Logs</div>
        <div class="stat-value text-accent" id="system-log-count">0</div>
        <div class="stat-desc">Syslog entries</div>
    </div>
    
    <div class="stat bg-base-200 rounded-xl shadow-sm cursor-pointer" onclick="loadLogs('algofactory')">
        <div class="stat-figure text-info">
            <i class="fas fa-cubes text-3xl"></i>
        </div>
        <div class="stat-title">AlgoFactory Logs</div>
        <div class="stat-value text-info" id="algofactory-log-count">0</div>
        <div class="stat-desc">Instance logs</div>
    </div>
</div>

<!-- Log Viewer -->
<div class="card bg-base-200 shadow-sm">
    <div class="card-body">
        <div class="flex justify-between items-center mb-6">
            <h2 class="card-title">
                <i class="fas fa-file-alt text-primary"></i>
                Log Viewer - <span id="current-log-source">Select a log source</span>
            </h2>
            <div class="flex gap-2">
                <select class="select select-bordered select-sm" id="log-level-filter">
                    <option value="all">All Levels</option>
                    <option value="error">Error</option>
                    <option value="warning">Warning</option>
                    <option value="info">Info</option>
                    <option value="debug">Debug</option>
                </select>
                <button class="btn btn-ghost btn-sm" onclick="refreshLogs()">
                    <i class="fas fa-sync-alt"></i>
                    Refresh
                </button>
                <button class="btn btn-primary btn-sm" onclick="downloadLogs()">
                    <i class="fas fa-download"></i>
                    Download
                </button>
            </div>
        </div>
        
        <div class="bg-base-300 rounded-lg p-4 h-96 overflow-y-auto font-mono text-sm" id="log-content">
            <div class="text-center text-base-content/50 mt-20">
                <i class="fas fa-file-alt text-4xl mb-4"></i>
                <div>Select a log source above to view logs</div>
            </div>
        </div>
        
        <div class="flex justify-between items-center mt-4">
            <div class="text-sm text-base-content/70">
                <span id="log-info">Ready to load logs</span>
            </div>
            <div class="flex gap-2">
                <label class="label cursor-pointer">
                    <span class="label-text mr-2">Auto-refresh</span>
                    <input type="checkbox" class="toggle toggle-sm" id="auto-refresh-toggle" onchange="toggleAutoRefresh()">
                </label>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    let currentLogSource = null;
    let autoRefreshInterval = null;
    
    document.addEventListener('DOMContentLoaded', function() {
        loadLogCounts();
    });
    
    async function loadLogCounts() {
        // Mock log counts
        document.getElementById('admin-log-count').textContent = '156';
        document.getElementById('nginx-log-count').textContent = '2,341';
        document.getElementById('system-log-count').textContent = '892';
        document.getElementById('algofactory-log-count').textContent = '445';
    }
    
    async function loadLogs(source) {
        currentLogSource = source;
        document.getElementById('current-log-source').textContent = source.charAt(0).toUpperCase() + source.slice(1) + ' Logs';
        
        const logContent = document.getElementById('log-content');
        logContent.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin mr-2"></i>Loading logs...</div>';
        
        // Mock log data based on source
        setTimeout(() => {
            let logs = '';
            const now = new Date();
            
            switch (source) {
                case 'admin':
                    logs = generateAdminLogs();
                    break;
                case 'nginx':
                    logs = generateNginxLogs();
                    break;
                case 'system':
                    logs = generateSystemLogs();
                    break;
                case 'algofactory':
                    logs = generateAlgoFactoryLogs();
                    break;
            }
            
            logContent.innerHTML = logs;
            logContent.scrollTop = logContent.scrollHeight;
            
            document.getElementById('log-info').textContent = `Loaded ${source} logs - Last updated: ${now.toLocaleTimeString()}`;
        }, 1000);
    }
    
    function generateAdminLogs() {
        return `
            <div class="space-y-1">
                <div class="text-info">[2025-06-24 06:32:50] INFO: Admin dashboard started successfully</div>
                <div class="text-success">[2025-06-24 06:32:49] INFO: Database connection established</div>
                <div class="text-info">[2025-06-24 06:32:48] INFO: System monitoring initialized</div>
                <div class="text-success">[2025-06-24 06:30:15] INFO: User admin logged in from 172.31.14.191</div>
                <div class="text-warning">[2025-06-24 06:25:30] WARNING: High memory usage detected: 85%</div>
                <div class="text-info">[2025-06-24 06:20:45] INFO: System optimization completed</div>
                <div class="text-success">[2025-06-24 06:15:20] INFO: SSL certificate renewed for admin.algofactory.in</div>
                <div class="text-info">[2025-06-24 06:10:10] INFO: Nginx configuration reloaded</div>
                <div class="text-warning">[2025-06-24 06:05:55] WARNING: Instance 8012 stopped unexpectedly</div>
                <div class="text-success">[2025-06-24 06:05:56] INFO: Instance 8012 restarted automatically</div>
            </div>
        `;
    }
    
    function generateNginxLogs() {
        return `
            <div class="space-y-1">
                <div class="text-success">[2025-06-24 06:32:45] 200 GET /api/system/metrics - admin.algofactory.in</div>
                <div class="text-success">[2025-06-24 06:32:40] 200 GET /dashboard - admin.algofactory.in</div>
                <div class="text-success">[2025-06-24 06:32:35] 200 POST /login - admin.algofactory.in</div>
                <div class="text-success">[2025-06-24 06:30:20] 200 GET / - 8010.algofactory.in</div>
                <div class="text-success">[2025-06-24 06:30:15] 200 GET / - 8011.algofactory.in</div>
                <div class="text-warning">[2025-06-24 06:25:30] 404 GET /favicon.ico - 8012.algofactory.in</div>
                <div class="text-success">[2025-06-24 06:25:25] 200 GET /static/css/style.css - 8012.algofactory.in</div>
                <div class="text-success">[2025-06-24 06:25:20] 200 GET / - 8012.algofactory.in</div>
                <div class="text-error">[2025-06-24 06:20:15] 502 Bad Gateway - 8013.algofactory.in</div>
                <div class="text-success">[2025-06-24 06:15:10] 200 GET / - admin.algofactory.in</div>
            </div>
        `;
    }
    
    function generateSystemLogs() {
        return `
            <div class="space-y-1">
                <div class="text-info">[2025-06-24 06:32:50] systemd[1]: Started algofactory-admin.service</div>
                <div class="text-warning">[2025-06-24 06:31:25] systemd[1]: algofactory-admin.service: Failed with result 'exit-code'</div>
                <div class="text-info">[2025-06-24 06:30:00] systemd[1]: Started Daily apt download activities</div>
                <div class="text-info">[2025-06-24 06:25:30] kernel: [12345.678] TCP: request_sock_TCP: Possible SYN flooding</div>
                <div class="text-success">[2025-06-24 06:20:15] systemd[1]: Reloaded nginx.service</div>
                <div class="text-info">[2025-06-24 06:15:45] cron[1234]: (root) CMD (test -x /usr/sbin/anacron)</div>
                <div class="text-warning">[2025-06-24 06:10:30] systemd[1]: snapd.service: Killing process 5678</div>
                <div class="text-info">[2025-06-24 06:05:20] systemd[1]: Started Session 123 of user ubuntu</div>
                <div class="text-success">[2025-06-24 06:00:15] systemd[1]: Reached target Timers</div>
                <div class="text-info">[2025-06-24 05:55:10] systemd[1]: Starting Daily apt upgrade activities</div>
            </div>
        `;
    }
    
    function generateAlgoFactoryLogs() {
        return `
            <div class="space-y-1">
                <div class="text-success">[2025-06-24 06:32:30] Instance 8010: WebSocket connection established</div>
                <div class="text-info">[2025-06-24 06:30:45] Instance 8011: Processing trading signal</div>
                <div class="text-success">[2025-06-24 06:30:40] Instance 8012: Database sync completed</div>
                <div class="text-warning">[2025-06-24 06:25:55] Instance 8010: High CPU usage detected: 95%</div>
                <div class="text-info">[2025-06-24 06:25:30] Instance 8011: Market data updated</div>
                <div class="text-error">[2025-06-24 06:20:15] Instance 8012: Connection to external API failed</div>
                <div class="text-success">[2025-06-24 06:20:16] Instance 8012: Retrying API connection</div>
                <div class="text-success">[2025-06-24 06:20:18] Instance 8012: API connection restored</div>
                <div class="text-info">[2025-06-24 06:15:45] Instance 8010: Scheduled backup completed</div>
                <div class="text-warning">[2025-06-24 06:10:30] Instance 8011: Memory usage above threshold: 80%</div>
            </div>
        `;
    }
    
    function refreshLogs() {
        if (currentLogSource) {
            showToast('Refreshing logs...', 'info');
            loadLogs(currentLogSource);
        } else {
            showToast('Please select a log source first', 'warning');
        }
    }
    
    function downloadLogs() {
        if (!currentLogSource) {
            showToast('Please select a log source first', 'warning');
            return;
        }
        
        showToast(`Downloading ${currentLogSource} logs...`, 'info');
        
        // Create download link
        const logContent = document.getElementById('log-content').textContent;
        const blob = new Blob([logContent], { type: 'text/plain' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${currentLogSource}-logs-${new Date().toISOString().split('T')[0]}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
        
        showToast('Logs downloaded successfully', 'success');
    }
    
    function toggleAutoRefresh() {
        const toggle = document.getElementById('auto-refresh-toggle');
        
        if (toggle.checked) {
            if (currentLogSource) {
                autoRefreshInterval = setInterval(() => {
                    loadLogs(currentLogSource);
                }, 10000); // Refresh every 10 seconds
                showToast('Auto-refresh enabled (10s interval)', 'info');
            } else {
                toggle.checked = false;
                showToast('Please select a log source first', 'warning');
            }
        } else {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                showToast('Auto-refresh disabled', 'info');
            }
        }
    }
    
    // Filter logs by level
    document.getElementById('log-level-filter').addEventListener('change', function() {
        const level = this.value;
        const logContent = document.getElementById('log-content');
        const lines = logContent.querySelectorAll('div > div');
        
        lines.forEach(line => {
            if (level === 'all') {
                line.style.display = 'block';
            } else {
                const text = line.textContent.toLowerCase();
                if (text.includes(level.toLowerCase())) {
                    line.style.display = 'block';
                } else {
                    line.style.display = 'none';
                }
            }
        });
    });
</script>
{% endblock %}
