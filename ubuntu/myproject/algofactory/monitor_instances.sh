#!/bin/bash

# AlgoFactory 24/7 Instance Monitor
# Automatically monitors and restarts instances if they go down
# Logs all activities and sends alerts

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INSTANCES_DIR="/home/<USER>/algofactory-multi"
LOG_DIR="/var/log/algofactory-monitor"
LOG_FILE="$LOG_DIR/monitor.log"
ALERT_LOG="$LOG_DIR/alerts.log"

# Create log directory
sudo mkdir -p "$LOG_DIR"
sudo chown ubuntu:ubuntu "$LOG_DIR"

# Function to log messages
log_message() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
    
    # Also log alerts to separate file
    if [ "$level" = "ALERT" ] || [ "$level" = "ERROR" ]; then
        echo "[$timestamp] [$level] $message" >> "$ALERT_LOG"
    fi
}

# Function to check if instance is running
check_instance() {
    local instance_id=$1
    local instance_dir="$INSTANCES_DIR/algofactory-$instance_id"
    local pid_file="$instance_dir/app.pid"
    
    if [ ! -d "$instance_dir" ]; then
        return 1
    fi
    
    if [ ! -f "$pid_file" ]; then
        return 1
    fi
    
    local pid=$(cat "$pid_file")
    if ! kill -0 "$pid" 2>/dev/null; then
        return 1
    fi
    
    # Check if the port is responding
    local flask_port=$instance_id
    if ! curl -s --max-time 10 "http://localhost:$flask_port" > /dev/null; then
        return 1
    fi
    
    return 0
}

# Function to restart instance
restart_instance() {
    local instance_id=$1
    
    log_message "ALERT" "Restarting instance $instance_id"
    
    # Stop the instance
    cd "$SCRIPT_DIR"
    ./multi_instance.sh stop "$instance_id" 2>/dev/null || true
    
    # Wait a moment
    sleep 5
    
    # Start the instance
    if ./multi_instance.sh start "$instance_id"; then
        log_message "INFO" "Successfully restarted instance $instance_id"
        
        # Wait for startup and verify
        sleep 10
        if check_instance "$instance_id"; then
            log_message "INFO" "Instance $instance_id is healthy after restart"
        else
            log_message "ERROR" "Instance $instance_id failed health check after restart"
        fi
    else
        log_message "ERROR" "Failed to restart instance $instance_id"
    fi
}

# Function to check system resources
check_system_resources() {
    local memory_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    local disk_usage=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    
    log_message "INFO" "System Resources - Memory: ${memory_usage}%, Disk: ${disk_usage}%, Load: ${load_avg}"
    
    # Alert if resources are high
    if (( $(echo "$memory_usage > 90" | bc -l) )); then
        log_message "ALERT" "High memory usage: ${memory_usage}%"
    fi
    
    if [ "$disk_usage" -gt 90 ]; then
        log_message "ALERT" "High disk usage: ${disk_usage}%"
    fi
}

# Function to monitor all instances
monitor_instances() {
    local instances=(8010 8011 8012)
    local restart_count=0
    
    log_message "INFO" "Starting monitoring cycle"
    
    for instance_id in "${instances[@]}"; do
        if check_instance "$instance_id"; then
            log_message "INFO" "Instance $instance_id is healthy"
        else
            log_message "ALERT" "Instance $instance_id is down - attempting restart"
            restart_instance "$instance_id"
            ((restart_count++))
        fi
    done
    
    # Check system resources every cycle
    check_system_resources
    
    if [ $restart_count -gt 0 ]; then
        log_message "ALERT" "Monitoring cycle completed - $restart_count instances restarted"
    else
        log_message "INFO" "Monitoring cycle completed - all instances healthy"
    fi
}

# Function to run continuous monitoring
run_monitor() {
    log_message "INFO" "AlgoFactory 24/7 Monitor started"
    log_message "INFO" "Monitoring instances: 8010, 8011, 8012"
    log_message "INFO" "Check interval: 60 seconds"
    
    while true; do
        monitor_instances
        sleep 60
    done
}

# Function to show status
show_status() {
    echo "🔍 AlgoFactory Instance Monitor Status"
    echo "======================================"
    echo ""
    
    # Show running instances
    cd "$SCRIPT_DIR"
    ./multi_instance.sh list
    
    echo ""
    echo "📊 System Resources:"
    echo "   Memory: $(free -h | grep Mem | awk '{print $3 "/" $2}')"
    echo "   Disk: $(df -h / | tail -1 | awk '{print $3 "/" $2 " (" $5 " used)"}')"
    echo "   Load: $(uptime | awk -F'load average:' '{print $2}')"
    
    echo ""
    echo "📝 Recent Monitor Logs (last 10 lines):"
    if [ -f "$LOG_FILE" ]; then
        tail -10 "$LOG_FILE"
    else
        echo "   No logs found"
    fi
    
    echo ""
    echo "🚨 Recent Alerts (last 5):"
    if [ -f "$ALERT_LOG" ]; then
        tail -5 "$ALERT_LOG"
    else
        echo "   No alerts found"
    fi
}

# Function to show logs
show_logs() {
    local lines=${1:-50}
    
    echo "📝 Monitor Logs (last $lines lines):"
    echo "=================================="
    if [ -f "$LOG_FILE" ]; then
        tail -$lines "$LOG_FILE"
    else
        echo "No logs found"
    fi
}

# Function to show alerts
show_alerts() {
    local lines=${1:-20}
    
    echo "🚨 Alert Logs (last $lines lines):"
    echo "================================"
    if [ -f "$ALERT_LOG" ]; then
        tail -$lines "$ALERT_LOG"
    else
        echo "No alerts found"
    fi
}

# Function to install as systemd service
install_service() {
    log_message "INFO" "Installing AlgoFactory Monitor as systemd service"
    
    # Create systemd service file
    sudo tee /etc/systemd/system/algofactory-monitor.service > /dev/null << EOF
[Unit]
Description=AlgoFactory 24/7 Instance Monitor
After=network.target
Wants=network.target

[Service]
Type=simple
User=ubuntu
Group=ubuntu
WorkingDirectory=$SCRIPT_DIR
ExecStart=$SCRIPT_DIR/monitor_instances.sh run
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
    
    # Reload systemd and enable service
    sudo systemctl daemon-reload
    sudo systemctl enable algofactory-monitor.service
    
    log_message "INFO" "Service installed successfully"
    echo "✅ AlgoFactory Monitor installed as systemd service"
    echo ""
    echo "🚀 To start the service:"
    echo "   sudo systemctl start algofactory-monitor"
    echo ""
    echo "📊 To check status:"
    echo "   sudo systemctl status algofactory-monitor"
    echo ""
    echo "📝 To view logs:"
    echo "   sudo journalctl -u algofactory-monitor -f"
}

# Function to display usage
usage() {
    echo "AlgoFactory 24/7 Instance Monitor"
    echo "================================"
    echo ""
    echo "Usage: $0 <command>"
    echo ""
    echo "Commands:"
    echo "  run              Start continuous monitoring (foreground)"
    echo "  status           Show current status of all instances"
    echo "  logs [lines]     Show monitor logs (default: 50 lines)"
    echo "  alerts [lines]   Show alert logs (default: 20 lines)"
    echo "  install          Install as systemd service for 24/7 operation"
    echo "  check            Run single monitoring check"
    echo ""
    echo "Examples:"
    echo "  $0 run           # Start monitoring in foreground"
    echo "  $0 status        # Show current status"
    echo "  $0 logs 100      # Show last 100 log lines"
    echo "  $0 install       # Install as system service"
    echo ""
    exit 1
}

# Main script logic
case "${1:-}" in
    run)
        run_monitor
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs "${2:-50}"
        ;;
    alerts)
        show_alerts "${2:-20}"
        ;;
    install)
        install_service
        ;;
    check)
        monitor_instances
        ;;
    *)
        usage
        ;;
esac
