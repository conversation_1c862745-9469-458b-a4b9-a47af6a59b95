#!/usr/bin/env python3

"""
Simple WebSocket Port Test for AlgoFactory Instances
Tests if WebSocket ports are listening and accessible
"""

import socket
import sys

def test_port_connection(host, port, instance_id):
    """Test if a port is listening and accepting connections"""
    try:
        print(f"🔌 Testing connection to instance {instance_id} at {host}:{port}")
        
        # Create a socket connection
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)  # 5 second timeout
        
        # Try to connect
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print(f"✅ Instance {instance_id} WebSocket port {port} is listening and accessible")
            return True
        else:
            print(f"❌ Instance {instance_id} WebSocket port {port} is not accessible (error code: {result})")
            return False
            
    except socket.gaierror as e:
        print(f"❌ DNS/Host error for instance {instance_id}: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing instance {instance_id}: {e}")
        return False

def test_all_instances():
    """Test WebSocket ports for all instances"""
    instances = [
        {"id": "8010", "port": 20010},
        {"id": "8011", "port": 20011},
        {"id": "8012", "port": 20012}
    ]
    
    print("🚀 AlgoFactory WebSocket Port Test")
    print("=" * 40)
    
    results = []
    
    for instance in instances:
        instance_id = instance["id"]
        port = instance["port"]
        
        result = test_port_connection("localhost", port, instance_id)
        results.append({
            "instance": instance_id,
            "port": port,
            "success": result
        })
        
        print()  # Add spacing between tests
    
    # Summary
    print("📊 Test Results Summary:")
    print("=" * 30)
    
    success_count = 0
    for result in results:
        status = "✅ PASS" if result["success"] else "❌ FAIL"
        print(f"Instance {result['instance']} (port {result['port']}): {status}")
        if result["success"]:
            success_count += 1
    
    print()
    print(f"🎯 Overall Result: {success_count}/{len(instances)} instances passed")
    
    if success_count == len(instances):
        print("🎉 All WebSocket ports are accessible!")
        return True
    else:
        print("⚠️  Some WebSocket ports have issues")
        return False

def test_single_instance(instance_id):
    """Test a single instance WebSocket port"""
    port = int(instance_id) + 12000  # Calculate WebSocket port
    
    print(f"🔌 Testing single instance {instance_id}")
    print("=" * 40)
    
    result = test_port_connection("localhost", port, instance_id)
    
    if result:
        print(f"✅ Instance {instance_id} WebSocket port is accessible!")
    else:
        print(f"❌ Instance {instance_id} WebSocket port has issues!")
    
    return result

def main():
    """Main function"""
    if len(sys.argv) > 1:
        # Test specific instance
        instance_id = sys.argv[1]
        try:
            result = test_single_instance(instance_id)
            sys.exit(0 if result else 1)
        except KeyboardInterrupt:
            print("\n🛑 Test interrupted by user")
            sys.exit(1)
    else:
        # Test all instances
        try:
            result = test_all_instances()
            sys.exit(0 if result else 1)
        except KeyboardInterrupt:
            print("\n🛑 Test interrupted by user")
            sys.exit(1)

if __name__ == "__main__":
    main()
