#!/usr/bin/env python3

"""
WebSocket Connection Test for AlgoFactory Instances
Tests WebSocket connectivity for all running instances
"""

import asyncio
import websockets
import json
import sys

async def test_websocket_connection(host, port, instance_id):
    """Test WebSocket connection to a specific instance"""
    uri = f"ws://{host}:{port}"
    
    try:
        print(f"🔌 Testing WebSocket connection to instance {instance_id} at {uri}")
        
        # Connect to WebSocket server
        async with websockets.connect(uri, timeout=10) as websocket:
            print(f"✅ Connected to instance {instance_id} WebSocket server")
            
            # Send a test message
            test_message = {
                "action": "get_supported_brokers"
            }
            
            await websocket.send(json.dumps(test_message))
            print(f"📤 Sent test message to instance {instance_id}")
            
            # Wait for response
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                response_data = json.loads(response)
                print(f"📥 Received response from instance {instance_id}: {response_data.get('type', 'unknown')}")
                
                if response_data.get('type') == 'supported_brokers':
                    brokers = response_data.get('brokers', [])
                    print(f"🔧 Supported brokers: {len(brokers)} brokers available")
                    return True
                else:
                    print(f"⚠️  Unexpected response type: {response_data.get('type')}")
                    return True  # Still connected, just different response
                    
            except asyncio.TimeoutError:
                print(f"⏰ Timeout waiting for response from instance {instance_id}")
                return False
                
    except websockets.exceptions.ConnectionRefused:
        print(f"❌ Connection refused to instance {instance_id} at {uri}")
        return False
    except websockets.exceptions.InvalidURI:
        print(f"❌ Invalid URI for instance {instance_id}: {uri}")
        return False
    except Exception as e:
        print(f"❌ Error connecting to instance {instance_id}: {e}")
        return False

async def test_all_instances():
    """Test WebSocket connections for all instances"""
    instances = [
        {"id": "8010", "port": 20010},
        {"id": "8011", "port": 20011},
        {"id": "8012", "port": 20012}
    ]
    
    print("🚀 AlgoFactory WebSocket Connection Test")
    print("=" * 50)
    
    results = []
    
    for instance in instances:
        instance_id = instance["id"]
        port = instance["port"]
        
        result = await test_websocket_connection("localhost", port, instance_id)
        results.append({
            "instance": instance_id,
            "port": port,
            "success": result
        })
        
        print()  # Add spacing between tests
    
    # Summary
    print("📊 Test Results Summary:")
    print("=" * 30)
    
    success_count = 0
    for result in results:
        status = "✅ PASS" if result["success"] else "❌ FAIL"
        print(f"Instance {result['instance']} (port {result['port']}): {status}")
        if result["success"]:
            success_count += 1
    
    print()
    print(f"🎯 Overall Result: {success_count}/{len(instances)} instances passed")
    
    if success_count == len(instances):
        print("🎉 All WebSocket servers are working correctly!")
        return True
    else:
        print("⚠️  Some WebSocket servers have issues")
        return False

async def test_single_instance(instance_id):
    """Test a single instance WebSocket connection"""
    port = int(instance_id) + 12000  # Calculate WebSocket port
    
    print(f"🔌 Testing single instance {instance_id}")
    print("=" * 40)
    
    result = await test_websocket_connection("localhost", port, instance_id)
    
    if result:
        print(f"✅ Instance {instance_id} WebSocket is working correctly!")
    else:
        print(f"❌ Instance {instance_id} WebSocket has issues!")
    
    return result

def main():
    """Main function"""
    if len(sys.argv) > 1:
        # Test specific instance
        instance_id = sys.argv[1]
        try:
            result = asyncio.run(test_single_instance(instance_id))
            sys.exit(0 if result else 1)
        except KeyboardInterrupt:
            print("\n🛑 Test interrupted by user")
            sys.exit(1)
    else:
        # Test all instances
        try:
            result = asyncio.run(test_all_instances())
            sys.exit(0 if result else 1)
        except KeyboardInterrupt:
            print("\n🛑 Test interrupted by user")
            sys.exit(1)

if __name__ == "__main__":
    main()
