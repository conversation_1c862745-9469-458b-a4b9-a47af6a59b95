<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Add blocking script to set theme before any content loads -->
    <script>
        (function() {
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme) {
                document.documentElement.setAttribute('data-theme', savedTheme);
            }
        })();
    </script>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="AlgoFactory is your premier open source algorithmic trading platform, offering a robust and secure environment for developing and executing trading strategies.">
    <title>{% block title %}AlgoFactory{% endblock %}</title>

    <!-- Favicon and touch icons -->
    <link rel="shortcut icon" href="{{ url_for('static', filename='favicon/favicon.ico') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ url_for('static', filename='favicon/apple-touch-icon.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='favicon/favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ url_for('static', filename='favicon/favicon-16x16.png') }}">
    <link rel="manifest" href="{{ url_for('static', filename='favicon/site.webmanifest') }}">
    <link rel="mask-icon" href="{{ url_for('static', filename='favicon/safari-pinned-tab.svg') }}" color="#5bbad5">
    
    <!-- Compiled Tailwind CSS with DaisyUI -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">

    <!-- Socket.IO -->
    <script src="https://cdn.socket.io/4.0.0/socket.io.min.js"></script>
    <audio id="alert-sound" src="{{ url_for('static', filename='sounds/alert.mp3') }}" preload="auto"></audio>
    
    <!-- Theme Scripts -->
    <script src="{{ url_for('static', filename='js/theme.js') }}"></script>
    
    <!-- Meta tags for color settings -->
    <meta name="msapplication-TileColor" content="#da532c">
    <meta name="theme-color" content="#ffffff">

    {% block head %}{% endblock %}
</head>
<body class="min-h-screen bg-base-100">
    <div class="drawer">
        <input id="main-drawer" type="checkbox" class="drawer-toggle" />
        <div class="drawer-content flex flex-col min-h-screen">
            <!-- Fixed Navbar -->
            <div class="sticky top-0 z-30 flex h-16 w-full justify-center bg-base-100 bg-opacity-90 backdrop-blur transition-all duration-100">
                <nav class="navbar w-full">
                    {% include 'public_navbar.html' %}
                </nav>
            </div>

            <!-- Toast Container for Notifications -->
            <div class="toast toast-top toast-end z-50">
                <!-- Notifications will be dynamically added here -->
            </div>

            <!-- Flash Messages -->
            <div class="container mx-auto px-4 mt-4">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert {{ 'alert-success' if category == 'success' else 'alert-error' }} shadow-lg mb-4">
                                <div class="flex items-center">
                                    {% if category == 'success' %}
                                    <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    {% else %}
                                    <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    {% endif %}
                                    <span>{{ message }}</span>
                                    <button onclick="this.parentElement.parentElement.remove()" class="btn btn-ghost btn-sm btn-circle ml-auto">✕</button>
                                </div>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
            </div>

            <!-- Main Content -->
            <main class="flex-grow bg-base-100 mt-6">
                <div class="container mx-auto px-4">
                    {% block content %}{% endblock %}
                </div>
            </main>

            <!-- Footer -->
            <footer class="mt-auto bg-base-200">
                {% include 'footer.html' %}
            </footer>
        </div>

        <!-- Drawer Side (Mobile Menu) -->
        <div class="drawer-side">
            <label for="main-drawer" aria-label="close sidebar" class="drawer-overlay"></label>
            <div class="menu p-4 w-80 min-h-full bg-base-200">
                <!-- Sidebar content here -->
                <div class="flex items-center gap-2 mb-8">
                    <img src="{{ url_for('static', filename='favicon/logo.png') }}" alt="AlgoFactory" class="h-8 w-8">
                    <span class="text-xl font-semibold">AlgoFactory</span>
                </div>
                <div class="flex flex-col gap-2">
                    <a href="/" class="btn btn-ghost justify-start gap-2 {{ 'btn-active' if request.path == '/' }}">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                        </svg>
                        Home
                    </a>
                    <a href="/faq" class="btn btn-ghost justify-start gap-2 {{ 'btn-active' if request.path == '/faq' }}">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        FAQ
                    </a>
                    <a href="/download" class="btn btn-primary justify-start gap-2 {{ 'btn-active' if request.path == '/download' }}">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                        Download
                    </a>
                    <a href="https://algofactory.in/discord" class="btn btn-ghost justify-start gap-2" target="_blank" rel="noopener noreferrer">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z" />
                        </svg>
                        Community
                    </a>
                    <a href="https://algofactory.in/roadmap" class="btn btn-ghost justify-start gap-2" target="_blank" rel="noopener noreferrer">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                        </svg>
                        Roadmap
                    </a>
                    <a href="https://docs.algofactory.in" class="btn btn-ghost justify-start gap-2" target="_blank" rel="noopener noreferrer">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                        </svg>
                        Docs
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Toast notification function
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type} shadow-lg mb-4`;
            
            const content = document.createElement('div');
            content.className = 'flex items-center';
            
            // Icon based on type
            let icon = '';
            switch(type) {
                case 'success':
                    icon = '<svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>';
                    break;
                case 'error':
                    icon = '<svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>';
                    break;
                default:
                    icon = '<svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>';
            }
            
            content.innerHTML = `
                ${icon}
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="btn btn-ghost btn-sm btn-circle ml-auto">✕</button>
            `;
            
            toast.appendChild(content);
            document.querySelector('.toast').appendChild(toast);

            // Remove toast after 3 seconds
            setTimeout(() => {
                toast.classList.add('animate-out');
                setTimeout(() => {
                    toast.remove();
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
