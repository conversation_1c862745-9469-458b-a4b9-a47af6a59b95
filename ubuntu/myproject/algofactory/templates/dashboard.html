{% extends "base.html" %}

{% block content %}
<!-- Dashboard Header -->
<div class="mb-12">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-base-content">Trading Dashboard</h1>
            <p class="text-base-content/60 mt-2">Overview of your trading account and market positions</p>
        </div>
        <!-- Master Contract Status Indicator -->
        <div class="flex items-center gap-3 bg-base-200 rounded-lg px-4 py-3">
            <span class="text-sm font-medium">Master Contract Status:</span>
            <div class="flex items-center gap-2">
                <div id="master-contract-led" class="w-3 h-3 rounded-full bg-gray-400 animate-pulse"></div>
                <span id="master-contract-status-text" class="text-sm">Checking...</span>
            </div>
        </div>
    </div>
</div>

<!-- Stats Grid -->
<div class="grid grid-cols-1 sm:grid-cols-3 lg:grid-cols-5 gap-6 mb-16">
    <!-- Available Balance -->
    <div class="stat-card">
        <div class="stat">
            <div class="stat-title">Available Balance</div>
            <div class="stat-value text-primary">
                {{ margin_data.get('availablecash', '0.00') }}
            </div>
            <div class="stat-desc mt-2">
                <div class="badge badge-neutral">Cash Balance</div>
            </div>
        </div>
    </div>

    <!-- Collateral -->
    <div class="stat-card">
        <div class="stat">
            <div class="stat-title">Collateral</div>
            <div class="stat-value text-secondary">
                {{ margin_data.get('collateral', '0.00') }}
            </div>
            <div class="stat-desc mt-2">
                <div class="badge badge-neutral">Total Collateral</div>
            </div>
        </div>
    </div>

    <!-- Unrealized P&L -->
    <div class="stat-card">
        <div class="stat">
            <div class="stat-title">Unrealized P&L</div>
            <div class="stat-value {% if margin_data.get('m2munrealized', '0.00')|float > 0 %}text-success{% elif margin_data.get('m2munrealized', '0.00')|float < 0 %}text-error{% else %}text-neutral{% endif %}">
                {{ margin_data.get('m2munrealized', '0.00') }}
            </div>
            <div class="stat-desc mt-2">
                <div class="badge {% if margin_data.get('m2munrealized', '0.00')|float > 0 %}badge-success{% elif margin_data.get('m2munrealized', '0.00')|float < 0 %}badge-error{% else %}badge-neutral{% endif %}">
                    Mark to Market
                </div>
            </div>
        </div>
    </div>

    <!-- Realized P&L -->
    <div class="stat-card">
        <div class="stat">
            <div class="stat-title">Realized P&L</div>
            <div class="stat-value {% if margin_data.get('m2mrealized', '0.00')|float > 0 %}text-success{% elif margin_data.get('m2mrealized', '0.00')|float < 0 %}text-error{% else %}text-neutral{% endif %}">
                {{ margin_data.get('m2mrealized', '0.00') }}
            </div>
            <div class="stat-desc mt-2">
                <div class="badge {% if margin_data.get('m2mrealized', '0.00')|float > 0 %}badge-success{% elif margin_data.get('m2mrealized', '0.00')|float < 0 %}badge-error{% else %}badge-neutral{% endif %}">
                    Booked P&L
                </div>
            </div>
        </div>
    </div>

    <!-- Utilised Margin -->
    <div class="stat-card">
        <div class="stat">
            <div class="stat-title">Utilised Margin</div>
            <div class="stat-value text-accent">
                {{ margin_data.get('utiliseddebits', '0.00') }}
            </div>
            <div class="stat-desc mt-2">
                <div class="badge badge-accent">Used Margin</div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions Grid -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-8">
    <!-- Search Symbols -->
    <div class="card bg-base-100 shadow-lg">
        <div class="card-body">
            <h2 class="card-title flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                Search Symbols
            </h2>
            <p class="text-base-content/70">Search for trading symbols across different exchanges</p>
            <div class="card-actions justify-end mt-4">
                <a href="{{ url_for('search_bp.token') }}" class="btn btn-primary">Search Now</a>
            </div>
        </div>
    </div>

    <!-- View Logs -->
    <div class="card bg-base-100 shadow-lg">
        <div class="card-body">
            <h2 class="card-title flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                View Logs
            </h2>
            <p class="text-base-content/70">Monitor your trading activity and system logs</p>
            <div class="card-actions justify-end mt-4">
                <a href="{{ url_for('log_bp.view_logs') }}" class="btn btn-primary">View Logs</a>
            </div>
        </div>
    </div>

    <!-- Documentation -->
    <div class="card bg-base-100 shadow-lg">
        <div class="card-body">
            <h2 class="card-title flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
                Documentation
            </h2>
            <p class="text-base-content/70">Access detailed guides and API documentation</p>
            <div class="card-actions justify-end mt-4">
                <a href="https://docs.algofactory.in" target="_blank" rel="noopener noreferrer" class="btn btn-primary">View Docs</a>
            </div>
        </div>
    </div>
</div>

<script>
// Master Contract Status LED Controller
class MasterContractStatus {
    constructor() {
        this.led = document.getElementById('master-contract-led');
        this.statusText = document.getElementById('master-contract-status-text');
        this.checkInterval = null;
        this.init();
    }

    init() {
        // Check status immediately
        this.checkStatus();
        
        // Set up periodic checking every 5 seconds
        this.checkInterval = setInterval(() => this.checkStatus(), 5000);
        
        // Listen for WebSocket events
        if (typeof socket !== 'undefined') {
            socket.on('master_contract_download', (data) => {
                this.handleWebSocketUpdate(data);
            });
        }
    }

    async checkStatus() {
        try {
            const response = await fetch('/api/master-contract/status');
            const data = await response.json();
            this.updateDisplay(data);
        } catch (error) {
            console.error('Error checking master contract status:', error);
            this.updateDisplay({ status: 'error', message: 'Failed to check status' });
        }
    }

    handleWebSocketUpdate(data) {
        // WebSocket update received, fetch fresh status
        this.checkStatus();
    }

    updateDisplay(data) {
        const { status, message, total_symbols } = data;
        
        // Remove all animation classes first
        this.led.classList.remove('animate-pulse', 'animate-spin');
        
        switch (status) {
            case 'success':
                this.led.className = 'w-3 h-3 rounded-full bg-green-500';
                this.statusText.textContent = total_symbols ? `Ready (${total_symbols} symbols)` : 'Ready';
                this.statusText.className = 'text-sm text-green-600';
                // Clear interval once successful
                if (this.checkInterval) {
                    clearInterval(this.checkInterval);
                    this.checkInterval = null;
                }
                break;
                
            case 'downloading':
                this.led.className = 'w-3 h-3 rounded-full bg-yellow-500 animate-pulse';
                this.statusText.textContent = 'Downloading...';
                this.statusText.className = 'text-sm text-yellow-600';
                break;
                
            case 'error':
                this.led.className = 'w-3 h-3 rounded-full bg-red-500';
                this.statusText.textContent = 'Error';
                this.statusText.className = 'text-sm text-red-600';
                // Show detailed error in tooltip
                this.statusText.title = message || 'Download failed';
                break;
                
            case 'pending':
                this.led.className = 'w-3 h-3 rounded-full bg-gray-400 animate-pulse';
                this.statusText.textContent = 'Pending';
                this.statusText.className = 'text-sm text-gray-600';
                break;
                
            default:
                this.led.className = 'w-3 h-3 rounded-full bg-gray-400';
                this.statusText.textContent = 'Unknown';
                this.statusText.className = 'text-sm text-gray-600';
        }
    }

    destroy() {
        if (this.checkInterval) {
            clearInterval(this.checkInterval);
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.masterContractStatus = new MasterContractStatus();
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (window.masterContractStatus) {
        window.masterContractStatus.destroy();
    }
});
</script>
{% endblock %}
