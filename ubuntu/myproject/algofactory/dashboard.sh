#!/bin/bash

# AlgoFactory Dashboard - Quick Status Overview
# Shows real-time status of all instances and system health

clear

echo "🚀 AlgoFactory Multi-Instance Dashboard"
echo "========================================"
echo "$(date '+%Y-%m-%d %H:%M:%S UTC')"
echo ""

# Function to check HTTPS status
check_https() {
    local domain=$1
    local status=$(curl -s -o /dev/null -w "%{http_code}" --max-time 5 "https://$domain" 2>/dev/null)
    if [ "$status" = "200" ]; then
        echo "✅ HTTPS"
    else
        echo "❌ HTTPS"
    fi
}

# Function to check HTTP status
check_http() {
    local port=$1
    local status=$(curl -s -o /dev/null -w "%{http_code}" --max-time 5 "http://localhost:$port" 2>/dev/null)
    if [ "$status" = "200" ]; then
        echo "✅ HTTP"
    else
        echo "❌ HTTP"
    fi
}

# Instance status
echo "📊 Instance Status:"
echo "==================="
instances=(8010 8011 8012)

for instance in "${instances[@]}"; do
    domain="$instance.algofactory.in"
    
    # Check if process is running
    if pgrep -f "python.*app.py.*$instance" > /dev/null; then
        process_status="✅ Running"
        pid=$(pgrep -f "python.*app.py.*$instance")
    else
        process_status="❌ Stopped"
        pid="N/A"
    fi
    
    # Check HTTP and HTTPS
    http_status=$(check_http $instance)
    https_status=$(check_https $domain)
    
    echo "🔹 Instance $instance:"
    echo "   Domain: https://$domain"
    echo "   Process: $process_status (PID: $pid)"
    echo "   Local: $http_status | Public: $https_status"
    echo ""
done

# System Resources
echo "💻 System Resources:"
echo "===================="
memory_info=$(free -h | grep Mem)
memory_used=$(echo $memory_info | awk '{print $3}')
memory_total=$(echo $memory_info | awk '{print $2}')
memory_percent=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')

disk_info=$(df -h / | tail -1)
disk_used=$(echo $disk_info | awk '{print $3}')
disk_total=$(echo $disk_info | awk '{print $2}')
disk_percent=$(echo $disk_info | awk '{print $5}' | sed 's/%//')

load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')

echo "🧠 Memory: $memory_used / $memory_total (${memory_percent}%)"
echo "💾 Disk: $disk_used / $disk_total (${disk_percent}%)"
echo "⚡ Load: $load_avg"
echo ""

# Monitor Service Status
echo "🔍 Monitor Service:"
echo "=================="
if systemctl is-active --quiet algofactory-monitor; then
    echo "✅ AlgoFactory Monitor is running"
    uptime_info=$(systemctl show algofactory-monitor --property=ActiveEnterTimestamp | cut -d= -f2)
    echo "   Started: $uptime_info"
else
    echo "❌ AlgoFactory Monitor is not running"
    echo "   Run: sudo systemctl start algofactory-monitor"
fi
echo ""

# SSL Certificate Status
echo "🔒 SSL Certificate Status:"
echo "=========================="
for instance in "${instances[@]}"; do
    domain="$instance.algofactory.in"
    if [ -f "/etc/letsencrypt/live/$domain/fullchain.pem" ]; then
        expiry=$(openssl x509 -enddate -noout -in "/etc/letsencrypt/live/$domain/fullchain.pem" | cut -d= -f2)
        echo "✅ $domain - Expires: $expiry"
    else
        echo "❌ $domain - No certificate found"
    fi
done
echo ""

# Recent Alerts
echo "🚨 Recent Alerts (last 3):"
echo "=========================="
if [ -f "/var/log/algofactory-monitor/alerts.log" ]; then
    tail -3 /var/log/algofactory-monitor/alerts.log 2>/dev/null || echo "   No recent alerts"
else
    echo "   No alert log found"
fi
echo ""

# Quick Actions
echo "⚡ Quick Actions:"
echo "================"
echo "📊 View full status:     ./monitor_instances.sh status"
echo "📝 View monitor logs:    ./monitor_instances.sh logs"
echo "🚨 View alerts:          ./monitor_instances.sh alerts"
echo "🔄 Manual check:         ./monitor_instances.sh check"
echo "📋 List instances:       ./multi_instance.sh list"
echo "🔍 Monitor service logs: sudo journalctl -u algofactory-monitor -f"
echo ""

# Auto-refresh option
if [ "$1" = "watch" ]; then
    echo "🔄 Auto-refreshing every 10 seconds... (Press Ctrl+C to stop)"
    sleep 10
    exec $0 watch
fi

echo "💡 Tip: Run '$0 watch' for auto-refreshing dashboard"
