# 🚀 AlgoFactory Multi-Instance Complete Implementation Guide

## 📋 **Current Status**
- ✅ Memory optimized (2GB swap added)
- ✅ Template system created
- ✅ Instance manager (`quick_instance.sh`) ready
- ✅ Port allocation strategy: Instance ID = Flask Port
- ⚠️ Need to complete Nginx automation and SSL setup

## 🎯 **Architecture Overview**

### **Port Allocation**
```
Instance 1010: Flask=1010, WebSocket=13010, ZMQ=16010
Instance 1011: Flask=1011, WebSocket=13011, ZMQ=16011
Instance 1012: Flask=1012, WebSocket=13012, ZMQ=16012
```

### **Domain Strategy**
```
1010.algofactory.in → Instance 1010
1011.algofactory.in → Instance 1011
1012.algofactory.in → Instance 1012
```

## 🛠️ **Step-by-Step Implementation**

### **Phase 1: Basic Multi-Instance (CURRENT)**
```bash
# Create instances
./quick_instance.sh create 1010
./quick_instance.sh create 1011

# Start instances (one at a time for 1GB RAM)
./quick_instance.sh start 1010

# List instances
./quick_instance.sh list

# Stop when not needed
./quick_instance.sh stop 1010
```

### **Phase 2: Nginx Automation**
```bash
# Create Nginx config for instance
./quick_instance.sh nginx 1010

# This will:
# 1. Create /etc/nginx/sites-available/1010.algofactory.in.conf
# 2. Enable the site
# 3. Reload Nginx
```

### **Phase 3: SSL Automation**
```bash
# Get SSL certificate for new subdomain
sudo certbot --nginx -d 1010.algofactory.in --non-interactive --agree-tos --email <EMAIL>
```

### **Phase 4: DNS Setup**
In Hostinger DNS:
```
Type: A
Name: 1010
Value: **************
TTL: 3600

Type: A  
Name: 1011
Value: **************
TTL: 3600
```

## 📁 **File Structure**
```
/home/<USER>/algofactory-multi/
├── template/                    # Master template
├── instances/
│   ├── algofactory-1010/       # Instance 1010
│   │   ├── .env                # Port 1010, WebSocket 13010, ZMQ 16010
│   │   ├── start_light.sh      # Lightweight startup
│   │   ├── db/                 # Isolated database
│   │   └── logs/               # Instance logs
│   └── algofactory-1011/       # Instance 1011
└── nginx/                      # Nginx configs (future)
```

## 🔧 **Management Commands**

### **Instance Management**
```bash
# Create new instance
./quick_instance.sh create INSTANCE_ID

# Start instance (memory-optimized)
./quick_instance.sh start INSTANCE_ID

# Stop instance
./quick_instance.sh stop INSTANCE_ID

# List all instances
./quick_instance.sh list

# Create Nginx config
./quick_instance.sh nginx INSTANCE_ID
```

### **Memory Management**
```bash
# Check memory usage
./optimize_memory.sh memory

# Clean system memory
./optimize_memory.sh clean

# Full optimization
./optimize_memory.sh optimize
```

## 🚀 **Production Deployment Steps**

### **Step 1: Create Instance**
```bash
./quick_instance.sh create 1010
```

### **Step 2: Setup DNS**
Add A record in Hostinger:
- Name: `1010`
- Value: `**************`

### **Step 3: Create Nginx Config**
```bash
./quick_instance.sh nginx 1010
```

### **Step 4: Get SSL Certificate**
```bash
sudo certbot --nginx -d 1010.algofactory.in
```

### **Step 5: Start Instance**
```bash
./quick_instance.sh start 1010
```

### **Step 6: Test**
```bash
curl -I https://1010.algofactory.in
```

## 💡 **Memory Optimization Tips**

### **For 1GB RAM Server:**
1. **Run only 1-2 instances at a time**
2. **Stop instances when not needed**
3. **Use swap file (already configured)**
4. **Monitor memory**: `watch -n 5 free -h`

### **Memory Usage Per Instance:**
- Each instance: ~150-200MB RAM
- VS Code: ~300MB RAM
- System: ~200MB RAM
- **Total for 1 instance**: ~650-700MB (safe for 1GB)

## 🔄 **Scaling Strategy**

### **Current (1GB RAM):**
- 1-2 instances maximum
- Manual start/stop as needed
- Development/testing mode

### **Future (2GB+ RAM):**
- 5-10 instances simultaneously
- Auto-start on boot
- Production mode

## 🛡️ **Security & Isolation**

### **Per Instance:**
- ✅ Separate database files
- ✅ Separate log files
- ✅ Separate .env configuration
- ✅ Isolated ports
- ✅ Individual SSL certificates

## 📊 **Monitoring**

### **Instance Status**
```bash
./quick_instance.sh list
```

### **Memory Monitoring**
```bash
./optimize_memory.sh memory
```

### **Application Logs**
```bash
tail -f /home/<USER>/algofactory-multi/instances/algofactory-1010/logs/app.log
```

## 🚀 **Next Steps**

### **Immediate (Today):**
1. Test instance creation: `./quick_instance.sh create 1010`
2. Setup DNS for 1010.algofactory.in
3. Create Nginx config: `./quick_instance.sh nginx 1010`
4. Get SSL certificate
5. Test full workflow

### **Short Term (This Week):**
1. Create 2-3 test instances
2. Automate SSL certificate generation
3. Create user assignment system
4. Build monitoring dashboard

### **Long Term (Production):**
1. Upgrade server to 2GB+ RAM
2. Implement auto-scaling
3. Add load balancing
4. Create web-based management interface

## 🎯 **Success Metrics**

- ✅ Create instance in <30 seconds
- ✅ Start instance in <60 seconds  
- ✅ Each instance isolated and secure
- ✅ SSL certificate auto-generation
- ✅ Memory usage <80% with 1 instance

## 📞 **Support Commands**

```bash
# Emergency memory cleanup
./optimize_memory.sh clean

# Check all processes
ps aux --sort=-%mem | head -10

# Check disk space
df -h

# Check swap usage
swapon --show
```

Your multi-instance AlgoFactory system is ready! 🎉
