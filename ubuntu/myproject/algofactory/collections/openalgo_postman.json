{"info": {"name": "openalgo", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "BasketOrder", "event": [], "request": {"method": "POST", "header": [], "url": {"raw": "http://127.0.0.1:5000/api/v1/basketorder", "host": ["127", "0", "0", "1"], "path": [], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n    \"apikey\": \"a85992a13ab7db424c239c50826116366e9f4fd8c591345a2d23aad01ffa4d00\",\n    \"strategy\": \"your-strategy\",\n    \"orders\": [\n        {\n            \"symbol\": \"RELIANCE\",\n            \"exchange\": \"NSE\",\n            \"action\": \"BUY\",\n            \"quantity\": \"1\",\n            \"pricetype\": \"MARKET\",\n            \"product\": \"MIS\"\n        },\n        {\n            \"symbol\": \"INFY\",\n            \"exchange\": \"NSE\",\n            \"action\": \"SELL\",\n            \"quantity\": \"1\",\n            \"pricetype\": \"MARKET\",\n            \"product\": \"MIS\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "CancelAllOrder", "event": [], "request": {"method": "POST", "header": [], "url": {"raw": "http://127.0.0.1:5000/api/v1/cancelallorder", "host": ["127", "0", "0", "1"], "path": [], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n    \"apikey\": \"a85992a13ab7db424c239c50826116366e9f4fd8c591345a2d23aad01ffa4d00\",\n    \"strategy\": \"Test Strategy\"\n  \n}", "options": {"raw": {"language": "json"}}}}}, {"name": "CancelOrder", "event": [], "request": {"method": "POST", "header": [], "url": {"raw": "http://127.0.0.1:5000/api/v1/cancelorder", "host": ["127", "0", "0", "1"], "path": [], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n    \"apikey\": \"a85992a13ab7db424c239c50826116366e9f4fd8c591345a2d23aad01ffa4d00\",\n    \"strategy\": \"Test Strategy\",\n    \"orderid\": \"241214000000015\"\n  \n}", "options": {"raw": {"language": "json"}}}}}, {"name": "Chartink", "event": [], "request": {"method": "POST", "header": [], "url": {"raw": "http://8d32-49-207-193-1.ngrok-free.app/chartink/webhook/0fd7ee15-c83b-4f6e-97ae-68cb4b9bdf95", "host": ["8d32-49-207-193-1", "ngrok-free", "app"], "path": [], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n    \"stocks\": \"RELIANCE\",\n    \"trigger_prices\": \"3.75,541.8,2.1,0.2,329.6,166.8,1.25\",\n    \"triggered_at\": \"2:34 pm\",\n    \"scan_name\": \"SELL\",\n    \"scan_url\": \"short-term-breakouts\",\n    \"alert_name\": \"Alert for Short term breakouts\",\n    \"webhook_url\": \"http://8d32-49-207-193-1.ngrok-free.app/chartink/webhook/835e678a-b278-4371-bb4a-f8055426b28e\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "CloseAllPositions", "event": [], "request": {"method": "POST", "header": [], "url": {"raw": "http://127.0.0.1:5000/api/v1/closeposition", "host": ["127", "0", "0", "1"], "path": [], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n    \"apikey\": \"a85992a13ab7db424c239c50826116366e9f4fd8c591345a2d23aad01ffa4d00\",\n    \"strategy\": \"Test Strategy\"\n  \n}", "options": {"raw": {"language": "json"}}}}}, {"name": "ModifyOrder", "event": [], "request": {"method": "POST", "header": [], "url": {"raw": "http://127.0.0.1:5000/api/v1/modifyorder", "host": ["127", "0", "0", "1"], "path": [], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n    \"apikey\": \"a85992a13ab7db424c239c50826116366e9f4fd8c591345a2d23aad01ffa4d00\",\n    \"strategy\": \"Test Message\",\n    \"symbol\": \"GOLDPETAL31OCT24FUT\",\n    \"action\": \"BUY\",\n    \"exchange\": \"MCX\",\n    \"orderid\":\"427510324509895\",\n    \"product\":\"MIS\",\n    \"pricetype\":\"LIMIT\",\n    \"price\":\"7410\",\n    \"quantity\":\"1\",\n    \"disclosed_quantity\":\"0\",\n    \"trigger_price\":\"0\"\n}\n", "options": {"raw": {"language": "json"}}}}}, {"name": "PlaceOrder", "event": [], "request": {"method": "POST", "header": [], "url": {"raw": "http://127.0.0.1:5000/api/v1/placeorder", "host": ["127", "0", "0", "1"], "path": [], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n    \"apikey\": \"a85992a13ab7db424c239c50826116366e9f4fd8c591345a2d23aad01ffa4d00\",\n    \"strategy\": \"My Strategy\",\n    \"exchange\": \"NSE\",\n    \"symbol\": \"M&M\",\n    \"action\": \"buy\",\n    \"product\": \"MIS\",\n    \"pricetype\": \"MARKET\",\n    \"quantity\": \"100\",\n    \"price\": \"0\",\n    \"trigger_price\": \"0\",\n    \"disclosed_quantity\": \"0\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "PlaceSmartOrder", "event": [], "request": {"method": "POST", "header": [], "url": {"raw": "http://127.0.0.1:5000/api/v1/placesmartorder", "host": ["127", "0", "0", "1"], "path": [], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n    \"apikey\": \"a85992a13ab7db424c239c50826116366e9f4fd8c591345a2d23aad01ffa4d00\",\n    \"strategy\": \"Test Strategy\",\n    \"exchange\": \"NSE\",\n    \"symbol\": \"YESBANK\",\n    \"action\": \"BUY\",\n    \"product\": \"MIS\",\n    \"pricetype\": \"MARKET\",\n    \"quantity\": \"10\",\n    \"price\": \"0\",\n    \"trigger_price\": \"0\",\n    \"disclosed_quantity\": \"0\",\n    \"position_size\": \"10\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "SplitOrder", "event": [], "request": {"method": "POST", "header": [], "url": {"raw": "http://127.0.0.1:5000/api/v1/splitorder", "host": ["127", "0", "0", "1"], "path": [], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n    \"apikey\": \"38f99d7d226cc0c3baa19dcacf0b1f049d2f68371da1dda2c97b1b63a3a9ca2e\",\n    \"strategy\": \"Test Strategy\",\n    \"exchange\": \"NSE\",\n    \"symbol\": \"YESBANK\",\n    \"action\": \"SELL\",\n    \"quantity\": \"105\",\n    \"splitsize\": \"20\",\n    \"pricetype\": \"MARKET\",\n    \"product\": \"MIS\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "bruno", "event": []}, {"name": "depth", "event": [], "request": {"method": "POST", "header": [], "url": {"raw": "http://127.0.0.1:5000/api/v1/depth", "host": ["127", "0", "0", "1"], "path": [], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n    \"apikey\": \"a85992a13ab7db424c239c50826116366e9f4fd8c591345a2d23aad01ffa4d00\",\n    \"symbol\": \"M&M\",\n    \"exchange\": \"NSE\"\n  \n}", "options": {"raw": {"language": "json"}}}}}, {"name": "funds", "event": [], "request": {"method": "POST", "header": [], "url": {"raw": "http://127.0.0.1:5000/api/v1/funds", "host": ["127", "0", "0", "1"], "path": [], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n    \"apikey\": \"a85992a13ab7db424c239c50826116366e9f4fd8c591345a2d23aad01ffa4d00\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "historical", "event": [], "request": {"method": "POST", "header": [], "url": {"raw": "http://127.0.0.1:5000/api/v1/history", "host": ["127", "0", "0", "1"], "path": [], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n    \"apikey\": \"a85992a13ab7db424c239c50826116366e9f4fd8c591345a2d23aad01ffa4d00\",\n    \"symbol\": \"SBIN\",\n    \"exchange\": \"NSE\",\n    \"interval\": \"1m\",\n    \"start_date\": \"2024-12-17\",\n    \"end_date\": \"2024-12-18\"\n  \n}\n", "options": {"raw": {"language": "json"}}}}}, {"name": "holdings", "event": [], "request": {"method": "POST", "header": [], "url": {"raw": "http://127.0.0.1:5000/api/v1/holdings", "host": ["127", "0", "0", "1"], "path": [], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n    \"apikey\": \"a85992a13ab7db424c239c50826116366e9f4fd8c591345a2d23aad01ffa4d00\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "intervals", "event": [], "request": {"method": "POST", "header": [], "url": {"raw": "http://127.0.0.1:5000/api/v1/intervals", "host": ["127", "0", "0", "1"], "path": [], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n    \"apikey\": \"a85992a13ab7db424c239c50826116366e9f4fd8c591345a2d23aad01ffa4d00\"\n  \n}", "options": {"raw": {"language": "json"}}}}}, {"name": "openposition", "event": [], "request": {"method": "POST", "header": [], "url": {"raw": "http://127.0.0.1:5000/api/v1/openposition", "host": ["127", "0", "0", "1"], "path": [], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n    \"apikey\": \"a85992a13ab7db424c239c50826116366e9f4fd8c591345a2d23aad01ffa4d00\",\n    \"strategy\": \"Test Strategy\",\n    \"symbol\": \"YESBANK\",\n    \"exchange\": \"NSE\",\n    \"product\": \"CNC\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "orderbook", "event": [], "request": {"method": "POST", "header": [], "url": {"raw": "http://127.0.0.1:5000/api/v1/orderbook", "host": ["127", "0", "0", "1"], "path": [], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n    \"apikey\": \"a85992a13ab7db424c239c50826116366e9f4fd8c591345a2d23aad01ffa4d00\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "orderstatus", "event": [], "request": {"method": "POST", "header": [], "url": {"raw": "http://127.0.0.1:5000/api/v1/orderstatus", "host": ["127", "0", "0", "1"], "path": [], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n    \"apikey\": \"a85992a13ab7db424c239c50826116366e9f4fd8c591345a2d23aad01ffa4d00\",\n    \"strategy\": \"Test Strategy\",\n    \"orderid\": \"**************\"\n  \n}", "options": {"raw": {"language": "json"}}}}}, {"name": "positionbook", "event": [], "request": {"method": "POST", "header": [], "url": {"raw": "http://127.0.0.1:5000/api/v1/positionbook", "host": ["127", "0", "0", "1"], "path": [], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n    \"apikey\": \"a85992a13ab7db424c239c50826116366e9f4fd8c591345a2d23aad01ffa4d00\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "quotes", "event": [], "request": {"method": "POST", "header": [], "url": {"raw": "http://127.0.0.1:5000/api/v1/quotes", "host": ["127", "0", "0", "1"], "path": [], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n    \"apikey\": \"a85992a13ab7db424c239c50826116366e9f4fd8c591345a2d23aad01ffa4d00\",\n    \"symbol\":\"SBIN\", \n    \"exchange\":\"NSE\"    \n}", "options": {"raw": {"language": "json"}}}}}, {"name": "tradebook", "event": [], "request": {"method": "POST", "header": [], "url": {"raw": "http://127.0.0.1:5000/api/v1/tradebook", "host": ["127", "0", "0", "1"], "path": [], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n    \"apikey\": \"a85992a13ab7db424c239c50826116366e9f4fd8c591345a2d23aad01ffa4d00\"\n}", "options": {"raw": {"language": "json"}}}}}], "variable": []}