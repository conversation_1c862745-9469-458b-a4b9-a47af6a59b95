#!/bin/bash

# Nginx Bulk Setup Script for AlgoFactory
# Sets up multiple subdomains with SSL certificates

set -e

echo "🌐 AlgoFactory Nginx Bulk Setup"
echo "==============================="

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "❌ Please run as root (use sudo)"
    exit 1
fi

# Function to setup domains 1010-1020
setup_1010_to_1020() {
    echo "🚀 Setting up domains 1010.algofactory.in to 1020.algofactory.in"
    echo "================================================================"
    
    # First, create all Nginx configurations without SSL
    echo "📝 Step 1: Creating Nginx configurations..."
    python3 nginx_manager.py --bulk 1010 1020
    
    echo ""
    echo "⏳ Waiting 5 seconds before SSL installation..."
    sleep 5
    
    # Then install SSL certificates
    echo "🔒 Step 2: Installing SSL certificates..."
    python3 nginx_manager.py --bulk-ssl 1010 1020
    
    echo ""
    echo "✅ Bulk setup completed!"
}

# Function to setup a custom range
setup_custom_range() {
    local start=$1
    local end=$2
    
    echo "🚀 Setting up domains ${start}.algofactory.in to ${end}.algofactory.in"
    echo "================================================================"
    
    # Create configurations
    echo "📝 Step 1: Creating Nginx configurations..."
    python3 nginx_manager.py --bulk "$start" "$end"
    
    echo ""
    echo "⏳ Waiting 5 seconds before SSL installation..."
    sleep 5
    
    # Install SSL
    echo "🔒 Step 2: Installing SSL certificates..."
    python3 nginx_manager.py --bulk-ssl "$start" "$end"
    
    echo ""
    echo "✅ Custom range setup completed!"
}

# Function to show current status
show_status() {
    echo "📊 Current Nginx Configuration Status"
    echo "====================================="
    python3 nginx_manager.py --list
    
    echo ""
    echo "🔍 Active Nginx Sites:"
    ls -la /etc/nginx/sites-enabled/ | grep algofactory || echo "   No AlgoFactory sites enabled"
    
    echo ""
    echo "🔒 SSL Certificates:"
    ls -la /etc/letsencrypt/live/ | grep algofactory || echo "   No SSL certificates found"
}

# Function to test a specific domain
test_domain() {
    local domain=$1
    
    echo "🧪 Testing domain: $domain"
    echo "=========================="
    
    # Test HTTP
    echo "📡 Testing HTTP..."
    if curl -I "http://$domain" 2>/dev/null | head -1 | grep -q "301\|200"; then
        echo "✅ HTTP working"
    else
        echo "❌ HTTP not working"
    fi
    
    # Test HTTPS
    echo "🔒 Testing HTTPS..."
    if curl -I "https://$domain" 2>/dev/null | head -1 | grep -q "200"; then
        echo "✅ HTTPS working"
    else
        echo "❌ HTTPS not working"
    fi
    
    # Test SSL certificate
    echo "📜 Checking SSL certificate..."
    if openssl s_client -connect "$domain:443" -servername "$domain" </dev/null 2>/dev/null | openssl x509 -noout -dates 2>/dev/null; then
        echo "✅ SSL certificate valid"
    else
        echo "❌ SSL certificate issues"
    fi
}

# Function to create a monitoring dashboard
create_monitoring_dashboard() {
    echo "📊 Creating Nginx monitoring dashboard..."
    
    cat > /home/<USER>/myproject/algofactory/nginx_dashboard.html << 'EOF'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AlgoFactory Nginx Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .card { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .status-good { color: #28a745; }
        .status-bad { color: #dc3545; }
        .status-warning { color: #ffc107; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; }
        .refresh-btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 AlgoFactory Nginx Dashboard</h1>
        
        <div class="card">
            <h2>📊 System Status</h2>
            <p><strong>Last Updated:</strong> <span id="lastUpdate"></span></p>
            <button class="refresh-btn" onclick="location.reload()">🔄 Refresh</button>
        </div>
        
        <div class="card">
            <h2>🔗 Configured Domains</h2>
            <table>
                <thead>
                    <tr>
                        <th>Domain</th>
                        <th>HTTP Status</th>
                        <th>HTTPS Status</th>
                        <th>SSL Certificate</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="domainsTable">
                    <!-- Domains will be populated here -->
                </tbody>
            </table>
        </div>
        
        <div class="card">
            <h2>📝 Quick Commands</h2>
            <pre>
# List all configured sites
sudo python3 nginx_manager.py --list

# Setup new subdomain
sudo python3 nginx_manager.py --setup 8013 8013

# Bulk setup range
sudo python3 nginx_manager.py --bulk 1010 1020

# Install SSL for specific domain
sudo python3 nginx_manager.py --ssl 8013

# Test Nginx configuration
sudo nginx -t

# Reload Nginx
sudo systemctl reload nginx
            </pre>
        </div>
    </div>
    
    <script>
        document.getElementById('lastUpdate').textContent = new Date().toLocaleString();
        
        // Sample domains - in real implementation, this would be populated from server
        const domains = [
            { name: '8010.algofactory.in', http: 'good', https: 'good', ssl: 'good' },
            { name: '8011.algofactory.in', http: 'good', https: 'good', ssl: 'good' },
            { name: '8012.algofactory.in', http: 'good', https: 'good', ssl: 'good' }
        ];
        
        function populateTable() {
            const tbody = document.getElementById('domainsTable');
            tbody.innerHTML = '';
            
            domains.forEach(domain => {
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td><a href="https://${domain.name}" target="_blank">${domain.name}</a></td>
                    <td class="status-${domain.http}">✅ Working</td>
                    <td class="status-${domain.https}">✅ Working</td>
                    <td class="status-${domain.ssl}">✅ Valid</td>
                    <td><button onclick="testDomain('${domain.name}')">🧪 Test</button></td>
                `;
            });
        }
        
        function testDomain(domain) {
            alert(`Testing ${domain}... (This would run actual tests in a real implementation)`);
        }
        
        populateTable();
    </script>
</body>
</html>
EOF
    
    echo "✅ Monitoring dashboard created: /home/<USER>/myproject/algofactory/nginx_dashboard.html"
}

# Main script logic
case "${1:-}" in
    "1010-1020")
        setup_1010_to_1020
        ;;
    "custom")
        if [ -z "${2:-}" ] || [ -z "${3:-}" ]; then
            echo "Usage: $0 custom <start> <end>"
            echo "Example: $0 custom 1010 1020"
            exit 1
        fi
        setup_custom_range "$2" "$3"
        ;;
    "status")
        show_status
        ;;
    "test")
        if [ -z "${2:-}" ]; then
            echo "Usage: $0 test <domain>"
            echo "Example: $0 test 8010.algofactory.in"
            exit 1
        fi
        test_domain "$2"
        ;;
    "dashboard")
        create_monitoring_dashboard
        ;;
    *)
        echo "AlgoFactory Nginx Bulk Setup Script"
        echo "==================================="
        echo ""
        echo "Usage: $0 <command> [options]"
        echo ""
        echo "Commands:"
        echo "  1010-1020           Setup domains 1010.algofactory.in to 1020.algofactory.in"
        echo "  custom <start> <end> Setup custom range of domains"
        echo "  status              Show current configuration status"
        echo "  test <domain>       Test specific domain"
        echo "  dashboard           Create monitoring dashboard"
        echo ""
        echo "Examples:"
        echo "  sudo $0 1010-1020"
        echo "  sudo $0 custom 1010 1015"
        echo "  sudo $0 status"
        echo "  sudo $0 test 8010.algofactory.in"
        echo "  sudo $0 dashboard"
        echo ""
        echo "📋 Current Status:"
        python3 nginx_manager.py --list
        ;;
esac
