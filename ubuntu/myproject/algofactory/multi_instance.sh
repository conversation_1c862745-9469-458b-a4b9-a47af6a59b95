#!/bin/bash

# Multi-Instance AlgoFactory Manager
# Creates separate instances with different ports while keeping original templates unchanged

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INSTANCES_DIR="/home/<USER>/algofactory-multi"

# Function to display usage
usage() {
    echo "Multi-Instance AlgoFactory Manager"
    echo ""
    echo "Usage: $0 <command> <instance_id>"
    echo ""
    echo "Commands:"
    echo "  create <id>    Create new instance (e.g., create 1010)"
    echo "  start <id>     Start instance"
    echo "  stop <id>      Stop instance"
    echo "  list           List all instances"
    echo "  nginx <id>     Create Nginx config for instance"
    echo "  remove <id>    Remove instance completely"
    echo ""
    echo "Examples:"
    echo "  $0 create 1010     # Creates algofactory-1010 instance"
    echo "  $0 start 1010      # Starts the 1010 instance"
    echo "  $0 nginx 1010      # Creates Nginx config for 1010.algofactory.in"
    echo ""
    exit 1
}

# Function to create instance
create_instance() {
    local instance_id=$1
    local instance_dir="$INSTANCES_DIR/algofactory-$instance_id"
    
    echo "🚀 Creating AlgoFactory instance: $instance_id"
    
    # Create instances directory
    mkdir -p "$INSTANCES_DIR"
    
    # Remove existing instance if it exists
    if [ -d "$instance_dir" ]; then
        echo "⚠️  Instance $instance_id already exists. Removing..."
        rm -rf "$instance_dir"
    fi
    
    # Copy original algofactory to new instance
    echo "📁 Copying original AlgoFactory..."
    cp -r "$SCRIPT_DIR" "$instance_dir"
    
    # Remove the multi_instance.sh script from the copy to avoid confusion
    rm -f "$instance_dir/multi_instance.sh"
    
    # Calculate ports
    local flask_port=$instance_id
    local websocket_port=$((instance_id + 12000))
    local zmq_port=$((instance_id + 15000))
    
    echo "🔧 Configuring ports:"
    echo "   Flask: $flask_port"
    echo "   WebSocket: $websocket_port"
    echo "   ZMQ: $zmq_port"
    
    # Create .env file with instance-specific configuration
    cat > "$instance_dir/.env" << EOF
# Broker Configuration
BROKER_API_KEY = 'MZA0cLWq'
BROKER_API_SECRET = 'XIA6RJ3HPG4ZRKKYJLIZ6ROKAM'
BROKER_API_KEY_MARKET = 'YOUR_BROKER_MARKET_API_KEY'
BROKER_API_SECRET_MARKET = 'YOUR_BROKER_MARKET_API_SECRET'

# Application Configuration
APP_KEY = '3daa0403ce2501ee7432b75bf100048e3cf510d63d2754f952e93d88bf07ea84'
API_KEY_PEPPER = 'a25d94718479b170c16278e321ea6c989358bf499a658fd20c90033cef8ce772'

# Flask Configuration
FLASK_ENV = 'production'
FLASK_DEBUG = 'False'
FLASK_HOST_IP = '0.0.0.0'
FLASK_PORT = '$flask_port'

# Database Configuration
DATABASE_URL = 'sqlite:///db/algofactory-$instance_id.db'

# WebSocket Configuration
WEBSOCKET_HOST = 'localhost'
WEBSOCKET_PORT = '$websocket_port'
WEBSOCKET_URL = 'ws://localhost:$websocket_port'

# ZMQ Configuration
ZMQ_HOST = 'localhost'
ZMQ_PORT = '$zmq_port'

# Server Configuration
HOST_SERVER = 'https://$instance_id.algofactory.in'
REDIRECT_URL = 'https://$instance_id.algofactory.in/angel/callback'

# Security Configuration
CORS_ENABLED = 'TRUE'
CORS_ALLOWED_ORIGINS = 'https://$instance_id.algofactory.in'
CORS_ALLOWED_METHODS = 'GET,POST,DELETE,PUT,PATCH'
CORS_ALLOWED_HEADERS = 'Content-Type,Authorization,X-Requested-With'
CORS_ALLOW_CREDENTIALS = 'FALSE'
CORS_EXPOSED_HEADERS = ''
CORS_MAX_AGE = '86400'

# CSP Configuration
CSP_ENABLED = 'TRUE'
CSP_DEFAULT_SRC = "'self'"
CSP_SCRIPT_SRC = "'self' 'unsafe-inline' https://cdn.socket.io https://static.cloudflareinsights.com"
CSP_STYLE_SRC = "'self' 'unsafe-inline'"
CSP_IMG_SRC = "'self' data:"
CSP_FONT_SRC = "'self'"
CSP_CONNECT_SRC = "'self' wss: ws:"
CSP_MEDIA_SRC = "'self' data: https://*.amazonaws.com https://*.cloudfront.net"
CSP_OBJECT_SRC = "'none'"
CSP_BASE_URI = "'self'"
CSP_FORM_ACTION = "'self'"
CSP_FRAME_ANCESTORS = "'self'"
CSP_FRAME_SRC = "'self'"
CSP_UPGRADE_INSECURE_REQUESTS = 'FALSE'
CSP_REPORT_ONLY = 'FALSE'
CSP_REPORT_URI = ''

# CSRF Configuration
CSRF_ENABLED = 'TRUE'

# Rate Limiting
API_RATE_LIMIT = '10 per second'
LOGIN_RATE_LIMIT_MIN = '5 per minute'
LOGIN_RATE_LIMIT_HOUR = '25 per hour'

# Session Configuration
SESSION_EXPIRY_TIME = '03:00'

# Trading Configuration
SMART_ORDER_DELAY = '0.5'

# Broker List
VALID_BROKERS = 'fivepaisa,fivepaisaxts,aliceblue,angel,compositedge,dhan,dhan_sandbox,firstock,flattrade,fyers,groww,iifl,kotak,jainam,jainampro,paytm,pocketful,shoonya,tradejini,upstox,wisdom,zebu,zerodha'

# Development Configuration
NGROK_ALLOW = 'FALSE'
EOF
    
    # Create instance-specific database directory
    mkdir -p "$instance_dir/db"
    
    # Create instance-specific logs directory
    mkdir -p "$instance_dir/logs"
    
    echo "✅ Instance $instance_id created successfully!"
    echo "📁 Location: $instance_dir"
    echo "🌐 Domain: $instance_id.algofactory.in"
}

# Function to start instance
start_instance() {
    local instance_id=$1
    local instance_dir="$INSTANCES_DIR/algofactory-$instance_id"
    
    if [ ! -d "$instance_dir" ]; then
        echo "❌ Instance $instance_id does not exist. Create it first with: $0 create $instance_id"
        exit 1
    fi
    
    echo "🚀 Starting AlgoFactory instance: $instance_id"
    
    # Stop any existing process
    stop_instance "$instance_id" 2>/dev/null || true
    
    # Change to instance directory and start
    cd "$instance_dir"

    # Extract key environment variables from .env file
    local flask_port=$(grep "^FLASK_PORT" .env | cut -d"'" -f2)
    local websocket_port=$(grep "^WEBSOCKET_PORT" .env | cut -d"'" -f2)
    local zmq_port=$(grep "^ZMQ_PORT" .env | cut -d"'" -f2)
    local database_url=$(grep "^DATABASE_URL" .env | cut -d"'" -f2)
    local host_server=$(grep "^HOST_SERVER" .env | cut -d"'" -f2)

    echo "🔧 Environment loaded:"
    echo "   FLASK_PORT=$flask_port"
    echo "   WEBSOCKET_PORT=$websocket_port"
    echo "   ZMQ_PORT=$zmq_port"
    echo "   DATABASE_URL=$database_url"
    echo "   HOST_SERVER=$host_server"

    # Start the application in background
    nohup python3 app.py > logs/app.log 2>&1 &
    echo $! > app.pid
    
    echo "✅ Instance $instance_id started!"
    echo "📊 Check logs: tail -f $instance_dir/logs/app.log"
}

# Function to stop instance
stop_instance() {
    local instance_id=$1
    local instance_dir="$INSTANCES_DIR/algofactory-$instance_id"
    
    if [ ! -d "$instance_dir" ]; then
        echo "❌ Instance $instance_id does not exist"
        return 1
    fi
    
    echo "🛑 Stopping AlgoFactory instance: $instance_id"
    
    # Kill by PID file
    if [ -f "$instance_dir/app.pid" ]; then
        local pid=$(cat "$instance_dir/app.pid")
        if kill -0 "$pid" 2>/dev/null; then
            kill "$pid"
            echo "✅ Stopped process $pid"
        fi
        rm -f "$instance_dir/app.pid"
    fi
    
    # Kill by port
    local flask_port=$instance_id
    pkill -f "python.*app.py.*$flask_port" || true
    
    echo "✅ Instance $instance_id stopped"
}

# Function to list instances
list_instances() {
    echo "📋 AlgoFactory Instances:"
    echo ""
    
    if [ ! -d "$INSTANCES_DIR" ]; then
        echo "   No instances found"
        return
    fi
    
    for instance_path in "$INSTANCES_DIR"/algofactory-*; do
        if [ -d "$instance_path" ]; then
            local instance_name=$(basename "$instance_path")
            local instance_id=${instance_name#algofactory-}
            local pid_file="$instance_path/app.pid"
            local status="STOPPED"
            
            if [ -f "$pid_file" ]; then
                local pid=$(cat "$pid_file")
                if kill -0 "$pid" 2>/dev/null; then
                    status="RUNNING (PID: $pid)"
                fi
            fi
            
            echo "   $instance_id - $status"
            echo "     Domain: $instance_id.algofactory.in"
            echo "     Path: $instance_path"
            echo ""
        fi
    done
}

# Function to create Nginx configuration
create_nginx_config() {
    local instance_id=$1
    local flask_port=$instance_id
    
    echo "🌐 Creating Nginx configuration for instance: $instance_id"
    
    # Create Nginx config
    sudo tee "/etc/nginx/sites-available/$instance_id.algofactory.in.conf" > /dev/null << EOF
server {
    listen 80;
    server_name $instance_id.algofactory.in;
    
    location / {
        proxy_pass http://127.0.0.1:$flask_port;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
EOF
    
    # Enable the site
    sudo ln -sf "/etc/nginx/sites-available/$instance_id.algofactory.in.conf" "/etc/nginx/sites-enabled/"
    
    # Test and reload Nginx
    sudo nginx -t && sudo systemctl reload nginx
    
    echo "✅ Nginx configuration created for $instance_id.algofactory.in"
    echo "🔒 To get SSL certificate, run:"
    echo "   sudo certbot --nginx -d $instance_id.algofactory.in"
}

# Function to remove instance
remove_instance() {
    local instance_id=$1
    local instance_dir="$INSTANCES_DIR/algofactory-$instance_id"
    
    if [ ! -d "$instance_dir" ]; then
        echo "❌ Instance $instance_id does not exist"
        exit 1
    fi
    
    echo "🗑️  Removing AlgoFactory instance: $instance_id"
    
    # Stop the instance first
    stop_instance "$instance_id" 2>/dev/null || true
    
    # Remove instance directory
    rm -rf "$instance_dir"
    
    # Remove Nginx config
    sudo rm -f "/etc/nginx/sites-enabled/$instance_id.algofactory.in.conf"
    sudo rm -f "/etc/nginx/sites-available/$instance_id.algofactory.in.conf"
    sudo nginx -t && sudo systemctl reload nginx 2>/dev/null || true
    
    echo "✅ Instance $instance_id removed completely"
}

# Main script logic
case "${1:-}" in
    create)
        if [ -z "${2:-}" ]; then
            echo "❌ Error: Instance ID required"
            usage
        fi
        create_instance "$2"
        ;;
    start)
        if [ -z "${2:-}" ]; then
            echo "❌ Error: Instance ID required"
            usage
        fi
        start_instance "$2"
        ;;
    stop)
        if [ -z "${2:-}" ]; then
            echo "❌ Error: Instance ID required"
            usage
        fi
        stop_instance "$2"
        ;;
    list)
        list_instances
        ;;
    nginx)
        if [ -z "${2:-}" ]; then
            echo "❌ Error: Instance ID required"
            usage
        fi
        create_nginx_config "$2"
        ;;
    remove)
        if [ -z "${2:-}" ]; then
            echo "❌ Error: Instance ID required"
            usage
        fi
        remove_instance "$2"
        ;;
    *)
        usage
        ;;
esac
