# AlgoFactory Global Nginx Configuration
# Place this in /etc/nginx/conf.d/algofactory-global.conf

# Rate Limiting Zones for AlgoFactory
limit_req_zone $binary_remote_addr zone=algofactory_api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=algofactory_login:10m rate=5r/m;
limit_req_zone $binary_remote_addr zone=algofactory_general:10m rate=20r/s;
limit_req_zone $binary_remote_addr zone=algofactory_websocket:10m rate=50r/s;

# Connection Limiting
limit_conn_zone $binary_remote_addr zone=algofactory_conn:10m;

# Cache zones
proxy_cache_path /var/cache/nginx/algofactory levels=1:2 keys_zone=algofactory_cache:10m max_size=100m inactive=60m use_temp_path=off;

# Map for WebSocket upgrade
map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
}

# Log format for AlgoFactory
log_format algofactory_access '$remote_addr - $remote_user [$time_local] '
                              '"$request" $status $body_bytes_sent '
                              '"$http_referer" "$http_user_agent" '
                              '"$http_x_forwarded_for" '
                              'rt=$request_time uct="$upstream_connect_time" '
                              'uht="$upstream_header_time" urt="$upstream_response_time" '
                              'instance="$upstream_addr"';

# Create cache directory
# This will be created automatically by nginx
