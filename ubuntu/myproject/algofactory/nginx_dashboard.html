<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AlgoFactory Nginx Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .card { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .status-good { color: #28a745; }
        .status-bad { color: #dc3545; }
        .status-warning { color: #ffc107; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; }
        .refresh-btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 AlgoFactory Nginx Dashboard</h1>
        
        <div class="card">
            <h2>📊 System Status</h2>
            <p><strong>Last Updated:</strong> <span id="lastUpdate"></span></p>
            <button class="refresh-btn" onclick="location.reload()">🔄 Refresh</button>
        </div>
        
        <div class="card">
            <h2>🔗 Configured Domains</h2>
            <table>
                <thead>
                    <tr>
                        <th>Domain</th>
                        <th>HTTP Status</th>
                        <th>HTTPS Status</th>
                        <th>SSL Certificate</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="domainsTable">
                    <!-- Domains will be populated here -->
                </tbody>
            </table>
        </div>
        
        <div class="card">
            <h2>📝 Quick Commands</h2>
            <pre>
# List all configured sites
sudo python3 nginx_manager.py --list

# Setup new subdomain
sudo python3 nginx_manager.py --setup 8013 8013

# Bulk setup range
sudo python3 nginx_manager.py --bulk 1010 1020

# Install SSL for specific domain
sudo python3 nginx_manager.py --ssl 8013

# Test Nginx configuration
sudo nginx -t

# Reload Nginx
sudo systemctl reload nginx
            </pre>
        </div>
    </div>
    
    <script>
        document.getElementById('lastUpdate').textContent = new Date().toLocaleString();
        
        // Sample domains - in real implementation, this would be populated from server
        const domains = [
            { name: '8010.algofactory.in', http: 'good', https: 'good', ssl: 'good' },
            { name: '8011.algofactory.in', http: 'good', https: 'good', ssl: 'good' },
            { name: '8012.algofactory.in', http: 'good', https: 'good', ssl: 'good' }
        ];
        
        function populateTable() {
            const tbody = document.getElementById('domainsTable');
            tbody.innerHTML = '';
            
            domains.forEach(domain => {
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td><a href="https://${domain.name}" target="_blank">${domain.name}</a></td>
                    <td class="status-${domain.http}">✅ Working</td>
                    <td class="status-${domain.https}">✅ Working</td>
                    <td class="status-${domain.ssl}">✅ Valid</td>
                    <td><button onclick="testDomain('${domain.name}')">🧪 Test</button></td>
                `;
            });
        }
        
        function testDomain(domain) {
            alert(`Testing ${domain}... (This would run actual tests in a real implementation)`);
        }
        
        populateTable();
    </script>
</body>
</html>
