<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AlgoFactory Monitoring Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            color: #4a5568;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .status-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-left: 5px solid #4CAF50;
        }
        
        .status-card.warning {
            border-left-color: #FF9800;
        }
        
        .status-card.error {
            border-left-color: #F44336;
        }
        
        .status-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-indicator.healthy {
            background-color: #4CAF50;
        }
        
        .status-indicator.warning {
            background-color: #FF9800;
        }
        
        .status-indicator.error {
            background-color: #F44336;
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .metric:last-child {
            border-bottom: none;
        }
        
        .metric-label {
            font-weight: 500;
            color: #555;
        }
        
        .metric-value {
            color: #333;
            font-weight: 600;
        }
        
        .refresh-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: transform 0.2s;
            margin: 0 auto;
            display: block;
        }
        
        .refresh-btn:hover {
            transform: translateY(-2px);
        }
        
        .last-updated {
            text-align: center;
            color: #666;
            margin-top: 20px;
            font-size: 14px;
        }
        
        .auto-refresh {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .auto-refresh label {
            color: #555;
            font-weight: 500;
        }
        
        .auto-refresh input {
            margin-left: 10px;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .loading {
            animation: pulse 1.5s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 AlgoFactory Monitoring Dashboard</h1>
            <p>Real-time monitoring for algo.algofactory.in</p>
        </div>
        
        <div class="auto-refresh">
            <label>
                <input type="checkbox" id="autoRefresh" checked> Auto-refresh every 30 seconds
            </label>
        </div>
        
        <div class="status-grid" id="statusGrid">
            <!-- Status cards will be populated by JavaScript -->
        </div>
        
        <button class="refresh-btn" onclick="refreshStatus()">🔄 Refresh Status</button>
        
        <div class="last-updated" id="lastUpdated">
            Last updated: Never
        </div>
    </div>

    <script>
        let autoRefreshInterval;
        
        function formatBytes(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        function getStatusClass(status) {
            if (status === 'healthy' || status === 'running' || status === 'active') {
                return 'healthy';
            } else if (status === 'warning') {
                return 'warning';
            } else {
                return 'error';
            }
        }
        
        function createStatusCard(title, status, metrics) {
            const statusClass = getStatusClass(status);
            const cardClass = statusClass === 'healthy' ? '' : statusClass;
            
            let metricsHtml = '';
            for (const [key, value] of Object.entries(metrics)) {
                metricsHtml += `
                    <div class="metric">
                        <span class="metric-label">${key}</span>
                        <span class="metric-value">${value}</span>
                    </div>
                `;
            }
            
            return `
                <div class="status-card ${cardClass}">
                    <h3>
                        <span class="status-indicator ${statusClass}"></span>
                        ${title}
                    </h3>
                    ${metricsHtml}
                </div>
            `;
        }
        
        async function refreshStatus() {
            const statusGrid = document.getElementById('statusGrid');
            statusGrid.classList.add('loading');
            
            try {
                // For demo purposes, we'll simulate the monitoring data
                // In production, this would fetch from your monitoring endpoint
                const mockData = {
                    application: {
                        status: 'healthy',
                        status_code: 200,
                        response_time: 0.125
                    },
                    process: {
                        status: 'running',
                        pid: 8420,
                        cpu_percent: 2.5,
                        memory_percent: 8.3
                    },
                    system: {
                        cpu_percent: 15.2,
                        memory: {
                            percent: 45.6,
                            used: 1024 * 1024 * 1024 * 2.3,
                            total: 1024 * 1024 * 1024 * 4
                        },
                        disk: {
                            percent: 67.8,
                            used: 1024 * 1024 * 1024 * 15.2,
                            total: 1024 * 1024 * 1024 * 20
                        }
                    },
                    nginx: {
                        status: 'active',
                        active: true
                    }
                };
                
                let html = '';
                
                // Application Status
                html += createStatusCard('Application Health', mockData.application.status, {
                    'Status Code': mockData.application.status_code,
                    'Response Time': `${mockData.application.response_time}s`,
                    'Endpoint': 'https://algo.algofactory.in'
                });
                
                // Process Status
                html += createStatusCard('Process Status', mockData.process.status, {
                    'PID': mockData.process.pid,
                    'CPU Usage': `${mockData.process.cpu_percent}%`,
                    'Memory Usage': `${mockData.process.memory_percent}%`
                });
                
                // System Resources
                const systemStatus = mockData.system.cpu_percent > 80 || mockData.system.memory.percent > 90 ? 'warning' : 'healthy';
                html += createStatusCard('System Resources', systemStatus, {
                    'CPU Usage': `${mockData.system.cpu_percent}%`,
                    'Memory Usage': `${mockData.system.memory.percent}%`,
                    'Memory Used': formatBytes(mockData.system.memory.used),
                    'Disk Usage': `${mockData.system.disk.percent.toFixed(1)}%`
                });
                
                // Nginx Status
                html += createStatusCard('Nginx Proxy', mockData.nginx.status, {
                    'Service': mockData.nginx.active ? 'Active' : 'Inactive',
                    'Reverse Proxy': 'Configured',
                    'SSL': 'Enabled'
                });
                
                statusGrid.innerHTML = html;
                
                // Update last updated time
                document.getElementById('lastUpdated').textContent = 
                    `Last updated: ${new Date().toLocaleString()}`;
                    
            } catch (error) {
                statusGrid.innerHTML = `
                    <div class="status-card error">
                        <h3>❌ Error</h3>
                        <div class="metric">
                            <span class="metric-label">Error</span>
                            <span class="metric-value">Failed to fetch status</span>
                        </div>
                    </div>
                `;
            }
            
            statusGrid.classList.remove('loading');
        }
        
        function setupAutoRefresh() {
            const checkbox = document.getElementById('autoRefresh');
            
            function toggleAutoRefresh() {
                if (checkbox.checked) {
                    autoRefreshInterval = setInterval(refreshStatus, 30000);
                } else {
                    clearInterval(autoRefreshInterval);
                }
            }
            
            checkbox.addEventListener('change', toggleAutoRefresh);
            toggleAutoRefresh(); // Initialize
        }
        
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            refreshStatus();
            setupAutoRefresh();
        });
    </script>
</body>
</html>
