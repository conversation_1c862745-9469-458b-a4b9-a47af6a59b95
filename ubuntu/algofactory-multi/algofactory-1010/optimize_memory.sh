#!/bin/bash

# Memory Optimization Script for 1GB RAM Server
# This script optimizes the system to prevent crashes during development

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[$(date '+%H:%M:%S')] SUCCESS:${NC} $1"
}

warning() {
    echo -e "${YELLOW}[$(date '+%H:%M:%S')] WARNING:${NC} $1"
}

error() {
    echo -e "${RED}[$(date '+%H:%M:%S')] ERROR:${NC} $1" >&2
}

# Function to show current memory usage
show_memory() {
    echo -e "\n${BLUE}Current Memory Usage:${NC}"
    free -h
    echo -e "\n${BLUE}Top Memory Consumers:${NC}"
    ps aux --sort=-%mem | head -10
}

# Function to create swap file
create_swap() {
    log "Creating swap file to increase virtual memory..."
    
    # Check if swap already exists
    if swapon --show | grep -q "/swapfile"; then
        warning "Swap file already exists"
        return 0
    fi
    
    # Create 2GB swap file
    sudo fallocate -l 2G /swapfile
    sudo chmod 600 /swapfile
    sudo mkswap /swapfile
    sudo swapon /swapfile
    
    # Make it permanent
    echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab
    
    # Optimize swap usage
    echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.conf
    echo 'vm.vfs_cache_pressure=50' | sudo tee -a /etc/sysctl.conf
    
    success "2GB swap file created and activated"
}

# Function to optimize system services
optimize_services() {
    log "Optimizing system services..."
    
    # Disable unnecessary services
    services_to_disable=(
        "snapd"
        "bluetooth"
        "cups"
        "avahi-daemon"
        "ModemManager"
    )
    
    for service in "${services_to_disable[@]}"; do
        if systemctl is-enabled "$service" >/dev/null 2>&1; then
            sudo systemctl disable "$service" >/dev/null 2>&1 || true
            sudo systemctl stop "$service" >/dev/null 2>&1 || true
            log "Disabled $service"
        fi
    done
    
    success "System services optimized"
}

# Function to optimize Python/Gunicorn settings
optimize_python() {
    log "Optimizing Python and Gunicorn settings..."
    
    # Create optimized gunicorn config
    cat > /tmp/gunicorn_optimized.conf << 'EOF'
# Optimized Gunicorn configuration for low memory
bind = "0.0.0.0:PORT_PLACEHOLDER"
workers = 1
worker_class = "sync"
worker_connections = 100
max_requests = 500
max_requests_jitter = 50
timeout = 30
keepalive = 2
preload_app = True
worker_tmp_dir = "/dev/shm"
EOF
    
    success "Python optimization configured"
}

# Function to clean system
clean_system() {
    log "Cleaning system to free memory..."
    
    # Clean package cache
    sudo apt clean
    sudo apt autoremove -y
    
    # Clear logs
    sudo journalctl --vacuum-time=1d
    
    # Clear tmp files
    sudo rm -rf /tmp/*
    sudo rm -rf /var/tmp/*
    
    # Drop caches
    sudo sync
    echo 3 | sudo tee /proc/sys/vm/drop_caches > /dev/null
    
    success "System cleaned"
}

# Function to optimize instance startup
optimize_instances() {
    log "Creating memory-optimized instance startup..."
    
    # Create lightweight start script for instances
    cat > /tmp/start_lightweight.sh << 'EOF'
#!/bin/bash

# Lightweight startup script for low memory environments
set -e

INSTANCE_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INSTANCE_ID=$(basename "$INSTANCE_DIR" | cut -d'-' -f2)
SHARED_VENV="/home/<USER>/shared-venv"
PID_FILE="$INSTANCE_DIR/app.pid"

# Source environment
source "$INSTANCE_DIR/.env"

# Activate virtual environment
source "$SHARED_VENV/bin/activate"

# Check if already running
if [ -f "$PID_FILE" ]; then
    if ps -p "$(cat "$PID_FILE")" > /dev/null 2>&1; then
        echo "Instance $INSTANCE_ID already running"
        exit 0
    else
        rm -f "$PID_FILE"
    fi
fi

# Start with minimal resources
cd "$INSTANCE_DIR"
nohup "$SHARED_VENV/bin/gunicorn" \
    --bind="0.0.0.0:$FLASK_PORT" \
    --workers=1 \
    --worker-class=sync \
    --worker-connections=50 \
    --max-requests=200 \
    --timeout=30 \
    --keepalive=2 \
    --preload \
    --worker-tmp-dir=/dev/shm \
    --log-level=warning \
    --access-logfile="$INSTANCE_DIR/logs/access.log" \
    --error-logfile="$INSTANCE_DIR/logs/error.log" \
    app:app > "$INSTANCE_DIR/logs/app.log" 2>&1 &

echo $! > "$PID_FILE"
echo "Instance $INSTANCE_ID started on port $FLASK_PORT"
EOF
    
    chmod +x /tmp/start_lightweight.sh
    success "Lightweight instance startup created"
}

# Function to set memory limits
set_memory_limits() {
    log "Setting memory limits for processes..."
    
    # Create systemd override for memory limits
    sudo mkdir -p /etc/systemd/system/nginx.service.d/
    cat > /tmp/nginx_memory.conf << 'EOF'
[Service]
MemoryMax=100M
MemoryHigh=80M
EOF
    sudo mv /tmp/nginx_memory.conf /etc/systemd/system/nginx.service.d/memory.conf
    
    # Reload systemd
    sudo systemctl daemon-reload
    
    success "Memory limits configured"
}

# Function to monitor memory usage
setup_monitoring() {
    log "Setting up memory monitoring..."
    
    # Create memory monitor script
    cat > /tmp/memory_monitor.sh << 'EOF'
#!/bin/bash

# Memory monitoring script
while true; do
    MEMORY_USAGE=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
    
    if [ "$MEMORY_USAGE" -gt 85 ]; then
        echo "$(date): High memory usage: ${MEMORY_USAGE}%" >> /var/log/memory_alerts.log
        
        # Kill memory-heavy processes if needed
        if [ "$MEMORY_USAGE" -gt 95 ]; then
            echo "$(date): Critical memory usage, cleaning caches" >> /var/log/memory_alerts.log
            echo 3 > /proc/sys/vm/drop_caches
        fi
    fi
    
    sleep 30
done
EOF
    
    chmod +x /tmp/memory_monitor.sh
    success "Memory monitoring setup"
}

# Main execution
main() {
    echo -e "${GREEN}=== Memory Optimization for 1GB RAM Server ===${NC}"
    
    show_memory
    
    log "Starting optimization process..."
    
    create_swap
    optimize_services
    optimize_python
    clean_system
    optimize_instances
    set_memory_limits
    setup_monitoring
    
    echo -e "\n${GREEN}=== Optimization Complete ===${NC}"
    show_memory
    
    echo -e "\n${GREEN}Recommendations:${NC}"
    echo "1. Use only 1-2 instances at a time during development"
    echo "2. Stop instances when not needed: python3 instance_manager.py stop INSTANCE_ID"
    echo "3. Monitor memory: watch -n 5 free -h"
    echo "4. Use lightweight startup: /tmp/start_lightweight.sh"
    echo "5. Consider upgrading to 2GB+ RAM for production"
}

# Handle command line arguments
case "${1:-optimize}" in
    "optimize")
        main
        ;;
    "memory")
        show_memory
        ;;
    "clean")
        clean_system
        ;;
    "swap")
        create_swap
        ;;
    *)
        echo "Usage: $0 {optimize|memory|clean|swap}"
        echo "  optimize - Run full optimization"
        echo "  memory   - Show current memory usage"
        echo "  clean    - Clean system to free memory"
        echo "  swap     - Create swap file"
        ;;
esac
