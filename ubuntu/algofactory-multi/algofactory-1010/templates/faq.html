{% extends "layout.html" %}

{% block title %}FAQ - AlgoFactory{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 py-8">
    <h1 class="text-4xl font-bold text-center mb-8">Frequently Asked Questions</h1>
    <p class="text-lg text-center text-base-content/70 mb-12">Find answers to the most common questions about AlgoFactory</p>

    <!-- Important Links Card -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-12">
        <a href="https://docs.algofactory.in/" target="_blank" rel="noopener noreferrer" 
           class="card bg-base-100 shadow-lg hover:shadow-xl transition-all">
            <div class="card-body flex flex-row items-center">
                <div class="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
                <div>
                    <h2 class="card-title text-lg">Documentation</h2>
                    <p class="text-base-content/70">Read our comprehensive guides</p>
                </div>
            </div>
        </a>
        
        <a href="https://docs.algofactory.in/getting-started" target="_blank" rel="noopener noreferrer" 
           class="card bg-base-100 shadow-lg hover:shadow-xl transition-all">
            <div class="card-body flex flex-row items-center">
                <div class="w-12 h-12 rounded-full bg-secondary/10 flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <div>
                    <h2 class="card-title text-lg">Installation Guide</h2>
                    <p class="text-base-content/70">Start using AlgoFactory today</p>
                </div>
            </div>
        </a>
        
        <a href="https://github.com/algofactory.in" target="_blank" rel="noopener noreferrer" 
           class="card bg-base-100 shadow-lg hover:shadow-xl transition-all">
            <div class="card-body flex flex-row items-center">
                <div class="w-12 h-12 rounded-full bg-accent/10 flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-accent" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"></path>
                    </svg>
                </div>
                <div>
                    <h2 class="card-title text-lg">GitHub Repository</h2>
                    <p class="text-base-content/70">Browse or contribute to the code</p>
                </div>
            </div>
        </a>
        
        <a href="https://www.algofactory.in" target="_blank" rel="noopener noreferrer" 
           class="card bg-base-100 shadow-lg hover:shadow-xl transition-all">
            <div class="card-body flex flex-row items-center">
                <div class="w-12 h-12 rounded-full bg-info/10 flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-info" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.946 2.4189-2.1568 2.4189Z"></path>
                    </svg>
                </div>
                <div>
                    <h2 class="card-title text-lg">Join Discord Community</h2>
                    <p class="text-base-content/70">Get help from other users</p>
                </div>
            </div>
        </a>
    </div>

    <!-- FAQ Sections -->
    <div class="space-y-6">
        <!-- What is AlgoFactory -->
        <div class="collapse collapse-arrow bg-base-100 shadow-md rounded-lg border border-base-200 hover:shadow-lg transition-all">
            <input type="radio" name="faq-accordion" checked="checked" /> 
            <div class="collapse-title text-xl font-medium flex items-center">
                <div class="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                What is AlgoFactory?
            </div>
            <div class="collapse-content">
                <p class="text-base-content/80">AlgoFactory is an algorithmic trading platform that acts as a bridge between various trading platforms and brokers. It's a web-based, self-hostable application that can run on Windows, Mac, Linux, or cloud environments. AlgoFactory currently supports connecting to your personal trading account and can interact with multiple popular trading platforms like Amibroker, MetaTrader, Python, NodeJS, Excel, and Google Spreadsheet.</p>
            </div>
        </div>

        <!-- Supported Brokers -->
        <div class="collapse collapse-arrow bg-base-100 shadow-md rounded-lg border border-base-200 hover:shadow-lg transition-all">
            <input type="radio" name="faq-accordion" />
            <div class="collapse-title text-xl font-medium flex items-center">
                <div class="w-8 h-8 rounded-full bg-secondary/10 flex items-center justify-center mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                </div>
                Which brokers are supported?
            </div>
            <div class="collapse-content">
                <p class="mb-4 text-base-content/80">AlgoFactory currently supports integration with 10 popular brokers in the Indian market:</p>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                    <ul class="list-disc list-inside space-y-1 text-base-content/80">
                        <li>5paisa</li>
                        <li>5paisa(XTS)</li>
                        <li>Angel One</li>
                        <li>Aliceblue</li>
                        <li>Compositedge</li>
                        <li>Dhan</li>
                        <li>Dhan (Sandbox)</li>
                        <li>Firstock</li>
                        <li>Flattrade</li>
                        <li>Fyers</li>
                    </ul>
                    <ul class="list-disc list-inside space-y-1 text-base-content/80">
                        <li>Kotak</li>
                        <li>ICICI Direct</li>
                        <li>IIFL</li>
                        <li>Jainam(Retail)</li>
                        <li>Jainam(Dealer)</li>
                        <li>Paytm</li>
                        <li>Pocketful</li>
                        <li>Shoonya</li>
                        <li>Tradejini</li>
                        <li>Upstox</li>
                        <li>Wisdom Capital</li>
                        <li>Zebu</li>
                        <li>Zerodha</li>
                        <li>And more being added regularly</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- System Requirements -->
        <div class="collapse collapse-arrow bg-base-100 shadow-md rounded-lg border border-base-200 hover:shadow-lg transition-all">
            <input type="radio" name="faq-accordion" />
            <div class="collapse-title text-xl font-medium flex items-center">
                <div class="w-8 h-8 rounded-full bg-accent/10 flex items-center justify-center mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                    </svg>
                </div>
                What are the system requirements?
            </div>
            <div class="collapse-content">
                <p class="mb-4 text-base-content/80">The minimum requirements are:</p>
                <ul class="list-disc list-inside space-y-2 text-base-content/80">
                    <li>2GB RAM minimum</li>
                    <li>Stable internet connection</li>
                    <li>Python 3.10 or higher</li>
                    <li>Any modern operating system (Windows, Mac, or Linux)</li>
                </ul>
            </div>
        </div>

        <!-- Hosting Options -->
        <div class="collapse collapse-arrow bg-base-100 shadow-md rounded-lg border border-base-200 hover:shadow-lg transition-all">
            <input type="radio" name="faq-accordion" />
            <div class="collapse-title text-xl font-medium flex items-center">
                <div class="w-8 h-8 rounded-full bg-info/10 flex items-center justify-center mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-info" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
                    </svg>
                </div>
                Where can I host AlgoFactory?
            </div>
            <div class="collapse-content">
                <p class="mb-4 text-base-content/80">AlgoFactory can be hosted in multiple environments:</p>
                <ul class="list-disc list-inside space-y-2 text-base-content/80">
                    <li>Locally on your Windows PC, Mac, or Linux machine</li>
                    <li>On cloud servers (AWS, Digital Ocean, etc.)</li>
                    <li>On your own private domain</li>
                    <li>For best performance with Indian markets, it's recommended to host on servers located in India to minimize latency</li>
                </ul>
            </div>
        </div>

        <!-- Costs -->
        <div class="collapse collapse-arrow bg-base-100 shadow-md rounded-lg border border-base-200 hover:shadow-lg transition-all">
            <input type="radio" name="faq-accordion" />
            <div class="collapse-title text-xl font-medium flex items-center">
                <div class="w-8 h-8 rounded-full bg-success/10 flex items-center justify-center mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-success" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                What are the costs involved?
            </div>
            <div class="collapse-content">
                <p class="mb-4 text-base-content/80">AlgoFactory itself is completely free. However, there may be associated costs:</p>
                <ul class="list-disc list-inside space-y-2 text-base-content/80">
                    <li>Trading platform costs (if using TradingView, Amibroker, etc.)</li>
                    <li>Broker API charges (varies by broker, many are free)</li>
                    <li>Real-time data feed subscription</li>
                    <li>Server hosting costs (if using cloud servers, typically $6-12/month for basic setups)</li>
                    <li>Standard trading costs (brokerage, STT, GST, etc.)</li>
                </ul>
            </div>
        </div>

        <!-- Security -->
        <div class="collapse collapse-arrow bg-base-100 shadow-md rounded-lg border border-base-200 hover:shadow-lg transition-all">
            <input type="radio" name="faq-accordion" />
            <div class="collapse-title text-xl font-medium flex items-center">
                <div class="w-8 h-8 rounded-full bg-warning/10 flex items-center justify-center mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-warning" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                </div>
                How secure is AlgoFactory?
            </div>
            <div class="collapse-content">
                <p class="mb-4 text-base-content/80">AlgoFactory prioritizes security with features like:</p>
                <ul class="list-disc list-inside space-y-2 text-base-content/80">
                    <li>Self-hosted environment - you control your infrastructure</li>
                    <li>Support for broker-specific security requirements (TOTP, 2FA)</li>
                    <li>Secure storage of API keys and credentials</li>
                    <li>Regular security updates and improvements</li>
                    <li>Source code that can be audited for security</li>
                </ul>
            </div>
        </div>

        <!-- Updates and Support -->
        <div class="collapse collapse-arrow bg-base-100 shadow-md rounded-lg border border-base-200 hover:shadow-lg transition-all">
            <input type="radio" name="faq-accordion" />
            <div class="collapse-title text-xl font-medium flex items-center">
                <div class="w-8 h-8 rounded-full bg-error/10 flex items-center justify-center mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-error" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                </div>
                How do I get updates and support?
            </div>
            <div class="collapse-content">
                <p class="mb-4 text-base-content/80">To update AlgoFactory, use these commands:</p>
                <div class="mockup-code mb-4 bg-base-300 text-base-content">
                    <pre data-prefix="$"><code>git pull</code></pre>
                    <pre data-prefix="$"><code>pip install -r requirements.txt</code></pre>
                </div>
                <p class="mb-4 text-base-content/80">For support:</p>
                <ul class="list-disc list-inside space-y-2 text-base-content/80">
                    <li>Join the Discord community for community support</li>
                    <li>Check the documentation at docs.algofactory.in</li>
                    <li>Follow announcements for new features and updates</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
