{% extends "layout.html" %}

{% block title %}Tradejini <PERSON>gin - AlgoFactory{% endblock %}

{% block content %}
<div class="min-h-[calc(100vh-8rem)] flex items-center justify-center py-8">
    <div class="container mx-auto px-4">
        <div class="flex flex-col lg:flex-row items-center justify-between gap-8 lg:gap-16">
            <!-- Right side login form - Shown first on mobile -->
            <div class="card flex-shrink-0 w-full max-w-md shadow-2xl bg-base-100 order-1 lg:order-2">
                <div class="card-body">
                    <div class="flex justify-center mb-6">
                        <img class="h-20 w-auto" src="{{ url_for('static', filename='favicon/apple-touch-icon.png') }}" alt="AlgoFactory">
                    </div>
                    <h2 class="card-title text-2xl font-bold text-center mb-6"><PERSON>jin<PERSON>gin</h2>
                    
                    <form method="POST" class="space-y-4">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text">Password</span>
                            </label>
                            <input type="password" name="password" class="input input-bordered" required>
                        </div>
                        
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text">2FA Code</span>
                            </label>
                            <input type="text" name="twofa" class="input input-bordered" required>
                            <label class="label">
                                <span class="label-text-alt text-info">To get TOTP, login to web app and click 'Having trouble with AppCode/TOTP' in 2FA page</span>
                            </label>
                        </div>
                        
                        <!-- Hidden input for TOTP type -->
                        <input type="hidden" name="twofatype" value="totp">
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text-alt text-info">Please enter the TOTP code from your Authenticator app. Make sure to set up TOTP in your Tradejini web portal settings first.</span>
                            </label>
                        </div>
                        
                        {% if error %}
                        <div class="alert alert-error">
                            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                            <span>{{ error }}</span>
                        </div>
                        {% endif %}
                        
                        <button type="submit" class="btn btn-primary w-full">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                            </svg>
                            Login
                        </button>
                    </form>
                </div>
            </div>

            <!-- Left side content - Shown second on mobile -->
            <div class="flex-1 max-w-xl text-center lg:text-left order-2 lg:order-1">
                <h1 class="text-4xl lg:text-5xl font-bold mb-6">Connect Your <span class="text-primary">Tradejini</span> Account</h1>
                <p class="text-lg lg:text-xl mb-8 opacity-80">
                    Link your Tradejini trading account to start executing trades through AlgoFactory's algorithmic trading platform.
                </p>
                <div class="flex flex-col gap-4">
                    <div class="alert alert-info shadow-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div>
                            <h3 class="font-bold">2FA Setup Required</h3>
                            <div class="text-sm">Make sure to set up 2FA in your Tradejini web portal before connecting.</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
