#!/bin/bash
set -e

INSTANCE_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INSTANCE_ID=$(basename "$INSTANCE_DIR" | cut -d'-' -f2)
SHARED_VENV="/home/<USER>/shared-venv"
PID_FILE="$INSTANCE_DIR/app.pid"

source "$INSTANCE_DIR/.env"
source "$SHARED_VENV/bin/activate"

case "${1:-start}" in
    "start")
        if [ -f "$PID_FILE" ] && ps -p "$(cat "$PID_FILE")" > /dev/null 2>&1; then
            echo "Instance $INSTANCE_ID already running"
            exit 0
        fi
        
        cd "$INSTANCE_DIR"
        nohup "$SHARED_VENV/bin/gunicorn" \
            --bind="0.0.0.0:$FLASK_PORT" \
            --workers=1 \
            --worker-class=sync \
            --worker-connections=50 \
            --max-requests=200 \
            --timeout=30 \
            --keepalive=2 \
            --preload \
            --worker-tmp-dir=/dev/shm \
            --log-level=warning \
            --access-logfile="logs/access.log" \
            --error-logfile="logs/error.log" \
            app:app > "logs/app.log" 2>&1 &
        
        echo $! > "$PID_FILE"
        echo "Instance $INSTANCE_ID started on port $FLASK_PORT"
        ;;
    "stop")
        if [ -f "$PID_FILE" ]; then
            kill "$(cat "$PID_FILE")" 2>/dev/null || true
            rm -f "$PID_FILE"
            echo "Instance $INSTANCE_ID stopped"
        fi
        ;;
    "status")
        if [ -f "$PID_FILE" ] && ps -p "$(cat "$PID_FILE")" > /dev/null 2>&1; then
            echo "Instance $INSTANCE_ID: RUNNING (PID: $(cat "$PID_FILE"))"
        else
            echo "Instance $INSTANCE_ID: STOPPED"
        fi
        ;;
esac
