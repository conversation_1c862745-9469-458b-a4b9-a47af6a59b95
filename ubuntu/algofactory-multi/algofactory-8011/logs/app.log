2025-06-22 22:17:39,965 - apscheduler.scheduler - INFO - Scheduler started
2025-06-22 22:17:40,154 - apscheduler.scheduler - INFO - Scheduler started
Initializing Traffic Logs DB
Initializing Latency DB
Initializing Auth DB
Initializing User DB
Initializing Master Contract DB
Initializing API Log DB
Initializing Analyzer Table
Initializing Settings DB
Creating default settings (Live Mode)
Initializing Chartink DB
Initializing Traffic Logs DB
Initializing Latency DB
Initializing Strategy DB
2025-06-22 22:17:42,051 - websocket_proxy.app_integration - INFO - Starting WebSocket server in Flask application process
2025-06-22 22:17:42,052 - websocket_proxy.app_integration - INFO - Starting WebSocket proxy server in a separate thread
2025-06-22 22:17:42,052 - websocket_proxy.app_integration - INFO - WebSocket proxy server thread started
2025-06-22 22:17:42,052 - websocket_proxy.app_integration - INFO - WebSocket server integration with Flask complete
2025-06-22 22:17:42,059 - websocket_proxy - INFO - Initializing ZeroMQ listener task
2025-06-22 22:17:42,059 - websocket_proxy - INFO - Running in a non-main thread. Signal handlers will not be used.
2025-06-22 22:17:42,059 - websocket_proxy - INFO - Starting WebSocket server on localhost:20011
2025-06-22 22:17:42,069 - websocket_proxy - INFO - Starting ZeroMQ listener
2025-06-22 22:17:42,069 - websockets.server - INFO - server listening on 127.0.0.1:20011
2025-06-22 22:17:42,070 - websocket_proxy - INFO - WebSocket server successfully started on localhost:20011
2025-06-22 22:18:01,785 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:28:01,029 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:28:09,875 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:28:10,696 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:28:13,523 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:28:16,593 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:28:24,268 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:28:37,858 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:29:08,704 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:31:18,040 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:31:37,709 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:32:29,570 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:32:37,834 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:33:37,939 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:34:38,051 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:34:45,024 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:34:45,083 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:35:38,147 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:35:57,466 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:35:58,783 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:36:38,260 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:37:39,637 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:38:44,108 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:39:44,241 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:40:44,366 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:41:44,491 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:42:44,588 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:43:44,687 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:44:44,794 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:45:44,909 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:46:45,032 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:47:45,164 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:48:45,362 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:49:45,539 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:50:45,644 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:51:27,838 - websockets.server - ERROR - opening handshake failed
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/websockets/http11.py", line 138, in parse
    request_line = yield from parse_line(read_line)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/websockets/http11.py", line 309, in parse_line
    line = yield from read_line(MAX_LINE_LENGTH)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/websockets/streams.py", line 46, in read_line
    raise EOFError(f"stream ends after {p} bytes, before end of line")
EOFError: stream ends after 0 bytes, before end of line

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/websockets/server.py", line 545, in parse
    request = yield from Request.parse(
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/websockets/http11.py", line 140, in parse
    raise EOFError("connection closed while reading HTTP request line") from exc
EOFError: connection closed while reading HTTP request line

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/websockets/asyncio/server.py", line 356, in conn_handler
    await connection.handshake(
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/websockets/asyncio/server.py", line 207, in handshake
    raise self.protocol.handshake_exc
websockets.exceptions.InvalidMessage: did not receive a valid HTTP request
2025-06-22 22:51:45,745 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:52:45,861 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:53:46,000 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:54:46,110 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:55:46,203 - utils.session - INFO - Invalid session detected - clearing session
