aniso8601==9.0.1
anyio==4.8.0
APScheduler==3.11.0
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
asttokens==3.0.0
attrs==24.2.0
bcrypt==4.1.3
bidict==0.23.1
blinker==1.8.2
cachetools==5.3.3
certifi==2024.7.4
cffi==1.17.1
charset-normalizer==3.3.2
click==8.1.7
colorama==0.4.6
comm==0.2.2
cryptography==44.0.1
darkdetect==0.8.0
debugpy==1.8.13
decorator==5.2.1
Deprecated==1.2.14
dnspython==2.6.1
duckdb==1.2.2
email_validator==2.1.1
executing==2.2.0
Flask==3.0.3
Flask-Bcrypt==1.0.1
Flask-Cors==6.0.0
Flask-Limiter==3.7.0
Flask-Login==0.6.3
flask-restx==1.3.0
Flask-SocketIO==5.3.6
Flask-SQLAlchemy==3.1.1
Flask-WTF==1.2.1
greenlet==3.1.1
h11==0.16.0
h2==4.2.0
hpack==4.1.0
httpcore==1.0.9
httpx==0.28.1
hyperframe==6.1.0
idna==3.7
importlib_resources==6.4.0
ipykernel==6.29.5
ipython==9.0.2
ipython_pygments_lexers==1.1.1
itsdangerous==2.2.0
jedi==0.19.2
Jinja2==3.1.6
jsonschema==4.23.0
jsonschema-specifications==2023.12.1
jupyter_client==8.6.3
jupyter_core==5.7.2
limits==3.13.0
logzero==1.7.0
markdown-it-py==3.0.0
MarkupSafe==2.1.5
marshmallow==3.22.0
matplotlib-inline==0.1.7
mdurl==0.1.2
nest-asyncio==1.6.0
numpy==2.2.4
openalgo==1.0.13
ordered-set==4.1.0
packaging==24.1
pandas==2.2.3
pandas_ta==0.3.14b0
parso==0.8.4
pillow==11.0.0
platformdirs==4.3.7
prompt_toolkit==3.0.50
psutil==7.0.0
pure_eval==0.2.3
pycparser==2.22
Pygments==2.18.0
PyJWT==2.8.0
pyngrok==7.1.6
pyotp==2.9.0
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-engineio==4.9.1
python-socketio==5.11.3
pytz==2024.1
PyYAML==6.0.1
pyzmq==26.3.0
qrcode==8.0
referencing==0.35.1
requests==2.32.3
rich==13.7.1
rpds-py==0.20.0
setuptools==80.3.1
simple-websocket==1.0.0
six==1.16.0
sniffio==1.3.1
SQLAlchemy==2.0.31
stack-data==0.6.3
tornado==6.5.0
traitlets==5.14.3
typing_extensions==4.12.2
tzdata==2024.1
tzlocal==5.2
urllib3==2.2.2
wcwidth==0.2.13
websocket-client==1.8.0
websockets==15.0.1
logzero==1.7.0
Werkzeug==3.1.2
wheel==0.43.0
wrapt==1.16.0
wsproto==1.2.0
WTForms==3.1.2
zipp==3.19.2
zmq==0.0.0
gunicorn
eventlet
