#!/usr/bin/env python3
"""
AlgoFactory Multi-Instance Manager
This script manages multiple AlgoFactory instances with unique ports and configurations.
"""

import os
import sys
import json
import shutil
import subprocess
import argparse
from pathlib import Path
import time

class AlgoFactoryInstanceManager:
    def __init__(self):
        self.base_dir = Path("/home/<USER>/algofactory-multi")
        self.template_dir = self.base_dir / "template"
        self.instances_dir = self.base_dir / "instances"
        self.nginx_dir = self.base_dir / "nginx"
        self.shared_venv = Path("/home/<USER>/shared-venv")
        
        # Port ranges - using instance_id directly as port
        self.flask_port_base = 0  # Will use instance_id directly (1010, 1011, etc.)
        self.websocket_port_base = 12000
        self.zmq_port_base = 15000
        
        # Ensure directories exist
        self.base_dir.mkdir(exist_ok=True)
        self.instances_dir.mkdir(exist_ok=True)
        self.nginx_dir.mkdir(exist_ok=True)
        (self.nginx_dir / "sites").mkdir(exist_ok=True)
    
    def get_instance_ports(self, instance_id):
        """Calculate ports for an instance"""
        return {
            'flask': instance_id,  # Use instance_id directly (1010, 1011, etc.)
            'websocket': self.websocket_port_base + instance_id,
            'zmq': self.zmq_port_base + instance_id
        }
    
    def get_instance_path(self, instance_id):
        """Get the path for an instance"""
        return self.instances_dir / f"algofactory-{instance_id}"
    
    def instance_exists(self, instance_id):
        """Check if instance exists"""
        return self.get_instance_path(instance_id).exists()
    
    def create_template(self):
        """Create template from current algofactory"""
        current_algo = Path("/home/<USER>/myproject/algofactory")
        
        if self.template_dir.exists():
            print(f"Template already exists at {self.template_dir}")
            return True
            
        if not current_algo.exists():
            print(f"ERROR: Current algofactory not found at {current_algo}")
            return False
        
        print(f"Creating template from {current_algo}...")
        shutil.copytree(current_algo, self.template_dir, 
                       ignore=shutil.ignore_patterns('__pycache__', '*.pyc', 'logs', 'db', 'app.pid'))
        
        # Create .env.template
        self.create_env_template()
        print(f"✅ Template created at {self.template_dir}")
        return True
    
    def create_env_template(self):
        """Create environment template file"""
        env_template = """# AlgoFactory Instance Configuration
# This file is auto-generated for instance {INSTANCE_ID}

# Instance Configuration
INSTANCE_ID={INSTANCE_ID}

# Broker Configuration
BROKER_API_KEY = 'MZA0cLWq'
BROKER_API_SECRET = 'XIA6RJ3HPG4ZRKKYJLIZ6ROKAM'

# Market Data Configuration (Optional and Required only for XTS API Supported Brokers)
BROKER_API_KEY_MARKET = 'YOUR_BROKER_MARKET_API_KEY'
BROKER_API_SECRET_MARKET = 'YOUR_BROKER_MARKET_API_SECRET'

REDIRECT_URL = 'https://{INSTANCE_ID}.algofactory.in/angel/callback'

# Valid Brokers Configuration
VALID_BROKERS = 'fivepaisa,fivepaisaxts,aliceblue,angel,compositedge,dhan,dhan_sandbox,firstock,flattrade,fyers,groww,iifl,kotak,jainam,jainampro,paytm,pocketful,shoonya,tradejini,upstox,wisdom,zebu,zerodha'

# Security Configuration
APP_KEY = '3daa0403ce2501ee7432b75bf100048e3cf510d63d2754f952e93d88bf07ea84'
API_KEY_PEPPER = 'a25d94718479b170c16278e321ea6c989358bf499a658fd20c90033cef8ce772'

# Database Configuration
DATABASE_URL = 'sqlite:///db/algofactory-{INSTANCE_ID}.db' 

# Ngrok Configuration
NGROK_ALLOW = 'FALSE' 

# Host Server Configuration
HOST_SERVER = 'https://{INSTANCE_ID}.algofactory.in'  

# Flask App Configuration
FLASK_HOST_IP='0.0.0.0'  
FLASK_PORT='{FLASK_PORT}' 
FLASK_DEBUG='False' 
FLASK_ENV='production'

# WebSocket Configuration
WEBSOCKET_HOST='localhost'
WEBSOCKET_PORT='{WEBSOCKET_PORT}'
WEBSOCKET_URL='ws://localhost:{WEBSOCKET_PORT}'

# ZeroMQ Configuration
ZMQ_HOST='localhost'
ZMQ_PORT='{ZMQ_PORT}'

# Rate Limit Settings
LOGIN_RATE_LIMIT_MIN = "5 per minute" 
LOGIN_RATE_LIMIT_HOUR = "25 per hour"
API_RATE_LIMIT="10 per second"

# API Configuration
SMART_ORDER_DELAY = '0.5'
SESSION_EXPIRY_TIME = '03:00'

# CORS Configuration
CORS_ENABLED = 'TRUE'
CORS_ALLOWED_ORIGINS = 'https://{INSTANCE_ID}.algofactory.in'
CORS_ALLOWED_METHODS = 'GET,POST,DELETE,PUT,PATCH'
CORS_ALLOWED_HEADERS = 'Content-Type,Authorization,X-Requested-With'
CORS_EXPOSED_HEADERS = ''
CORS_ALLOW_CREDENTIALS = 'FALSE'
CORS_MAX_AGE = '86400'

# CSP Configuration
CSP_ENABLED = 'TRUE'
CSP_REPORT_ONLY = 'FALSE'
CSP_DEFAULT_SRC = "'self'"
CSP_SCRIPT_SRC = "'self' 'unsafe-inline' https://cdn.socket.io https://static.cloudflareinsights.com"
CSP_STYLE_SRC = "'self' 'unsafe-inline'"
CSP_IMG_SRC = "'self' data:"
CSP_CONNECT_SRC = "'self' wss: ws:"
CSP_FONT_SRC = "'self'"
CSP_OBJECT_SRC = "'none'"
CSP_MEDIA_SRC = "'self' data: https://*.amazonaws.com https://*.cloudfront.net"
CSP_FRAME_SRC = "'self'"
CSP_FORM_ACTION = "'self'"
CSP_FRAME_ANCESTORS = "'self'"
CSP_BASE_URI = "'self'"
CSP_UPGRADE_INSECURE_REQUESTS = 'FALSE'
CSP_REPORT_URI = ''

# CSRF Configuration
CSRF_ENABLED = 'TRUE'
CSRF_TIME_LIMIT = ''
"""
        
        template_file = self.template_dir / ".env.template"
        with open(template_file, 'w') as f:
            f.write(env_template)
    
    def create_instance(self, instance_id):
        """Create a new instance"""
        if self.instance_exists(instance_id):
            print(f"❌ Instance {instance_id} already exists")
            return False
        
        if not self.template_dir.exists():
            print("📋 Creating template first...")
            if not self.create_template():
                return False
        
        instance_path = self.get_instance_path(instance_id)
        ports = self.get_instance_ports(instance_id)
        
        print(f"🚀 Creating instance {instance_id}...")
        print(f"   Path: {instance_path}")
        print(f"   Flask Port: {ports['flask']}")
        print(f"   WebSocket Port: {ports['websocket']}")
        print(f"   ZMQ Port: {ports['zmq']}")
        
        # Copy template
        shutil.copytree(self.template_dir, instance_path)
        
        # Create instance-specific .env file
        self.create_instance_env(instance_id, ports)
        
        # Create instance-specific directories
        (instance_path / "db").mkdir(exist_ok=True)
        (instance_path / "logs").mkdir(exist_ok=True)
        (instance_path / "tmp").mkdir(exist_ok=True)
        
        # Set permissions
        os.chmod(instance_path / "start.sh", 0o755)
        
        print(f"✅ Instance {instance_id} created successfully")
        print(f"   URL: https://{instance_id}.algofactory.in")
        return True
    
    def create_instance_env(self, instance_id, ports):
        """Create instance-specific .env file"""
        template_file = self.template_dir / ".env.template"
        instance_env_file = self.get_instance_path(instance_id) / ".env"
        
        with open(template_file, 'r') as f:
            content = f.read()
        
        # Replace placeholders
        content = content.replace('{INSTANCE_ID}', str(instance_id))
        content = content.replace('{FLASK_PORT}', str(ports['flask']))
        content = content.replace('{WEBSOCKET_PORT}', str(ports['websocket']))
        content = content.replace('{ZMQ_PORT}', str(ports['zmq']))
        
        with open(instance_env_file, 'w') as f:
            f.write(content)
    
    def start_instance(self, instance_id):
        """Start an instance"""
        if not self.instance_exists(instance_id):
            print(f"❌ Instance {instance_id} does not exist")
            return False
        
        instance_path = self.get_instance_path(instance_id)
        print(f"🚀 Starting instance {instance_id}...")
        
        try:
            result = subprocess.run(
                ["./start.sh", "start"],
                cwd=instance_path,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                print(f"✅ Instance {instance_id} started successfully")
                return True
            else:
                print(f"❌ Failed to start instance {instance_id}")
                print(f"Error: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ Error starting instance {instance_id}: {e}")
            return False
    
    def stop_instance(self, instance_id):
        """Stop an instance"""
        if not self.instance_exists(instance_id):
            print(f"❌ Instance {instance_id} does not exist")
            return False
        
        instance_path = self.get_instance_path(instance_id)
        print(f"🛑 Stopping instance {instance_id}...")
        
        try:
            result = subprocess.run(
                ["./start.sh", "stop"],
                cwd=instance_path,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                print(f"✅ Instance {instance_id} stopped successfully")
                return True
            else:
                print(f"❌ Failed to stop instance {instance_id}")
                return False
        except Exception as e:
            print(f"❌ Error stopping instance {instance_id}: {e}")
            return False
    
    def delete_instance(self, instance_id):
        """Delete an instance"""
        if not self.instance_exists(instance_id):
            print(f"❌ Instance {instance_id} does not exist")
            return False
        
        # Stop instance first
        self.stop_instance(instance_id)
        
        instance_path = self.get_instance_path(instance_id)
        print(f"🗑️  Deleting instance {instance_id}...")
        
        try:
            shutil.rmtree(instance_path)
            print(f"✅ Instance {instance_id} deleted successfully")
            return True
        except Exception as e:
            print(f"❌ Error deleting instance {instance_id}: {e}")
            return False
    
    def list_instances(self):
        """List all instances"""
        instances = []
        for path in self.instances_dir.glob("algofactory-*"):
            if path.is_dir():
                instance_id = int(path.name.split("-")[1])
                instances.append(instance_id)
        
        instances.sort()
        
        if not instances:
            print("📭 No instances found")
            return []
        
        print("📋 AlgoFactory Instances:")
        print("=" * 50)
        for instance_id in instances:
            ports = self.get_instance_ports(instance_id)
            status = self.get_instance_status(instance_id)
            print(f"Instance {instance_id:4d} | Status: {status:8s} | Port: {ports['flask']:4d} | URL: https://{instance_id}.algofactory.in")
        
        return instances
    
    def get_instance_status(self, instance_id):
        """Get status of an instance"""
        if not self.instance_exists(instance_id):
            return "NOT_FOUND"
        
        instance_path = self.get_instance_path(instance_id)
        pid_file = instance_path / "app.pid"
        
        if not pid_file.exists():
            return "STOPPED"
        
        try:
            with open(pid_file, 'r') as f:
                pid = int(f.read().strip())
            
            # Check if process is running
            result = subprocess.run(["ps", "-p", str(pid)], capture_output=True)
            if result.returncode == 0:
                return "RUNNING"
            else:
                return "STOPPED"
        except:
            return "UNKNOWN"

def main():
    parser = argparse.ArgumentParser(description="AlgoFactory Multi-Instance Manager")
    parser.add_argument("action", choices=["create", "start", "stop", "delete", "list", "status", "template"])
    parser.add_argument("instance_id", nargs="?", type=int, help="Instance ID (e.g., 1010)")
    
    args = parser.parse_args()
    manager = AlgoFactoryInstanceManager()
    
    if args.action == "template":
        manager.create_template()
    elif args.action == "list":
        manager.list_instances()
    elif args.action == "status":
        if args.instance_id:
            status = manager.get_instance_status(args.instance_id)
            print(f"Instance {args.instance_id}: {status}")
        else:
            manager.list_instances()
    else:
        if not args.instance_id:
            print("❌ Instance ID required for this action")
            sys.exit(1)
        
        if args.action == "create":
            manager.create_instance(args.instance_id)
        elif args.action == "start":
            manager.start_instance(args.instance_id)
        elif args.action == "stop":
            manager.stop_instance(args.instance_id)
        elif args.action == "delete":
            manager.delete_instance(args.instance_id)

if __name__ == "__main__":
    main()
