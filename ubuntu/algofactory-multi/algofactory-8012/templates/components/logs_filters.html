<div class="card bg-base-100 shadow-lg mb-8">
    <div class="card-body">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
            <h2 class="text-xl font-bold">Trading Logs</h2>
            <button onclick="exportLogs()" class="btn btn-primary gap-2">
                <svg class="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
                </svg>
                Export to CSV
            </button>
        </div>
        <form id="log-filters" class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Date Range -->
            <div class="form-control w-full">
                <label class="label">
                    <span class="label-text">Start Date</span>
                </label>
                <input type="date" 
                       id="start_date" 
                       name="start_date" 
                       value="{{ start_date if start_date else '' }}"
                       class="input input-bordered w-full" />
            </div>
            <div class="form-control w-full">
                <label class="label">
                    <span class="label-text">End Date</span>
                </label>
                <input type="date" 
                       id="end_date" 
                       name="end_date" 
                       value="{{ end_date if end_date else '' }}"
                       class="input input-bordered w-full" />
            </div>
            <!-- Search -->
            <div class="form-control w-full">
                <label class="label">
                    <span class="label-text">Search</span>
                </label>
                <div class="relative">
                    <input type="text" 
                           id="search" 
                           name="search" 
                           value="{{ search_query }}"
                           placeholder="Search logs..." 
                           class="input input-bordered w-full pl-10" />
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 opacity-50" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                        </svg>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
