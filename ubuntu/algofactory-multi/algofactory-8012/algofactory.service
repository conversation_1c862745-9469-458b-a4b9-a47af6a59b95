[Unit]
Description=AlgoFactory Trading Application
After=network.target
Wants=network-online.target
StartLimitIntervalSec=0

[Service]
Type=simple
User=ubuntu
Group=ubuntu
WorkingDirectory=/home/<USER>/myproject/algofactory
Environment=PATH=/home/<USER>/shared-venv/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
ExecStart=/bin/bash -c "cd /home/<USER>/myproject/algofactory && ./start.sh monitor"
ExecStop=/bin/bash -c "cd /home/<USER>/myproject/algofactory && ./start.sh stop"
ExecReload=/bin/bash -c "cd /home/<USER>/myproject/algofactory && ./start.sh restart"
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=algofactory
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=/home/<USER>/myproject/algofactory
ProtectHome=true

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
