#!/usr/bin/env python3
"""
AlgoFactory Monitoring Script
This script provides various monitoring capabilities for the AlgoFactory application.
"""

import requests
import json
import time
import psutil
import os
import sys
from datetime import datetime
import subprocess

class AlgoFactoryMonitor:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.app_pid_file = "/home/<USER>/myproject/algofactory/app.pid"
        
    def check_application_health(self):
        """Check if the application is responding"""
        try:
            response = requests.get(f"{self.base_url}/", timeout=10)
            return {
                "status": "healthy" if response.status_code == 200 else "unhealthy",
                "status_code": response.status_code,
                "response_time": response.elapsed.total_seconds(),
                "timestamp": datetime.now().isoformat()
            }
        except requests.exceptions.RequestException as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def check_process_status(self):
        """Check if the application process is running"""
        try:
            if os.path.exists(self.app_pid_file):
                with open(self.app_pid_file, 'r') as f:
                    pid = int(f.read().strip())
                
                if psutil.pid_exists(pid):
                    process = psutil.Process(pid)
                    return {
                        "status": "running",
                        "pid": pid,
                        "cpu_percent": process.cpu_percent(),
                        "memory_percent": process.memory_percent(),
                        "memory_info": process.memory_info()._asdict(),
                        "create_time": datetime.fromtimestamp(process.create_time()).isoformat(),
                        "timestamp": datetime.now().isoformat()
                    }
                else:
                    return {
                        "status": "not_running",
                        "error": "PID exists but process not found",
                        "timestamp": datetime.now().isoformat()
                    }
            else:
                return {
                    "status": "not_running",
                    "error": "PID file not found",
                    "timestamp": datetime.now().isoformat()
                }
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def check_system_resources(self):
        """Check system resource usage"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            return {
                "cpu_percent": cpu_percent,
                "memory": {
                    "total": memory.total,
                    "available": memory.available,
                    "percent": memory.percent,
                    "used": memory.used,
                    "free": memory.free
                },
                "disk": {
                    "total": disk.total,
                    "used": disk.used,
                    "free": disk.free,
                    "percent": (disk.used / disk.total) * 100
                },
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            return {
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def check_nginx_status(self):
        """Check Nginx status"""
        try:
            result = subprocess.run(['sudo', 'systemctl', 'is-active', 'nginx'], 
                                  capture_output=True, text=True)
            return {
                "status": result.stdout.strip(),
                "active": result.stdout.strip() == "active",
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def get_full_status(self):
        """Get comprehensive status report"""
        return {
            "application": self.check_application_health(),
            "process": self.check_process_status(),
            "system": self.check_system_resources(),
            "nginx": self.check_nginx_status(),
            "overall_timestamp": datetime.now().isoformat()
        }
    
    def send_to_external_monitor(self, webhook_url, status_data):
        """Send status data to external monitoring service"""
        try:
            headers = {'Content-Type': 'application/json'}
            response = requests.post(webhook_url, 
                                   data=json.dumps(status_data), 
                                   headers=headers, 
                                   timeout=30)
            return {
                "success": response.status_code == 200,
                "status_code": response.status_code,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

def main():
    monitor = AlgoFactoryMonitor()
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "health":
            result = monitor.check_application_health()
            print(json.dumps(result, indent=2))
            sys.exit(0 if result["status"] == "healthy" else 1)
            
        elif command == "process":
            result = monitor.check_process_status()
            print(json.dumps(result, indent=2))
            sys.exit(0 if result["status"] == "running" else 1)
            
        elif command == "system":
            result = monitor.check_system_resources()
            print(json.dumps(result, indent=2))
            
        elif command == "nginx":
            result = monitor.check_nginx_status()
            print(json.dumps(result, indent=2))
            sys.exit(0 if result["active"] else 1)
            
        elif command == "full":
            result = monitor.get_full_status()
            print(json.dumps(result, indent=2))
            
            # Determine overall health
            app_healthy = result["application"]["status"] == "healthy"
            process_running = result["process"]["status"] == "running"
            nginx_active = result["nginx"]["active"]
            
            overall_healthy = app_healthy and process_running and nginx_active
            sys.exit(0 if overall_healthy else 1)
            
        elif command == "monitor":
            # Continuous monitoring mode
            print("Starting continuous monitoring...")
            while True:
                status = monitor.get_full_status()
                print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Status Check:")
                print(f"  Application: {status['application']['status']}")
                print(f"  Process: {status['process']['status']}")
                print(f"  Nginx: {'active' if status['nginx']['active'] else 'inactive'}")
                print(f"  CPU: {status['system']['cpu_percent']:.1f}%")
                print(f"  Memory: {status['system']['memory']['percent']:.1f}%")
                
                time.sleep(60)  # Check every minute
                
        else:
            print(f"Unknown command: {command}")
            sys.exit(1)
    else:
        # Default: show full status
        result = monitor.get_full_status()
        print(json.dumps(result, indent=2))

if __name__ == "__main__":
    main()
