#!/bin/bash

# AlgoFactory 24/7 Setup and Management Script
# This script helps you manage your AlgoFactory application for 24/7 operation

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_DIR="/home/<USER>/myproject/algofactory"
DOMAIN="algo.algofactory.in"

# Logging function
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1"
}

warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" >&2
}

show_status() {
    log "=== AlgoFactory 24/7 Status ==="
    
    # Check application status
    echo -e "\n${BLUE}Application Status:${NC}"
    cd "$PROJECT_DIR"
    ./start.sh status
    
    # Check systemd service (if enabled)
    echo -e "\n${BLUE}SystemD Service Status:${NC}"
    if systemctl is-enabled algofactory >/dev/null 2>&1; then
        sudo systemctl status algofactory --no-pager -l
    else
        warning "SystemD service not enabled"
    fi
    
    # Check Nginx status
    echo -e "\n${BLUE}Nginx Status:${NC}"
    sudo systemctl status nginx --no-pager -l
    
    # Check monitoring
    echo -e "\n${BLUE}Health Check:${NC}"
    /home/<USER>/shared-venv/bin/python3 "$PROJECT_DIR/monitoring.py" health
    
    # Check firewall
    echo -e "\n${BLUE}Firewall Status:${NC}"
    sudo ufw status
}

enable_24x7() {
    log "Enabling 24/7 operation..."
    
    # Enable and start systemd service
    log "Configuring systemd service..."
    sudo systemctl enable algofactory
    sudo systemctl start algofactory
    
    # Enable Nginx
    log "Enabling Nginx..."
    sudo systemctl enable nginx
    sudo systemctl start nginx
    
    success "24/7 operation enabled!"
    success "Your application will now:"
    success "  - Start automatically on boot"
    success "  - Restart automatically if it crashes"
    success "  - Be accessible via Nginx reverse proxy"
}

disable_24x7() {
    log "Disabling 24/7 operation..."
    
    # Stop and disable systemd service
    sudo systemctl stop algofactory
    sudo systemctl disable algofactory
    
    warning "24/7 operation disabled. Application will not start automatically."
}

setup_ssl() {
    log "Setting up SSL certificate for $DOMAIN..."
    
    # Check if domain is pointing to this server
    log "Checking domain configuration..."
    
    # Get server's public IP
    SERVER_IP=$(curl -s ifconfig.me || curl -s ipinfo.io/ip || echo "Unable to detect")
    log "Server IP: $SERVER_IP"
    
    # Check domain resolution
    DOMAIN_IP=$(dig +short "$DOMAIN" | tail -n1)
    log "Domain IP: $DOMAIN_IP"
    
    if [ "$SERVER_IP" = "$DOMAIN_IP" ]; then
        success "Domain is correctly pointing to this server!"
        
        # Get Let's Encrypt certificate
        log "Obtaining Let's Encrypt certificate..."
        sudo certbot --nginx -d "$DOMAIN" --non-interactive --agree-tos --email <EMAIL>
        
        success "SSL certificate installed successfully!"
    else
        warning "Domain is not pointing to this server yet."
        warning "Please update your DNS settings in Hostinger:"
        warning "  1. Go to your Hostinger control panel"
        warning "  2. Navigate to DNS settings for algofactory.in"
        warning "  3. Update the A record for 'algo' subdomain to: $SERVER_IP"
        warning "  4. Wait for DNS propagation (5-30 minutes)"
        warning "  5. Run this script again with 'ssl' option"
    fi
}

show_monitoring_info() {
    log "=== Monitoring Information ==="
    
    echo -e "\n${GREEN}Local Monitoring:${NC}"
    echo "  • Status check: cd $PROJECT_DIR && ./start.sh status"
    echo "  • Health check: /home/<USER>/shared-venv/bin/python3 $PROJECT_DIR/monitoring.py health"
    echo "  • Full status: /home/<USER>/shared-venv/bin/python3 $PROJECT_DIR/monitoring.py full"
    echo "  • Live monitoring: /home/<USER>/shared-venv/bin/python3 $PROJECT_DIR/monitoring.py monitor"
    
    echo -e "\n${GREEN}Web-based Monitoring:${NC}"
    echo "  • Dashboard: file://$PROJECT_DIR/monitor_dashboard.html"
    echo "  • Simple status: http://$DOMAIN:8080/monitor"
    
    echo -e "\n${GREEN}External Monitoring URLs:${NC}"
    echo "  • Main site: https://$DOMAIN"
    echo "  • Health endpoint: https://$DOMAIN/health (if implemented)"
    echo "  • Status endpoint: https://$DOMAIN/status (restricted access)"
    
    echo -e "\n${GREEN}Monitoring Services You Can Use:${NC}"
    echo "  • UptimeRobot (free): https://uptimerobot.com"
    echo "  • Pingdom: https://pingdom.com"
    echo "  • StatusCake: https://statuscake.com"
    echo "  • Site24x7: https://site24x7.com"
    
    echo -e "\n${GREEN}Log Files:${NC}"
    echo "  • Application logs: $PROJECT_DIR/logs/"
    echo "  • Nginx access log: /var/log/nginx/algofactory_access.log"
    echo "  • Nginx error log: /var/log/nginx/algofactory_error.log"
    echo "  • System logs: sudo journalctl -u algofactory -f"
}

show_domain_setup() {
    log "=== Domain Setup Instructions ==="
    
    SERVER_IP=$(curl -s ifconfig.me || curl -s ipinfo.io/ip || echo "Unable to detect")
    
    echo -e "\n${GREEN}Your Server IP:${NC} $SERVER_IP"
    echo -e "\n${GREEN}Hostinger DNS Setup:${NC}"
    echo "  1. Login to your Hostinger account"
    echo "  2. Go to 'Domains' section"
    echo "  3. Click on 'algofactory.in'"
    echo "  4. Go to 'DNS Zone'"
    echo "  5. Add/Edit these records:"
    echo "     • Type: A"
    echo "     • Name: algo"
    echo "     • Value: $SERVER_IP"
    echo "     • TTL: 3600"
    echo "  6. Save changes"
    echo "  7. Wait 5-30 minutes for DNS propagation"
    echo "  8. Test with: nslookup algo.algofactory.in"
    
    echo -e "\n${GREEN}After DNS Setup:${NC}"
    echo "  • Run: $0 ssl (to get SSL certificate)"
    echo "  • Your site will be available at: https://algo.algofactory.in"
}

show_help() {
    echo -e "${GREEN}AlgoFactory 24/7 Management Script${NC}"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  status      - Show comprehensive status"
    echo "  enable      - Enable 24/7 operation (systemd + nginx)"
    echo "  disable     - Disable 24/7 operation"
    echo "  ssl         - Setup SSL certificate (after DNS is configured)"
    echo "  monitor     - Show monitoring information"
    echo "  domain      - Show domain setup instructions"
    echo "  help        - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 status   # Check current status"
    echo "  $0 enable   # Enable 24/7 operation"
    echo "  $0 ssl      # Setup SSL certificate"
}

# Main execution
case "${1:-help}" in
    "status")
        show_status
        ;;
    "enable")
        enable_24x7
        ;;
    "disable")
        disable_24x7
        ;;
    "ssl")
        setup_ssl
        ;;
    "monitor")
        show_monitoring_info
        ;;
    "domain")
        show_domain_setup
        ;;
    "help"|*)
        show_help
        ;;
esac
