2025-06-22 19:36:33,959 - apscheduler.scheduler - INFO - Scheduler started
2025-06-22 19:36:34,242 - apscheduler.scheduler - INFO - Scheduler started
2025-06-22 19:36:35,802 - websocket_proxy.app_integration - INFO - Starting WebSocket server in Flask application process
2025-06-22 19:36:35,803 - websocket_proxy.app_integration - INFO - Starting WebSocket proxy server in a separate thread
2025-06-22 19:36:35,804 - websocket_proxy.app_integration - INFO - WebSocket proxy server thread started
2025-06-22 19:36:35,804 - websocket_proxy.app_integration - INFO - WebSocket server integration with Flask complete
[2025-06-22 19:36:35 +0000] [5839] [INFO] Starting gunicorn 23.0.0
[2025-06-22 19:36:35 +0000] [5839] [INFO] Listening at: http://0.0.0.0:5000 (5839)
[2025-06-22 19:36:35 +0000] [5839] [INFO] Using worker: eventlet
[2025-06-22 19:36:35 +0000] [5876] [INFO] Booting worker with pid: 5876
2025-06-22 19:36:35,842 - websocket_proxy - INFO - Initializing ZeroMQ listener task
2025-06-22 19:36:35,842 - websocket_proxy - INFO - Running in a non-main thread. Signal handlers will not be used.
2025-06-22 19:36:35,842 - websocket_proxy - INFO - Starting WebSocket server on localhost:8765
2025-06-22 19:36:35,857 - websocket_proxy - INFO - Starting ZeroMQ listener
2025-06-22 19:36:35,861 - websockets.server - INFO - server listening on 127.0.0.1:8765
2025-06-22 19:36:35,863 - websocket_proxy - INFO - WebSocket server successfully started on localhost:8765
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
13 RLock(s) were not greened, to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
2025-06-22 19:36:53,651 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 19:36:53,677 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
[2025-06-22 19:37:15 +0000] [5839] [INFO] Handling signal: term
[2025-06-22 19:37:15 +0000] [5876] [INFO] Worker exiting (pid: 5876)
Initializing Traffic Logs DB
Initializing Latency DB
Initializing Auth DB
Initializing User DB
Initializing Master Contract DB
Initializing API Log DB
Initializing Analyzer Table
Initializing Settings DB
Creating default settings (Live Mode)
Initializing Chartink DB
Initializing Traffic Logs DB
Initializing Latency DB
Initializing Strategy DB
[2025-06-22 19:37:16 +0000] [5839] [INFO] Shutting down: Master
Initializing Traffic Logs DB
Initializing Latency DB
Initializing Auth DB
Initializing User DB
Initializing Master Contract DB
Initializing API Log DB
Initializing Analyzer Table
Initializing Settings DB
Creating default settings (Live Mode)
Initializing Chartink DB
Initializing Traffic Logs DB
Initializing Latency DB
Initializing Strategy DB
2025-06-22 19:37:26,970 - apscheduler.scheduler - INFO - Scheduler started
2025-06-22 19:37:27,207 - apscheduler.scheduler - INFO - Scheduler started
2025-06-22 19:37:28,249 - websocket_proxy.app_integration - INFO - Starting WebSocket server in Flask application process
2025-06-22 19:37:28,249 - websocket_proxy.app_integration - INFO - Starting WebSocket proxy server in a separate thread
2025-06-22 19:37:28,250 - websocket_proxy.app_integration - INFO - WebSocket proxy server thread started
2025-06-22 19:37:28,250 - websocket_proxy.app_integration - INFO - WebSocket server integration with Flask complete
[2025-06-22 19:37:28 +0000] [6163] [INFO] Starting gunicorn 23.0.0
[2025-06-22 19:37:28 +0000] [6163] [INFO] Listening at: http://0.0.0.0:5000 (6163)
[2025-06-22 19:37:28 +0000] [6163] [INFO] Using worker: eventlet
[2025-06-22 19:37:28 +0000] [6190] [INFO] Booting worker with pid: 6190
2025-06-22 19:37:28,284 - websocket_proxy - INFO - Initializing ZeroMQ listener task
2025-06-22 19:37:28,288 - websocket_proxy - INFO - Running in a non-main thread. Signal handlers will not be used.
2025-06-22 19:37:28,288 - websocket_proxy - INFO - Starting WebSocket server on localhost:8765
2025-06-22 19:37:28,310 - websocket_proxy - INFO - Starting ZeroMQ listener
2025-06-22 19:37:28,317 - websockets.server - INFO - server listening on 127.0.0.1:8765
2025-06-22 19:37:28,317 - websocket_proxy - INFO - WebSocket server successfully started on localhost:8765
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
13 RLock(s) were not greened, to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
[2025-06-22 19:37:40 +0000] [6163] [INFO] Handling signal: term
[2025-06-22 19:37:41 +0000] [6190] [INFO] Worker exiting (pid: 6190)
Initializing Traffic Logs DB
Initializing Latency DB
Initializing Auth DB
Initializing User DB
Initializing Master Contract DB
Initializing API Log DB
Initializing Analyzer Table
Initializing Settings DB
Initializing Chartink DB
Initializing Traffic Logs DB
Initializing Latency DB
Initializing Strategy DB
[2025-06-22 19:37:41 +0000] [6163] [INFO] Shutting down: Master
Initializing Traffic Logs DB
Initializing Latency DB
Initializing Auth DB
Initializing User DB
Initializing Master Contract DB
Initializing API Log DB
Initializing Analyzer Table
Initializing Settings DB
Initializing Chartink DB
Initializing Traffic Logs DB
Initializing Latency DB
Initializing Strategy DB
2025-06-22 19:51:40,014 - apscheduler.scheduler - INFO - Scheduler started
2025-06-22 19:51:40,261 - apscheduler.scheduler - INFO - Scheduler started
2025-06-22 19:51:41,367 - websocket_proxy.app_integration - INFO - Starting WebSocket server in Flask application process
2025-06-22 19:51:41,367 - websocket_proxy.app_integration - INFO - Starting WebSocket proxy server in a separate thread
2025-06-22 19:51:41,373 - websocket_proxy.app_integration - INFO - WebSocket proxy server thread started
2025-06-22 19:51:41,373 - websocket_proxy.app_integration - INFO - WebSocket server integration with Flask complete
[2025-06-22 19:51:41 +0000] [8420] [INFO] Starting gunicorn 23.0.0
[2025-06-22 19:51:41 +0000] [8420] [INFO] Listening at: http://0.0.0.0:5000 (8420)
[2025-06-22 19:51:41 +0000] [8420] [INFO] Using worker: eventlet
[2025-06-22 19:51:41 +0000] [8452] [INFO] Booting worker with pid: 8452
2025-06-22 19:51:41,390 - websocket_proxy - INFO - Initializing ZeroMQ listener task
2025-06-22 19:51:41,390 - websocket_proxy - INFO - Running in a non-main thread. Signal handlers will not be used.
2025-06-22 19:51:41,390 - websocket_proxy - INFO - Starting WebSocket server on localhost:8765
2025-06-22 19:51:41,411 - websocket_proxy - INFO - Starting ZeroMQ listener
2025-06-22 19:51:41,420 - websocket_proxy - ERROR - Failed to start WebSocket server: [Errno 98] error while attempting to bind on address ('127.0.0.1', 8765): address already in use
Traceback (most recent call last):
  File "/home/<USER>/myproject/algofactory/websocket_proxy/server.py", line 117, in start
    async with websockets.serve(self.handle_client, self.host, self.port):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/websockets/asyncio/server.py", line 813, in __aenter__
    return await self
           ^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/websockets/asyncio/server.py", line 831, in __await_impl__
    server = await self.create_server
             ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/base_events.py", line 1572, in create_server
    raise OSError(err.errno, msg) from None
OSError: [Errno 98] error while attempting to bind on address ('127.0.0.1', 8765): address already in use

2025-06-22 19:51:41,423 - websocket_proxy - ERROR - Error in start method: [Errno 98] error while attempting to bind on address ('127.0.0.1', 8765): address already in use
2025-06-22 19:51:41,423 - websocket_proxy - ERROR - Server error: [Errno 98] error while attempting to bind on address ('127.0.0.1', 8765): address already in use
Traceback (most recent call last):
  File "/home/<USER>/myproject/algofactory/websocket_proxy/server.py", line 903, in main
    await proxy.start()
  File "/home/<USER>/myproject/algofactory/websocket_proxy/server.py", line 117, in start
    async with websockets.serve(self.handle_client, self.host, self.port):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/websockets/asyncio/server.py", line 813, in __aenter__
    return await self
           ^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/websockets/asyncio/server.py", line 831, in __await_impl__
    server = await self.create_server
             ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/base_events.py", line 1572, in create_server
    raise OSError(err.errno, msg) from None
OSError: [Errno 98] error while attempting to bind on address ('127.0.0.1', 8765): address already in use

An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
13 RLock(s) were not greened, to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
[2025-06-22 19:59:28 +0000] [8420] [ERROR] Worker (pid:8452) was sent SIGHUP!
[2025-06-22 19:59:28 +0000] [8420] [INFO] Handling signal: hup
[2025-06-22 19:59:28 +0000] [8420] [INFO] Hang up: Master
[2025-06-22 19:59:28 +0000] [8620] [INFO] Booting worker with pid: 8620
Initializing Traffic Logs DB
Initializing Latency DB
Initializing Auth DB
Initializing User DB
Initializing Master Contract DB
Initializing API Log DB
Initializing Analyzer Table
Initializing Settings DB
Initializing Chartink DB
Initializing Traffic Logs DB
Initializing Latency DB
Initializing Strategy DB
[2025-06-22 19:59:28 +0000] [8420] [INFO] Handling signal: term
[2025-06-22 19:59:28 +0000] [8623] [INFO] Booting worker with pid: 8623
2025-06-22 19:59:28,836 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-2' coro=<WebSocketProxy.zmq_listener() running at /home/<USER>/myproject/algofactory/websocket_proxy/server.py:776> wait_for=<Future cancelled>>
2025-06-22 19:59:28,842 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-2' coro=<WebSocketProxy.zmq_listener() running at /home/<USER>/myproject/algofactory/websocket_proxy/server.py:776> wait_for=<Future cancelled>>
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 593, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
13 RLock(s) were not greened, to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
13 RLock(s) were not greened, to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
[2025-06-22 19:59:58 +0000] [8420] [INFO] Shutting down: Master
2025-06-22 19:59:58,631 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-2' coro=<WebSocketProxy.zmq_listener() running at /home/<USER>/myproject/algofactory/websocket_proxy/server.py:776> wait_for=<Future cancelled>>
2025-06-22 20:08:36,144 - apscheduler.scheduler - INFO - Scheduler started
2025-06-22 20:08:36,371 - apscheduler.scheduler - INFO - Scheduler started
2025-06-22 20:08:37,447 - websocket_proxy.app_integration - INFO - Starting WebSocket server in Flask application process
2025-06-22 20:08:37,447 - websocket_proxy.app_integration - INFO - Starting WebSocket proxy server in a separate thread
2025-06-22 20:08:37,448 - websocket_proxy.app_integration - INFO - WebSocket proxy server thread started
2025-06-22 20:08:37,448 - websocket_proxy.app_integration - INFO - WebSocket server integration with Flask complete
[2025-06-22 20:08:37 +0000] [2896] [INFO] Starting gunicorn 23.0.0
[2025-06-22 20:08:37 +0000] [2896] [INFO] Listening at: http://0.0.0.0:5000 (2896)
[2025-06-22 20:08:37 +0000] [2896] [INFO] Using worker: eventlet
[2025-06-22 20:08:37 +0000] [2919] [INFO] Booting worker with pid: 2919
2025-06-22 20:08:37,477 - websocket_proxy - INFO - Initializing ZeroMQ listener task
2025-06-22 20:08:37,477 - websocket_proxy - INFO - Running in a non-main thread. Signal handlers will not be used.
2025-06-22 20:08:37,478 - websocket_proxy - INFO - Starting WebSocket server on localhost:8765
2025-06-22 20:08:37,500 - websocket_proxy - INFO - Starting ZeroMQ listener
2025-06-22 20:08:37,503 - websockets.server - INFO - server listening on 127.0.0.1:8765
2025-06-22 20:08:37,504 - websocket_proxy - INFO - WebSocket server successfully started on localhost:8765
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
13 RLock(s) were not greened, to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
2025-06-22 20:08:48,124 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 20:08:48,150 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 20:09:00,322 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 20:09:00,357 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 20:16:04,514 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 20:16:04,521 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 20:16:09,497 - app - ERROR - Exception on /auth/login [GET]
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/flask/app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/flask/app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/flask_restx/api.py", line 672, in error_router
    return original_handler(e)
           ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/flask_cors/extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/flask/app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/flask/app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/flask_limiter/extension.py", line 1303, in __inner
    return cast(R, flask.current_app.ensure_sync(obj)(*a, **k))
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/flask_limiter/extension.py", line 1303, in __inner
    return cast(R, flask.current_app.ensure_sync(obj)(*a, **k))
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/myproject/algofactory/blueprints/auth.py", line 26, in login
    if find_user_by_username() is None:
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/myproject/algofactory/database/user_db.py", line 114, in find_user_by_username
    return User.query.filter_by(is_admin=True).first()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/orm/query.py", line 2728, in first
    return self.limit(1)._iter().first()  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/orm/query.py", line 2827, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/orm/session.py", line 2351, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/orm/session.py", line 2226, in _execute_internal
    conn = self._connection_for_bind(bind)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/orm/session.py", line 2095, in _connection_for_bind
    return trans._connection_for_bind(engine, execution_options)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<string>", line 2, in _connection_for_bind
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/orm/state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/orm/session.py", line 1189, in _connection_for_bind
    conn = bind.connect()
           ^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 3276, in connect
    return self._connection_cls(self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 146, in __init__
    self._dbapi_connection = engine.raw_connection()
                             ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 3300, in raw_connection
    return self.pool.connect()
           ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py", line 449, in connect
    return _ConnectionFairy._checkout(self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py", line 1263, in _checkout
    fairy = _ConnectionRecord.checkout(pool)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py", line 712, in checkout
    rec = pool._do_get()
          ^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/pool/impl.py", line 158, in _do_get
    return self._pool.get(wait, self._timeout)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/sqlalchemy/util/queue.py", line 203, in get
    self.not_full.notify()
  File "/usr/lib/python3.12/threading.py", line 405, in notify
    raise RuntimeError("cannot notify on un-acquired lock")
RuntimeError: cannot notify on un-acquired lock
2025-06-22 20:16:09,549 - app - ERROR - Server Error: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-22 20:16:09,569 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 20:16:18,443 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 20:16:18,445 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 20:16:19,814 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 20:16:19,833 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 20:16:22,432 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 20:18:34,996 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 20:18:35,013 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 20:18:37,870 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 20:18:37,873 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 20:18:42,171 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 20:18:42,185 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 20:21:18,759 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 20:21:18,773 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 20:21:24,637 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 20:21:24,665 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
[2025-06-22 20:46:53 +0000] [2896] [ERROR] Worker (pid:2919) was sent SIGHUP!
[2025-06-22 20:46:53 +0000] [6439] [INFO] Booting worker with pid: 6439
[2025-06-22 20:46:53 +0000] [2896] [INFO] Handling signal: hup
[2025-06-22 20:46:53 +0000] [2896] [INFO] Hang up: Master
Initializing Traffic Logs DB
Initializing Latency DB
Initializing Auth DB
Initializing User DB
Initializing Master Contract DB
Initializing API Log DB
Initializing Analyzer Table
Initializing Settings DB
Initializing Chartink DB
Initializing Traffic Logs DB
Initializing Latency DB
Initializing Strategy DB
[2025-06-22 20:46:53 +0000] [2896] [INFO] Handling signal: term
[2025-06-22 20:46:53 +0000] [6443] [INFO] Booting worker with pid: 6443
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 593, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
13 RLock(s) were not greened, to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
13 RLock(s) were not greened, to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
[2025-06-22 20:47:23 +0000] [2896] [INFO] Shutting down: Master
2025-06-22 20:56:44,780 - apscheduler.scheduler - INFO - Scheduler started
2025-06-22 20:56:45,022 - apscheduler.scheduler - INFO - Scheduler started
2025-06-22 20:56:46,238 - websocket_proxy.app_integration - INFO - Starting WebSocket server in Flask application process
2025-06-22 20:56:46,238 - websocket_proxy.app_integration - INFO - Starting WebSocket proxy server in a separate thread
2025-06-22 20:56:46,238 - websocket_proxy.app_integration - INFO - WebSocket proxy server thread started
2025-06-22 20:56:46,238 - websocket_proxy.app_integration - INFO - WebSocket server integration with Flask complete
[2025-06-22 20:56:46 +0000] [2263] [INFO] Starting gunicorn 23.0.0
[2025-06-22 20:56:46 +0000] [2263] [INFO] Listening at: http://0.0.0.0:5000 (2263)
[2025-06-22 20:56:46 +0000] [2263] [INFO] Using worker: eventlet
[2025-06-22 20:56:46 +0000] [2298] [INFO] Booting worker with pid: 2298
2025-06-22 20:56:46,277 - websocket_proxy - INFO - Initializing ZeroMQ listener task
2025-06-22 20:56:46,278 - websocket_proxy - INFO - Running in a non-main thread. Signal handlers will not be used.
2025-06-22 20:56:46,278 - websocket_proxy - INFO - Starting WebSocket server on localhost:8765
2025-06-22 20:56:46,308 - websocket_proxy - INFO - Starting ZeroMQ listener
2025-06-22 20:56:46,313 - websockets.server - INFO - server listening on 127.0.0.1:8765
2025-06-22 20:56:46,313 - websocket_proxy - INFO - WebSocket server successfully started on localhost:8765
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
13 RLock(s) were not greened, to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
2025-06-22 20:57:11,318 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 20:57:11,344 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 20:57:13,983 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 20:57:14,001 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 20:57:51,590 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 20:57:51,592 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 20:58:48,897 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 20:58:48,909 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 20:59:20,739 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 20:59:20,743 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 20:59:24,096 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 20:59:24,114 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 20:59:26,605 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 20:59:26,606 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 20:59:28,610 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 20:59:28,620 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 20:59:29,310 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 20:59:29,314 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 20:59:43,173 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 20:59:43,184 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 20:59:54,611 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 20:59:54,613 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 21:00:27,353 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 21:00:27,363 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 21:00:28,167 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 21:00:28,168 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 21:00:28,729 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 21:00:28,739 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 21:00:37,322 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 21:00:37,323 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 21:00:37,657 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 21:00:37,667 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 21:00:37,742 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 21:00:37,796 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 21:00:37,840 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 21:00:37,939 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 21:00:37,983 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 21:00:38,038 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 21:00:38,083 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 21:00:38,135 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 21:00:38,179 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 21:00:51,134 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 21:00:51,144 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 21:00:52,600 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 21:00:52,601 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 21:00:58,228 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 21:00:58,238 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 21:00:58,754 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 21:00:58,755 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 21:00:59,463 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 21:00:59,483 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 21:01:01,320 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 21:01:01,321 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 21:01:02,541 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 21:01:02,550 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 21:01:03,474 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 21:01:03,476 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 21:01:03,898 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 21:01:03,907 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 21:01:14,342 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 21:01:14,344 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 21:01:18,362 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 21:01:18,371 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 21:04:38,761 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 21:04:39,251 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 21:04:39,497 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 21:04:39,754 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 21:04:39,962 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 21:04:40,484 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
2025-06-22 21:04:40,811 - database.traffic_db - ERROR - Error logging traffic: cannot notify on un-acquired lock
[2025-06-22 21:13:36 +0000] [2263] [INFO] Handling signal: term
[2025-06-22 21:13:37 +0000] [2298] [INFO] Worker exiting (pid: 2298)
Initializing Traffic Logs DB
Initializing Latency DB
Initializing Auth DB
Initializing User DB
Initializing Master Contract DB
Initializing API Log DB
Initializing Analyzer Table
Initializing Settings DB
Initializing Chartink DB
Initializing Traffic Logs DB
Initializing Latency DB
Initializing Strategy DB
[2025-06-22 21:13:38 +0000] [2263] [INFO] Shutting down: Master
Initializing Traffic Logs DB
Initializing Latency DB
Initializing Auth DB
Initializing User DB
Initializing Master Contract DB
Initializing API Log DB
Initializing Analyzer Table
Initializing Settings DB
Initializing Chartink DB
Initializing Traffic Logs DB
Initializing Latency DB
Initializing Strategy DB
2025-06-22 21:14:19,141 - apscheduler.scheduler - INFO - Scheduler started
2025-06-22 21:14:19,385 - apscheduler.scheduler - INFO - Scheduler started
2025-06-22 21:14:20,560 - websocket_proxy.app_integration - INFO - Starting WebSocket server in Flask application process
2025-06-22 21:14:20,560 - websocket_proxy.app_integration - INFO - Starting WebSocket proxy server in a separate thread
2025-06-22 21:14:20,562 - websocket_proxy.app_integration - INFO - WebSocket proxy server thread started
2025-06-22 21:14:20,563 - websocket_proxy.app_integration - INFO - WebSocket server integration with Flask complete
[2025-06-22 21:14:20 +0000] [4053] [INFO] Starting gunicorn 23.0.0
[2025-06-22 21:14:20 +0000] [4053] [INFO] Listening at: http://0.0.0.0:5000 (4053)
[2025-06-22 21:14:20 +0000] [4053] [INFO] Using worker: eventlet
[2025-06-22 21:14:20 +0000] [4093] [INFO] Booting worker with pid: 4093
2025-06-22 21:14:20,588 - websocket_proxy - INFO - Initializing ZeroMQ listener task
2025-06-22 21:14:20,588 - websocket_proxy - INFO - Running in a non-main thread. Signal handlers will not be used.
2025-06-22 21:14:20,588 - websocket_proxy - INFO - Starting WebSocket server on localhost:8765
2025-06-22 21:14:20,608 - websocket_proxy - INFO - Starting ZeroMQ listener
2025-06-22 21:14:20,612 - websockets.server - INFO - server listening on 127.0.0.1:8765
2025-06-22 21:14:20,612 - websocket_proxy - INFO - WebSocket server successfully started on localhost:8765
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
An exception was thrown while monkey_patching for eventlet. to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
Traceback (most recent call last):
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 611, in _upgrade_instances
    new = upgrade_or_traverse(v)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 588, in upgrade_or_traverse
    _upgrade_instances(obj, klass, upgrade, visited, old_to_new)
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/eventlet/patcher.py", line 602, in _upgrade_instances
    container_vars = vars(container)
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/shared-venv/lib/python3.12/site-packages/werkzeug/local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
13 RLock(s) were not greened, to fix this error make sure you run eventlet.monkey_patch() before importing any other modules.
