2025-06-22 22:22:38,606 - apscheduler.scheduler - INFO - Scheduler started
2025-06-22 22:22:38,795 - apscheduler.scheduler - INFO - Scheduler started
Initializing Traffic Logs DB
Initializing Latency DB
Initializing Auth DB
Initializing User DB
Initializing Master Contract DB
Initializing API Log DB
Initializing Analyzer Table
Initializing Settings DB
Creating default settings (Live Mode)
Initializing Chartink DB
Initializing Traffic Logs DB
Initializing Latency DB
Initializing Strategy DB
2025-06-22 22:22:41,051 - websocket_proxy.app_integration - INFO - Starting WebSocket server in Flask application process
2025-06-22 22:22:41,051 - websocket_proxy.app_integration - INFO - Starting WebSocket proxy server in a separate thread
2025-06-22 22:22:41,055 - websocket_proxy.app_integration - INFO - WebSocket proxy server thread started
2025-06-22 22:22:41,055 - websocket_proxy.app_integration - INFO - WebSocket server integration with Flask complete
2025-06-22 22:22:41,060 - websocket_proxy - INFO - Initializing ZeroMQ listener task
2025-06-22 22:22:41,061 - websocket_proxy - INFO - Running in a non-main thread. Signal handlers will not be used.
2025-06-22 22:22:41,061 - websocket_proxy - INFO - Starting WebSocket server on localhost:20012
2025-06-22 22:22:41,071 - websocket_proxy - INFO - Starting ZeroMQ listener
2025-06-22 22:22:41,072 - websockets.server - INFO - server listening on 127.0.0.1:20012
2025-06-22 22:22:41,072 - websocket_proxy - INFO - WebSocket server successfully started on localhost:20012
2025-06-22 22:22:54,056 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:26:52,483 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:27:13,033 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:29:12,829 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:29:43,769 - blueprints.core - INFO - New admin user 8012 created successfully
2025-06-22 22:29:44,117 - blueprints.core - INFO - API key created successfully for user 8012
login success
Broker is angel
2025-06-22 22:30:01,817 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:30:01,914 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:30:04,373 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:30:11,273 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:30:15,169 - utils.session - INFO - Invalid session detected - clearing session
Broker is angel
2025-06-22 22:30:23,248 - httpx - INFO - HTTP Request: POST https://apiconnect.angelbroking.com/rest/auth/angelbroking/user/v1/loginByPassword "HTTP/1.1 200 "
Connected broker: angel
2025-06-22 22:30:23,258 - utils.session - INFO - Session login time set to: 2025-06-23 04:00:23.258445+05:30
2025-06-22 22:30:23,258 - utils.auth_utils - INFO - User 8012 logged in successfully with broker angel
2025-06-22 22:30:23,276 - utils.auth_utils - INFO - Database record upserted with ID: 1
2025-06-22 22:30:23,291 - database.master_contract_status_db - INFO - Initialized master contract status for angel
2025-06-22 22:30:23,305 - database.master_contract_status_db - INFO - Updated master contract status for angel: downloading
/home/<USER>/algofactory-multi/algofactory-8012/broker/angel/database/master_contract_db.py:179: SyntaxWarning: invalid escape sequence '\.'
  df.loc[(df['instrumenttype'] == 'OPTCUR') & (df['exchange'] == 'CDS'), 'symbol'] = df['name'] + df['expiry'].str.replace('-', '', regex=False) + df['strike'].astype(str).str.replace('\.0', '', regex=True) + df['symbol'].str[-2:]
/home/<USER>/algofactory-multi/algofactory-8012/broker/angel/database/master_contract_db.py:180: SyntaxWarning: invalid escape sequence '\.'
  df.loc[(df['instrumenttype'] == 'OPTIRC') & (df['exchange'] == 'CDS'), 'symbol'] = df['name'] + df['expiry'].str.replace('-', '', regex=False) + df['strike'].astype(str).str.replace('\.0', '', regex=True) + df['symbol'].str[-2:]
/home/<USER>/algofactory-multi/algofactory-8012/broker/angel/database/master_contract_db.py:181: SyntaxWarning: invalid escape sequence '\.'
  df.loc[(df['instrumenttype'] == 'OPTFUT') & (df['exchange'] == 'MCX'), 'symbol'] = df['name'] + df['expiry'].str.replace('-', '', regex=False) + df['strike'].astype(str).str.replace('\.0', '', regex=True) + df['symbol'].str[-2:]
Downloading Master Contract
Downloading JSON data
2025-06-22 22:30:23,578 - httpx - INFO - HTTP Request: GET https://apiconnect.angelbroking.com/rest/secure/angelbroking/user/v1/getRMS "HTTP/1.1 200 "
Margin Data {'status': True, 'message': 'SUCCESS', 'errorcode': '', 'data': {'net': '0.0000', 'availablecash': '0.0000', 'availableintradaypayin': '0.0000', 'availablelimitmargin': '0.0000', 'collateral': '0.0000', 'm2munrealized': '0.0000', 'm2mrealized': '0.0000', 'utiliseddebits': '0.0000', 'utilisedspan': None, 'utilisedoptionpremium': None, 'utilisedholdingsales': None, 'utilisedexposure': None, 'utilisedturnover': None, 'utilisedpayout': '0.0000'}}
Download complete
2025-06-22 22:30:27,964 - httpx - INFO - HTTP Request: GET https://apiconnect.angelbroking.com/rest/secure/angelbroking/order/v1/getOrderBook "HTTP/1.1 200 "
No data available.
The temporary file tmp/angel.json has been deleted.
Deleting Symtoken Table
Performing Bulk Insert
2025-06-22 22:30:30,768 - httpx - INFO - HTTP Request: GET https://apiconnect.angelbroking.com/rest/secure/angelbroking/order/v1/getTradeBook "HTTP/1.1 200 "
No data available.
2025-06-22 22:30:38,575 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:30:38,616 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:30:46,605 - blueprints.settings - ERROR - Error getting analyze mode: (sqlite3.OperationalError) database is locked
[SQL: SELECT settings.id AS settings_id, settings.analyze_mode AS settings_analyze_mode 
FROM settings
 LIMIT ? OFFSET ?]
[parameters: (1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Bulk insert completed successfully with 127665 new records.
2025-06-22 22:30:49,306 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:30:49,392 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:30:49,472 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:30:49,511 - database.master_contract_status_db - INFO - Updated master contract status for angel: success
2025-06-22 22:30:49,527 - utils.auth_utils - INFO - Master contract download completed for angel
2025-06-22 22:30:49,527 - utils.auth_utils - INFO - Master Contract Database Processing Completed
2025-06-22 22:30:54,407 - httpx - INFO - HTTP Request: GET https://apiconnect.angelbroking.com/rest/secure/angelbroking/order/v1/getPosition "HTTP/1.1 200 "
No data available.
2025-06-22 22:31:00,290 - blueprints.tv_json - INFO - Processing TradingView request - Symbol: ETERNAL, Exchange: NSE, Product: MIS
2025-06-22 22:31:00,355 - blueprints.tv_json - INFO - Found matching symbol: ETERNAL
2025-06-22 22:31:00,355 - blueprints.tv_json - INFO - Successfully generated TradingView webhook data
2025-06-22 22:31:19,633 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:31:37,739 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:32:07,743 - httpx - INFO - HTTP Request: GET https://apiconnect.angelbroking.com/rest/secure/angelbroking/user/v1/getRMS "HTTP/1.1 200 "
Margin Data {'status': True, 'message': 'SUCCESS', 'errorcode': '', 'data': {'net': '0.0000', 'availablecash': '0.0000', 'availableintradaypayin': '0.0000', 'availablelimitmargin': '0.0000', 'collateral': '0.0000', 'm2munrealized': '0.0000', 'm2mrealized': '0.0000', 'utiliseddebits': '0.0000', 'utilisedspan': None, 'utilisedoptionpremium': None, 'utilisedholdingsales': None, 'utilisedexposure': None, 'utilisedturnover': None, 'utilisedpayout': '0.0000'}}
2025-06-22 22:32:13,370 - httpx - INFO - HTTP Request: GET https://apiconnect.angelbroking.com/rest/secure/angelbroking/order/v1/getPosition "HTTP/1.1 200 "
No data available.
2025-06-22 22:32:15,342 - blueprints.strategy - INFO - Fetching strategies for user: 8012
2025-06-22 22:32:15,343 - database.strategy_db - INFO - Fetching strategies for user: 8012
2025-06-22 22:32:15,435 - database.strategy_db - INFO - Found 0 strategies
2025-06-22 22:32:25,844 - httpx - INFO - HTTP Request: GET https://apiconnect.angelbroking.com/rest/secure/angelbroking/user/v1/getRMS "HTTP/1.1 200 "
Margin Data {'status': True, 'message': 'SUCCESS', 'errorcode': '', 'data': {'net': '0.0000', 'availablecash': '0.0000', 'availableintradaypayin': '0.0000', 'availablelimitmargin': '0.0000', 'collateral': '0.0000', 'm2munrealized': '0.0000', 'm2mrealized': '0.0000', 'utiliseddebits': '0.0000', 'utilisedspan': None, 'utilisedoptionpremium': None, 'utilisedholdingsales': None, 'utilisedexposure': None, 'utilisedturnover': None, 'utilisedpayout': '0.0000'}}
2025-06-22 22:32:37,859 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:32:38,026 - utils.session - INFO - Invalid session detected - clearing session
Database Upserted record with ID: 1
Auth Revoked in the Database
2025-06-22 22:33:28,773 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/auth/login', 200, 2.5806427001953125, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:33:35,328 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:33:35,330 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/', 200, 1.5773773193359375, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:33:37,128 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/auth/login', 200, 2.2776126861572266, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:33:37,964 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:33:37,966 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/', 200, 1.135110855102539, 'localhost:8012', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:34:06,280 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/auth/login', 200, 3.1249523162841797, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:34:07,774 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/auth/login', 200, 2.0852088928222656, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:34:11,198 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:34:11,200 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/', 200, 1.0380744934082031, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
login success
2025-06-22 22:34:15,200 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'POST', '/auth/login', 200, 293.1780815124512, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:34:15,238 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/auth/broker', 200, 4.497528076171875, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:34:16,323 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:34:16,324 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/', 200, 1.0030269622802734, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Broker is angel
2025-06-22 22:34:16,509 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/angel/callback', 200, 4.526615142822266, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:34:17,199 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/@vite/env', 404, 0.8084774017333984, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:34:17,844 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/actuator/env', 404, 0.9086132049560547, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:34:18,490 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/server', 404, 1.1818408966064453, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:34:19,122 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/.vscode/sftp.json', 404, 0.7648468017578125, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:34:19,763 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/about', 404, 0.9069442749023438, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:34:20,417 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/debug/default/view', 404, 0.7760524749755859, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:34:21,077 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/v2/_catalog', 404, 0.7679462432861328, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:34:21,739 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/ecp/Current/exporttool/microsoft.exchange.ediscovery.exporttool.application', 404, 0.743865966796875, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:34:22,392 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/server-status', 404, 0.8122920989990234, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:34:23,032 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/login.action', 404, 0.7326602935791016, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:34:23,669 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/_all_dbs', 404, 1.1255741119384766, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:34:24,314 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/.DS_Store', 404, 0.9150505065917969, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:34:24,967 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/.env', 404, 0.7188320159912109, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:34:25,598 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/.git/config', 404, 0.9276866912841797, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:34:26,253 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/s/530313e2737313e2230323e23313/_/;/META-INF/maven/com.atlassian.jira/jira-webapp-dist/pom.properties', 404, 0.8018016815185547, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:34:26,890 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/config.json', 404, 0.7317066192626953, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:34:27,550 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/telescope/requests', 404, 0.7610321044921875, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:34:28,204 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/info.php', 404, 0.7355213165283203, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:34:28,833 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:34:28,836 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/', 200, 2.7785301208496094, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:34:38,074 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:34:38,076 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/', 200, 1.4200210571289062, 'localhost:8012', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:34:42,117 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:34:42,118 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'HEAD', '/', 200, 1.016855239868164, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:34:42,996 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:34:42,998 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/', 200, 0.9865760803222656, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:34:45,116 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:34:45,118 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/', 200, 1.0213851928710938, 'localhost:8012', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:34:45,164 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:34:45,168 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/', 200, 1.760721206665039, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:35:38,183 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:35:38,186 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/', 200, 1.7652511596679688, 'localhost:8012', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:36:03,202 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:36:03,204 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/', 200, 1.3685226440429688, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:36:04,449 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:36:04,451 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/', 200, 1.461029052734375, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:36:38,292 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:36:38,298 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/', 200, 1.0132789611816406, 'localhost:8012', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:37:42,300 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:37:43,966 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/', 200, 1146.0044384002686, 'localhost:8012', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:38:44,140 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:38:44,165 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/', 200, 10.110616683959961, 'localhost:8012', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:39:44,273 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:39:44,291 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/', 200, 6.872415542602539, 'localhost:8012', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:40:44,387 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:40:44,391 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/', 200, 1.4486312866210938, 'localhost:8012', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:41:44,511 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:41:44,519 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/', 200, 1.0066032409667969, 'localhost:8012', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:42:44,615 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:42:44,619 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/', 200, 2.991199493408203, 'localhost:8012', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:43:44,709 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:43:44,710 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/', 200, 1.0027885437011719, 'localhost:8012', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:44:44,832 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:44:44,841 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/', 200, 4.294157028198242, 'localhost:8012', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:45:44,931 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:45:44,933 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/', 200, 1.7986297607421875, 'localhost:8012', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:45:46,402 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:45:46,410 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/', 200, 3.5157203674316406, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:45:47,196 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/favicon.png', 404, 105.79228401184082, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:46:03,251 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:46:03,257 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/', 200, 3.0586719512939453, '8012.algofactory.in', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 22:46:45,059 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:46:45,067 - database.traffic_db - ERROR - Error logging traffic: (sqlite3.OperationalError) attempt to write a readonly database
[SQL: INSERT INTO traffic_logs (client_ip, method, path, status_code, duration_ms, host, error, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id, timestamp]
[parameters: ('127.0.0.1', 'GET', '/', 200, 2.03704833984375, 'localhost:8012', None, None)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
