2025-06-22 22:22:38,606 - apscheduler.scheduler - INFO - Scheduler started
2025-06-22 22:22:38,795 - apscheduler.scheduler - INFO - Scheduler started
Initializing Traffic Logs DB
Initializing Latency DB
Initializing Auth DB
Initializing User DB
Initializing Master Contract DB
Initializing API Log DB
Initializing Analyzer Table
Initializing Settings DB
Creating default settings (Live Mode)
Initializing Chartink DB
Initializing Traffic Logs DB
Initializing Latency DB
Initializing Strategy DB
2025-06-22 22:22:41,051 - websocket_proxy.app_integration - INFO - Starting WebSocket server in Flask application process
2025-06-22 22:22:41,051 - websocket_proxy.app_integration - INFO - Starting WebSocket proxy server in a separate thread
2025-06-22 22:22:41,055 - websocket_proxy.app_integration - INFO - WebSocket proxy server thread started
2025-06-22 22:22:41,055 - websocket_proxy.app_integration - INFO - WebSocket server integration with Flask complete
2025-06-22 22:22:41,060 - websocket_proxy - INFO - Initializing ZeroMQ listener task
2025-06-22 22:22:41,061 - websocket_proxy - INFO - Running in a non-main thread. Signal handlers will not be used.
2025-06-22 22:22:41,061 - websocket_proxy - INFO - Starting WebSocket server on localhost:20012
2025-06-22 22:22:41,071 - websocket_proxy - INFO - Starting ZeroMQ listener
2025-06-22 22:22:41,072 - websockets.server - INFO - server listening on 127.0.0.1:20012
2025-06-22 22:22:41,072 - websocket_proxy - INFO - WebSocket server successfully started on localhost:20012
2025-06-22 22:22:54,056 - utils.session - INFO - Invalid session detected - clearing session
