# AlgoFactory Multi-Instance Architecture Roadmap

## 🎯 **Objective**
Create a scalable multi-tenant system where each user gets their own isolated AlgoFactory instance with unique ports and subdomains.

## 🏗️ **Architecture Overview**

### **Port Allocation Strategy**
```
Instance ID: 1010, 1011, 1012, etc.
- Flask Port: 1000 + instance_id (1010, 1011, 1012...)
- WebSocket Port: 12000 + instance_id (12010, 12011, 12012...)
- ZMQ Port: 15000 + instance_id (15010, 15011, 15012...)
```

### **Domain Strategy**
```
- 1010.algofactory.in → Instance 1010 (Port 1010)
- 1011.algofactory.in → Instance 1011 (Port 1011)
- 1012.algofactory.in → Instance 1012 (Port 1012)
```

### **Folder Structure**
```
/home/<USER>/algofactory-multi/
├── template/                    # Master template (copy of current algofactory)
│   ├── app.py
│   ├── requirements.txt
│   ├── .env.template
│   └── ... (all current files)
├── instances/
│   ├── algofactory-1010/       # Instance 1010
│   │   ├── app.py
│   │   ├── .env                # Port 1010, WebSocket 12010, ZMQ 15010
│   │   ├── db/                 # Isolated database
│   │   └── logs/               # Instance-specific logs
│   ├── algofactory-1011/       # Instance 1011
│   └── algofactory-1012/       # Instance 1012
├── manager/                     # Management scripts
│   ├── instance_manager.py     # Main management script
│   ├── create_instance.sh      # Create new instance
│   ├── start_all.sh           # Start all instances
│   ├── stop_all.sh            # Stop all instances
│   └── monitor_all.py         # Monitor all instances
├── nginx/                       # Nginx configurations
│   ├── template.conf           # Nginx template
│   └── sites/                  # Generated configs
│       ├── 1010.algofactory.in.conf
│       └── 1011.algofactory.in.conf
└── monitoring/                  # Centralized monitoring
    ├── dashboard.html          # Multi-instance dashboard
    └── status_api.py           # Status API for all instances
```

## 🚀 **Implementation Phases**

### **Phase 1: Setup Multi-Instance Foundation**
1. Create folder structure
2. Copy current algofactory as template
3. Create instance manager script
4. Test with 2 instances (1010, 1011)

### **Phase 2: Nginx & SSL Automation**
1. Create dynamic Nginx configuration generator
2. Setup automatic SSL certificate generation
3. Configure wildcard DNS (*.algofactory.in)
4. Test subdomain routing

### **Phase 3: Instance Management**
1. Build web-based instance manager
2. Create user assignment system
3. Implement instance lifecycle management
4. Add resource monitoring

### **Phase 4: Monitoring & Scaling**
1. Centralized monitoring dashboard
2. Auto-scaling based on demand
3. Health checks and auto-restart
4. Performance optimization

## 🔧 **Key Components to Build**

### **1. Instance Manager Script**
```bash
./instance_manager.py create 1010    # Create instance 1010
./instance_manager.py start 1010     # Start instance 1010
./instance_manager.py stop 1010      # Stop instance 1010
./instance_manager.py delete 1010    # Delete instance 1010
./instance_manager.py list           # List all instances
./instance_manager.py status         # Status of all instances
```

### **2. Environment Template**
```env
# Instance-specific configuration
INSTANCE_ID=1010
FLASK_PORT=1010
WEBSOCKET_PORT=12010
ZMQ_PORT=15010
DATABASE_URL=sqlite:///db/algofactory-1010.db
HOST_SERVER=https://1010.algofactory.in
```

### **3. Nginx Template**
```nginx
server {
    listen 443 ssl http2;
    server_name {INSTANCE_ID}.algofactory.in;
    
    location / {
        proxy_pass http://127.0.0.1:{FLASK_PORT};
        # ... other proxy settings
    }
}
```

### **4. SystemD Service Template**
```ini
[Unit]
Description=AlgoFactory Instance {INSTANCE_ID}

[Service]
ExecStart=/home/<USER>/algofactory-multi/instances/algofactory-{INSTANCE_ID}/start.sh monitor
WorkingDirectory=/home/<USER>/algofactory-multi/instances/algofactory-{INSTANCE_ID}
```

## 📊 **Resource Planning**

### **Port Ranges**
- Flask: 1010-1999 (990 instances max)
- WebSocket: 12010-12999 (990 instances max)
- ZMQ: 15010-15999 (990 instances max)

### **System Resources**
- Each instance: ~200MB RAM
- 10 instances: ~2GB RAM
- 50 instances: ~10GB RAM

### **Storage**
- Each instance: ~100MB disk
- Logs: ~10MB per day per instance
- Database: Variable based on usage

## 🔐 **Security Considerations**

1. **Isolation**: Each instance has separate database and files
2. **Firewall**: Only necessary ports exposed
3. **SSL**: Automatic certificate generation for each subdomain
4. **Access Control**: User-to-instance mapping
5. **Resource Limits**: CPU and memory limits per instance

## 🎯 **Success Metrics**

1. **Scalability**: Ability to create 100+ instances
2. **Performance**: <2 second instance creation
3. **Reliability**: 99.9% uptime per instance
4. **Management**: Web-based instance management
5. **Monitoring**: Real-time status of all instances

## 🚀 **Next Steps**

1. **Start with Phase 1**: Create basic multi-instance setup
2. **Test with 2-3 instances**: Validate the concept
3. **Automate Nginx**: Dynamic configuration generation
4. **Build management interface**: Web-based control panel
5. **Scale gradually**: Add monitoring and optimization

## 💡 **Benefits**

1. **User Isolation**: Each user has dedicated environment
2. **Scalability**: Easy to add new instances
3. **Reliability**: One instance failure doesn't affect others
4. **Customization**: Each instance can have different configurations
5. **Resource Management**: Better resource allocation and monitoring
