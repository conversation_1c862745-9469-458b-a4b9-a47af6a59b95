2025-06-22 22:16:43,134 - apscheduler.scheduler - INFO - Scheduler started
2025-06-22 22:16:43,230 - apscheduler.scheduler - INFO - Scheduler started
Initializing Traffic Logs DB
Initializing Latency DB
Initializing Auth DB
Initializing User DB
Initializing Master Contract DB
Initializing API Log DB
Initializing Analyzer Table
Initializing Settings DB
Creating default settings (Live Mode)
Initializing Chartink DB
Initializing Traffic Logs DB
Initializing Latency DB
Initializing Strategy DB
2025-06-22 22:16:44,480 - websocket_proxy.app_integration - INFO - Starting WebSocket server in Flask application process
2025-06-22 22:16:44,480 - websocket_proxy.app_integration - INFO - Starting WebSocket proxy server in a separate thread
2025-06-22 22:16:44,489 - websocket_proxy.app_integration - INFO - WebSocket proxy server thread started
2025-06-22 22:16:44,489 - websocket_proxy.app_integration - INFO - WebSocket server integration with Flask complete
2025-06-22 22:16:44,511 - websocket_proxy - INFO - Initializing ZeroMQ listener task
2025-06-22 22:16:44,511 - websocket_proxy - INFO - Running in a non-main thread. Signal handlers will not be used.
2025-06-22 22:16:44,511 - websocket_proxy - INFO - Starting WebSocket server on localhost:20010
2025-06-22 22:16:44,525 - websocket_proxy - INFO - Starting ZeroMQ listener
2025-06-22 22:16:44,527 - websockets.server - INFO - server listening on 127.0.0.1:20010
2025-06-22 22:16:44,530 - websocket_proxy - INFO - WebSocket server successfully started on localhost:20010
2025-06-22 22:16:57,188 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:26:49,791 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:27:46,006 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:27:46,812 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:27:49,496 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:27:50,270 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:27:50,744 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:27:52,264 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:27:56,544 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:28:32,502 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:28:33,240 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:28:39,739 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:28:43,633 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:28:54,700 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:29:36,319 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:31:15,369 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:31:37,675 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:32:37,807 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:33:37,917 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:34:38,022 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:34:44,902 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:34:44,971 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:35:01,388 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:35:38,124 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:35:48,191 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:35:50,614 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:35:52,815 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:36:08,595 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:36:25,267 - blueprints.core - INFO - New admin user 8010 created successfully
2025-06-22 22:36:26,351 - blueprints.core - INFO - API key created successfully for user 8010
2025-06-22 22:36:38,235 - utils.session - INFO - Invalid session detected - clearing session
login success
Broker is angel
Broker is angel
2025-06-22 22:36:55,956 - httpx - INFO - HTTP Request: POST https://apiconnect.angelbroking.com/rest/auth/angelbroking/user/v1/loginByPassword "HTTP/1.1 200 "
Connected broker: angel
2025-06-22 22:36:56,034 - utils.session - INFO - Session login time set to: 2025-06-23 04:06:56.034679+05:30
2025-06-22 22:36:56,037 - utils.auth_utils - INFO - User 8010 logged in successfully with broker angel
2025-06-22 22:36:56,060 - utils.auth_utils - INFO - Database record upserted with ID: 1
2025-06-22 22:36:56,133 - database.master_contract_status_db - INFO - Initialized master contract status for angel
2025-06-22 22:36:56,170 - database.master_contract_status_db - INFO - Updated master contract status for angel: downloading
/home/<USER>/algofactory-multi/algofactory-8010/broker/angel/database/master_contract_db.py:179: SyntaxWarning: invalid escape sequence '\.'
  df.loc[(df['instrumenttype'] == 'OPTCUR') & (df['exchange'] == 'CDS'), 'symbol'] = df['name'] + df['expiry'].str.replace('-', '', regex=False) + df['strike'].astype(str).str.replace('\.0', '', regex=True) + df['symbol'].str[-2:]
/home/<USER>/algofactory-multi/algofactory-8010/broker/angel/database/master_contract_db.py:180: SyntaxWarning: invalid escape sequence '\.'
  df.loc[(df['instrumenttype'] == 'OPTIRC') & (df['exchange'] == 'CDS'), 'symbol'] = df['name'] + df['expiry'].str.replace('-', '', regex=False) + df['strike'].astype(str).str.replace('\.0', '', regex=True) + df['symbol'].str[-2:]
/home/<USER>/algofactory-multi/algofactory-8010/broker/angel/database/master_contract_db.py:181: SyntaxWarning: invalid escape sequence '\.'
  df.loc[(df['instrumenttype'] == 'OPTFUT') & (df['exchange'] == 'MCX'), 'symbol'] = df['name'] + df['expiry'].str.replace('-', '', regex=False) + df['strike'].astype(str).str.replace('\.0', '', regex=True) + df['symbol'].str[-2:]
2025-06-22 22:36:56,449 - httpx - INFO - HTTP Request: GET https://apiconnect.angelbroking.com/rest/secure/angelbroking/user/v1/getRMS "HTTP/1.1 200 "
Margin Data {'status': True, 'message': 'SUCCESS', 'errorcode': '', 'data': {'net': '0.0000', 'availablecash': '0.0000', 'availableintradaypayin': '0.0000', 'availablelimitmargin': '0.0000', 'collateral': '0.0000', 'm2munrealized': '0.0000', 'm2mrealized': '0.0000', 'utiliseddebits': '0.0000', 'utilisedspan': None, 'utilisedoptionpremium': None, 'utilisedholdingsales': None, 'utilisedexposure': None, 'utilisedturnover': None, 'utilisedpayout': '0.0000'}}
Downloading Master Contract
Downloading JSON data
Download complete
The temporary file tmp/angel.json has been deleted.
Deleting Symtoken Table
Performing Bulk Insert
Bulk insert completed successfully with 127665 new records.
2025-06-22 22:37:20,024 - database.master_contract_status_db - INFO - Updated master contract status for angel: success
2025-06-22 22:37:20,027 - utils.auth_utils - INFO - Master contract download completed for angel
2025-06-22 22:37:20,027 - utils.auth_utils - INFO - Master Contract Database Processing Completed
2025-06-22 22:37:38,544 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:38:30,939 - blueprints.apikey - INFO - Checking API key status for user: 8010
2025-06-22 22:38:44,082 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:39:44,215 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:40:44,338 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:41:44,469 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:42:44,565 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:43:44,662 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:44:44,754 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:45:44,885 - utils.session - INFO - Invalid session detected - clearing session
2025-06-22 22:46:44,993 - utils.session - INFO - Invalid session detected - clearing session
